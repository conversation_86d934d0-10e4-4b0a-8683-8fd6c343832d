<script>
import FinishedFeedback from "@/views/plan/components/FinishedFeedback/index.vue"
import ProgressFeedback from "@/views/plan/components/ProgressFeedback/index.vue";
import ResultFile from "@/views/plan/components/ResultFile/index.vue";
import {finishFeedback, progressFeedback, getFeedback, flowCreate, daiban<PERSON>lowCreat} from "@/api/plan/feedback";
import {formatDateWithTime} from '@/utils';
export default {
  components: {ResultFile, FinishedFeedback, ProgressFeedback},
  data(){
    return {
      // feedback: {},
      currentTabComponent: null,
      resultOpen: false,
      feedbackDetail: {},
      form: {},
      nodeOutcomeDocumentList: [],
      loading: false,
    }
  },
  created() {
    // this.componentFilter(this.$route.query.feedbackType);
    this.componentFilter();
    this.handleGetFeedback();
  },
  methods: {
    async handleGetFeedback(){
      let planId = this.$route.query.planId;
      // console.log("planId = " + planId)
      let id = this.$route.query.id;
      // console.log("id = " + id)
      // let feedbackType = this.form.feedbackType = this.$route.query.feedbackType;
      getFeedback(planId, id).then(response => {
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList || [];
        this.form.nodeId = response.data.id;
        this.form.feedbackProgress = response.data.feedbackProgress || 0; // 如果没有反馈进度为null，表单校验报错
        this.form.expectedCompletionDate = response.data.expectedCompletionDate || new Date();
        this.form.actualCompletionDate = response.data.actualCompletionDate || formatDateWithTime(new Date());
        this.form.notes = response.data.notes;
        /*if (feedbackType == 2) {
          this.form.feedbackProgress = 100;
        }*/
        this.form.feedbackProgress = 100;
        console.log(this.feedbackDetail)
        this.$refs.feedback.handleSetForm(this.form, this.feedbackDetail);
        this.$refs.feedback.handleSetNodeOutcomeDocumentList(this.nodeOutcomeDocumentList);
      });
    },
    componentFilter(type){
      /*if(type == 1) { // 计划反馈
        // console.log('计划反馈')
        this.currentTabComponent = ProgressFeedback
      } else if(type == 2) { // 完成反馈
        // console.log('完成反馈')
        this.currentTabComponent = FinishedFeedback
      }*/
      this.currentTabComponent = FinishedFeedback
    },
    submitForm() {
      /*const obj = {
        path: "/IAMFeedback"
      };*/
      this.$forceUpdate();
      this.$refs.feedback.$refs["form"].validate(valid => {
        if (valid && this.$refs.feedback.fileValidate()) {
          // 过程反馈、完成反馈接口判断
          let form = this.$refs.feedback.form;
          let nodeOutcomeDocumentList = this.$refs.feedback.handleGetNodeOutcomeDocumentList();
          // let feedbackPost = form.feedbackType === 1 ? progressFeedback : finishFeedback; // 1 过程反馈，2 完成反馈
          let feedbackPost = finishFeedback; // 1 过程反馈，2 完成反馈
          this.loading = true;
          feedbackPost({
            ...form,
            planId: this.$route.query.planId,
            nodeOutcomeDocumentList
          }).then(response => {
            //保存成功后发起流程
            flowCreate({
              ...this.form,
              planId: this.$route.query.planId,
            }).then(response => {
              window.open(response.data)
              this.$modal.msgSuccess("节点反馈成功");
              window.close();

              // this.$tab.closeOpenPage(obj);
              /*this.$router.push({
                path: "/plan/feedback"
              });*/
              this.loading = false;
            }).catch(() => {
              this.loading = false;
            })
            ;
          }).catch(() => {
              this.loading = false;
            })
          ;
        }
      });
    },
    handleResultFileConfirm(){ // 获取成果文件选择弹窗选中的文件
      this.handleResultOpen(false);
      this.selectedFileList = this.$refs.resultFile.getSelectedFileList();
      this.submitResultFile();
    },
    handleResultOpen(open){ // 打开、关闭成果文件选择弹窗
      this.resultOpen = open;
    },
    submitResultFile() { // 提交成果文件
      for (let item of this.selectedFileList) {
        item.resultConfigId = item.id;
        delete item.id;
      }

      let nodeOutcomeDocumentList = this.$refs.feedback.handleGetNodeOutcomeDocumentList();
      nodeOutcomeDocumentList = nodeOutcomeDocumentList.concat(
        this.selectedFileList.map(item => {
          return {
            ...item,
            createTime: null,
            fileList: [],
          };
        }));
      this.$refs.feedback.handleSetNodeOutcomeDocumentList(nodeOutcomeDocumentList);
    },
  }
}
</script>

<template>
  <div class="app-container" v-loading="loading">
      <component v-bind:is="currentTabComponent" ref="feedback" :feedbackDetail="feedbackDetail" @handleResultOpen="handleResultOpen"></component>
      <el-row v-if="feedbackDetail.feedbackFlowStatus != 1 && feedbackDetail.feedbackFlowStatus != 2 " class="mt20" style="display: flex; justify-content: center;">
        <el-button  type="primary" @click="submitForm">发起审批</el-button>
      </el-row>
    <!--  成果文件选择  -->
    <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
      <ResultFile ref="resultFile"></ResultFile>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResultFileConfirm">确 认</el-button>
        <el-button @click="handleResultOpen(false)">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
