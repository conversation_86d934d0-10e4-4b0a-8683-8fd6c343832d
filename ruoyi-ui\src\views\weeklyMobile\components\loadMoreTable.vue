<template>
    <div class="table-container" ref="tableContainer">
      <!-- 表格头部 -->
      <div class="table-header">
        <div class="table-cell">部门</div>
        <div class="table-cell">姓名</div>
        <div class="table-cell">周期</div>
      </div>

      <!-- 表格内容区域 -->
      <div
        class="table-body"
        ref="tableBody"
        @scroll="handleScroll"
      >
        <div v-for="(item, index) in list" :key="index" class="table-row">
          <div class="table-cell">{{ item.deptName }}</div>
          <div class="table-cell">{{ item.nickName }}</div>
          <div class="table-cell">第{{ item.week }}周({{ filterDayByWeekNum(weekOptions, item.week) }})</div>
<!--          ({{ item.startDate }}-{{ item.endDate }})-->
        </div>
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
          <span class="loading-text">加载中...</span>
        </div>

        <!-- 无更多数据 -->
        <div v-if="finished && list.length > 0" class="no-more">
          没有更多数据了
        </div>

        <!-- 无数据提示 -->
        <div v-if="list.length === 0 && !loading" class="empty-data">
          暂无数据
        </div>
      </div>
    </div>
  </template>

  <script>
  import { Loading, Toast } from 'vant';
  export default {
    name: 'LoadMoreTable',
    components: {
      [Loading.name]: Loading,
      [Toast.name]: Toast
    },
    props: {
      // 是否自动加载（初始化时）
      autoLoad: {
        type: Boolean,
        default: true
      },
      // 每页加载数量
      pageSize: {
        type: Number,
        default: 20
      },
      // 加载数据的API函数，需要返回Promise
      fetchApi: {
        type: Function,
        default: null
      },
      // 查询参数
      queryParams: {
        type: Object,
        default: () => ({})
      },
      weekOptions: { // 周时间列表
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        list: [], // 表格数据
        loading: false, // 加载状态
        finished: false, // 是否已加载全部
        currentPage: 1, // 当前页码
        total: 0, // 总数据量
        scrollLock: false // 滚动锁，防止频繁触发
      }
    },
   /*  watch: {
      // 监听查询参数变化，重置并重新加载
      queryParams: {
        handler() {
          this.reset();
        },
        deep: true
      }
    }, */
    mounted() {
      // 添加滚动事件监听
      this.$nextTick(() => {
        const tableBody = this.$refs.tableBody;
        if (tableBody) {
          tableBody.addEventListener('scroll', this.handleScroll);
        }

        if (this.autoLoad) {
          this.loadData();
        }
      });
    },
    beforeDestroy() {
      // 移除滚动事件监听，防止内存泄漏
      const tableBody = this.$refs.tableBody;
      if (tableBody) {
        tableBody.removeEventListener('scroll', this.handleScroll);
      }
    },
    methods: {
      filterDayByWeekNum(options, weekNum){
        return this.weekOptions.find(item => item.weekNum === weekNum).weekDay;
      },
      // 处理滚动事件
      handleScroll() {
        // 如果已经在加载或已完成，则不处理
        if (this.loading || this.finished || this.scrollLock) return;

        const tableBody = this.$refs.tableBody;
        if (!tableBody) return;

        const { scrollTop, scrollHeight, clientHeight } = tableBody;

        // 滚动到底部前50px时触发加载
        if (scrollHeight - scrollTop - clientHeight < 50) {
          // 设置滚动锁，防止频繁触发
          this.scrollLock = true;
          setTimeout(() => {
            this.scrollLock = false;
          }, 200);

          this.loadMore();
        }
      },

      // 加载更多数据
      loadMore() {
        console.log('加载更多');
        if (this.loading || this.finished) return;
        this.currentPage++;
        this.loadData();
      },
      initLoadData() {
        this.currentPage = 1;
        this.finished = false;
        this.loadData();
      },
      // 加载数据
      loadData() {
        Toast.loading({
          message: 'Loading...',
          forbidClick: true,
        });
        console.log('加载数据');
        this.loading = true;
        // 使用传入的API函数或默认的模拟函数
        // const fetchFunction = this.fetchApi || this.fetchData;
        const fetchFunction = this.fetchApi;

        fetchFunction({
          pageNum: this.currentPage,
          ...this.queryParams,
          pageSize: this.pageSize,
        }).then(res => {
          if(this.currentPage === 1){
            this.list = [];
          }
          if (res.rows && res.rows.length > 0) {
            this.list = [...this.list, ...res.rows];
            this.total = res.total || 0;

            // 判断是否已加载全部数据
            if (this.list.length >= this.total || res.rows.length < this.pageSize) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;

          // 触发加载完成事件
          this.$emit('loaded', {
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            total: this.total,
            list: this.list
          });
        }).catch(err => {
          console.error('加载数据失败', err);
          this.loading = false;
          this.$emit('error', err);
        }).finally(() => {
          Toast.clear();
        });
      },

      // 模拟API请求，实际使用时替换为真实API
      fetchData(params) {
        return new Promise(resolve => {
          setTimeout(() => {
            // 模拟数据
            const mockData = Array.from({ length: this.pageSize }, (_, i) => {
              const index = (params.page - 1) * this.pageSize + i;
              // 模拟数据到达上限
              if (index >= 50) return null;

              return {
                deptName: '运营部',
                userName: ['张涛', '李涛', '王涛'][index % 3],
                week: (index % 4) + 1,
                startDate: '1/1',
                endDate: '1/7'
              };
            }).filter(Boolean); // 过滤掉null值

            resolve({
              data: mockData,
              total: 50
            });
          }, 500); // 模拟网络延迟
        });
      },

      // 重置列表并重新加载
      reset() {
        this.list = [];
        this.currentPage = 1;
        this.finished = false;
        console.log('重置并重新加载');
        this.loadData();
      },

      // 手动触发加载更多
      triggerLoadMore() {
        if (!this.loading && !this.finished) {
          this.loadMore();
        }
      }
    }
  }
  </script>

  <style scoped lang="scss">
  .table-container {
    width: 100%;
    background-color:#EBF0F8;
    border-radius: 8px;
  }

  .table-header {
    display: flex;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .table-body {
    min-height: 60vh;
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .table-row {
    display: flex;
    border-bottom: 1px solid #ebeef5;
    /* background-color: #fff; */
  }

  .table-row:nth-child(even) {
    /* background-color: #fafafa; */
  }

  .table-cell {
    flex: 1;
    padding: 12px 4px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 设置列宽比例 */
  .table-cell:nth-child(1) {
    flex: 1.5; /* 部门列稍窄 */
  }

  .table-cell:nth-child(2) {
    flex: 0.7; /* 姓名列最窄 */
  }

  .table-cell:nth-child(3) {
    flex: 1.5; /* 周期列最宽 */
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
  }

  .loading-text {
    margin-left: 8px;
    color: #909399;
  }

  .no-more {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 10px 0;
  }

  .empty-data {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 30px 0;
  }
  </style>
