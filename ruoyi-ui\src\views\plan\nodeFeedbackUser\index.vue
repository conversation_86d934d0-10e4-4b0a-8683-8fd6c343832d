<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="责任人" prop="responsibilityPeople">
        <el-input
          suffix-icon="el-icon-search"
          v-model="responsibilityerNameOld"
          placeholder="请选择要提换的责任人"
          clearable
          @keyup.enter.native="handleQuery"
          @focus="selectFeedbackFun('responsibilityer')"
        />
        <el-input
          v-model="queryParams.responsibilityPeople"
          v-show="false"
          placeholder="请选择要提换的责任人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="节点反馈人" prop="feedbackUserName">
        <el-input
          v-model="queryParams.feedbackUserName"
          placeholder="请输入节点反馈人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="责任部门" prop="department" label-width="100px">
        <el-select clearable v-model="queryParams.department" placeholder="请选择责任部门">
          <el-option v-for="dict in dict.type.resp_department"
                     :key="dict.value"
                     :label="dict.label"
                     :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="分期" prop="stageId">
        <el-select v-model="queryParams.stageId" placeholder="请选择分期">
          <el-option
            v-for="dict in dict.type.stages"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>



    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="button"
          plain
          icon="el-icon-edit"
          class="el-button el-button--success el-button--mini is-plain"
          size="mini"
          @click="handleEditDeapt"
          >批量修改责任人</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="button"
          plain
          icon="el-icon-edit"
          class="el-button el-button--success el-button--mini is-plain"
          size="mini"
          @click="handleFeedbackUser"
          >批量修改反馈人</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:info:export']"
          >导出</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="feedbackUserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" align="center" type="index"/>
      <el-table-column label="节点名称" align="center" prop="nodeName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.nodeName)">{{ scope.row.nodeName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点状态" align="center" prop="status">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column label="计划名称" align="center" prop="planName" width="100">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.planName)">{{ scope.row.planName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" width="100">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.projectName)">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成标准" align="center" prop="completeCriteria"/>
      <el-table-column label="计划开始时间" width="100" align="center" prop="startTime"/>
      <el-table-column label="计划完成时间" width="100" align="center" prop="endTime"/>
      <!-- <el-table-column label="预计完成时间" width="100" align="center" prop="expectedCompletionDate"/> -->

      <!-- <el-table-column label="实际完成时间" width="100" align="center" prop="actualCompletionDate"/>
      <el-table-column label="实际偏差" width="100" align="center" prop="deviationNum">
        <template slot-scope="scope">
          {{!!scope.row.deviationNum ? scope.row.deviationNum : '--'}}
        </template>
      </el-table-column> -->
      <el-table-column label="责任部门" align="center" prop="departmentName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.departmentName)">{{ scope.row.departmentName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="责任人" align="center" prop="respPeopleName"/>
      <el-table-column label="反馈人" align="center" prop="feedbackUserName"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
        <!--    计划按期完成显示查看按钮，未按期完成显示反馈      -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >详情
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <div class="feedback-container">
        <div class="feedback-detail">
          <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
          <el-form class="feedback-detail">
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目名称：" prop="projectName">
                  {{feedbackDetail.projectName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目分期：" prop="stageId">
                  <dict-tag :options="dict.type.stages" :value="feedbackDetail.stageId"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="节点顺序：" prop="nodeIndex">
                  {{feedbackDetail.nodeIndex || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="节点名称：" prop="nodeName">
                  {{feedbackDetail.nodeName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="计划开始时间：" prop="startTime">
                  {{feedbackDetail.startTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划完成时间：" prop="endTime">
                  {{feedbackDetail.endTime || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="完成标准：" prop="completeCriteria">
                  {{feedbackDetail.completeCriteria || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="成果文件：" prop="resultFileName">
                  {{feedbackDetail.resultFileName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="责任部门" prop="departmentName">
                  {{feedbackDetail.departmentName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="反馈人" prop="feedbackUserName">
                  {{feedbackDetail.feedbackUserName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="节点描述：" prop="nodeDesc">
                  {{feedbackDetail.nodeDesc || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleDept"
      v-if="openEditDept"
      :visible.sync="openEditDept"
      width="500px"
      v-loading = "openEditDeptLoading"
      append-to-body
    >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="老责任人" prop="departmentNames">
          <div style="width: 300px" class="flex-row">
            {{ responsibilityerNameOld }}
          </div>
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="新责任人" prop="departmentNames">
          <div style="width: 300px" class="flex-row">
            <el-input
              suffix-icon="el-icon-search"
              v-model="responsibilityerNameNew"
              placeholder="请选择责任人"
              clearable
              @focus="selectFeedbackFun('responsibilityerNew')"
            />
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDeptForm">确 定</el-button>
        <el-button @click="cancelDeptForm">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="titleFeedbackUser"
      v-if="openEditFeedbackUser"
      :visible.sync="openEditFeedbackUser"
      width="500px"
      v-loading = "openEditFeedbackUserLoading"
      append-to-body
    >
      <el-form ref="form" :model="form"  :rules="rules" label-width="80px">
        <el-form-item label="反馈人" prop="feedbackUser">
          <div style="width: 220px" class="flex-row">
            <el-input
              suffix-icon="el-icon-search"
              v-model="feedbackUserName"
              placeholder="请选择"
              clearable
              @focus="selectFeedbackFun('feedbackUser')"
            />

          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFeedbackUser">确 定</el-button>
        <el-button @click="cancelFeedbackUser">取 消</el-button>
      </div>
    </el-dialog>

<!--    <select-depart
      ref="departRef"
      :selectDepartIds="selectDepartIds"
      @departEmit="selectDepartData"
    />-->
    <select-user
      ref="userRef"
      :roleId="roleName"
      @feedbackEmit="selectFeedbackData"
    />
  </div>
</template>
<script>

  import { feedbackUserList,editDepart,editFeedbackUser } from "@/api/plan/node"
  import {
    getFeedback,
    delFeedback,
  } from "@/api/plan/feedback";
  import {nodeStatusOption, feedbackFlowStatusOption, statusOption, fillFlagOption} from '@/constant';
  import StatusTag from "@/views/plan/components/StatusTag/index.vue";
  import SelectTree from '@/components/SelectTree.vue';
  import SelectUser from "@/views/plan/components/SelectUser/allIndex.vue"
  export default {
    name: "NodeFeedbackUser",
    components: { StatusTag, SelectTree,SelectUser},
    dicts: ['stages', "resp_department"],
    data() {
      return {
        calcDate: 0,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 反馈表人列表数据
        feedbackUserList: [],

        feedbackDetail: {}, // 反馈详情
        // 弹出层标题
        title: "节点反馈人维护",
        titleFeedbackUser:"修改节点反馈人",
        titleDept:"替换节点责任人",
        selectDepartIds:[],
        roleName:"",
        // 是否显示弹出层
        open: false,
        openEditDept:false,
        openEditDeptLoading:false,
        openEditFeedbackUser:false,
        openEditFeedbackUserLoading:false,

        projectList: [],

        departmentNames:null,
        feedbackUserName:null,
        feedbackUser: null,

        responsibilityerNew:null,
        responsibilityerNameNew:null,

        responsibilityerOld:null,
        responsibilityerNameOld:null,

        selectFeedbackType: null,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 200,
          responsibilityPeople: null,
          feedbackUser: null,
          projectName: null, // 项目id
          stageId: null,
          department: null

        },
        // 表单参数
        form: {
          expectedCompletionDate: null,
          actualCompletionDate: null
        }, // 反馈表单-节点反馈内容

        // 表单校验
        rules: {
          feedbackType: [{
            required: true,
            message: '反馈内容不能为空',
            trigger: 'change'
          }],

        },
        feedbackTypeOptions: [{
          "label": "过程反馈",
          "value": 1
        }, {
          "label": "完成反馈",
          "value": 2
        }],

      };
    },
    computed: {

      nodeStatusOption() {
        return nodeStatusOption;
      },
      statusOption() {
        return statusOption
      },
      fillFlagOption() {
        return fillFlagOption
      },
      feedbackFlowStatusOption(){
        return feedbackFlowStatusOption
      },
      departList () {
        return this.$store.state.plan.departList
      }
    },
    watch:{
      //开始监听返回该对象属性值计算属性
      responsibilityerNameOld: function(newVal, oldVal){
        if(newVal ==null || newVal ==""){
          this.responsibilityerOld = null;
          this.queryParams.responsibilityPeople = null;
          this.getList();
        }
      }
    },
    created() {
      this.getList();
      //获取
      this.$store.dispatch('plan/fetchDepartList');
    },

    methods: {

      /** 查询计划反馈列表 */
      getList() {
        this.loading = true;

        feedbackUserList(this.queryParams).then(response => {
          this.feedbackUserList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },

      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {

        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.queryParams.responsibilityPeople = this.responsibilityerOld;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.responsibilityerOld = null;
        this.responsibilityerNameOld = null;
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },

      /** 新增按钮操作 */
      handleView(row) {

        const id = row.id || this.ids;
        getFeedback(row.planId, id).then(response => {
          this.feedbackDetail = response.data;
          this.feedbackDetail.feedbackUserName = row.feedbackUserName;
          this.feedbackDetail.departmentName = row.departmentName;
          this.open = true;
          this.title = "节点详情";

        });

      },

     /* selectDepartFun () {
        this.selectDepartIds= this.departments.split(',');
        this.$refs.departRef.show();
      },*/
      selectFeedbackFun (type) {
        this.selectFeedbackType = type;
        this.$refs.userRef.show()
      },
      selectDepartData (data) {
        console.log(data)
        this.departmentNames = data.map(item => item.departName).join(',')
        this.departments = data.map(item => item.department).join(',')
      },
      selectFeedbackData (data) {
        if (this.selectFeedbackType =="feedbackUser" ) {
          this.feedbackUser = data.userName
          this.feedbackUserName = data.nickName
        }
        else if (this.selectFeedbackType =="responsibilityer"){
          this.responsibilityerOld = data.userId;
          this.responsibilityerNameOld = data.nickName;
          this.queryParams.responsibilityPeople = data.userId;
          this.getList();
        }
       else{
          this.responsibilityerNew = data.userId;
          this.responsibilityerNameNew = data.nickName;
       }
      },

      /** 批量修改责任人按钮操作 */
      handleEditDeapt() {
        const ids = this.ids;
        if(ids.length>0) {
          if(this.responsibilityerOld == null){
            this.$modal.msgSuccess("请选择要替换的责任人");
          }
          else{
            this.openEditDept=true;
          }
        }
        else {
          this.$modal.msgSuccess("请选择要修改的项");
        }

      },

       /** 提交按钮 */
       submitDeptForm () {
         this.openEditDeptLoading = true;
        if(this.responsibilityerNew!=null){

            editDepart({
              ids: this.ids,
              responsibilityerOld: this.responsibilityerOld,
              responsibilityerNameOld: this.responsibilityerNameOld,
              responsibilityerNew: this.responsibilityerNew,
              responsibilityerNameNew: this.responsibilityerNameNew
            }).then(response => {
              this.openEditDeptLoading = false;
              this.$modal.msgSuccess("责任人修改成功");
              this.responsibilityerNameOld=null;
              this.responsibilityerOld=null;
              this.openEditDept = false;
              this.queryParams.pageNum = 1;
              this.getList();
            });
        }else{
          this.openEditDeptLoading = false;
          this.$modal.msgSuccess("请选择责任人");
        }

      },

        /** 批量修改反馈人按钮操作 */
      handleFeedbackUser() {
        const ids = this.ids;
        if(ids.length>0) {
          this.openEditFeedbackUser=true;
        }
        else {
          this.$modal.msgSuccess("请选择要修改的项");
        }
      },
        /** 提交按钮 */
      submitFeedbackUser () {
        this.openEditFeedbackUserLoading = true
        if(this.feedbackUser!=null){

            editFeedbackUser({
              ids: this.ids,
              feedbackUser: this.feedbackUser
            }).then(response => {
              this.openEditFeedbackUserLoading = false
              this.$modal.msgSuccess("反馈人修改成功");
              this.openEditFeedbackUser = false;
              this.queryParams.pageNum = 1;
              this.getList();
            });

        } else{
          this.openEditFeedbackUserLoading = false
          this.$modal.msgSuccess("请输入反馈人");
        }
      },

      // 取消按钮
      cancelDeptForm() {
        this.openEditDept = false;
        this.responsibilityerNameNew=null;
        this.responsibilityerNew=null;
      },

       // 取消按钮
       cancelFeedbackUser() {
        this.openEditFeedbackUser = false;
        this.feedbackUser=null;
        this.feedbackUserName=null;

        this.responsibilityerOld=null;
        this.responsibilityerNewName=null;
        this.responsibilityerNew=null;
      },

      /** 导出按钮操作 */
      handleExport() {
        this.download('plan/feedback/export', {
          ...this.queryParams
        }, `feedback_${new Date().getTime()}.xlsx`)
      }
    }
  };
</script>
<style lang="scss" scoped>
.feedback-form-title {
  margin-bottom: 20px;
  background-color: #f8f8f9;
  padding: 10px;
  width: 100%;
}
.icon-primary {
  color: #409eff;
}
</style>

