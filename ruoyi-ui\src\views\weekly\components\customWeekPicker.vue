<template>
  <div class="custom-week-picker" ref="weekPicker">
    <!-- 输入框触发器 -->
    <div class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange">
      <div class="picker-input" @click="togglePicker">
        <i class="el-input__icon el-range__icon el-icon-date"></i>
        <div class="date-display">
          <div class="start-date" v-if="startDate">{{ startDate }}</div>
          <input
            v-else
            type="text"
            readonly
            autocomplete="off"
            placeholder="开始日期"
            class="el-range-input"
          >
          <div class="el-range-separator">至</div>
          <div class="end-date" v-if="endDate">{{ endDate }}</div>
          <input
            v-else
            type="text"
            readonly
            autocomplete="off"
            placeholder="结束日期"
            class="el-range-input"
          >
        </div>
        <i v-if="!startDate && !endDate" class="el-input__icon el-range__close-icon el-icon-arrow-down"></i>
      </div>
    </div>

    <!-- 清除按钮 - 完全独立于输入框 -->
    <div
      v-if="startDate && endDate"
      class="clear-button"
      @click.stop="clearDates"
    >
      <i class="el-icon-circle-close"></i>
    </div>

    <!-- 弹出选择器 -->
    <transition name="el-zoom-in-top">
      <div v-show="showPicker" class="picker-dropdown">
        <div class="el-picker-panel el-date-picker el-popper picker-content">
          <div class="picker-layout">
            <!-- 左侧年份选择 -->
            <div class="year-panel">
              <div
                v-for="year in availableYears"
                :key="year"
                class="year-item"
                :class="{ 'active-year': selectedYear === year }"
                @click="selectYear(year)"
              >
                {{ year }}年
              </div>
            </div>

            <!-- 右侧周选择面板 -->
            <div class="week-panel">
              <div class="week-grid">
                <div
                  v-for="week in weekDetails"
                  :key="week.weekNum"
                  class="week-item"
                  :class="{
                    selected: isWeekSelected(week.weekNum),
                    'in-range': isWeekInRange(week.weekNum),
                  }"
                  @click="selectWeek(week.weekNum)"
                >
                  <div class="week-number">第{{ week.weekNum }}周</div>
                  <div class="week-dates">{{ formatShortDate(week.startDate) }}至{{ formatShortDate(week.endDate) }}</div>
                </div>
              </div>

              <!-- 周日期范围显示 -->
              <div class="week-date-display-container" v-if="selectedWeekRange">
                <div class="week-date-display">
                  {{ selectedWeekRange }}
                </div>
              </div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="el-picker-panel__footer">
            <button type="button" class="el-button el-button--mini" @click="showPicker = false">
              取消
            </button>
            <button
              type="button"
              class="el-button el-button--primary el-button--mini"
              @click="confirmSelection"
              :disabled="!canConfirm"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import dayjs from "dayjs";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

export default defineComponent({
  name: "CustomWeekPicker",
  emits: ["change"],

  data() {
    return {
      showPicker: false,
      selectedYear: new Date().getFullYear(),
      startWeek: null,
      endWeek: null,
      availableYears: [],
      weeks: [],
      weekDetails: [],
      startDate: "",
      endDate: "",
      currentMonth: new Date().getMonth() + 1
    };
  },

  computed: {
    canConfirm() {
    //   return this.startWeek !== null && (this.endWeek !== null || this.startWeek === this.endWeek);
      return this.startWeek !== null;
    },

    formatDisplayValue() {
      if (!this.startDate) return '';
      return `${this.startDate} 至 ${this.endDate}`;
    },

    selectedWeekRange() {
      if (!this.startWeek) return '';

      const startWeekData = this.weekDetails.find(w => w.weekNum === this.startWeek);
      if (!startWeekData) return '';

      if (!this.endWeek || this.endWeek === this.startWeek) {
        return `${startWeekData.startDate} 至 ${startWeekData.endDate}`;
      }

      const endWeekData = this.weekDetails.find(w => w.weekNum === this.endWeek);
      if (!endWeekData) return '';

      return `${startWeekData.startDate} 至 ${endWeekData.endDate}`;
    }
  },

  created() {
    this.initYears();
    this.initWeeks();
  },

  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },

  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },

  methods: {
    togglePicker() {
      this.showPicker = !this.showPicker;
    },

    clearDates(event) {
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      this.startDate = null;
      this.endDate = null;
      this.startWeek = null;
      this.endWeek = null;

      this.showPicker = false;

      this.$emit("change", { startDate: null, endDate: null });
    },

    handleClickOutside(event) {
      const pickerEl = this.$refs.weekPicker;
      if (pickerEl && !pickerEl.contains(event.target) && this.showPicker) {
        this.showPicker = false;
      }
    },

    initYears() {
      const currentYear = new Date().getFullYear();
      const startYear = 2025;
      this.availableYears = [];

      for (let year = startYear; year <= currentYear; year++) {
        this.availableYears.push(year);
      }

      // 如果当前年份是2025，只显示2025
      if (currentYear === 2025) {
        this.availableYears = [2025];
      }

      this.selectedYear = this.availableYears[0];
    },

    initWeeks() {
      const weeksInYear = this.getWeeksInYear(this.selectedYear);
      this.weeks = Array.from({ length: weeksInYear }, (_, i) => i + 1);

      // Generate detailed week information
      this.weekDetails = this.weeks.map(weekNum => {
        const range = this.getWeekDateRange(weekNum);
        return {
          weekNum,
          startDate: range.start,
          endDate: range.end,
          month: dayjs(range.start).month() + 1
        };
      });
    },

    getWeeksInYear(year) {
      // Last day of the year will tell us how many ISO weeks are in the year
      const lastDay = dayjs(`${year}-12-31`);
      const lastWeek = lastDay.isoWeek();

      // Handle edge case: last few days of the year might belong to week 1 of next year
      return lastDay.month() === 11 && lastWeek === 1 ? 52 : lastWeek;
    },

    getWeekDateRange(week) {
      const startDate = dayjs()
        .year(this.selectedYear)
        .isoWeek(week)
        .startOf("isoWeek");
      const endDate = startDate.endOf("isoWeek");

      return {
        start: startDate.format("YYYY-MM-DD"),
        end: endDate.format("YYYY-MM-DD"),
      };
    },

    selectYear(year) {
      if (this.selectedYear !== year) {
        this.selectedYear = year;
        this.startWeek = null;
        this.endWeek = null;
        this.initWeeks();
      }
    },

    confirmSelection(event) {
      event.preventDefault();
      if (!this.canConfirm) return;

      // If only start week is selected, use it for both start and end
      const effectiveEndWeek = this.endWeek === null ? this.startWeek : this.endWeek;

      const { start } = this.getWeekDateRange(this.startWeek);
      const { end } = this.getWeekDateRange(effectiveEndWeek);

      this.startDate = start;
      this.endDate = end;
      this.$emit("change", { startDate: start, endDate: end , startWeek: this.startWeek , endWeek: effectiveEndWeek});
      this.showPicker = false;
    },

    selectWeek(week) {
      if (!this.startWeek || (this.startWeek && this.endWeek)) {
        this.startWeek = week;
        this.endWeek = null;
      } else {
        if (week < this.startWeek) {
          this.endWeek = this.startWeek;
          this.startWeek = week;
        } else {
          this.endWeek = week;
        }
      }
    },

    isWeekSelected(week) {
      return week === this.startWeek || week === this.endWeek;
    },

    isWeekInRange(week) {
      return (
        this.startWeek &&
        this.endWeek &&
        week > this.startWeek &&
        week < this.endWeek
      );
    },

    formatShortDate(dateString) {
      // Convert YYYY-MM-DD to MM/DD format
      const date = dayjs(dateString);
      return date.format("MM/DD");
    },
  },
});
</script>

<style scoped>
.custom-week-picker {
  position: relative;
  width: 350px;
}

.el-date-editor.el-range-editor {
  display: inline-flex;
  align-items: center;
  height: 36px;
  width: 100%;
  padding: 3px 10px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-sizing: border-box;
  position: relative;
}

.picker-input {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.el-date-editor.el-range-editor:hover {
  border-color: #c0c4cc;
}

.date-display {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
  box-sizing: border-box;
  z-index: 1;
  line-height: 36px;
}

.start-date, .end-date {
  flex: 1;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-range-input {
  flex: 1;
  min-width: 0;
  height: 100%;
  text-align: center;
  border: 0;
  outline: none;
  padding: 0;
  font-size: 14px;
  color: #606266;
  background-color: transparent;
  line-height: 36px;
}

.el-range-separator {
  line-height: 36px;
  padding: 0 5px;
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;
  height: 36px;
}

.el-range__icon {
  font-size: 14px;
  margin-right: 5px;
  color: #c0c4cc;
}

.el-range__close-icon {
  font-size: 14px;
  margin-left: 5px;
  color: #c0c4cc;
  cursor: pointer;
  transition: color 0.2s;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.el-icon-circle-close {
  color: #c0c4cc;
}

.el-icon-circle-close:hover {
  color: #909399;
}

.clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #c0c4cc;
  z-index: 10;
}

.clear-button:hover {
  color: #909399;
}

.picker-dropdown {
  position: absolute;
  z-index: 2001;
  top: 40px;
  left: 0;
  margin: 5px 0;
}

.picker-content {
  position: relative;
  width: 520px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.picker-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: 8px;
}

.year-panel {
  width: 70px;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
  height: 240px;
}

.year-item {
  padding: 4px 6px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
}

.year-item:hover {
  color: #409eff;
}

.year-item.active-year {
  color: #409eff;
  font-weight: 700;
}

.week-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 8px;
}

.week-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-bottom: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 2px 0;
}

.week-item {
  padding: 6px;
  border-radius: 2px;
  cursor: pointer;
  text-align: center;
  color: #606266;
  margin-bottom: 0;
  width: calc(20% - 2px);
}

.week-number {
  font-size: 14px;
  line-height: 1.1;
}

.week-dates {
  margin-top: 7px;
  font-size: 11px;
  color: #909399;
  line-height: 1.1;
}

.week-item:hover {
  color: #409eff;
}

.week-item.selected {
  background-color: #409eff;
  color: white;
}

.week-item.selected .week-dates {
  color: rgba(255, 255, 255, 0.8);
}

.week-item.in-range {
  background-color: #f2f6fc;
  color: #409eff;
}

.week-item.in-range .week-dates {
  color: #409eff;
}

.week-date-display-container {
  margin-top: 6px;
  border-top: 1px solid #e4e7ed;
  padding: 0;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.week-date-display {
  color: #606266;
  font-size: 14px;
  text-align: center;
  width: 100%;
  line-height: 40px;
}

.el-picker-panel__footer {
  padding: 3px 10px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
  background: #fff;
}

/* Element UI transition classes */
.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter-from,
.el-zoom-in-top-leave-to {
  opacity: 0;
  transform: scaleY(0);
}
</style>
