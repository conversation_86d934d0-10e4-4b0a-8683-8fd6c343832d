<template>
  <!-- 父容器或者需要水印的元素，这里直接用一个 div -->
  <div
    class="vue-watermark"
    :style="containerStyle"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'VueWatermark',
  props: {
    text: {
      type: String,
      default: 'Watermark'
    },
    fontSize: {
      type: Number,
      default: 14       // px
    },
    fontFamily: {
      type: String,
      default: 'Arial'
    },
    color: {
      type: String,
      default: '#000000'
    },
    opacity: {
      type: Number,
      default: 0.15
    },
    rotate: {
      type: Number,
      default: -30       // 旋转角度（单位：度）
    },
    width: {
      type: Number,
      default: 200       // 水印单元格宽度
    },
    height: {
      type: Number,
      default: 120       // 水印单元格高度
    },
    textStartX: {
      type: Number,
      default: 20        // 文字在 canvas 单元格中的起始 X 坐标
    },
    textStartY: {
      type: Number,
      default: 60        // 文字在 canvas 单元格中的基线 Y 坐标
    }
  },
  data () {
    return {
      backgroundImage: ''
    }
  },
  computed: {
    containerStyle() {
      return {
        width: '100%',
        height: '100%',
        backgroundRepeat: 'repeat',
        backgroundImage: `url(${this.backgroundImage})`
      }
    }
  },
  mounted() {
    this.generateWatermark()
  },
  watch: {
    // 如果需要在 props 改变时，自动重新生成水印，可以在这儿 watch
    text: 'generateWatermark',
    fontSize: 'generateWatermark',
    fontFamily: 'generateWatermark',
    color: 'generateWatermark',
    opacity: 'generateWatermark',
    rotate: 'generateWatermark',
    width: 'generateWatermark',
    height: 'generateWatermark',
    textStartX: 'generateWatermark',
    textStartY: 'generateWatermark'
  },
  methods: {
    generateWatermark() {
      // 1. 创建离屏 canvas
      const canvas = document.createElement('canvas')
      canvas.width = this.width
      canvas.height = this.height

      // 2. 获取 context 并设置样式
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, this.width, this.height)

      // 设置文字透明度（通过 globalAlpha 或 color 的透明度来实现）
      ctx.globalAlpha = this.opacity

      // 设置字体和颜色
      ctx.font = `${this.fontSize}px ${this.fontFamily}`
      ctx.fillStyle = this.color

      // 3. 设置旋转角度
      ctx.translate(this.textStartX, this.textStartY)
      ctx.rotate(this.rotate * Math.PI / 180)  // 角度 -> 弧度

      // 4. 绘制文字
      ctx.fillText(this.text, 0, 0)

      // 5. 恢复 context 状态（仅在旋转前后需要做的安全处理）
      ctx.rotate(-this.rotate * Math.PI / 180)
      ctx.translate(-this.textStartX, -this.textStartY)

      // 6. 生成 Base64 URL
      this.backgroundImage = canvas.toDataURL('image/png')
    }
  }
}
</script>

<style scoped>
.vue-watermark {
  /* 需要撑满全局可以在外部容器上设置 width: 100vw; height: 100vh; 或者直接给 body 设置 */
  /* 这里先随意写满父容器 */
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  z-index: 9999;
}
</style>
