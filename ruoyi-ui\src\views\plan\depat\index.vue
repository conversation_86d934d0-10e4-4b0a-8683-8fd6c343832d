<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :inline="true"
      :model="queryParams"
      label-width="68px"
    >
      <el-form-item label="城市公司" prop="cityCompanyNum">
        <el-select
          v-model="queryParams.cityCompanyNum"
          clearable
          placeholder="请选择城市公司"
          @change="handleQueryProject"
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict.deptNum"
            :label="dict.deptName"
            :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in storeProjectList"
            :key="dict.name"
            :label="dict.name"
            :value="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分期" prop="stageId">
        <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                   clearable
                   filterable>
          <el-option
            v-for="dict in stages"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="责任人" prop="respPeopleName">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.respPeopleName"-->
      <!--          clearable-->
      <!--          placeholder="请输入责任人"-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button
          icon="el-icon-search"
          size="mini"
          type="primary"
          @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置
        </el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['plan:depat:add']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['plan:depat:remove']"
          :disabled="multiple"
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>

      <el-col :span="3">

      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="depatList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column align="center" label="序号" prop="id" width="80">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="城市" prop="cityCompanyName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.cityCompanyName)">{{ scope.row.cityCompanyName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="項目" prop="projectName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.projectName)">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分期" prop="stageId" width="55">
        <template slot-scope="scope">
          <dict-tag :options="stages" :value="scope.row.stageId"/>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['plan:depat:edit']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row, scope.$index)"
          >修改
          </el-button
          >
          <el-button
            v-hasPermi="['plan:depat:remove']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除
          </el-button
          >
          <el-button
            icon="el-icon-copy"
            size="mini"
            type="text"
            @click="handleCopy(scope.row)"
          >复制
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改责任部门角色对话框 -->
    <el-dialog
      v-if="open"
      :title="title"
      :visible.sync="open"
      append-to-body
      width="700px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="城市公司" prop="cityCompanyNum">
          <el-select v-model="form.cityCompanyNum" placeholder="请选择城市公司">
            <el-option
              v-for="dict in cityCompanys"
              :key="dict.deptNum"
              :label="dict.deptName"
              :value="dict.deptNum"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择项目">
            <el-option
              v-for="dict in projectList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分期" prop="stageId">
          <el-select v-model="form.stageId" placeholder="请选择分期">
            <el-option
              v-for="dict in stages"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门" props="respDeptDetailsList">
          <div v-for="(tag,index) in roleDepatDtos"
            :key="tag.value">
            <el-tag
              :disable-transitions="false"
              size="mini"
              @close="handleClose(tag)"
            >
              {{ tag.respDept }}
            </el-tag>
            <el-tag
              v-for="(tag, tagIndex) in tag.respPeople"
              :key="`${queryParams.pageNum}-${rowIndex}-${tag.userId}-${tagIndex}-${index}`"
              :disable-transitions="false"
              closable
              effect="plain"
              size="mini"
              type="info"
              @close="handleClose(tag,index)"
            >
              {{ tag.nickName }}
            </el-tag>
            <el-button
              icon="el-icon-plus"
              size="mini"
              style="margin-left: 15px"
              type="text"
              @click="openSelectUser(index)"
            >责任人
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <select-user
      ref="select"
      :selectUsers="selectUsers"
      @selectUser="selectUsersFun"
    />
    <!-- 复制-->
    <el-dialog
      v-if="showCopy"
      :visible.sync="showCopy"
      append-to-body
      title="复制添加"
      width="700px"
    >
      <el-form ref="formCopy" :model="copyData" :rules="rules" label-width="80px">
        <el-form-item label="城市公司" prop="cityCompanyNum">
          <el-select v-model="copyData.cityCompanyNum" placeholder="请选择城市公司">
            <el-option
              v-for="dict in cityCompanys"
              :key="dict.deptNum"
              :label="dict.deptName"
              :value="dict.deptNum"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="copyData.projectId" placeholder="请选择项目">
            <el-option
              v-for="dict in projectList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分期" prop="stageId">
          <el-select v-model="copyData.stageId" placeholder="请选择分期"
          >
            <el-option
              v-for="dict in stages"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门" props="">
          <div v-for="(tag,index) in roleDepatDtos"
               :key="tag.value">
            <el-tag
              :disable-transitions="false"
              size="mini"
            >
              {{ tag.respDept }}
            </el-tag>
            <el-tag
              v-for="tag in tag.respPeople"
              :key="tag.userId"
              :disable-transitions="false"
              effect="plain"
              size="mini"
              type="info"
            >
              {{ tag.nickName }}
            </el-tag>
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="cancelCopy">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import {addDept, delDept, getDept, listDept, updateDept, clone} from "@/api/plan/dept"
  import {listProject} from "@/api/plan/project"
  import selectUser from './selectUser'
  import Treeselect from "@riophae/vue-treeselect"
  import "@riophae/vue-treeselect/dist/vue-treeselect.css"
  import { mapState } from 'vuex';

  export default {
    name: "Depat",
    dicts: ['sys_second_institution', 'role'],
    components: {selectUser, Treeselect},
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任部门角色表格数据
        depatList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 200,
          respPeople: null,
          "cityCompanyNum": "",//被复制的城市公司编码
          "projectName": "",//被复制的项目编码
          "stageId": "",//被复制的项目分期
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          cityCompanyNum: [{
            required: true,
            message: "城市公司不能为空",
            trigger: "change"
          }],
          projectId: [{
            required: true,
            message: "项目不能为空",
            trigger: "change"
          }],
          stageId: [{
            required: true,
            message: "分期不能为空",
            trigger: "change"
          }],
          respDeptDetailsList: [
            {required: true,}
          ]
        },
        userList: [],
        selectUsers: [],
        stages: [],
        projectList: [],
        departments: [],
        roleDepatDtos: [],
        selectDpartIndex: 0,
        //复制相关参数
        showCopy: false,
        copyOption: [],
        copyData: {
          "cityCompanyNum": "",
          "cityCompanyName": "",
          "projectId": "",
          "projectNameNew": "",
          "stageId": ""
        },
        respDeptDetailsList: [],
        copyValue: null,
        rowIndex: -1
      }
    },
    created() {
      this.getList()
      //获取
      this.$store.dispatch('plan/fetchDepartList')
      this.getdicts()
      //获取城市公司
      this.$store.dispatch('plan/fetchDeptList')
    },
    computed: {
      cityCompanys() {
        return this.$store.state.plan.deptList
      },
      departList() {
        return this.$store.state.plan.departList
      },
      ...mapState('plan', {storeProjectList: 'projectList'})

    },
    watch: {
      'form.cityCompanyNum': {
        handler(newVal, oldVal,) {
          if (newVal && newVal != oldVal) {
            this.fetchProjectByCity(newVal)
          }
        }
      },
      'queryParams.deptNum': {
        handler(newVal, oldVal,) {
          if (newVal && newVal != oldVal) {
            this.fetchProjectByCity(newVal)
          }
        }
      },
      'copyData.cityCompanyNum': {
        handler(newVal, oldVal,) {
          if (newVal && newVal != oldVal) {
            this.fetchProjectByCity(newVal)
          }
        }
      },
    },
    methods: {
      handleQueryProject(value){
        this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
      },
      resetCopy() {
        this.copyValue = null
        this.copyData = {
          "cityCompanyNum": "",
          "cityCompanyName": "",
          "projectId": "",
          "projectNameNew": "",
          "stageId": "",
        }
      },
      confirm() {
        this.$refs["formCopy"].validate(valid => {
          if (valid) {
            this.copyData.cityCompanyName = this.cityCompanys.filter(item => item.deptNum == this.copyData.cityCompanyNum)[0].deptName
            this.copyData.projectName = this.projectList.filter(item => item.id == this.copyData.projectId)[0].name
            clone({
              "respDeptId": this.copyValue,//被复制的id
              "respDeptNew": {...this.copyData}
            }).then(res => {
              this.showCopy=false
              this.resetQuery()
            }).catch(err => {
              console.elog(err)
            })
          }
        })
      },
      cancelCopy() {
        this.showCopy = false
        this.resetCopy()
      },
      handleCopy(row) {
        this.resetCopy()
        this.copyValue = row.id
        getDept(row.id).then(response => {
          this.showCopy = true
          let data = JSON.parse(JSON.stringify(response.data))
          this.form=data
          this.respDeptDetailsList=data.respDeptDetailsList||[]
          this.roleDepatDtos = data && data.respDeptDetailsList.map(item => {
            let itemData = JSON.parse(JSON.stringify(item))
            let arr = itemData.respPeople?itemData.respPeople.split(","):[]
            return {
              respDept: itemData.respDept,
              respPeople: arr.length ? arr.map((itemS, index) => {
                return {
                  userId: itemS,
                  nickName: itemData.respPeopleName.split(",")[index]
                }
              }) : null
            }
          })
          console.log(this.roleDepatDtos)
          this.title = "复制部门角色"
        })
      },
      //根据城市数据获项目
      fetchProjectByCity(params) {
        // this.loading = true
        listProject({
          name: '',
          company: params
        }).then(res => {
          this.projectList = res.rows
          this.queryParams.projectId = null
          this.loading = false
        }).catch(err => {
          this.loading = false

        })
      },

      getdicts() {
        //字典责任部门
        this.getDicts('resp_department').then(res => {
          let data = res.data
          this.departments = data.map(item => {
              return {
                label: item.dictLabel,
                value: item.dictValue,
              }
            }
          )
          this.roleDepatDtos = this.departments.map(item => {
              return {
                "respDept": item.label,
                "respPeople": [],
              }
            }
          )
        }).catch(err => {
          console.log(err)
        }),
          //字典分期
          this.getDicts('stages').then(res => {
            let data = res.data
            this.stages = data.map(item => {
                return {
                  label: item.dictLabel,
                  value: item.dictValue,
                  raw: item
                }
              }
            )
          }).catch(err => {
            console.log(err)
          })
      },

      handleUserData() {
        this.form.respDeptDetailsList = this.roleDepatDtos.map(item => {
          return {
            "respDept": item.respDept,
            "respPeople": item.respPeople?item.respPeople.map(item => item.userId).join(","):'',
            "respPeopleName": item.respPeople?item.respPeople.map(item => item.nickName).join(","):'',
          }
        })
      },
      handleClose(tag, index) {
        let data = this.roleDepatDtos[index].respPeople
        data.splice(data.indexOf(tag), 1)
        this.handleUserData()
      },
      /**选择用户**/
      selectUsersFun(users) {
        this.roleDepatDtos[this.selectDpartIndex].respPeople = [...users]
        this.handleUserData()
      },
      /** 打开授权用户表弹窗 */
      openSelectUser(index) {
        this.selectDpartIndex = index
        this.selectUsers = this.roleDepatDtos[this.selectDpartIndex].respPeople||[]
        setTimeout(() => {
          this.$refs.select.show()
        }, 100)
      },
      /** 查询责任部门角色列表 */
      getList() {
        this.loading = true
        listDept(this.queryParams).then(response => {
          this.depatList = response.rows
          this.depatList.map(item => {
            if (!item.respPeopleName) return null
            item.users = item.respPeopleName.split(',')
          })
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          cityCompanyName: '',
          cityCompanyNum: '',
          projectId: null, //项目
          projectName: null,
          stageId: null, //分期
          respDeptDetailsList: []
        }
        this.resetForm("form")
        this.roleDepatDtos = this.departments.map(item => {
            return {
              "respDept": item.label,
              "respPeople": [],
            }
          }
        )
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm")
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      async handleAdd() {
        this.reset()
        this.getdicts()
        await this.$store.dispatch('plan/fetchDeptList')
        this.open = true
        this.title = "添加责任部门角色"
      },
      /** 修改按钮操作 */
      handleUpdate(row, index) {
        this.rowIndex = index
        this.getdicts()
        this.reset()
        const id = row.id || this.ids
        getDept(id).then(response => {
          this.form = response.data
          this.open = true
          let data = JSON.parse(JSON.stringify(response.data))
          this.roleDepatDtos = data && data.respDeptDetailsList.map(item => {
            let itemData = JSON.parse(JSON.stringify(item))
            var arr = ""
            if (itemData.respPeople) {
              arr = itemData.respPeople.split(",")
            }
            return {
              respDept: itemData.respDept,
              respPeople: arr.length ? arr.map((itemS, index) => {
                return {
                  userId: itemS,
                  nickName: itemData.respPeopleName.split(",")[index]
                }
              }) : null
            }
          })
          console.log(index, '-------', this.roleDepatDtos)
          this.handleUserData()
          this.title = "修改责任部门角色"
        })
      },
      validDepat(type) {
        let arr=this.form.respDeptDetailsList
        if(!arr.length){
          return true
        }
        let data = arr.some(item => !item.respPeople );
        return data
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.form.cityCompanyName = this.cityCompanys.filter(item => item.deptNum == this.form.cityCompanyNum)[0].deptName
            this.form.projectName = this.projectList.filter(item => item.id == this.form.projectId)[0].name
            // if (this.validDepat(1)) {
            //   return this.$message({
            //     type: "warning",
            //     message: "有责任部门没有添加责任人"
            //   })
            // }
            if (this.form.id != null) {
              //修改
              this.form.respPeopleName = this.form.respDeptDetailsList[0].respPeople,
                this.form.respPeople = this.form.respDeptDetailsList[0].respPeopleName
              updateDept(this.form).then(response => {
                this.$modal.msgSuccess("修改成功")
                this.open = false
                this.getList()
              })
            } else {
              addDept(this.form).then(response => {
                this.$modal.msgSuccess("新增成功")
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids
        let msg = '所选数据'
        if (row.id) {
          msg = '当前数据'
        }
        this.$modal.confirm(`是否确认删除${msg}？`).then(function () {
          return delDept(ids)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess("删除成功")
        }).catch(() => {
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('plan/dept/export', {
          ...this.queryParams
        }, `dept_${new Date().getTime()}.xlsx`)
      }
    }
  };
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
