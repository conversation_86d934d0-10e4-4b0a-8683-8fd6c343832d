<template>
    <el-dialog :title="assetDetailTitle" :visible.sync="isVisible" width="80%" ppend-to-body class="asset-detail" @close="closeHandle">
      <el-form>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司代码：">{{ assetDetail.companyCode || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="利润中心：">{{ assetDetail.profitCenter  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产分类：">{{ assetDetail.assetClassification  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号：">{{ assetDetail.model  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="明细类别：">{{ assetDetail.detailedCategories  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务范围：">{{ assetDetail.businessScope  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用状态：">{{ assetDetail.usageStatus  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理部门：">{{ assetDetail.managementDepartment  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用部门：">{{ assetDetail.usingDepartment  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用项目：">{{ assetDetail.usingProjects  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计提部门：">{{ assetDetail.accrualDepartment  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量：">{{ assetDetail.number  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位：">{{ assetDetail.measurementUnit  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存放地点：">{{ assetDetail.storageLocation  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用人：">{{ assetDetail.user  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制造厂商：">{{ assetDetail.manufacturers  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附加描述：">{{ assetDetail.additionalDescription  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="增加方式：">{{ assetDetail.increaseMethod  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折旧范围：">{{ assetDetail.depreciationRange  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折旧方法：">{{ assetDetail.depreciationMethod  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用年限：">{{ assetDetail.serviceLife  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制造厂商：">{{ assetDetail.manufacturers  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用月数：">{{ assetDetail.useMonths  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入账日期：">{{ assetDetail.receiptDate  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折旧开始日期：">{{ assetDetail.depreciationStartdate  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房产编号：">{{ assetDetail.propertyNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="土地等级：">{{ assetDetail.landGrade  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房产原值：">{{ assetDetail.houseValue  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="币种：">{{ assetDetail.currency  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宗地的地号：">{{ assetDetail.parcelNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="土地编号：">{{ assetDetail.landNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="土地总面积：">{{ assetDetail.areaTotal  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆牌照号：">{{ assetDetail.vehicleLicenseNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆识别号码（车架号）：">{{ assetDetail.vehicleIdentificationNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="船舶登记号：">{{ assetDetail.shipRegistrationNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否安全生产费资产：">{{ assetDetail.safetyProductionCostAsset  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否科研资产：">{{ assetDetail.researchAsset  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="科研课题：">{{ assetDetail.researchTopics  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出厂日期：">{{ assetDetail.factoryDate  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商编码：">{{ assetDetail.supplierCode  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内部订单：">{{ assetDetail.internalOrders  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务形态：">{{ assetDetail.businessForm  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投资运作模式：">{{ assetDetail.investmentOperationMode  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始资产号：">{{ assetDetail.originalAssetNumber  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="个性化字段：">{{ assetDetail.personalizedFields  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用单位：">{{ assetDetail.usingUnit  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人：">{{ assetDetail.responsiblePerson  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验收人：">{{ assetDetail.acceptancePerson  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="核算模式：">{{ assetDetail.accountingMode  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注2：">{{ assetDetail.remark  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资金来源：">{{ assetDetail.assetSource  || '-' }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产实物编号：">{{ assetDetail.assetPhysicalNumber  || '-' }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
</template>

<script>
export default {
  props: {
    assetDetailVisible: Boolean,
    assetDetail: Object,
    assetDetailTitle: String,
  },
  data() {
    return {
      isVisible: false,
    }
  },
  watch: {
    assetDetailVisible(val){
      if(val){
        this.isVisible = val
      }
    }
  },
  methods: {
    closeHandle() {
      this.$emit('closeViewHandle')
    }
  },
}
</script>

<style lang="scss" scoped>
   .asset-detail{
    ::v-deep .el-dialog{
      .el-dialog__body{
        height: 600px!important;
        overflow: auto;
      }
    }
   } 
</style>