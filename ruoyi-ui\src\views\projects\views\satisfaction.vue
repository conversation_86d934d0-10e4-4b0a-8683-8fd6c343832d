<template>
  <div class="projects-content-container">
    <!-- 无权限状态 -->
    <div v-if="!hasPermi(['project:satisfied'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else style="width: 100%">
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中">
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>
      <div v-else style="width: 100%;">
        <div class="category-tab-wrapper">
          <CardTab v-model="activeTab" :tabs="categoryTabs" class="mb-14" @click="handleTabClick" />
        </div>
        <!-- 无数据状态 -->
        <div v-if="!hasData" class="empty-status">
          <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
          <div class="desc">暂无数据</div>
        </div>
        <div v-else v-loading="loading" class="satisfaction-content-wrapper">
          <!-- 得分趋势 -->
          <div class="score-trend-wrapper">
            <div class="card-title mb-12">得分趋势</div>
            <div class="score-trend-content">
              <SatifactionScoreTrend class="score-trend-item" :score-trend="scoreTrend" v-for="(item, i) in scoreTrend"
                :key="i" :chart-data="item.kgScoreTrendDtoList" :title="item.kgProjectName" />
            </div>
          </div>
          <!-- 历史数据 -->
          <div class="history-data-wrapper" v-for="(item, i) in historyData" :key="i">
            <div class="card-title mb-12">{{ item.year }}年</div>
            <div class="history-data-content">
              <SatifactionScoreHistory class="history-data-item"
                v-for="(dataItem, dataIndex) in item.kgSatisfiedHistoryMonthDtoList"
                :chart-data="dataItem.kgSatisfiedMonthDetailDtoList" :title="dataItem.yearMonth" :key="dataIndex"
                :top10="item.top10" :industryAvg="item.industryAvg" :top20="item.top20" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CardTab from '@/views/projects/components/CardTabSales.vue'
import SatifactionScoreHistory from '../components/SatifactionScoreHistory.vue'
import SatifactionScoreTrend from '../components/SatifactionScoreTrend.vue'
import API from '@/views/projects/api'
export default {
  name: 'Satisfaction',
  components: {
    CardTab,
    SatifactionScoreHistory,
    SatifactionScoreTrend,
  },
  data() {
    return {
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      activeTab: '', // 活动标签;
      categoryTabs: [],
      dictType: 'kg_stage',
      projectCode: this.$route.query.projectCode,
      projectName: this.$route.query.projectName,
      loading: false,
      scoreTrend: [],
      historyData: [],
    }
  },
  created() {
    this.getTabs()
  },
  methods: {
    fetchTrend() {
      this.loading = true
      API.Satisfaction.scoreTrend({
        "projectCode": this.projectCode,
        "stage": this.activeTab
      })
        .then(res => {
          console.log(res)
          if (res.code === 200) {
            this.scoreTrend = res.data
            console.log(this.scoreTrend)
            this.checkHasData()
          }
          else {
            this.scoreTrend = [];
            this.hasData = false;
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    fetchHistory() {
      this.loading = false
      API.Satisfaction.historyData({
        "projectCode": this.projectCode,
        "stage": this.activeTab
      })
        .then(res => {
          if (res.code === 200) {
            this.historyData = res.data;
            this.checkHasData()
          }
          else {
            this.historyData = [];
            this.hasData = false
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    checkHasData() {
      // Set hasData to false if both arrays are empty
      if (this.scoreTrend.length === 0 && this.historyData.length === 0) {
        this.hasData = false;
      } else {
        this.hasData = true;
      }
    },
    fetchData(code) {
      this.fetchHistory();
      this.fetchTrend();
    },
    handleTabClick(item) {
      console.log(item)
      this.activeTab = item.code
      this.fetchData(item.code)
    },
    getTabs() {
      this.getDicts(this.dictType).then(res => {
        let data = res.data
        let list = data.map(item => {
          return {
            name: item.dictLabel,
            code: item.dictValue,
          }
        }
        )
        this.activeTab = list[0].code
        this.fetchData(list[0].code) // 初始化时获取第一个tab的数据
        this.categoryTabs = list
      })
    },
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions
      // Check for all permissions wildcard first
      if (permissions.includes('*:*:*')) {
        return true
      }
      // Check specific permissions
      return permissions.some(p => permission.includes(p))
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/views/projects/styles/projects.scss';

.score-trend-wrapper,
.history-data-wrapper {
  background: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
}

.score-trend-content,
.history-data-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.score-trend-title,
.history-data-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 1.25rem;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 1rem;
}

.score-trend-item {
  width: 33%;
  height: 18.75rem;
  display: inline-block;
}

.history-data-item {
  width: calc(50vw - 5rem);
  height: 18.75rem;
  display: inline-block;
}
.satisfaction-content-wrapper {
  max-height: calc(100vh - 19.75rem) !important;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin
}
</style>
