<template>
  <div class="chart-container">
    <Chart ref="changeChart" :option="option" :resizeChartFlag="resizeChartFlag"></Chart>
  </div>
</template>

<script>
import Chart from './Chart'
// import * as echarts from "echarts";
/* const screenWidth = window.innerWidth; // 获取当前屏幕宽度
const designWidth = 1680; // 设计尺寸宽度
const scale = screenWidth / designWidth; // 计算比例 */
export default {
  name: 'SatifactionScoreHistory',
  components: {
    Chart
  },
  props: {
    // 图表数据
    chartData: {
      type: Array,
      required: true
    },
    // 主题颜色
    themeColor: {
      type: String,
      default: '#91d5ff'
    },
    // 高亮颜色
    highlightColor: {
      type: String,
      default: '#ff4d4f'
    },
    // 平均分
    averageScore: {
      type: Number,
      default: 83
    },
    // 图表标题
    title: {
      type: String,
      required: true
    },
    resizeChartFlag: {
      type: Boolean,
      default: true
    },
    top10: {
      type: Number,
      default: 0
    },
    top20: {
      type: Number,
      default: 0
    },
    industryAvg: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      chartId: `chart-${Date.now()}`, // 确保多个图表实例ID唯一
      chart: null,
      option: null
    }
  },
  mounted() {
    this.initChart()
    // 监听窗口大小变化，重绘图表
    // window.addEventListener("resize", this.resizeChart);
  },
  beforeDestroy() {
    // window.removeEventListener("resize", this.resizeChart);
    /* if (this.chart) {
      this.chart.dispose();
    } */
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.updateChart()
      }
    }
    /*  themeColor() {
       this.updateChart();
     }, */
  },
  methods: {
    initChart() {
      this.updateChart()
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    findMinMax(data) {
      // 初始化最大最小值
      let min = Infinity;
      let max = -Infinity;
      
      // 第一步：从图表数据中找出最大最小值
      if (data && data.length > 0) {
        data.forEach(item => {
          if (item.score !== undefined && item.score !== null) {
            // 排除0值和无效值
            if (item.score > 0) {
              min = Math.min(min, item.score);
              max = Math.max(max, item.score);
            }
          }
        });
      }
      
      // 如果没有有效数据，设置默认值
      if (min === Infinity || max === -Infinity) {
        min = 0;
        max = 100;
      }
      
      // 第二步：考虑参考线值
      const referenceValues = [];
      
      if (this.top10 && this.top10 > 0) {
        referenceValues.push(this.top10);
      }
      
      if (this.top20 && this.top20 > 0) {
        referenceValues.push(this.top20);
      }
      
      if (this.industryAvg && this.industryAvg > 0) {
        referenceValues.push(this.industryAvg);
      }
      
      // 如果有参考线值，更新最大最小值
      if (referenceValues.length > 0) {
        const refMin = Math.min(...referenceValues);
        const refMax = Math.max(...referenceValues);
        
        // 更新最大值，确保所有参考线都在视图范围内
        max = Math.max(max, refMax);
        
        // 仅当参考线最小值小于数据最小值时，才更新最小值
        if (refMin < min) {
          min = refMin;
        }
      }
      
      // 第三步：将min和max调整为生成均匀分割线的值
      // 向下取整到最接近的20的倍数
      min = Math.max(0, Math.floor(min / 20) * 20);
      
      // 向上取整到最接近的20的倍数，确保至少有5个刻度
      max = Math.ceil(max / 20) * 20;
      
      // 确保min和max之间至少有80的差距（5个等分的标准间隔）
     /*  if (max - min < 80) {
        max = min + 100;
      } */
      
      console.log('调整后的y轴范围(将生成均匀分割线):', { min, max });
      
      return { min, max };
    },
    updateChart() {
      const screenWidth = window.innerWidth // 获取当前屏幕宽度
      const designWidth = 1680 // 设计尺寸宽度
      const scale = screenWidth / designWidth // 计算比例
      const windowWidth = window.innerWidth
      const fontSize = Math.floor(18 * (windowWidth / 1680))
      
      
      if (!this.chartData || this.chartData.length === 0) {
        console.warn('No chart data available')
        return
      }
      
      
      const xAxisLabels = this.chartData.map(item => item.kgProjectName);
      
      // 计算原始数据中kgProjectName的最大长度
      const maxLabelLength = Math.max(...xAxisLabels.map(label => label ? label.length : 0));
      
      const { min, max } = this.findMinMax(this.chartData)

      // 处理数据，设置高亮
      const seriesData = this.chartData.map((item, index) => {
        const isProject = item.isProject
        return {
          value: item.score,
          isProject: isProject,
          // 确保使用的属性名与数据中实际的属性名一致
          itemStyle: {
            // 第一个柱子为红色，isProject为true的柱子为蓝色，其他为主题色
            color: index === 0 ? '#ff4d4f' : (isProject ? '#1890ff' : this.themeColor)
          },
          label: {
            show: true,
            position: 'top',
            // 第一个柱子文字为红色，isProject为true的文字为红色，其他为主题色
            color: index === 0 ? '#ff4d4f' : (isProject ? '#ff4d4f' : this.themeColor)
          }
        }
      })

      this.option = {
        title: {
          text: this.title,
          left: 'center',
          top: '0px',
          textStyle: {
            color: '#333333',
            fontSize: fontSize,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
          textStyle: {
            fontSize: fontSize,
            lineHeight: Math.floor(fontSize * 1.5) + 'px'
          },
        },
        grid: {
          left: '3%',
          right: '18%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map((item) => item.kgProjectName),
          axisLabel: {
            interval: 0,
            rotate: 45,
            margin: 15 * scale,  // 按比例调整
            fontSize: 12 * scale, // 按比例调整字体大小
            color: (value, index) => {
              // isProject为true的x轴文字为红色，其他为灰色
              return this.chartData[index].isProject ? '#ff4d4f' : '#999999'
            },
            formatter: (value) => {
              if (value.length > 20) {
                // 截取前20个字符，分成两行，并添加省略号
                return value.substring(0, 10) + '\n' + value.substring(10, 20) + '...'
              } else if (value.length > 10) {
                // 超过10个字符但不超过20个字符，分成两行
                return value.substring(0, 10) + '\n' + value.substring(10)
              }
              return value
            },
            rich: {
              // 强制所有标签使用相同的宽度
              value: {
                width: 80 * scale,
                align: 'center'
              }
            }
          },
          axisTick: {
            alignWithLabel: false,
            show: false,
            length: 10 * scale // 按比例调整
          },
          axisLine: {
            onZero: true,
            show: false,
            lineStyle: {
              color: '#999',
              width: 1 * scale
            }
          }
        },
        yAxis: {
          type: 'value',
          // 固定分割段数，确保标线距离一致
          splitNumber: 5,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#DDD',
              width: 1 * scale
            }
          },
          axisTick: {
            alignWithLabel: false,
            show: false,
            color: '#8C8C8C',
            fontSize: 12 * scale // 按比例调整字体大
          },
          axisLine: {
            onZero: true,
            show: false
          },
          axisLabel: {
            interval: 0,
            color: '#999999',
            fontSize: fontSize,
          },
          min: function(value) {
            // 固定使用0作为最小值，统一所有图表
            return min;
          },
          max: function(value) {
            // 固定使用100作为最大值，统一所有图表
            return max;
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            barWidth: 16 * scale, // 简化配置，先确保能正常显示
            barGap: '30%',
            label: {
              show: true,
              position: 'top',
              distance: 5 * scale,
              fontSize: 12 * scale
            },
            markLine: {
              silent: true,
              symbol: 'none',
              emphasis: {
                lineStyle: {
                  width: 2
                }
              },
              data: [
                // TOP10参考线
                ...(this.top10 > 0 ? [{
                  name: 'TOP10',
                  yAxis: this.top10,
                  lineStyle: {
                    color: '#FF4141',
                    type: 'solid',
                    width: 1 * scale
                  },
                  label: {
                    formatter: `TOP10: ${this.top10}`,
                    position: 'end',
                    distance: 8,
                    color: '#FF4141',
                    fontSize: 12 * scale,
                    fontWeight: 'bold'
                  }
                }] : []),

                // TOP20参考线
                ...(this.top20 > 0 ? [{
                  name: 'TOP20',
                  yAxis: this.top20,
                  lineStyle: {
                    color: '#FF9500',
                    type: 'solid',
                    width: 1 * scale
                  },
                  label: {
                    formatter: `TOP20: ${this.top20}`,
                    position: 'end',
                    distance: 8,
                    color: '#FF9500',
                    fontSize: 12 * scale,
                    fontWeight: 'bold'
                  }
                }] : []),

                // 行业均值参考线
                ...(this.industryAvg > 0 ? [{
                  name: '行业均值',
                  yAxis: this.industryAvg,
                  lineStyle: {
                    color: '#376DF7',
                    type: 'solid',
                    width: 1 * scale
                  },
                  label: {
                    formatter: `行业均值: ${this.industryAvg}`,
                    position: 'end',
                    distance: 8,
                    color: '#376DF7',
                    fontSize: 12 * scale,
                    fontWeight: 'bold'
                  }
                }] : [])
              ]
            }
          }
        ]
      }
      // this.chart.setOption(option);
    }
  }
}
</script>
<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
