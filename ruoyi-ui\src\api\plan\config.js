import request from '@/utils/request'

// 查询成果设置列表
export function listConfig(query) {
  return request({
    url: '/plan/config/list',
    method: 'get',
    params: query
  })
}

// 查询成果设置详细
export function getConfig(id) {
  return request({
    url: '/plan/config/' + id,
    method: 'get'
  })
}

// 新增成果设置
export function addConfig(data) {
  return request({
    url: '/plan/config',
    method: 'post',
    data: data
  })
}

// 修改成果设置
export function updateConfig(data) {
  return request({
    url: '/plan/config',
    method: 'put',
    data: data
  })
}

// 删除成果设置
export function delConfig(id) {
  return request({
    url: '/plan/config/' + id,
    method: 'delete'
  })
}

// 成果设置状态变更
export function changeStatus(data) {
  return request({
    url: '/plan/config/changeStatus',
    data,
    method: 'post'
  })
}


