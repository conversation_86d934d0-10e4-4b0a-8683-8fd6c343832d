import request from '@/utils/request'

// 查询考核指标版本数据列表
export function listData(query) {
  return request({
    url: '/project/examineTargetVersionData/list',
    method: 'get',
    params: query
  })
}

// 查询考核指标版本数据详细
export function getData(id) {
  return request({
    url: '/project/examineTargetVersionData/' + id,
    method: 'get'
  })
}

// 新增考核指标版本数据
export function addData(data) {
  return request({
    url: '/project/examineTargetVersionData',
    method: 'post',
    data: data
  })
}

// 修改考核指标版本数据
export function updateData(data) {
  return request({
    url: '/project/examineTargetVersionData',
    method: 'put',
    data: data
  })
}

// 删除考核指标版本数据
export function delData(id) {
  return request({
    url: '/project/examineTargetVersionData/' + id,
    method: 'delete'
  })
}

// 查询考核指标版本数据列表
export function getDetail(data) {
  // 项目编码  projectCode;
  // 版本类型  versionType; 版本类型(1-可研版,2-投决版,3-目标版,4-动态版,5-后评价版)
  return request({
    url: '/project/examineTargetVersionData/detail',
    method: 'post',
    data
  })
}

// 新增考核指标新版本
export function updateVersion(data) {
  return request({
    url: '/project/examineTargetVersionData/detail/add',
    method: 'post',
    data
  })
}

// 修改考核指标新版本
export function editVersion(data) {
  return request({
    url: '/project/examineTargetVersionData/detail/edit',
    method: 'post',
    data
  })
}



// 考核科目集合
export function examineTargetDefine() {
  return request({
    url: '/project/examineTargetDefine',
    method: 'get',
  })
}

// 考核指标模板下载
export function downloadTemplate(){
  return request({
    url: '/project/examineTargetVersionData/detail/exportTemplate',
    method: 'post'
  })
}

// 考核指标导入，返回导入结果
export function importExamineTarget(params) {
  return request({
    url: '/project/examineTargetVersionData/detail/upload',
    method: 'post',
    data: params
  })
}
