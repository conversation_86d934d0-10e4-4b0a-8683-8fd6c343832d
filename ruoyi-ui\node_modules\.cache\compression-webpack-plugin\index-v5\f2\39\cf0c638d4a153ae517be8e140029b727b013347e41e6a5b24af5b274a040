
26168c60871338970750dc3ab6de62fa93046250	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"6ea0ed44cb5b11c2caa8249a80aee132\"}","integrity":"sha512-6UlBi+VIoCWHGU9aMu26DU/tE3jGuq/VPfXvltn6FhgOAhZYOZoVQRw9k9WMltYihEDkR7H2KHR6TEYhwK1r6g==","time":1754311552591,"size":12043794}