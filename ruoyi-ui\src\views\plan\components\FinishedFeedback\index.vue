<script>
//完成反馈
import Template from "@/views/plan/template/index.vue";
import {listConfig} from "@/api/plan/config";
import {uploadFileMultiple} from "@/api/plan/common";
import {formatDateWithTime} from '@/utils';
export default {
  dicts: ['stages'],
  components: {Template},
  props:{
    feedbackDetail: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data: function (){
    return {
      form: {},
      nodeOutcomeDocumentList: [],
      // 表单校验
      rules: {
        actualCompletionDate:[{
          required: true,
          message: '实际完成时间不能为空',
          trigger: 'change'
        }],
      },
      uploadFileLoading: false,
      beforeDay: null,
      afterDay: null,
    }
  },
  computed: {
    pickerOptions () {
      let _this = this;
      return {
        disabledDate(time) {
          const today = new Date();
          if(!!_this.beforeDay && !!_this.afterDay){
            const daysBefore = new Date(today.getTime() - _this.beforeDay * 24 * 60 * 60 * 1000);
            const daysAfter = new Date(today.getTime() + _this.afterDay * 24 * 60 * 60 * 1000);
            return time.getTime() < daysBefore.getTime() || time.getTime() > daysAfter.getTime();
          }
          else if(!!_this.beforeDay){
            const daysBefore = new Date(today.getTime() - _this.beforeDay * 24 * 60 * 60 * 1000);
            return time.getTime() < daysBefore.getTime();
          }
          else if(!!_this.afterDay){
            const daysAfter = new Date(today.getTime() + _this.afterDay * 24 * 60 * 60 * 1000);
            return time.getTime() > daysAfter.getTime();
          }
          else{
            return false;
          }
        }
      }
    },
    nodeStatus(){
      return this.nodeStatusFilter(this.form.actualCompletionDate, this.feedbackDetail.endTime);
    },
    nodeStatusClass(){
      return this.nodeStatus === '延期完成' ? 'custom-node-warning' : 'custom-node-success';
    }
  },
  watch: {
    feedbackDetail: function (){
      this.feedbackDetail.actualCompletionDate = this.form.actualCompletionDate;
      this.feedbackDetail.deviationNum = this.calculateDaysDifference(this.form.actualCompletionDate, this.feedbackDetail.endTime);
    }
  },
  async created() {
    await this.getFeedbackDate();
  },
  methods:{
    async getFeedbackDate(){
      let before = await this.getConfigKey("feedback.before.day"); // 反馈时间最大前推
      let after = await this.getConfigKey("feedback.after.day"); // 反馈时间最大后推
      this.beforeDay = parseInt(before.msg);
      this.afterDay = parseInt(after.msg);
    },
    fileValidate(){
      // 校验成果文件选择文件是否上传
      // fillFlag 0 必填，1 非必填
      let list = this.nodeOutcomeDocumentList.map(item => {
        if(item.fillFlag === 0 && item.fileList && item.fileList.length === 0){
          item.fileListError = '成果文件不能为空';
        }
        else{
          item.fileListError = null;
        }
        return item;
      });
      this.nodeOutcomeDocumentList.splice(0, this.nodeOutcomeDocumentList.length, ...list);
      return this.nodeOutcomeDocumentList.every(item => !item.fileListError);
    },
    handleSetForm(form, feedbackDetail){
      this.form = {...this.form, ...form};
      // this.feedbackDetail.deviationNum = this.calculateDaysDifference(this.form.actualCompletionDate, feedbackDetail.endTime);
      // console.log('this.feedbackDetail.deviationNum---', this.feedbackDetail.deviationNum);
      this.$forceUpdate();
    },
    handleGetForm(){
      return this.form;
    },
    handleSetNodeOutcomeDocumentList(nodeOutcomeDocumentList){
      this.nodeOutcomeDocumentList = nodeOutcomeDocumentList;
    },
    handleGetNodeOutcomeDocumentList(){
      return this.nodeOutcomeDocumentList;
    },
    calculateDaysDifference(date1, date2) {
      console.log(date1, date2)
      // 将日期转换为时间戳
      const timestamp1 = new Date(date1).getTime();
      const timestamp2 = new Date(date2).getTime();

      // 计算时间差（以毫秒为单位）
      const timeDifference = timestamp1 - timestamp2;

      // 将毫秒转换为天数
      const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
      console.log('daysDifference---', daysDifference)
      return daysDifference;
    },
    setDatePikcer(value, formKey) { // el-date-picker绑定form-date无效，使用此方法
      this.form[formKey] = value;
      this.feedbackDetail.actualCompletionDate = value;
      this.feedbackDetail.deviationNum = this.calculateDaysDifference(value, this.feedbackDetail.endTime);
      this.$forceUpdate();
    },
    /** 查询成果设置列表 */
    getResultFileNodeList() {
      this.resultFileLoading = true;
      listConfig(this.queryResultFileParams).then(response => {
        this.resultFileList = response.rows;
        this.resultFileTotal = response.total;
        this.resultFileLoading = false;
      });
    },
    handleResultOpen() {
      this.$emit('handleResultOpen', true);
    },
    handleDeleteAnnexUrl(row, index) {
      row.fileList.splice(index, 1);
      if(row.fileList.length === 0) {
        row.createTime = null;
      }
    },
    uploadFileHandle(event, row, index) {
      const files = event.target.files;
      if (!files) { //
        // 没有选择文件
        return;
      }
      this.uploadFileLoading = true;
      const formData = new FormData();
      for(let item of files) {
        formData.append('files', item);
      }
      uploadFileMultiple(formData).then(response => {
        this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
        this.nodeOutcomeDocumentList[index].fileList = response.data;
        this.$forceUpdate();
        this.uploadFileLoading = false;
      }).catch(error => {
          this.uploadFileLoading = false;
          console.error(error);
        });
    },
    handleNodeDocumentDelete(row, index) { // 成果文件删除
      this.nodeOutcomeDocumentList.splice(index, 1);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "节点反馈维护";
    },
    handleNodeDocumentChange(selection) { // 成果文件选中

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleNodeDocument(){
      /*成果类型String type;
       成果名称String name;
       创建时间"yyyy-MM-dd HH:mm:ss" Date createTime;
       专业成果文件和证明材料描述 String outcomeDocumentsDesc;
       附件 String annexUrl;
       是否必填（0-是 1-否）Integer fillFlag;
       文件名称 annexName;
       文件上传后的附件名称 ossFileName
       文件集合fileList
       */
      this.nodeOutcomeDocumentList.push({
        type: null,
        createTime: null,
        annexUrl: null,
        outcomeDocumentsDesc: null,
        annexName: null,
        ossFileName: null,
        fileList: [],
      });
    },
    nodeStatusFilter(actualCompletionDate, endTime){ // 实际完成时间，计划完成时间
      if(!!actualCompletionDate && !!endTime){
        return new Date(actualCompletionDate).getTime() > new Date(endTime).getTime() ? '延期完成' : '按期完成';
      }
      return '--';
    },
  }
}
</script>

<template>
  <section class="planning-back">
    <div class="feedback-container">
      <div class="feedback-detail">
        <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
        <el-form class="feedback-detail">
          <el-row>
            <el-col :span="12">
              <el-form-item label="项目名称：" prop="projectName">
                {{feedbackDetail.projectName || '--'}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目分期：" prop="stageId">
                <dict-tag :options="dict.type.stages" :value="feedbackDetail.stageId"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="节点顺序：" prop="nodeIndex">
                {{feedbackDetail.nodeIndex || '--'}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="节点名称：" prop="nodeName">
                {{feedbackDetail.nodeName || '--'}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="计划开始时间：" prop="startTime">
                {{feedbackDetail.startTime || '--'}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划完成时间：" prop="endTime">
                {{feedbackDetail.endTime || '--'}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="完成时间：" prop="actualCompletionDate">
                {{feedbackDetail.actualCompletionDate || '--'}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际偏差：" prop="deviationNum">
                {{feedbackDetail.deviationNum != undefined || feedbackDetail.deviationNum != null ? feedbackDetail.deviationNum : '--'}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="完成标准：" prop="completeCriteria">
                {{feedbackDetail.completeCriteria || '--'}}
              </el-form-item>
            </el-col>
<!--            <el-col :span="12">
              <el-form-item label="成果文件：" prop="resultFileName">
                {{feedbackDetail.resultFileName || '&#45;&#45;'}}
              </el-form-item>
            </el-col>-->
            <el-col :span="12">
              <el-form-item label="节点描述：" prop="nodeDesc">
                {{feedbackDetail.nodeDesc || '--'}}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="节点状态：" prop="nodeStatus">
                  <span :class="nodeStatusClass">{{nodeStatus}}</span>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </div>
      <div class="feecback-form">
        <div class="el-icon-s-comment feedback-form-title icon-primary">节点反馈</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="实际完成时间" prop="actualCompletionDate">
                <el-date-picker v-model="form.actualCompletionDate" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @input="(value)=>setDatePikcer(value, 'actualCompletionDate')"
                  :pickerOptions="pickerOptions"
                  :style="{width: '100%'}" placeholder="请选择实际完成时间" clearable>
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="进度说明" prop="notes">
              <el-input v-model="form.notes" type="textarea" placeholder="请输入进度说明"
                        :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div class="feedback-file">
        <div class="el-icon-files feedback-form-title icon-primary">成果文件</div>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-setting"
              size="mini"
              @click="handleResultOpen"
            >节点列表选择
            </el-button>
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleNodeDocument"
            >新增
            </el-button>
          </el-col>
        </el-row>
        <el-table  v-loading="uploadFileLoading" :data="nodeOutcomeDocumentList" @selection-change="handleNodeDocumentChange" ref="nodeOut" style="width: 100%;">
          <el-table-column type="index" width="55" align="center"/>
          <el-table-column label="成果类型" align="center" prop="type" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.type"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="成果文件" align="center" prop="annexUrl" width="200">
            <template slot-scope="scope">
              <section class="file-wrapper">
                <label v-if="!scope.row.fileList || scope.row.fileList.length === 0"  :for="'uploadFile_'+scope.$index"
                       class="el-icon-upload cursor icon-primary" :class="{'custom-warning': !!scope.row.fileListError}">
                  {{scope.row.fileListError || '请上传文件'}}
                </label>
                <section v-else>
                  <div v-for="(file, index) in scope.row.fileList" :key="index" class="file-show">
                    <a :href="file.url" class="link" target="_blank" :title="file.name">{{file.name || '--'}}</a>
                    <i class="el-icon-circle-close cursor link" @click="handleDeleteAnnexUrl(scope.row, index)"></i>
                  </div>
                </section>
              </section>
              <input type="file" class="display-none" multiple :id="'uploadFile_'+scope.$index"
                     @change="(event) => uploadFileHandle(event, scope.row, scope.$index)"/>
            </template>
          </el-table-column>
          <el-table-column label="上传时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <el-input v-model="scope.row.createTime" disabled=""></el-input>
            </template>
          </el-table-column>
          <el-table-column label="备注说明" align="center" prop="outcomeDocumentsDesc">
            <template slot-scope="scope">
              <el-input v-model="scope.row.outcomeDocumentsDesc"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <!--已有成果文件，禁止删除-->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                :disabled="!!scope.row.id && !!scope.row.fileList && scope.row.fileList.length !== 0"
                @click="handleNodeDocumentDelete(scope.row, scope.$index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </section>
</template>

<style scoped lang="scss">
.planning-back{
  .feedback-container {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    .feedback-form-title {
      margin-bottom: 20px;
      background-color: #f8f8f9;
      padding: 10px;
      width: 100%;
    }
    .icon-primary {
      color: #409eff;
    }
    .cursor {
      cursor: pointer;
    }
    .display-none {
      display: none !important;
    }
    .file-wrapper {
      .el-icon-circle-close {
        margin-left: 10px;
      }
    }
    .link {
      color: #409eff;
      display: inline-block;  /* 或 block，取决于布局需求 */
      max-width: 150px;       /* 设置最大宽度 */
      white-space: nowrap;    /* 防止文本换行 */
      overflow: hidden;       /* 隐藏溢出的文本 */
      text-overflow: ellipsis;/* 显示省略号 */
      direction: ltr;         /* 从右到左的文本方向 */
      text-align: left;       /* 文本对齐方式 */
      &:hover{
        color: #1ab394;
      }
      margin-bottom: 3px;
    }
  }
  .custom-node-warning {
    color: #f56c6c;
    font-weight: bold;
  }
  .custom-node-success {
    color: #67c23a;
    font-weight: bold;
  }
}
</style>

