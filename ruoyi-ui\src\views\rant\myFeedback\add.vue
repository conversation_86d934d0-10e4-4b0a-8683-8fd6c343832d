
<template>
    <div class="app-container">
        <div class="rant-container" v-loading="loading">
            <div class="rant-detail">
                <!--    <div class="card-title">{{ formTitle }}</div> -->
                <div class="rant-form-header">
                    <img src="@/assets/rant/rant-info.png" class="icon">
                    {{ formTitle }}
                </div>
                <section class="rant-detail-form">
                    <el-form  label-position="right" :model="rantDetail">
                        <el-row v-if="type == 'add'" class="rant-form-item">
                            <el-col :span="6">
                                <el-form-item label="选择任务" prop="rantTitle" class="custom-form-item-required">
                                    <el-button class="custom-btn-submit" type="primary"
                                        @click="handleSelectTask()">选择任务</el-button>
                                </el-form-item>
                            </el-col>

                        </el-row>
                        <el-row class="rant-form-item">
                            <el-col :span="6">
                                <el-form-item label="来源" prop="ranter">
                                    {{ rantDetail.ranterName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="类型" prop="mattersType">
                                    <section class="matters-type-box">
                                        <dict-tag v-for="(type, index) in rantDetail.mattersType?.split(',')"
                                            :key="index" :options="dict.type.rant_matters_type" :value="type" style="width: 69px;"/>
                                    </section>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="分类" prop="rantClassify">
                                    <dict-tag :options="dict.type.rant_classify" :value="rantDetail.rantClassify" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="计划完成时间" prop="planTime">
                                    {{ rantDetail.planTime }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="责任人" prop="responsiblePerson">
                                    {{ rantDetail.responsiblePersonName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="责任部门" prop="deptId">
                                    {{ rantDetail.deptName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row class="rant-form-item">
                            <el-col :span="24">
                                <el-form-item label="内容" prop="rantContent">
                                    {{ rantDetail.rantContent }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row class="rant-form-item">
                            <el-col :span="24">
                                <el-form-item label="措施" prop="solution">
                                    {{ rantDetail.solution }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                      <el-row class="rant-form-item">
                        <el-col v-if="rantDetail.progressFeedbackStatus == 3" :span="24">
                          <el-form-item label="驳回原因" prop="approvalDesc">
                            {{ rantDetail.approvalDesc }}
                          </el-form-item>
                        </el-col>
                      </el-row>

                        <div class="rant-form-title">
                            <img src="@/assets/rant/rant-this-progress.png" class="icon">
                            本次进展
                            <span class="required-icon">*</span>
                        </div>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="" prop="solution" style="width:100%;" class="custom-editor">
                                    <div style="width: 100%;">
                                        <editor v-model="rantDetail.thisProgress" :height="192"
                                            style="width:100%;" />
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="是否结项" prop="isCompletion" class="custom-form-item-bac" :rules="[{ required: true, message: '请选择是否结项', trigger: 'blur' }]">
                                    <el-select v-model="isCompletion" placeholder="请选择">
                                        <el-option label="是" :value="true"></el-option>
                                        <el-option label="否" :value="false"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col v-if="isCompletion" :span="6">
                                <el-form-item label="结项时间" prop="closingTime" class="custom-form-item-bac"  :rules="[{ required: isCompletion, message: '请选择结项时间', trigger: ['blur', 'change'] }]">
                                    <el-date-picker clearable size="small" v-model="rantDetail.closingTime" type="date"
                                        value-format="yyyy-MM-dd" placeholder="选择结项时间" :picker-options="pickerOptions">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <!-- 成果当是否结项为是的时候，则有必填标识，反之则没有 -->
                                <el-form-item label="成果" prop="achievement" class="custom-form-item-bac" :rules="[{ required: isCompletion, message: '请上传成果文件', trigger: ['blur', 'change'] }]">
                                    <section class="file-wrapper">
                                        <label v-if="!fileList || fileList.length === 0" :for="'uploadFile_'"
                                            class="cursor" :class="{
                                              'custom-warning': !!rantAchievementFiles.fileListError,
                                          }">
                                          <i class="el-icon-folder-add" style="color: #3673FF;font-size: 16px;"></i>
                                            {{ rantAchievementFiles.fileListError || "请上传文件" }}
                                        </label>
                                        <section v-else>
                                            <div v-for="(file, index) in fileList" :key="index" class="file-show">
                                                <a :href="file.url" class="link" target="_blank" :title="file.name">{{file.name || "--" }}</a>
                                                <i class="el-icon-circle-close cursor link"
                                                    @click="handleDeleteAnnexUrl(index)"></i>
                                            </div>
                                        </section>
                                    </section>
                                    <input type="file" class="display-none" multiple :id="'uploadFile_'"
                                        @change="(event) => uploadFileHandle(event, rantDetail)" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div class="rant-form-title"><img src="@/assets/rant/rant-progress.png" class="icon">进度情况</div>
                    <el-table :data="recordList" style="width: 100%">
                        <el-table-column align="center" label="序号" prop="id" width="80">
                            <template slot-scope="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="实际进展" align="center" prop="actualProgress">
                            <template slot-scope="scope">
                                <div v-html="scope.row.actualProgress"></div>
                            </template>
                        </el-table-column>
                        <el-table-column label="汇报时间" align="center" width="100" prop="feedbackTime">
                            <template slot-scope="scope">
                                <span>{{ parseTime(scope.row.feedbackTime, "{y}-{m}-{d}") }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="成果" align="center" prop="achievementFileUrl" width="300">
                            <template slot-scope="scope">
                                <section style="display: flex; flex-direction: column">
                                    <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url"
                                        class="link" target="_blank">
                                        {{ file.name || "--" }}</a>
                                </section>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--      </div>-->
                </section>

            </div>

            <div class="bottom-buttons">
                <el-button class="custom-btn-save" type="primary" @click="submitForm(0)">保 存</el-button>
                <el-button class="custom-btn-submit" type="primary" @click="submitForm(1)">提交</el-button>
            </div>
        </div>

        <el-dialog :title="titleTask" :visible.sync="openTask" width="1200px" top="5vh" append-to-body>
            <el-form :model="queryParams" :inline="true">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="类型" prop="mattersType">
                            <el-select v-model="queryParams.mattersType" multiple placeholder="请选择分类" clearable
                                @keyup.enter.native="handleQuery" style="width: 200px;">
                                <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value"
                                    :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="分类" prop="rantClassify">
                            <el-select v-model="queryParams.rantClassify" placeholder="请选择分类" clearable
                                @keyup.enter.native="handleQuery" style="width: 200px;">
                                <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                                    :value="dict.label"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="计划完成时间" prop="planTime">
                            <el-date-picker clearable size="small" v-model="queryParams.planTime" type="date"
                                value-format="yyyy-MM-dd" placeholder="选择计划完成时间" style="width: 200px;">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="内容" prop="rantContent">
                            <el-input v-model="queryParams.rantContent" placeholder="请输入内容" clearable size="small"
                                @keyup.enter.native="handleQuery" style="width: 200px;" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"> <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini"
                                @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        </el-form-item></el-col>
                </el-row>
            </el-form>
            <el-row>
                <el-table v-loading="loading" :data="mattersList" height="500px" stripe @row-click="clickRow">
                    <el-table-column width="55" label="选择">
                        <template slot-scope="scope">
                            <el-radio :label="scope.row.id" v-model="selectId">
                                {{ "" }}
                            </el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="序号" prop="id" width="50">
                        <template slot-scope="scope">
                            {{ scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" align="center" prop="status" width="100">
                        <template slot-scope="scope">
                            <status-tag class="no-transition" :status="scope.row.status" :options="rantStatusOption" />
                        </template>
                    </el-table-column>
                    <el-table-column label="来源" align="center" prop="ranterName">
                        <template slot-scope="scope">
                            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.ranterName"
                                placement="top-start" popper-class="custom-tooltip">
                                <span>{{ scope.row.ranterName }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column label="类型" width="200" align="center" prop="mattersType">
                        <template slot-scope="scope">
                            <section class="matters-type-box">
                                <dict-tag v-for="(type, index) in scope.row.mattersType.split(',')" :key="index"
                                    :options="dict.type.rant_matters_type" :value="type" />
                            </section>
                        </template>
                    </el-table-column>
                    <el-table-column label="分类" align="center" prop="rantClassify" width="80" />
                    <el-table-column label="内容" align="center" prop="rantContent">
                        <template slot-scope="scope">
                            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.rantContent"
                                placement="top-start" popper-class="custom-tooltip">
                                <span>{{ scope.row.rantContent }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column label="措施" align="center" prop="solution">
                        <template slot-scope="scope">
                            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.solution"
                                placement="top-start" popper-class="custom-tooltip">
                                <span>{{ scope.row.solution }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column label="计划完成时间" align="center" prop="planTime" width="100">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.planTime, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="责任人" align="center" prop="responsiblePersonName" />
                    <el-table-column label="责任部门" align="center" prop="deptName">
                        <template slot-scope="scope">
                            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.deptName"
                                placement="top-start" popper-class="custom-tooltip">
                                <span>{{ scope.row.deptName }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination v-show="totalTask > 0" :total="totalTask" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="getList" :page-sizes="[10, 20, 50, 100, 200, 500, 1000]" />
            </el-row>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitFormTask">确 定</el-button>
                <el-button @click="cancelTask">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {myTaskList, feedbackDetail, feedbackRankMatter} from "@/api/rant/matters";
import { submitFeedback } from "@/api/rant/record";
import { uploadFileMultiple } from "@/api/rant/common";
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import mixin from "@/views/rant/mixins/mixin";
export default {
    name: "addFeedback",
    mixins: [mixin],
    components: { StatusTag },
    dicts: ["rant_classify", "rant_completion_status", 'rant_matters_type'],
    data() {
        return {
            // 遮罩层
            loading: true,
            rantDetail: {}, // 吐槽详情
            recordList: [], // 进度记录
            fileList: [],
            rantAchievementFiles: {}, // 附件信息
            uploadFileLoading: false,
            isCompletion: false,
            formTitle: "新增反馈",
            totalTask: 0,
            titleTask: "选择用户",
            openTask: false,
            visible: false,
            type: "add",
            // 督办任务事项表格数据
            mattersList: [],
            selectId: null,
            selectData: {},
            // 表单验证规则
           /*  formRules: {
                isCompletion: [{
                    validator: (rule, value, callback) => {
                        if (value === true || value === false) {
                            callback();
                        } else {
                            callback(new window.Error('请选择是否结项'));
                        }
                    },
                    trigger: ['blur', 'change']
                }]
            }, */
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                ranter: null,
                rantClassify: null,
                planTime: null,
                deptId: null,
                responsiblePerson: null,
                rantContent: null,
                solution: null,
                thisProgress: null,
                closingTime: null,
                achievement: null,
                mattersType: [],
                status: null,
                inProgress: null
            },
            // 表单数据对象
            rantFormData: {
                isCompletion: false,
            },

            rules: {
                isCompletion: [{
                    required: true,
                    message: '请选择是否结项'
                }],
                closingTime: [{
                    required: true,
                    message: '请选择结项时间'
                }],
                achievement: [{
                    required: true,
                    message: '请上传成果文件'
                }]
            },
          isEnable: null,
          limitDay: null,
        };
    },
    computed: {
        rantStatusOption() {
            return rantStatusOption
        }
    },
    async created() {
        this.type = this.$route.query.type;
        this.id = this.$route.query.id;
        if (this.type === 'add') {
            this.formTitle = '新增反馈';
            this.handleAdd()
        }
        else if (this.type === 'edit') {
            this.formTitle = '编辑反馈';
            this.handleUpdate(this.id)
        }
      await this.getLimitDayIsEnable();
    },
    methods: {
      async getLimitDayIsEnable() {
        let isEnable = await this.getConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
        let limitDay = await this.getConfigKey("rant.finish.date.limit.day"); // 设置限制天数
        this.isEnable = parseInt(isEnable.msg);
        this.limitDay = parseInt(limitDay.msg);
      },
      handleGetRantDetail(id) {
        feedbackRankMatter(id).then((response) => {
          this.recordList = response?.data?.recordDtoList || [];
        });
      },
        handleCompletionChange(value) {
            this.isCompletion = value;
        },
        /** 查询督办事项列表 */
        getList() {
            this.loading = true
            this.mattersList = []
            this.queryParams.inProgress = 1
            myTaskList(this.queryParams).then((response) => {
                this.mattersList = response.rows
                this.totalTask = response.total
                this.loading = false
            })
        },
        handleAdd() { this.loading = false; },
        handleUpdate(id) {
            feedbackDetail(id).then((response) => {
                this.rantDetail = response.data;
                this.rantFormData.isCompletion = response.data.isCompletion;
                this.recordList = response.data.recordDtoList;
                this.fileList = response.data.fileList;
                this.isCompletion = response.data.isCompletion;
                this.loading = false;
            });
        },
        handleSelectTask() {
            this.openTask = true;
            this.titleTask = "选择任务";
            this.getList();
        },

        uploadFileHandle(event, row, index) {
            const files = event.target.files;
            if (!files) {
                //
                // 没有选择文件
                return;
            }
            this.loading = true;
            this.uploadFileLoading = true;
            const formData = new FormData();
            for (let item of files) {
                formData.append("files", item);
            }
            uploadFileMultiple(formData)
                .then((response) => {
                    this.fileList = response.data;

                    this.$forceUpdate();
                    this.uploadFileLoading = false;
                })
                .catch((error) => {
                    this.uploadFileLoading = false;
                    console.error(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleDeleteAnnexUrl(index) {
            this.fileList.splice(index, 1);
        },

        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id)
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm')
            this.handleQuery()
        },
        /** 提交按钮 */
        submitForm(submitStatus) {
            if (this.rantDetail == null || this.rantDetail == undefined || this.rantDetail.id == null) {
              this.$modal.msgError("请选择任务后再操作");
              this.loading = false;
              return;
            }
            this.loading = true;
            let fileList = this.fileList;
            if (!this.rantDetail.thisProgress) {
                this.$modal.msgError("请输入本次进展");
                this.loading = false;
                return;
            }
            if (this.isCompletion) {
                if (!this.rantDetail.closingTime) {
                    this.$modal.msgError("选择结项时间");
                    this.loading = false;
                    return;
                }
                if (!this.fileList || this.fileList.length == 0) {
                    this.$modal.msgError("请上传成果文件");
                    this.loading = false;
                    return;
                }
            }
            this.rantDetail.isCompletion = this.isCompletion;
            let rantDetail = this.rantDetail;
            rantDetail.fileList = fileList;
            rantDetail.rantMattersId = this.rantDetail.id;
            rantDetail.submitStatus = submitStatus;
            submitFeedback(rantDetail).then((response) => {
                if (submitStatus === 1) {
                    this.$modal.msgSuccess("提交成功");
                } else {
                    this.$modal.msgSuccess("保存成功");
                }
                this.$tab.closeOpenPage({
                    path: "/rant/myFeedback",
                });
            }).finally(() => {
                this.loading = false;
            });
        },

        /** 选择任务提交按钮 */
        submitFormTask() {
            this.rantDetail.id = this.selectData.id;
            this.rantDetail.ranter = this.selectData.ranter;
            this.rantDetail.ranterName = this.selectData.ranterName;
            this.rantDetail.rantContent = this.selectData.rantContent;
            this.rantDetail.solution = this.selectData.solution

            this.rantDetail.rantClassify = this.selectData.rantClassify;
            this.rantDetail.mattersType = this.selectData.mattersType;
            this.rantDetail.planTime = this.selectData.planTime;
            this.rantDetail.responsiblePerson = this.selectData.responsiblePerson;
            this.rantDetail.responsiblePersonName = this.selectData.responsiblePersonName;
            this.rantDetail.deptId = this.selectData.deptId;
            this.rantDetail.deptName = this.selectData.deptName;
            this.rantDetail.thisProgress = this.selectData.thisProgress;
            this.rantDetail.feedbackSource = 3;
            this.handleGetRantDetail(this.selectData.id); // 查询进度情况
            this.openTask = false;
        },
        cancelTask() {
            this.openTask = false;
        },
        clickRow(row) {
            this.selectId = row.id
            this.selectData = row


        },
    },
};
</script>
<style lang="scss" scoped>
:deep(.custom-form-item-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
:deep(.custom-editor) {
    .el-form-item__content {
        width: 100%;
    }
}

:deep(.ql-toolbar.ql-snow) {
    background: rgba(54, 115, 255, 0.05);
}

:deep(.el-form-item) {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

:deep(.el-form-item__label) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

:deep(.el-form-item__content) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

:deep(.el-table__header .el-table__cell) {
    //background: rgba(54, 115, 255, 0.05);
    background: rgba(54, 115, 255, 0.1);

}

:deep(.el-table__header .el-table__cell .cell) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    font-style: normal;
    text-transform: none;

}

:deep(.el-table__body .el-table__row .el-table__cell) {
    background-color: #F3F8FC;
}

:deep(.el-table__empty-block) {
    background-color: #F3F8FC;
}

:deep(.custom-form-item-bac input) {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(55, 109, 247, 0.3);
    background: #F1F7FE;
}

.custom-btn-save {
    width: 160px;
    background: #C8DDFA;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    color: #3673FF;
}

.custom-btn-submit {
    width: 160px;
    background: #3673FF;
    border-radius: 4px 4px 4px 4px;
    color: #FFFFFF;
}

.app-container {
    height: calc(100vh - 150px);
    overflow-y: auto;
    box-sizing: border-box;
    background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%)
}

.rant-container {
    margin-bottom: 30px;
    max-height: calc(100vh - 150px);

    .card-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 32px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 13px;
            height: 17px;
            background: url("~@/assets/rant/rant-item.png") no-repeat;
            background-size: contain;
            margin-right: 10px;
        }
    }

    //overflow-y: auto;
    //overflow-x: hidden;
    .rant-detail {
        //background: rgba(255,255,255,0.7);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #FFFFFF;
        //padding: 16px;
        margin-bottom: 12px;

        .rant-detail-form {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 0px 0px 8px 8px;
            border: 1px solid #FFFFFF;
            padding: 16px;
        }
    }

    .rant-form-header {
        background: rgba(255, 255, 255, 0.3);
        box-shadow: 0px 4px 20px 0px rgba(55, 109, 247, 0.1);
        border-radius: 8px 8px 0px 0px;
        border: 1px solid #FFFFFF;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 13px 16px;
        display: flex;
        align-items: center;

        .icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
    }
    :deep(.el-form-item) {
      margin-bottom: 16px;
    }
    :deep(.rant-form-item .el-col){
      height: 47px;
    }
    .rant-form-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 13px 0px;
        display: flex;
        align-items: center;

        .icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
    }

    .rant-submit {
        margin-bottom: 20px;
        background-color: #f8f8f9;
        padding: 10px;
        width: 100%;
    }

    .icon-primary {
        color: #409eff;
    }

    .cursor {
        cursor: pointer;
    }

    .display-none {
        display: none !important;
    }

    .file-wrapper {
        width: 400px;
        min-height: 36px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid rgba(55, 109, 247, 0.3);
        justify-content: center;

        >label {
            display: inline-block;
            width: 100%;
            height: 100%;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 12px;
        }

        .el-icon-circle-close {
            margin-left: 10px;
        }
    }

    .link {
        color: #409eff;
        margin-bottom: 8px;
        margin-left: 8px;
    }
}

.transfer-item {
    display: flex;

    .item {
        //width: 25%;
        text-align: center;
        margin-right: 5px;
    }
}

.bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 255px;
    right: 0;
    padding: 10px;
    background-color: #fff;
    text-align: center;
    // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

// 为了防止底部按钮遮挡内容，给容器添加底部内边距
.rant-container {
    padding-bottom: 60px;
}

.matters-type-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
}
</style>

</style>
