
5e88cfdeb3ad1ce35e9d4748b2e0bbfcf23be3bb	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"5bb0edacf0366f8784872312d4b2cc4a\"}","integrity":"sha512-rdlID8SL2zFTCashM6znHSB2+2LXk4g29Hy/J7t1y44OHuQhIqaQ6WXSe9Xu5IwmWJ1ncpFOoGzPN35cukxziQ==","time":1754312352675,"size":12045291}