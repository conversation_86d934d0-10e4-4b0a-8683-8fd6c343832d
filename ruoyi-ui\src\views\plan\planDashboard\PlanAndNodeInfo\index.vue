<template>
  <div class="app-container" style="padding-top: 0">
    <div class="el-icon-info feedback-form-title icon-primary">计划信息</div>
    <el-form>
      <el-row>
        <el-col :span="8">
          <el-form-item label="城市公司：">
            {{ info.deptName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称：">
            {{ info.projectName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分期：">
            <dict-tag :options="dict.type.stages" :value="info.stageId" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标段：">
            <dict-tag :options="dict.type.project_lot" :value="info.lot" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划名称：">
            {{ info.planName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版本号：">
            {{ info.version }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="el-icon-search feedback-form-title icon-primary feedback-search" @click="searchFormShow = !searchFormShow">
      搜索<span class="tip">(搜索折叠，点击打开)</span>
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
      class="height-animation"
      :class="{'active': searchFormShow}"
      >
      <el-row type="flex" justify="start">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status"  clearable style="width: 15vw;">
              <el-option v-for="(item, i) in nodeStatusOption" :key="i" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点名称" prop="nodeName">
            <el-input
              style="width: 15vw;"
              v-model="queryParams.nodeName"
              placeholder="请输入节点名称"
              clearable
              size="small"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="queryParams.type"  clearable style="width: 15vw;">
              <el-option v-for="(item, i) in typeOption" :key="i" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="时间段">
              <el-date-picker
                v-model="queryParams.dateRange"
                style="width: 15vw;"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                @change="dateRangeConfirm"
              >
              </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否可用" prop="isValid">
            <el-select v-model="queryParams.isValid"  clearable style="width: 15vw;">
              <el-option v-for="(item, i) in isValidOptions" :key="i" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-show="showAdvancedSearch">
          <el-form-item label="责任部门" prop="department" >
            <el-select clearable v-model="queryParams.department" placeholder="请选择责任部门">
              <el-option v-for="dict in dict.type.resp_department"
                         :key="dict.value"
                         :label="dict.label"
                         :value="dict.value"
              ></el-option>
            </el-select>
<!--            <div class="flex-row">
              <div style="margin-right: 15px">{{ departInfo.deptName }}</div>
              <el-button size="mini" type="primary" @click="selectDepartFun">选择</el-button>
            </div>-->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" v-show="showAdvancedSearch">
          <el-form-item label="反馈人" prop="feedbackUserName">
            <div class="flex-row">
              <div class="feedback-people-container" v-if="!!feedBackUserInfo && feedBackUserInfo.nickName">
                <div style="margin-right: 15px">
                  {{ feedBackUserInfo ? feedBackUserInfo.nickName : "" }}
                  <i class="el-icon-circle-close" @click="clearFeedbackUserInfo"></i>
                </div>
              </div>

              <el-button type="primary" @click="selectFeedbackFun" size="mini">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button icon="el-icon-setting" type="primary" size="mini" @click="showAdvancedSearch = !showAdvancedSearch">更多筛选</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="el-icon-document feedback-form-title icon-primary">节点列表
      <el-button
        style="margin-left: 50px;"
        type="warning"
        plain
        icon="el-icon-download"
        size="mini"
        @click="handleExport"
        v-hasPermi="['plan:info:export']"
      >导出</el-button
      ></div>
<!--    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:info:export']"
          >导出</el-button
        >
      </el-col>

    </el-row>-->
    <el-table v-loading="loading" :data="nodeList">
      <el-table-column label="序号" align="center" prop="id" width="65">
        <template slot-scope="scope">
          <span class="textStyle">
            {{ scope.$index + 1 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" v-if="statusShow" width="110">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column
        label="节点名称"
        align="center"
        prop="nodeName"
        show-overflow-tooltip
      >
<!--        <template slot-scope="scope">
          <div class="textStyle" @click="fetchApprovalHandle(scope.row)">
            <el-link type="primary">{{ scope.row.nodeName }}</el-link>
          </div>
        </template>-->
        <template slot-scope="scope">
          <el-link class="wrap-line" v-if="tableBthShow('getApproveFlow', scope.row)" type="primary"
                   style="text-align: center"
                   @click="fetchApprovalUser(scope.row)">
            {{ scope.row.nodeName || '无' }}
          </el-link>
          <span v-else class="wrap-line" @click="$copyToClipboard(scope.row.nodeName)">{{ scope.row.nodeName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划开始时间"
        align="center"
        prop="startTime"
      >
      </el-table-column>
      <el-table-column
        label="计划完成时间"
        align="center"
        prop="endTime"
      >
      </el-table-column>
      <el-table-column
        label="实际完成时间"
        align="center"
        prop="actualCompletionDate"
        width="120"
      >
        <template slot-scope="scope">
          <div class="textStyle">
            {{ parseTime(scope.row.actualCompletionDate, "{y}-{m}-{d}") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="责任部门"
        align="center"
        width="90"
        prop="department"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="反馈人"
        width="90"
        align="center"
        prop="feedbackUserName"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="是否可用"
        width="90"
        align="center"
        prop="isValid"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="textStyle">
            {{ isValidFilter(scope.row.isValid) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row, scope.index)"
            >查看</el-button
          >
          <!--添加具名插槽-->
          <slot
            name="progressFeedback"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
          <slot
            name="finishedFeedback"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[1000, 2000, 3000, 4000]"
      @pagination="getList"
    />
    <!-- 添加或修改计划-节点对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form>
        <el-row>
          <el-col :span="12">
            <el-form-item label="节点序号">{{ form.nodeCode }}</el-form-item>
<!--            <div class="textStyleFont">节点序号： {{  form.nodeCode }}</div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点名称：">{{ form.standardNodeName }}</el-form-item>
<!--            <div class="textStyleFont">
              节点名称： {{ form.standardNodeName }}
            </div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准工期：">{{ form.durationNum }}</el-form-item>
<!--            <div class="textStyleFont">标准工期： {{ form.durationNum }}</div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任部门：">{{ form.department }}</el-form-item>
<!--            <div class="textStyleFont" style="display: flex; align-items: center">
              责任部门：{{ form.departmentNames }}
            </div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="反馈人：">{{ form.feedbackUserName }}</el-form-item>
<!--            <div class="textStyleFont">反馈人： {{ form.feedbackUserName }}</div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划开始时间：">{{ form.startTime }}</el-form-item>
<!--            <div class="textStyleFont">计划开始时间： {{ form.startTime }}</div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划完成时间：">{{ form.endTime }}</el-form-item>
<!--            <div class="textStyleFont">计划完成时间： {{ form.endTime }}</div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否可用：">{{ form.isValid ? "是" : "否" }}</el-form-item>
<!--            <div class="textStyleFont">
              是否可用： {{ form.isValid ? "是" : "否" }}
            </div>-->
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成标准：">{{ form.completeCriteria }}</el-form-item>
<!--            <div class="textStyleFont">
              完成标准： {{ form.completeCriteria }}
            </div>-->
          </el-col>
        </el-row>
      </el-form>

    </el-dialog>
<!--    <select-depart
      ref="departRef"
      :selectDepartIds="departInfo"
      @departEmit="selectDepartData"
      :single="true"
    />-->
    <select-user
      ref="userRef"
      :roleId="feedBackUserInfo.userName"
      @feedbackEmit="selectFeedbackData"
    />
  </div>
</template>

<script>
// import SelectTree from '@/components/SelectTree.vue'
import { isEditOption } from '@/views/plan/constant'
import { listNode } from "@/api/plan/node"
import Template from "@/views/plan/template/index.vue";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import {nodeStatusOption, planStatusOption} from "@/constant";
import PanelGroup from "@/views/dashboard/PanelGroup.vue";
import SelectUser from '@/views/plan/components/SelectUser'
import {
  getApproveFlow,
} from "@/api/plan/feedback";
export default {
  name: "PlanAndNodeInfo",
  components: {
    SelectUser,
    PanelGroup,
    StatusTag,
    Template,
    // SelectTree
  },
  props: ['info', 'statusShow'],
  dicts: ['stages', 'resp_department', 'project_lot'],
  data () {
    return {
      searchFormShow: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 1000,
        nodeLevel: null,
        nodeName: null,
        planId: null,
        version: null,
        type: 1,
        startTime: '',
        endTime: '',
        actualCompletionDate: '',
        departmentNames: null,
        feedbackUserName: null,
        isValid: null,
      },
      showAdvancedSearch: false,
      // 表单参数
      form: {},
      // 表单校验`
      rules: {
        nodeName: [{
          required: true,
          message: "节点名称不能为空",
          trigger: "blur"
        }],
        nodeLevel: [{
          required: true,
          message: "节点等级不能为空",
          trigger: "blur"
        }],
        durationNum: [{
          required: true,
          message: "标准工期不能为空",
          trigger: "blur"
        }],
        startTime: [{
          required: true,
          message: "开始时间不能为空",
          trigger: "change"
        }],
        endTime: [{
          required: true,
          message: "开始时间不能为空",
          trigger: "change"
        }],
        isEdit: [{
          required: true,
          message: "是否可以编辑不能为空",
          trigger: "change"
        }],
        department: [{
          required: true,
          message: "责任部门不能为空",
          trigger: "change"
        }],
        responsibilityPeople: [{
          required: true,
          message: "责任人不能为空",
          trigger: "change"
        }]
      },
      editIndex: 0,
      // info: null,
      versionList: [],
      typeOption: [
        {
          value: 1,
          label: '计划开始时间'
        },
        {
          value: 2,
          label: '计划完成时间'
        },
        {
          value: 3,
          label: '实际完成时间'
        },
      ],
      departInfo: { department: "", deptName: "" },
      feedBackUserInfo: { userName: '' },
      isValidOptions: [{
        label: '是',
        value: 1
      }, {
        label: '否',
        value: 0

      }],
    }
  },
  created () {
    // this.info = this.data
    // console.log('this.info---', this.info)
    this.queryParams.planId = this.$route.query.id
    this.getList()
    //获取部门列表
    this.$store.dispatch('plan/fetchDepartList')
  },
  computed: {
    nodeStatusOption() {
      return nodeStatusOption;
    },
    isEditOption: () => isEditOption,
    cityCompanys () {
      console.log(this.$store.state.plan.deptList)
      return this.$store.state.plan.deptList
    },
    departList () {
      return this.$store.state.plan.departList
    }
  },
  watch: {
    dateRange: function (newval, oldval) {
      this.queryParams.startTime = !newval ? null : this.parseTime(newval[0], "{y}-{m}-{d}")
      this.queryParams.endTime = !newval ? null : this.parseTime(newval[1], "{y}-{m}-{d}")
    },
  },
  methods: {
    tableBthShow(btnType, row){
      // status为 0 1 4   同时 feedback_flow_status 为0 3 4时，显示过程反馈  完成反馈
      // status 为 2  3时，显示 查看
      // 审批中的 刷新状态
      // 查看审批流的 除了0  都显示
      switch (btnType) {
        case 'view':
          return row.status === 2 || row.status === 3;
        case 'update':
          return (row.status === 0 || row.status === 1 || row.status === 4)
            && (row.feedbackFlowStatus === 0 || row.feedbackFlowStatus === 3 || row.feedbackFlowStatus === 4);
        case 'getApproveFlow': // 查看审批流
          return row.feedbackFlowStatus !== 0;
        case 'refreshFlowStatus': // 刷新状态
          return row.feedbackFlowStatus === 1;
        default:
          return false;
      }
    },
    //获取审批流
    fetchApprovalUser (row) {
      this.loading = true
      getApproveFlow(row.id).then(res => {
        this.loading = false
        window.open(res.data)
      }).catch(err => {
        this.loading = false
      })
    },
    fetchApprovalHandle(row){
      this.fetchApprovalUser(row)
        // this.$emit('fetchApprovalUser')
    },
    clearFeedbackUserInfo(){
      this.$refs.userRef.clearSelect()
      this.feedBackUserInfo = {}
      this.queryParams.feedbackUser = ''
    },
    isValidFilter(val){
      return val === 1 ? '是' : '否'
    },
    dateRangeConfirm(date){
      console.log(date);
      this.queryParams.startTime = date[0]
      this.queryParams.endTime = date[1]
    },
    selectFeedbackData (data) {
      this.feedBackUserInfo = data
      this.queryParams.feedbackUser = data.userName
    },
    selectDepartData (data) {
      this.departInfo = data
      this.queryParams.departmentNames = data.deptName
    },
   /* selectDepartFun () {
      this.$refs.departRef.show()
    },*/
    selectFeedbackFun () {
      this.$refs.userRef.show()
    },
    saveFun () {
      //保存至本地
      save({
        id: this.planId,
        planNodeList: this.nodeList
      }).then(res => {
        console.log(res)
      }).catch(err => {
        console.log(err)
      })
    },
    //提交审批
    submitFun () {

    },
    /** 查询计划-节点列表 */
    receiveData (data) {
      this.form.department = data
    },
    getList () {
      this.loading = true
      // console.log('this.queryParams---', this.queryParams);
      listNode(this.queryParams).then(response => {
        this.nodeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
    },
    /** 搜索按钮操作 */
    handleQuery () {
      delete this.queryParams.dateRange
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleDetail (row, index) {
      this.form = row
      this.open = true
      this.title = "节点明细详情"
    },
     /** 导出按钮操作 */
     handleExport () {
      this.download('plan/node/exportDashboardNodeList', {
        ...this.queryParams
      }, `计划节点列表（计划查看）_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
.height-animation {
  overflow: hidden;
  transition: max-height 0.5s ease-in-out;
  max-height: 0; /* 默认折叠状态 */
}

.height-animation.active {
  max-height: 320px; /* 展开状态的最大高度 */
}
.textStyle {
  font-size: 14px;
  color: #333;
}
.textStyleFont {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 40px;
}
.feedback-form-title {
  margin-bottom: 20px;
  background-color: #f8f8f9;
  padding: 10px;
  width: 100%;
}
.icon-primary {
  color: #409eff;
}
.icon-more{
  margin-left: 10px;
  color: #1890ff;
  font-weight: 600;
  font-size: 18px;
  cursor: pointer;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.feedback-search{
  display: flex;
  align-items: center;
  cursor: pointer;
  .tip{
    margin-left: 15px;
    font-size: 12px;
    color: #999;
  }
}
</style>
