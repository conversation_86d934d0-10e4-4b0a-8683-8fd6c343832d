import request from '@/utils/request'

// 查询工程项目信息列表
export function listData(query) {
  return request({
    url: '/project/gcProject/list',
    method: 'get',
    params: query
  })
}

// 查询工程项目信息列表
export function listAll() {
  return request({
    url: '/project/gcProject/listAll',
    method: 'get'
  })
}

// 查询工程项目信息详细
export function getData(gcProjectId) {
  return request({
    url: '/project/gcProject/' + gcProjectId,
    method: 'get'
  })
}

// 新增工程项目信息
export function addData(data) {
  return request({
    url: '/project/gcProject',
    method: 'post',
    data: data
  })
}

// 修改工程项目信息
export function updateData(data) {
  return request({
    url: '/project/gcProject',
    method: 'put',
    data: data
  })
}

// 删除工程项目信息
export function delData(gcProjectId) {
  return request({
    url: '/project/gcProject/' + gcProjectId,
    method: 'delete'
  })
}
