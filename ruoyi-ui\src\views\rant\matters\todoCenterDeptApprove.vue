<!--进度反馈后，会给责任部门负责人发送审批待办，督办审批页面-->
<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container" v-loading="loading">
      <div class="rant-title">{{ rantTitle }}</div>
      <div v-for="(rant, index) in rantList" :key="index" class="rant-content">
        <!--        <div class="section-title" v-if="rantList.length > 1">-->
        <div class="section-title" v-if="rantList.length > 1">
          <span class="rant-item-title">
            <el-checkbox v-model="rant.isSelected" @change="handleItemSelect(rant)"></el-checkbox>
            <img src="@/assets/rant/rant-item.png" class="icon" alt="">
            督办事项{{ index + 1 }}
          </span>
          <section class="rant-tools">
            <section class="operate-btn" v-if="!isRead" @click="handleSingleReject(index)">驳回</section>
            <section class="operate-btn" v-if="!isRead" @click="handleSingleApprove(index)">同意</section>
            <section class="expand" @click="toggleCollapse(index)">
              <img src="@/assets/rant/rant-collapse.png" class="icon" :class="{ 'is-active': collapseStates[index] }"
                alt="">
              {{ !collapseStates[index] ? '收起' : '展开' }}
            </section>
          </section>

        </div>
        <template v-if="!collapseStates[index]">
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-info.png" class="icon">基本信息</div>
            <el-form class="rant-detail-form" label-position="right" label-width="120px">
              <el-row>
                <!--                <el-col :span="6">-->
                <!--                  <el-form-item label="来源" prop="ranter">-->
                <!--                    {{ rant.ranterName }}-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :span="6">
                  <el-form-item label="类型" prop="mattersType">
                    <section class="custom-matters-type">
                      <dict-tag v-for="(type, index) in rant.mattersType?.split(',')" :key="index"
                        :options="dict.type.rant_matters_type" :value="type" style="height: 29px" />
                    </section>

                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分类" prop="rantClassify">
                    <dict-tag :options="dict.type.rant_classify" :value="rant.rantClassify" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任人" prop="responsiblePerson">
                    {{ rant.responsiblePersonName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="计划完成时间" prop="planTime">
                    {{ rant.planTime }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6">
                  <el-form-item label="责任部门" prop="deptName">
                    {{ rant.deptName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任部门负责人" prop="respDeptResponsiblerName">
                    {{ rant.respDeptResponsiblerName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分管领导" prop="respDeptLeaderName">
                    {{ rant.respDeptLeaderName }}
                  </el-form-item>
                </el-col>
                <el-col v-if="type == 'matters'" :span="6">
                  <el-form-item label="是否私密" prop="isPrivate">
                    {{ rant.isPrivate == 1 ? "是" : "否" }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="内容" prop="rantContent">
                    {{ rant.rantContent }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="措施" prop="solution">
                    {{ rant.solution }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

              </el-row>
              <div class="rant-form-title">
                <img src="@/assets/rant/rant-this-progress.png" class="icon">
                本次进展
              </div>
              <editor v-model="rant.thisProgress" :height="192" class="width-100 mb-16" :readOnly="true" />
              <el-row>
                <el-col :span="6">
                  <el-form-item label="是否结项" prop="isCompletion" class="custom-form-item-bac">
                    <el-select v-model="rant.isCompletion" placeholder="请选择" class="width-100 custom-date-picker"
                      disabled>
                      <el-option label="是" :value="true"></el-option>
                      <el-option label="否" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="rant.isCompletion" :span="6">
                  <el-form-item label="结项时间" prop="closingTime" class="custom-form-item-bac">
                    <el-date-picker clearable v-model="rant.closingTime" type="date" disabled
                      class="width-100-important custom-date-picker" value-format="yyyy-MM-dd" placeholder="选择结项时间"
                      :picker-options="{
                        disabledDate(time) {
                          const oneWeekAgo = new Date();
                          oneWeekAgo.setTime(oneWeekAgo.getTime() - 7 * 24 * 3600 * 1000);
                          return time.getTime() < oneWeekAgo.getTime();
                        }
                      }">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="成果" prop="achievement" class="custom-form-item-bac">
                    <section class="file-wrapper">
                      <!--                      <label v-if="!rant.fileList || rant.fileList.length === 0" :for="'uploadFile_'"
                             class="cursor"
                             :class="{
                              'custom-warning': !!rantAchievementFiles.fileListError,
                            }">
                        {{ rantAchievementFiles.fileListError || "请上传文件" }}
                      </label>-->
                      <section class="file-wrapper">
                        <!--                      <div v-for="(file, index) in rant.fileList" :key="index" class="file-show">-->
                     <!--    <a v-for="(file, index) in rant.fileList" :key="index" class="file-show link file-name"
                          :href="file.url" target="_blank" :title="file.name">
                          <div class="filename-part">{{ getTruncatedFileName(file.name) }}</div>
                          <div class="extension-part">{{ getFileExtension(file.name) }}</div>
                        </a> -->
                        <file-link v-for="(file, index) in rant.fileList" :key="index" :file="file" />
                        <!--                          <i class="el-icon-circle-close cursor link" @click="handleDeleteAnnexUrl(index, rant.fileList)"></i>-->
                        <!--                        </div>-->
                      </section>
                    </section>
                    <input type="file" class="display-none" multiple :id="'uploadFile_'"
                      @change="(event) => uploadFileHandle(event, index)" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-progress.png" class="icon">进度情况</div>
            <el-table :data="rant.recordDtoList" style="width: 100%">
              <el-table-column align="center" label="序号" prop="id" width="80" :header-row-class-name="'header-row'">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="实际进展" align="center" prop="actualProgress">
                <template slot-scope="scope">
                  <div v-html="scope.row.actualProgress"></div>
                </template>
              </el-table-column>
              <el-table-column label="汇报时间" align="center" width="100" prop="feedbackTime">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.feedbackTime, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column label="成果" align="center" width="300" prop="achievementFileUrl">
                <template slot-scope="scope">
                  <section style="display: flex; flex-direction: column">
                 <!--    <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" class="link"
                      target="_blank">
                      {{ file.name || "--" }}</a> -->
                      <file-link v-for="(file, index) in scope.row.fileList" :key="index" :file="file" />
                  </section>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-buttons btn-center" v-if="rantList.length === 1 && !isRead">
      <el-button type="primary" @click="handleSingleApprove(0)">同意</el-button>
      <el-button type="danger" @click="handleSingleReject(0)">驳回</el-button>
    </div>
    <div class="bottom-buttons" v-if="rantList.length > 1 && !isRead">
      <el-checkbox v-model="isAllSelected" @change="handleSelectAll">全选</el-checkbox>
      <el-button type="primary" @click="handleBatchApprove">批量同意</el-button>
      <el-button type="danger" @click="handleBatchReject">批量驳回</el-button>
    </div>

    <!-- 审批弹窗 -->
    <el-dialog :title="approveDialog.type === 'reject' ? '驳回意见' : '审批意见'" :visible.sync="approveDialog.visible"
      width="500px" :close-on-click-modal="false" @close="cancelApprove">
      <el-form ref="approveForm" :model="approveDialog.form" :rules="approveDialog.rules" label-width="80px">
        <el-form-item label="审批意见" prop="approveDesc">
          <el-input v-model="approveDialog.form.approveDesc" type="textarea" :rows="4"
            :placeholder="approveDialog.type === 'reject' ? '请输入驳回意见' : '请输入审批意见'" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelApprove">取 消</el-button>
        <el-button type="primary" @click="submitApprove" :loading="approveDialog.loading">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// http://localhost/rant/todoCenterDeptApprove?todoNoticeId=30
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}
import { getMatters, approveTodoInfo, submitFeedback } from "@/api/rant/matters";
import { deptLeaderApprove } from "@/api/rant/record";
import { uploadFileMultiple } from "@/api/rant/common";
export default {
  name: "Feedback",
  dicts: ["rant_classify", "rant_completion_status", "rant_matters_type"],
  data() {
    return {
      // 遮罩层
      loading: false,
      rantList: [], // 吐槽详情
      recordList: [], // 进度记录
      fileList: [],
      rantAchievementFiles: {}, // 附件信息
      uploadFileLoading: false,
      isCompletion: false,
      collapseStates: [], // 控制每个事项的折叠状态
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      hoverScore: null,
      isAllSelected: false, // 控制全选状态
      approveDialog: {
        visible: false,
        type: '', // 'reject' 或 'approve'
        isBatch: false, // 是否批量操作
        loading: false,
        form: {
          approveDesc: '',
          selectedIds: [], // 选中的事项ID列表
        },
        rules: {
          approveDesc: [
            { required: true, message: '请输入审批意见', trigger: 'blur' },
            { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
          ]
        },
      },
      rantTitle: "",
      isRead: false
    };
  },

  mounted() {
    const todoNoticeId = this.$route.query.todoNoticeId;
    this.handleGetRantList(todoNoticeId);
    // 添加自动滚动到顶部

  },
  methods: {
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    /**
     * 统一处理审批操作
     * @param {Object} options 审批选项
     * @param {string} options.type 审批类型：'approve' 或 'reject'
     * @param {boolean} options.isBatch 是否批量操作
     * @param {number|null} options.index 单个操作时的事项索引
     */
    handleApprove(options) {
      const { type, isBatch, index } = options;

      // 重置表单
      this.approveDialog.form.approveDesc = '';
      this.approveDialog.type = type;
      this.approveDialog.isBatch = isBatch;

      // 设置选中的事项ID
      if (isBatch) {
        // 批量操作：获取所有选中的事项ID
        this.approveDialog.form.selectedIds = this.rantList
          .filter(item => item.isSelected)
          .map(item => {
            return {
              mattersId: item?.id,
              progressFeedbackId: item?.thisProgressFeedbackId
            }
          });
        if (this.approveDialog.form.selectedIds.length === 0) {
          this.$modal.msgError('请至少选择一个事项');
          return;
        }
      } else {
        // 单个操作：获取指定索引的事项ID
        // this.approveDialog.form.selectedIds = [this.rantList[index].id];
        this.approveDialog.form.selectedIds = [{
          mattersId: this.rantList[index]?.id,
          progressFeedbackId: this.rantList[index]?.thisProgressFeedbackId
        }];
      }

      // 驳回时需要填写意见，同意时直接提交
      if (type === 'reject') {
        this.approveDialog.visible = true;
      } else {
        this.submitApprove();
      }
    },

    /**
     * 提交审批
     */
    submitApprove() {
      const { type, form } = this.approveDialog;

      // 如果是驳回操作，需要验证表单
      if (type === 'reject') {
        this.$refs.approveForm.validate((valid) => {
          if (!valid) {
            this.loading = false
            return;
          }
          this.doSubmitApprove();
        });
      } else {
        this.doSubmitApprove();
      }
    },

    /**
     * 执行审批提交
     */
    doSubmitApprove() {
      const { type, form } = this.approveDialog;
      this.approveDialog.loading = true;
      this.loading = true;
      console.log(form.selectedIds)
      deptLeaderApprove({
        todoNoticeId: this.$route.query.todoNoticeId,
        approveStatus: type === 'approve' ? 1 : 2,
        feedbackApproveVoList: form.selectedIds,
        approveDesc: type === 'reject' ? form.approveDesc : undefined
      }).then((response) => {
        this.$modal.msgSuccess("提交成功");
        this.approveDialog.visible = false;
        const todoNoticeId = this.$route.query.todoNoticeId;
        approveTodoInfo(todoNoticeId).then((response) => {
          console.log("response------------", response)
          var isRead = response.data?.isRead;
          if (isRead) {
            this.$modal.msgSuccess("待办已完结，即将关闭页面");
            setTimeout(() => {
              close();
            }, 1500);
          } else {
            // this.rantList = response.data;
            this.rantList = response.data?.rankMattersFeedbackDtoList || [];
            this.rantTitle = response.data?.title;

            // 初始化每个事项的选中状态为false
            this.rantList.forEach((item, index) => {
              this.$set(this.rantList[index], 'isSelected', false);
            });

            // 重置全选状态
            this.isAllSelected = false;

            // 初始化折叠状态，默认折叠
            this.collapseStates = new Array(this.rantList.length).fill(true);
            if (this.collapseStates.length > 0) {
              this.collapseStates[0] = false; // 设置第一个元素为 false
            }
          }
        }).finally(() => {
          this.approveDialog.loading = false;
          this.loading = false;
        });
      }).finally(() => {
        this.approveDialog.loading = false;
        this.loading = false;
      });
    },

    /**
     * 取消审批
     */
    cancelApprove() {
      this.approveDialog.visible = false;
      this.approveDialog.form.approveDesc = '';
      this.approveDialog.form.selectedIds = [];
      this.loading = false;
    },

    // 快捷方法
    handleSingleApprove(index) {
      this.handleApprove({ type: 'approve', isBatch: false, index });
    },

    handleBatchApprove() {
      this.handleApprove({ type: 'approve', isBatch: true });
    },

    handleSingleReject(index) {
      this.handleApprove({ type: 'reject', isBatch: false, index });
    },

    handleBatchReject() {
      this.handleApprove({ type: 'reject', isBatch: true });
    },

    handleGetRantList(todoNoticeId) {
      this.loading = true;
      approveTodoInfo(todoNoticeId).then((response) => {
        this.rantList = response.data?.rankMattersFeedbackDtoList || [];
        this.isRead = response.data?.isRead;
        this.rantTitle = response.data?.title;

        // 初始化每个事项的选中状态为false
        this.rantList.forEach((item, index) => {
          this.$set(this.rantList[index], 'isSelected', false);
        });

        // 重置全选状态
        this.isAllSelected = false;

        // 初始化折叠状态，默认折叠
        this.collapseStates = new Array(this.rantList.length).fill(true);
        if (this.collapseStates.length > 0) {
          this.collapseStates[0] = false; // 设置第一个元素为 false
        }
        this.$nextTick(() => {
          const container = document.querySelector('html');
          if (container) {
            container.scrollTop = 0;
          }
        });

      }).finally(() => {
        this.loading = false;
      });
    },

    uploadFileHandle(event, index) {
      const files = event.target.files;
      if (!files) {
        //
        // 没有选择文件
        return;
      }
      this.loading = true;
      this.uploadFileLoading = true;
      const formData = new FormData();
      for (let item of files) {
        formData.append("files", item);
      }
      uploadFileMultiple(formData)
        .then((response) => {
          console.log(response);
          if (!this.rantList[index].fileList) this.rantList[index].fileList = [];
          this.rantList[index].fileList = [...this.rantList[index].fileList, ...response.data];
          console.log(this.rantList[index].fileList);
          this.$forceUpdate();
          this.uploadFileLoading = false;
        })
        .catch((error) => {
          this.uploadFileLoading = false;
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDeleteAnnexUrl(index, fileList) {
      fileList.splice(index, 1);
    },
    handleItemSelect(rant) {
      console.log('rant---------------', rant.isSelected);
      // 使用索引查找当前项
      const index = this.rantList.findIndex(item => item.id === rant.id);
      if (index !== -1) {
        // 使用 $set 确保响应式更新
        this.$set(this.rantList[index], 'isSelected', rant.isSelected);
      }
      // 检查是否所有事项都被选中
      this.isAllSelected = this.rantList.every(item => item.isSelected);
    },
    handleSelectAll(val) {
      // 将所有事项的选中状态设置为全选框的状态
      this.rantList.forEach((item, index) => {
        this.$set(this.rantList[index], 'isSelected', val);
      });
    },
    toggleCollapse(index) {
      this.$set(this.collapseStates, index, !this.collapseStates[index]);
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.custom-date-picker .el-input.is-disabled .el-input__inner) {
  color: #333333;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: unset;
}

.file-name {
  width: 100%;
  display: flex;
  color: #4080FF;
  line-height: 1.8;

  .filename-part {
    min-width: 0;
    flex-grow: 1;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #4080FF;
  }

  .extension-part {
    word-break: keep-all;
    color: #4080FF;
  }
}

:deep(.ql-toolbar.ql-snow) {
  background: rgba(54, 115, 255, 0.05);
}

:deep(.custom-editor) {
  .el-form-item__content {
    width: 100%;
  }
}

.expand {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #3673FF;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    transition: transform 0.3s ease;

    &.is-active {
      transform: rotate(180deg);
    }
  }
}

// 添加折叠展开动画
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease-out;
  max-height: 1000px; // 设置一个足够大的高度
  overflow: hidden;
}

.fade-slide-enter,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.rant-content {
  transition: all 0.3s ease-out;
  background: rgba(255, 255, 255, 0.7);
  margin-bottom: 16px;
  border-radius: 8px 8px 8px 8px;
}

:deep(.custom-form-item-bac input) {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(55, 109, 247, 0.3);
  background: #F1F7FE;
}

:deep(.custom-form-item-bac .el-form-item__label) {
  line-height: 36px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

:deep(.el-form-item__content) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__header .el-table__cell) {
  //background: rgba(54, 115, 255, 0.05);
  background: rgba(54, 115, 255, 0.1);

}

:deep(.el-table__header .el-table__cell .cell) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  font-style: normal;
  text-transform: none;

}

:deep(.el-table__body .el-table__row .el-table__cell) {
  background-color: #F3F8FC;
}

:deep(.el-table__empty-block) {
  background-color: #F3F8FC;
}

.custom-btn-save {
  width: 160px;
  background: #C8DDFA;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  color: #3673FF;
}

.custom-btn-submit {
  width: 160px;
  background: #3673FF;
  border-radius: 4px 4px 4px 4px;
  color: #FFFFFF;
}

.app-container {
  //max-height: 90vh;
  //height: (100vh- 56px);
  //overflow-y: auto;
  //overflow-x: hidden;
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  overflow-y: auto;
}

.rant-detail-content {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0px 0px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
}

.rant-detail {
  margin-bottom: 44px;
  padding: 19px 16px 0 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.rant-container {
  height: (100vh- 56px);
  overflow-y: auto;

  /*  .rant-form-title {
     margin-bottom: 20px;
     background-color: #f8f8f9;
     padding: 10px;
     width: 100%;
   } */
  .rant-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 9rpx;
    color: #333333;
    line-height: 11rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 10px;
    margin-left: 20px;
  }

  .rant-form-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
    display: flex;
    align-items: center;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .icon-primary {
    color: #409eff;
  }

  .cursor {
    cursor: pointer;
  }

  .display-none {
    display: none !important;
  }

  .file-wrapper {
    width: 400px;
    min-height: 36px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(55, 109, 247, 0.3);

    >label {
      display: inline-block;
      width: 100%;
      height: 100%;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 36px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 12px;
    }

    .el-icon-circle-close {
      margin-left: 10px;
    }
  }

  .link {
    color: #409eff;
    margin-bottom: 8px;
    margin-left: 8px;
  }
}

.transfer-item {
  display: flex;

  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}

.rant-content {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  // background-color: #f1ee10;
  .rant-item-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .el-icon-arrow-down {
    transition: transform 0.3s;

    &.is-active {
      transform: rotate(-180deg);
    }
  }

  .rant-tools {
    display: flex;
    align-items: center;
    gap: 16px;

    .operate-btn {
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #3673FF;
      padding: 4px 8px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #3673FF;
      line-height: 19px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      cursor: pointer;

      &:last-child {
        margin-right: 32px;
      }
    }
  }

  box-shadow: 0px 4px 20px 0px rgba(55, 109, 247, 0.1);
  border-radius: 8px 8px 0px 0px;
  border: 1px solid #FFFFFF;
  //background: rgba(54, 115, 255, 0.2);
  padding: 13px 16px;
  //margin: 20px 0 0;
  font-family: PingFang SC,
  PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  // background-color: #fff;
  text-align: center;
  z-index: 1000;
  //background: rgba(255,255,255,0.5);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  background-color: #EDF7F9;
  display: flex;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #EBEEF5;

  .el-checkbox {
    margin-right: 16px; // 与其他按钮保持间距
  }

  &.btn-center {
    display: flex;
    justify-content: center;
  }
}

// 为了防止底部按钮遮挡内容，给容器添加底部内边距
.rant-container {
  padding-bottom: 60px;
}

.collapse-transition-enter-active,
.collapse-transition-leave-active {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.collapse-transition-enter,
.collapse-transition-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  height: 0;
}

.collapse-transition-enter-to,
.collapse-transition-leave {
  opacity: 1;
  height: auto;
}
</style>
