<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="责任部门" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入责任部门" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="负责人" prop="respPeopleName">
        <el-input v-model="queryParams.respPeopleName" placeholder="请输入负责人" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="分管领导" prop="responsibleLeaderName">
        <el-input v-model="queryParams.responsibleLeaderName" placeholder="请输入分管领导" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="respdetList" row-key="id" :default-expand-all="isExpandAll"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="责任部门" align="left" prop="name"/>
      <el-table-column label="负责人" align="center" prop="respPeopleName"/>
      <el-table-column label="分管领导" align="center" prop="responsibleLeaderName"/>
      <el-table-column label="排序" align="center" prop="orderNum"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)">新增</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 添加或修改责任人责任部门分管领导关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px"  append-to-body
               @close="handleDialogClose">
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="24" v-if="form.parentId != 0">
              <el-form-item label="上级部门" prop="parentId">
                <treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer"
                            placeholder="选择上级部门"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="责任部门" prop="name">
                <el-input v-model="form.name" placeholder="请输入责任部门"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="责任人" prop="respPeopleName">
                <div style="width: 225px" class="flex-row">
                  <el-input suffix-icon="el-icon-search" v-model="form.respPeopleName" placeholder="请选择" clearable
                            @focus="selectUserFun('respPeople')"/>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="分管领导" prop="responsibleLeaderName">
                <div style="width: 225px" class="flex-row">
                  <el-input suffix-icon="el-icon-search" v-model="form.responsibleLeaderName" placeholder="请选择"
                            clearable
                            @focus="selectUserFun('responsibleLeader')"/>
                </div>

              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序序号" prop="orderNum">

                <el-input-number style="width: 225px" v-model="form.orderNum" controls-position="right" :min="0"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData"/>
  </div>
</template>

<script>
import {getRespdet, listRespdet, addRespdet, updateRespdet, delRespdet} from "@/api/rant/respdet";
import SelectUser from "@/views/rant/components/SelectUser/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "RespDept",
  components: {SelectUser, Treeselect},
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 责任人责任部门分管领导关系表格数据
      respdetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      selectUserShow: true,
      roleName: "",
      isMultiple: false,
      //选择用户
      selectUserType: "",
      // 部门列表
      deptOptions: [],

      // 查询参数
      queryParams: {

        name: null,
        parentId: null,
        respPeople: null,
        respPeopleName: null,
        responsibleLeader: null,
        responsibleLeaderName: null,
        orderNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {required: true, message: "责任部门不能为空", trigger: ["blur", "change"]}
        ],
        respPeopleName: [
          {required: true, message: "责任人不能为空", trigger: ["blur", "change"]}
        ],
        responsibleLeaderName: [
          {required: true, message: "分管领导不能为空", trigger: ["blur", "change"]}
        ],
        orderNum: [
          {required: true, message: "排序序号不能为空", trigger: ["blur", "change"]},
          {type: "number", min: 1, message: "排序序号不能为0", trigger: ["blur", "change"]}
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询责任人责任部门分管领导关系列表 */
    getList() {
      this.loading = true;
      listRespdet(this.queryParams).then(response => {
        this.respdetList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    handleDialogClose() {
      console.log("handleDialogClose");
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        parentId: null,
        respPeople: null,
        respPeopleName: null,
        responsibleLeader: null,
        responsibleLeaderName: null,
        orderNum: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd(row) {

      this.reset();
      if (row != undefined) {
        this.form.parentId = row.id;
      }
      this.selectUserShow = true;
      this.open = true;
      this.title = "添加责任部门";
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getRespdet(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改责任部门";
      });
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true
          if (this.form.id != null) {
            updateRespdet(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.submitLoading = false;
              this.getList();
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            addRespdet(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.submitLoading = false;
              this.getList();
            }).finally(() => {
              this.submitLoading = false
            })
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {

      this.$modal.confirm('是否确认删除责任部门名称为"' + row.name + '"的数据项？').then(function () {
        return delRespdet(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('rant/respdet/export', {
        ...this.queryParams
      }, `respdet_${new Date().getTime()}.xlsx`)
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    selectUserFun(type) {
      console.log("selectUserFun", type);
      this.selectUserType = type;
      this.$refs.userRef.show();
    },
    selectUserData(data) {
      if (this.selectUserType == "responsibleLeader") {
        this.form.responsibleLeader = data.userId;
        this.form.responsibleLeaderName = data.nickName;
      } else if (this.selectUserType == "respPeople") {
        this.form.respPeople = data.userId;
        this.form.respPeopleName = data.nickName;
      }

    },
  }
};
</script>
