class LineTrendOption {
  constructor(xAxisData = [], seriesData = []) {
    let scale = 1;
    if(window.innerWidth <  1680){
      scale = window.innerWidth / 1680;  // 基于1680px宽度的缩放比例
    }
    
    this.option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      xAxis: [{
        name: '月',
        nameTextStyle: {
          fontSize:  12 * scale,
          padding: [0, 0, 0, -10]
        },
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999',
            width: 1 * scale
          }
        },
        axisLabel: {
          align: 'center',
          fontSize:  12 * scale,
          margin: 12 * scale
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
          length: 6 * scale,
          lineStyle: {
            color: '#999',
            width: 1 * scale
          }
        }
      }],
      yAxis: [{
        name: '亿',
        nameTextStyle: {
          color: '#999',
          fontSize: 12 * scale,
          padding: [0, 20 * scale, 0, 0]
        },
        type: 'value',
        splitNumber: 4,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#DDD',
            width: 1 * scale
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#333'
          }
        },
        axisLabel: {
          fontSize:  12 * scale
        },
        splitArea: {
          show: false
        }
      }],
      series: [{
        type: 'line',
        data: seriesData,
        label: {
          normal: {
            show: true,
            position: 'top',
            fontSize:  12 * scale,
            padding: [4 * scale, 0, 0, 0]
          }
        },
        lineStyle: {
          normal: {
            width: 2.5 * scale,
            color: {
              type: 'linear',
              colorStops: [{
                offset: 0,
                color: '#1EE2FF'
              }, {
                offset: 1,
                color: '#6F42FB'
              }],
              globalCoord: false
            },
            shadowColor: 'rgba(72,216,191, 0.3)',
            shadowBlur: 10 * scale,
            shadowOffsetY: 20 * scale
          }
        },
        areaStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(30,226,255,0.3)'  // 渐变开始色，带透明度
              }, {
                offset: 1,
                color: 'rgba(111,66,251,0.1)'  // 渐变结束色，带透明度
              }]
            }
          }
        },
        itemStyle: {
          normal: {
            color: '#1EE2FF',
            borderWidth: 4 * scale,
          }
        },
        smooth: true,
        symbolSize: 8 * scale
      }]
    };
  }

  getOption() {
    return this.option;
  }

  setAxisData(xAxisData, seriesData) {
    this.option.xAxis[0].data = xAxisData;
    this.option.series[0].data = seriesData;
  }

  updateData(xAxisData, seriesData) {
    this.option.xAxis[0].data = xAxisData;
    this.option.series[0].data = seriesData;
  }
}

export default LineTrendOption;
