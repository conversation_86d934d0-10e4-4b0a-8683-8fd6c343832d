<template>
  <div class="rant-container">
    <div class="search-bar">
      <div class="filter-buttons">
        <div class="filter-button" @click="showTypePopup = true">
          <span>类型</span>
          <van-icon name="arrow-down" />
        </div>
        <div class="filter-button" @click="showStatusPopup = true">
          <span>{{ getSelectedRantClassify || '分类' }}</span>
          <van-icon name="arrow-down" />
        </div>
        <div class="search-input">
          <van-search
            v-model="queryParams.rantContent"
            placeholder="搜索督办内容"
            @search="onSearch"
            shape="round"
            style="background-color: #3673FF"
          />
        </div>
      </div>
    </div>
    <div class="rant-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          :error.sync="error"
          error-text="请求失败，点击重新加载"
          @load="onLoad"
        >
          <div class="task-list">
            <div class="task-card" v-for="(task, index) in mattersList" :key="index" @click="handleView(task)">
              <div class="task-header">
                <!-- 督办类型 -->
                <div class="task-title">{{getTaskTypeLabel(task.mattersType) || task.title}}</div>
                <!-- 事项状态 -->
                <div :class="['task-status', getStatusClass(task.status)]">{{getStatusText(task.status)}}</div>
              </div>
              <div class="task-subheader">
                <div class="person-type">
                  <!-- 来源 -->
                  <div class="task-person">{{task.ranterName}}</div>

                </div>
                <!-- 分类 -->
                <div class="task-type">{{task.rantClassify}}</div>
              </div>
              <!-- 督办内容 -->
              <div class="task-content">
                <div>{{task.rantContent}}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <!-- 类型选择弹出层 -->
    <van-popup
      v-model="showTypePopup"
      position="bottom"
      round
    >
      <div class="popup-header">
        <div class="popup-title">选择类型</div>
        <van-icon name="cross" @click="showTypePopup = false" />
      </div>
      <div class="popup-content">
        <van-checkbox-group v-model="selectedTypes">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in types"
              :key="index"
              :title="item.dictLabel"
              clickable
              @click="toggleType(item.dictValue)"
            >
              <template #right-icon>
                <van-checkbox :name="item.dictValue"  shape="square"  />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
      <div class="popup-footer">
        <van-button block type="primary" @click="confirmTypeSelection" class="custom-btn">确认</van-button>
      </div>
    </van-popup>

    <!-- 分类选择弹出层 -->
    <van-popup
      v-model="showStatusPopup"
      position="bottom"
      round
    >
      <div class="popup-header">
        <div class="popup-title">选择分类</div>
        <van-icon name="cross" @click="showStatusPopup = false" />
      </div>
      <div class="popup-content">
        <van-radio-group v-model="selectedStatus">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in rant_classify_list"
              :key="index"
              :title="item.dictLabel"
              clickable
              @click="selectedStatus = item.dictValue"
            >
              <template #right-icon>
                <van-radio :name="item.dictValue" :disabled="false" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
      <div class="popup-footer">
        <van-button block type="primary" @click="confirmStatusSelection" class="custom-btn">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
// http://localhost/wechatE/mobile/rant/todoCenterMattersNotice?todoNoticeId=1
import {
  remindList,
  readDaiBanChange
}from "@/api/rantMobile/matters";
import { getDicts } from '@/api/rantMobile/common'
import {
  List,
  PullRefresh,
  Cell,
  CellGroup,
  Popup,
  Search,
  Icon,
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Button
} from 'vant';

export const rantStatusOption = [
  // 0草稿 -蓝色 ，1进行中 -黄色，2按时完成 -绿色，3延期完成-浅红，4延期未完成-深红，5终止-灰色, 6审批中，7驳回
  {label: '草稿', value: 0, type: null},
  {label: '进行中', value: 1, type: 'info', color: 'yellow'},
  {label: '按时完成', value: 2, type: 'success'},
  {label: '延期完成', value: 3, type: 'danger'},
  {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
  {label: '终止', value: 5, type: 'info'},
  {label: '审批中', value: 6, type: 'warning'},
  {label: '驳回', value: 7,  type: 'success', color: 'red', fontColor: 'white!important'},
]
export default {
  components: {
    'van-list': List,
    'van-pull-refresh': PullRefresh,
    'van-cell': Cell,
    'van-cell-group': CellGroup,
    'van-popup': Popup,
    'van-search': Search,
    'van-icon': Icon,
    'van-checkbox': Checkbox,
    'van-checkbox-group': CheckboxGroup,
    'van-radio': Radio,
    'van-radio-group': RadioGroup,
    'van-button': Button
  },
  data: function () {
    return {
      mattersList: [],
      loading: false,
      refreshing: false,
      finished: false,
      error: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranter: null,
        mattersType: [],
        rantClassify: null,
        planTime: null,
        deptId: null,
        responsiblePerson: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: null,
        isPrivate: null,
        todoNoticeId: this.$route.query.todoNoticeId, // 必传
      },
      total: 0,
      showTypePopup: false,
      showStatusPopup: false,
      selectedTypes: [],
      selectedStatus: null,
      types: [],
      rant_classify_list: [],
    }
  },
  created() {
    // 初始加载第一页数据
    this.queryParams.pageNum = 1;
    // this.getMattersList();
    this.fetchTypes();
    this.fetchRantClassify();
  },
  methods: {
    handleView(task) {
      this.$router.push({
        path: '/wechatE/mobile/rant/todoCenterMattersDetail',
        query: {
          id: task.id
        }
      })
    },
    getStatusText(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption.find(s => s.value === statusValue);
      return statusObj ? statusObj.label : status;
    },
    getStatusClass(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption?.find(s => s.value === statusValue);

      if (!statusObj) return '';

      switch(statusValue) {
        case 0: // 草稿
          return 'status-draft';
        case 1: // 进行中
          return 'status-in-progress';
        case 2: // 按时完成
          return 'status-on-time';
        case 3: // 延期完成
          return 'status-delayed-finished';
        case 4: // 延期未完成
          return 'status-delayed-unfinished';
        case 5: // 终止
          return 'status-ended';
        case 6: // 审批中
          return 'status-approving';
        case 7: // 驳回
          return 'status-rejected';
        default:
          return '';
      }
    },
    // 加载数据
    onLoad() {
      // 如果是刷新，不增加页码
      if (!this.refreshing) {
        this.queryParams.pageNum += 1;
      }
      this.getMattersList();
    },
    // 下拉刷新
    onRefresh() {
      this.finished = false;
      this.error = false;
      this.queryParams.pageNum = 1;
      this.mattersList = [];
      this.loading = true;
      this.getMattersList();
    },
    fetchRantClassify() {
      getDicts('rant_classify').then(res => {
        // 添加"全部"选项
        this.rant_classify_list = [{ dictValue: '', dictLabel: '全部' }, ...res.data];
      });
    },
    // 获取类型选项
    async fetchTypes() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.types = [{ dictValue: '', dictLabel: '全部' }, ...res.data.map(item => ({
            dictValue:item.dictValue,
            dictLabel: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取类型选项失败:', error);
        /*uni.showToast({
          icon: 'none',
          title: '获取类型选项失败'
        });*/
      }
    },

    getTaskTypeLabel(value) {
      if (!value) return '';

      // If value is a comma-separated string (from API), split it into an array
      const typeValues = typeof value === 'string' ? value.split(',') :
        Array.isArray(value) ? value : [value];

      // Map each value to its label
      const typeLabels = typeValues.map(val => {
        const type = this.types.find(t => t.dictValue === val);
        return type ? type.dictLabel : '';
      }).filter(label => label); // Remove empty labels

      return typeLabels.join('|');
    },
    getMattersList() {
      remindList(this.queryParams).then(res => {
        if (this.refreshing) {
          this.mattersList = [];
          this.refreshing = false;
        }

        // 追加数据
        this.mattersList = [...this.mattersList, ...res.rows];
        this.total = res.total;
        this.loading = false;
        console.log('this.queryParams.pageNum------', this.queryParams.pageNum);
        if(this.queryParams.pageNum === 1) {
          readDaiBanChange(this.$route.query.todoNoticeId);
        }
        // 判断是否还有更多数据
        if (this.mattersList.length >= res.total) {
          this.finished = true;
        }
      }).catch(err => {
        this.loading = false;
        this.error = true;
        console.error(err);
      });
    },
    toggleType(type) {
      // 找到类型在当前选中数组中的索引
      const index = this.selectedTypes.indexOf(type);

      // 如果已经选中，则取消选中；如果未选中，则添加到选中数组
      if (index === -1) {
        // 如果是空字符串 (全部选项)，则清空其他所有选择
        if(type === '') {
          this.selectedTypes = [type];
        } else {
          // 如果选中的不是"全部"，则从选中列表中移除"全部"选项
          const allIndex = this.selectedTypes.indexOf('');
          if(allIndex !== -1) {
            this.selectedTypes.splice(allIndex, 1);
          }
          // 添加当前选项
          this.selectedTypes.push(type);
        }
      } else {
        // 取消选中
        this.selectedTypes.splice(index, 1);
      }

      // 防止事件冒泡
      this.$nextTick(() => {
        // 强制更新视图
        this.$forceUpdate();
      });
    },
    confirmTypeSelection() {
      console.log('this.selectedTypes------', this.selectedTypes, typeof this.selectedTypes);
      if(this.selectedTypes[0] === '') { // 全部
        this.queryParams.mattersType = [];
      } else {
        this.queryParams.mattersType = this.selectedTypes;
      }

      this.onRefresh();
      this.showTypePopup = false;
    },
    confirmStatusSelection() {
      if (this.selectedStatus === '') {
        // 如果选择了"全部"，则不设置分类过滤
        this.queryParams.rantClassify = null;
      } else {
        this.queryParams.rantClassify = this.selectedStatus;
      }
      this.onRefresh();
      this.showStatusPopup = false;
    },
    onSearch() {
      this.onRefresh();
    }
  },
  computed: {
    // 获取已选择的分类名称
    getSelectedRantClassify() {
      if (!this.selectedStatus) return '';
      const selectedItem = this.rant_classify_list.find(item => item.dictValue === this.selectedStatus);
      return selectedItem ? selectedItem.dictLabel : '';
    }
  },
}
</script>
<style lang="scss" scoped>
.rant-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #D6E1F1;
  overflow-y: auto;

  // 自定义按钮样式
  .custom-btn {
    background-color: #376DF7;
    border-color: #376DF7;
    color: #FFFFFF;
  }

  .rant-content {
    flex: 1;
    overflow-y: auto;
    margin: 12px;

    .task-card {
      padding: 14px 12px;
      margin-bottom: 8px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
    }
  }
  // 列表卡片样式
  .task-list {
    padding: 0;
  }
  .task-card {
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
  }

  .task-title {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 8px;
  }

  .task-date {
    font-size: 28rpx;
    color: #999;
  }

  .task-subheader {
    display: flex;
    justify-content: space-between;
    margin-bottom: 9px;
  }
  .person-type {
    display: flex;
  }

  .task-person, .task-type {
    font-size: 28rpx;
    color: #666;
    margin-right: 16rpx;
  }

  .task-content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
  }
  // 吐槽状态
  .task-status {
    font-size: 28rpx;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    height: fit-content;
  }

  .status-draft {
    color: #4080FF;
    background: rgba(64,128,255,0.1);
  }

  .status-delayed-unfinished {
    color: #CC1414;
    background: rgba(204,20,20,0.1);
  }

  .status-delayed-finished {
    color: #FF9900;
    background: rgba(255,149,0,0.1);

  }

  .status-in-progress {
    color: #376DF7;
    background: rgba(55,109,247,0.1);

  }

  .status-on-time {
    color: #26BF73;
    background: rgba(38,191,115,0.1);
  }

  .status-ended {
    color: #999;
    background: rgba(102,102,102,0.1);
  }

  .status-approving {
    color: #FAAD14;
    background: rgba(255,149,0,0.1);
  }

  .status-rejected {
    color: #FF4D4F;
    background: rgba(255,77,79,0.1);
  }
  .search-bar {
    padding: 12px;
    //background-color: #fff;
    border-bottom: 1px solid #eee;
  }
  .filter-buttons {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }
  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 32px;
    border-radius: 100px 100px 100px 100px;
    border: 1px solid rgba(55,109,247,0.3);
    background-color: #DBE4F6;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    margin-right: 8px;
  }
  .filter-button .van-icon {
    margin-left: 4px;
  }
  :deep(.van-search){
    padding: 0;
    background-color: #DBE4F6;
    border-radius: 100px 100px 100px 100px;
    border: 1px solid rgba(55,109,247,0.3);
  }
  :deep(.van-search__content){
    background-color: #DBE4F6;
  }
  :deep(.van-search__content input){
    &::placeholder{
      color: #999999;
    }
  }
  .search-input {
    flex: 1;
    margin-left: 12px;
    height: 32px;
   /* background-color: #DBE4F6;
    border: 1px solid rgba(55,109,247,0.3);
    border-radius: 100px 100px 100px 100px;*/
  }
  .popup-header {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #eee;
  }
  .popup-title {
    font-size: 16px;
    font-weight: bold;
  }
  .popup-content {
    padding: 12px;
  }
  .popup-footer {
    padding: 12px;
    border-top: 1px solid #eee;
  }
}
</style>
