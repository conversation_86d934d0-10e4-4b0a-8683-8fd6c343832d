import request from '@/utils/request'

// 查询大型机械检查列表
export function listData(query) {
  return request({
    url: '/project/gcLargeMachineryCheckData/list',
    method: 'get',
    params: query
  })
}

// 查询大型机械检查详细
export function getData(id) {
  return request({
    url: '/project/gcLargeMachineryCheckData/' + id,
    method: 'get'
  })
}

// 新增大型机械检查
export function addData(data) {
  return request({
    url: '/project/gcLargeMachineryCheckData',
    method: 'post',
    data: data
  })
}

// 修改大型机械检查
export function updateData(data) {
  return request({
    url: '/project/gcLargeMachineryCheckData',
    method: 'put',
    data: data
  })
}

// 删除大型机械检查
export function delData(id) {
  return request({
    url: '/project/gcLargeMachineryCheckData/' + id,
    method: 'delete'
  })
}

// 文件导入
export function importFile(data) {
  return request({
    url: '/project/gcLargeMachineryCheckData/import',
    method: 'post',
    data: data
  })
}

// 大型机械检查导入数据提交
export function importDataSubmit(data) {
  return request({
    url: '/project/gcLargeMachineryCheckData/importDataSubmit',
    method: 'post',
    data: data
  })
}
