<template>
  <project-info-form
    mode="add"
    @submit="handleSubmit"
  />
</template>

<script>
import ProjectInfoForm from './ProjectInfoForm'
import { addInfo } from "@/api/projectOperate/projectInfo"

export default {
  name: "AddProjectInfo",
  components: {
    ProjectInfoForm
  },
  methods: {
    handleSubmit(formData) {
      addInfo(formData).then(response => {
        this.$modal.msgSuccess("新增成功")
        this.$bus.$emit('refreshProjectInfoList')
        const obj = {
          path: `/project/dashboard/project`,
        }
        this.$tab.closeOpenPage(obj)
      })
    }
  }
}
</script>
