{"remainingRequest": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue?vue&type=template&id=724887a0&scoped=true", "dependencies": [{"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue", "mtime": 1754312350764}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\babel.config.js", "mtime": 1743918438036}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743920556726}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1743920558214}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743920555710}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "exports", "_vm", "_c", "_self", "staticClass", "<PERSON><PERSON><PERSON><PERSON>", "attrs", "src", "require", "alt", "_v", "hasData", "directives", "name", "rawName", "value", "loadingSales", "expression", "_s", "allQyData", "zzUnitPrice", "_e", "option", "qyProgressOption", "style", "color", "title", "textStyle", "text", "subtext", "$toFixed2", "dynamicAmount", "qyAmount", "zzDynamicAmount", "noZzDynamicAmount", "zzQyAmount", "noZzDyAmount", "zzRate", "noZzRate", "hkProgressOption", "allHkData", "hkAmount", "qyRate", "zzHkAmount", "noZzHkAmount", "loadingAllTargetQYProgress", "signDataByTime", "openDate", "concat", "liquidateDate", "left", "filterRatioMax", "allSdRate", "getTooltipStyle", "deviationRate", "getArrowStyle", "allSdTargetRate", "allSdTargetAmount", "targetAmount", "zzSdRate", "zzSdTargetAmount", "zzSdTargetRate", "zzTargetAmount", "$formatNull", "zzLiquidateDate", "noZzQyAmount", "noZzSdRate", "noZzSdTargetAmount", "noZzSdTargetRate", "noZzTargetAmount", "noZzLiquidateDate", "loadingAllTargetHKProgress", "hkDataByTime", "loadingYearProgress", "yearQyData", "ratio", "totalAmount", "dayRatio", "month", "zzTotalAmount", "zzRatio", "noZzTotalAmount", "noZzRatio", "yearHkData", "loadingPayments", "kqProgressOption", "_l", "cardData", "item", "backgroundColor", "boxShadow", "label", "loadingTables", "tabs", "queryTypeSignList", "on", "click", "handleTabSign", "model", "queryTypeSign", "callback", "$$v", "staticStyle", "width", "data", "qyData", "border", "background", "fontWeight", "padding", "height", "fontSize", "lineHeight", "borderColor", "align", "prop", "scopedSlots", "_u", "key", "fn", "scope", "row", "toFixed", "qyArea", "rgData", "colspan", "rgAmount", "noQyTotalAmount", "qyYTOption", "analysisData", "handleSpanMethod", "loadingTrends", "queryTypeTrendList", "handleTrendTab", "queryTypeTrend", "dfOptionName", "dfOption", "rgOptionName", "tab", "index", "class", "active", "activeTabRGTrend", "$event", "selectTabRGTrend", "rgOption", "projectTypeList", "handleRgTab", "rgProjectType", "qyOptionName", "activeTabQYTrend", "selectTabQYTrend", "qyOption", "handleQyTab", "qyProjectType", "hkOptionName", "hkOption", "handleHKTab", "hkProjectType", "staticRenderFns", "_withStripped"], "sources": ["E:/KZDProjects/<PERSON><PERSON><PERSON>@zhidi@planSystem/ruoyi-cloud-zjzd/ruoyi-ui/src/views/projects/views/sales.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"section\", { staticClass: \"projects-content-container w-100\" }, [\n    !_vm.hasPermi([\"project:sales:manage\"])\n      ? _c(\"div\", { staticClass: \"empty-status\" }, [\n          _c(\"img\", {\n            attrs: {\n              src: require(\"@/views/projects/assets/images/no-auth.png\"),\n              alt: \"无权限\",\n            },\n          }),\n          _c(\"div\", { staticClass: \"desc\" }, [_vm._v(\"暂无权限\")]),\n          _c(\"div\", { staticClass: \"desc\" }, [_vm._v(\"请联系管理员\")]),\n        ])\n      : !_vm.hasData\n      ? _c(\"div\", { staticClass: \"empty-status\" }, [\n          _c(\"img\", {\n            attrs: {\n              src: require(\"@/views/projects/assets/images/no-data.png\"),\n              alt: \"无数据\",\n            },\n          }),\n          _c(\"div\", { staticClass: \"desc\" }, [_vm._v(\"暂无数据\")]),\n        ])\n      : _c(\"section\", { staticClass: \"w-100\" }, [\n          _c(\"div\", { staticClass: \"sales-content\" }, [\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loadingSales,\n                    expression: \"loadingSales\",\n                  },\n                ],\n                staticClass: \"sales card mb-16 mt-16\",\n              },\n              [\n                _c(\"div\", { staticClass: \"card-title\" }, [\n                  _vm._v(\" 全周期销售进度 \"),\n                  _vm.hasPermi([\"sales:all:price\"])\n                    ? _c(\"span\", { staticClass: \"total-target\" }, [\n                        _vm._v(\n                          \"全周期住宅均价 ¥ \" +\n                            _vm._s(_vm.allQyData.zzUnitPrice || \"--\") +\n                            \"元\"\n                        ),\n                      ])\n                    : _vm._e(),\n                ]),\n                _c(\"div\", { staticClass: \"flex-container\" }, [\n                  _c(\"div\", { staticClass: \"flex-item\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"progress-item\" },\n                      [\n                        _c(\"div\", { staticClass: \"progress-item-title\" }, [\n                          _vm._v(\"签约\"),\n                        ]),\n                        _c(\n                          \"Empty\",\n                          {\n                            attrs: {\n                              \"no-authority\": _vm.hasPermi([\"sales:all:qy\"]),\n                            },\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"progress-item-content\" },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"progress-item-content-item\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"progress-item-content-chart\",\n                                      },\n                                      [\n                                        _c(\n                                          \"section\",\n                                          { staticClass: \"card-with-title\" },\n                                          [\n                                            _c(\"Chart\", {\n                                              staticClass: \"chart\",\n                                              attrs: {\n                                                option: _vm.qyProgressOption,\n                                              },\n                                            }),\n                                            _c(\n                                              \"section\",\n                                              {\n                                                staticClass:\n                                                  \"chart-title-block\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass: \"title-1\",\n                                                    style: {\n                                                      color:\n                                                        _vm.qyProgressOption\n                                                          .title.textStyle\n                                                          .color,\n                                                    },\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.qyProgressOption\n                                                            .title.text\n                                                        ) +\n                                                        \" \"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  { staticClass: \"title-2\" },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        _vm.qyProgressOption\n                                                          .title.subtext\n                                                      )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"progress-item-content-detail\",\n                                          },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"progress-item-content-detail-item\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-title\",\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \"¥ \" +\n                                                        _vm._s(\n                                                          _vm.$toFixed2(\n                                                            _vm.allQyData\n                                                              .dynamicAmount\n                                                          )\n                                                        ) +\n                                                        \" 亿\"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-value\",\n                                                  },\n                                                  [_vm._v(\"动态货值\")]\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"progress-item-content-detail-item\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-title\",\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \"¥ \" +\n                                                        _vm._s(\n                                                          _vm.$toFixed2(\n                                                            _vm.allQyData\n                                                              .qyAmount\n                                                          )\n                                                        ) +\n                                                        \" 亿\"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-value\",\n                                                  },\n                                                  [_vm._v(\"累计签约\")]\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"progress-item-content-data\",\n                                      },\n                                      [\n                                        _c(\"table\", [\n                                          _c(\"tr\", [\n                                            _c(\"th\"),\n                                            _c(\n                                              \"th\",\n                                              { staticClass: \"progress-label\" },\n                                              [_vm._v(\"住宅\")]\n                                            ),\n                                            _c(\n                                              \"th\",\n                                              { staticClass: \"progress-label\" },\n                                              [_vm._v(\"非住\")]\n                                            ),\n                                          ]),\n                                          _c(\"tbody\", [\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"动态货值\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData\n                                                          .zzDynamicAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData\n                                                          .noZzDynamicAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"累计签约\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData.zzQyAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData\n                                                          .noZzDyAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"完成比例\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData.zzRate\n                                                      )\n                                                    ) + \"%\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allQyData.noZzRate\n                                                      )\n                                                    ) + \"%\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                          ]),\n                                        ]),\n                                      ]\n                                    ),\n                                  ]\n                                ),\n                              ]\n                            ),\n                          ]\n                        ),\n                      ],\n                      1\n                    ),\n                  ]),\n                  _c(\"div\", { staticClass: \"flex-item hk-item\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"progress-item\" },\n                      [\n                        _c(\"div\", { staticClass: \"progress-item-title\" }, [\n                          _vm._v(\"回款\"),\n                        ]),\n                        _c(\n                          \"Empty\",\n                          {\n                            attrs: {\n                              \"no-authority\": _vm.hasPermi([\"sales:all:hk\"]),\n                            },\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"progress-item-content\" },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"progress-item-content-item\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"progress-item-content-chart\",\n                                      },\n                                      [\n                                        _c(\n                                          \"section\",\n                                          { staticClass: \"card-with-title\" },\n                                          [\n                                            _c(\"Chart\", {\n                                              staticClass: \"chart\",\n                                              attrs: {\n                                                option: _vm.hkProgressOption,\n                                              },\n                                            }),\n                                            _c(\n                                              \"section\",\n                                              {\n                                                staticClass:\n                                                  \"chart-title-block\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass: \"title-1\",\n                                                    style: {\n                                                      color:\n                                                        _vm.hkProgressOption\n                                                          .title.textStyle\n                                                          .color,\n                                                    },\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.hkProgressOption\n                                                            .title.text\n                                                        ) +\n                                                        \" \"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  { staticClass: \"title-2\" },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        _vm.hkProgressOption\n                                                          .title.subtext\n                                                      )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"progress-item-content-detail\",\n                                          },\n                                          [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"progress-item-content-detail-item\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-title\",\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \"¥ \" +\n                                                        _vm._s(\n                                                          _vm.$toFixed2(\n                                                            _vm.allHkData\n                                                              .dynamicAmount\n                                                          )\n                                                        ) +\n                                                        \" 亿\"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-value\",\n                                                  },\n                                                  [_vm._v(\"动态货值\")]\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"progress-item-content-detail-item\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-title\",\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      \"¥ \" +\n                                                        _vm._s(\n                                                          _vm.$toFixed2(\n                                                            _vm.allHkData\n                                                              .hkAmount\n                                                          )\n                                                        ) +\n                                                        \" 亿\"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-value\",\n                                                  },\n                                                  [_vm._v(\"累计回款\")]\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass:\n                                                  \"progress-item-content-detail-item\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-title\",\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        _vm.allHkData.qyRate\n                                                      ) + \"%\"\n                                                    ),\n                                                  ]\n                                                ),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"progress-item-content-detail-item-value\",\n                                                  },\n                                                  [_vm._v(\"权益比例\")]\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"progress-item-content-data\",\n                                      },\n                                      [\n                                        _c(\"table\", [\n                                          _c(\"tr\", [\n                                            _c(\"th\"),\n                                            _c(\n                                              \"th\",\n                                              { staticClass: \"progress-label\" },\n                                              [_vm._v(\"住宅\")]\n                                            ),\n                                            _c(\n                                              \"th\",\n                                              { staticClass: \"progress-label\" },\n                                              [_vm._v(\"非住\")]\n                                            ),\n                                          ]),\n                                          _c(\"tbody\", [\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"动态货值\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData\n                                                          .zzDynamicAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData\n                                                          .noZzDynamicAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"累计回款\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData.zzHkAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData\n                                                          .noZzHkAmount\n                                                      )\n                                                    ) + \" 亿\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                            _c(\"tr\", [\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-label\",\n                                                },\n                                                [_vm._v(\"完成比例\")]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData.zzRate\n                                                      )\n                                                    ) + \"%\"\n                                                  ),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"td\",\n                                                {\n                                                  staticClass: \"progress-text\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      _vm.$toFixed2(\n                                                        _vm.allHkData.noZzRate\n                                                      )\n                                                    ) + \"%\"\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                          ]),\n                                        ]),\n                                      ]\n                                    ),\n                                  ]\n                                ),\n                              ]\n                            ),\n                          ]\n                        ),\n                      ],\n                      1\n                    ),\n                  ]),\n                ]),\n              ]\n            ),\n            _c(\"div\", { staticClass: \"time-progress card mb-16 mt-16\" }, [\n              _c(\"div\", { staticClass: \"card-title\" }, [\n                _vm._v(\"全周期进度偏差\"),\n              ]),\n              _c(\"div\", { staticClass: \"flex-container\" }, [\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"loading\",\n                        rawName: \"v-loading\",\n                        value: _vm.loadingAllTargetQYProgress,\n                        expression: \"loadingAllTargetQYProgress\",\n                      },\n                    ],\n                    staticClass: \"flex-item\",\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"target-title mb-4rem\" }, [\n                      _vm._v(\"签约\"),\n                    ]),\n                    _c(\n                      \"Empty\",\n                      {\n                        attrs: {\n                          \"no-authority\": _vm.hasPermi([\n                            \"sales:all:qyDeviation\",\n                          ]),\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"progress-timeline\" }, [\n                          _c(\"div\", { staticClass: \"time-line-wrapper\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"全盘\"),\n                            ]),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"timeline number-de\",\n                                style: {\n                                  \"--timeline-before-content\": _vm\n                                    .signDataByTime.openDate\n                                    ? `'${_vm.signDataByTime.openDate}'`\n                                    : `'--'`,\n                                  \"--timeline-after-content\": _vm.signDataByTime\n                                    .liquidateDate\n                                    ? `'${_vm.signDataByTime.liquidateDate}'`\n                                    : `'--'`,\n                                },\n                              },\n                              [\n                                _c(\"div\", {\n                                  staticClass: \"marker blue\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.signDataByTime.allSdRate\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress blue sign-tooltip-1\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.signDataByTime.allSdRate\n                                      ),\n                                      \"sign-tooltip-1\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"时点完成\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.qyAmount\n                                            )\n                                          ) +\n                                          \"亿（\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.allSdRate\n                                            )\n                                          ) +\n                                          \"%）\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"全盘动态货值\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.dynamicAmount\n                                            )\n                                          ) +\n                                          \"亿\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"偏差率\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.deviationRate\n                                            )\n                                          ) +\n                                          \"%\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"down\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(\n                                      _vm.signDataByTime.allSdRate\n                                    )\n                                  ),\n                                }),\n                                _c(\"div\", {\n                                  staticClass: \"marker yellow\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.signDataByTime.allSdTargetRate\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress yellow sign-tooltip-2\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.signDataByTime.allSdTargetRate\n                                      ),\n                                      \"sign-tooltip-2\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"时点目标\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime\n                                                .allSdTargetAmount\n                                            )\n                                          ) +\n                                          \"亿（\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.allSdTargetRate\n                                            )\n                                          ) +\n                                          \"%）\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"全盘目标货值\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.signDataByTime.targetAmount\n                                            )\n                                          ) +\n                                          \"亿\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"up\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(\n                                      _vm.signDataByTime.allSdTargetRate\n                                    )\n                                  ),\n                                }),\n                              ]\n                            ),\n                          ]),\n                          _c(\"div\", { staticClass: \"details\" }, [\n                            _c(\"table\", [\n                              _c(\"tr\", [\n                                _c(\"td\", { staticClass: \"progress-label\" }, [\n                                  _vm._v(\n                                    \"住宅: 时点完成\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzQyAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzSdRate\n                                        )\n                                      ) +\n                                      \"%)\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 动态货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzDynamicAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 时点目标\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzSdTargetAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzSdTargetRate\n                                        )\n                                      ) +\n                                      \"%）\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 目标货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.zzTargetAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\n                                  \"td\",\n                                  { staticClass: \"progress-text pl-date\" },\n                                  [\n                                    _vm._v(\n                                      \" 清盘日期 \" +\n                                        _vm._s(\n                                          _vm.$formatNull(\n                                            _vm.signDataByTime.zzLiquidateDate\n                                          )\n                                        )\n                                    ),\n                                  ]\n                                ),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\"td\", { staticClass: \"progress-label\" }, [\n                                  _vm._v(\n                                    \"非住: 时点完成\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzQyAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzSdRate\n                                        )\n                                      ) +\n                                      \"%)\"\n                                  ),\n                                ]),\n                                _c(\"td\", [\n                                  _vm._v(\n                                    \"| 动态货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzDynamicAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                                _c(\"td\", [\n                                  _vm._v(\n                                    \"| 时点目标\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzSdTargetAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzSdTargetRate\n                                        )\n                                      ) +\n                                      \"%)\"\n                                  ),\n                                ]),\n                                _c(\"td\", [\n                                  _vm._v(\n                                    \"| 目标货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.signDataByTime.noZzTargetAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\"td\", { staticClass: \"pl-date\" }, [\n                                  _vm._v(\n                                    \"清盘日期 \" +\n                                      _vm._s(\n                                        _vm.$formatNull(\n                                          _vm.signDataByTime.noZzLiquidateDate\n                                        )\n                                      )\n                                  ),\n                                ]),\n                              ]),\n                            ]),\n                          ]),\n                        ]),\n                      ]\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"loading\",\n                        rawName: \"v-loading\",\n                        value: _vm.loadingAllTargetHKProgress,\n                        expression: \"loadingAllTargetHKProgress\",\n                      },\n                    ],\n                    staticClass: \"flex-item\",\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"target-title mb-4rem\" }, [\n                      _vm._v(\"回款\"),\n                    ]),\n                    _c(\n                      \"Empty\",\n                      {\n                        attrs: {\n                          \"no-authority\": _vm.hasPermi([\n                            \"sales:all:hkDeviation\",\n                          ]),\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"progress-timeline\" }, [\n                          _c(\"div\", { staticClass: \"time-line-wrapper\" }, [\n                            _c(\"span\", { staticClass: \"label\" }, [\n                              _vm._v(\"全盘\"),\n                            ]),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"timeline number-de\",\n                                style: {\n                                  \"--timeline-before-content\": _vm.hkDataByTime\n                                    .openDate\n                                    ? `'${_vm.hkDataByTime.openDate}'`\n                                    : `'--'`,\n                                  \"--timeline-after-content\": _vm.hkDataByTime\n                                    .liquidateDate\n                                    ? `'${_vm.hkDataByTime.liquidateDate}'`\n                                    : `'--'`,\n                                },\n                              },\n                              [\n                                _c(\"div\", {\n                                  staticClass: \"marker blue\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.hkDataByTime.allSdRate\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress blue hk-tooltip-1\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.hkDataByTime.allSdRate\n                                      ),\n                                      \"hk-tooltip-1\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"时点完成\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.hkAmount\n                                            )\n                                          ) +\n                                          \"亿（\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.allSdRate\n                                            )\n                                          ) +\n                                          \"%）\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"全盘动态货值\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.dynamicAmount\n                                            )\n                                          ) +\n                                          \"亿\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"偏差率\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.deviationRate\n                                            )\n                                          ) +\n                                          \"%\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"down\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(\n                                      _vm.hkDataByTime.allSdRate\n                                    )\n                                  ),\n                                }),\n                                _c(\"div\", {\n                                  staticClass: \"marker yellow\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.hkDataByTime.allSdTargetRate\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress yellow hk-tooltip-2\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.hkDataByTime.allSdTargetRate\n                                      ),\n                                      \"hk-tooltip-2\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"时点目标\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.allSdTargetAmount\n                                            )\n                                          ) +\n                                          \"亿（\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.allSdTargetRate\n                                            )\n                                          ) +\n                                          \"%）\"\n                                      ),\n                                    ]),\n                                    _c(\"div\", [\n                                      _vm._v(\n                                        \"全盘目标货值\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.hkDataByTime.targetAmount\n                                            )\n                                          ) +\n                                          \"亿\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"up\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(\n                                      _vm.hkDataByTime.allSdTargetRate\n                                    )\n                                  ),\n                                }),\n                              ]\n                            ),\n                          ]),\n                          _c(\"div\", { staticClass: \"details\" }, [\n                            _c(\"table\", [\n                              _c(\"tr\", [\n                                _c(\"td\", { staticClass: \"progress-label\" }, [\n                                  _vm._v(\n                                    \"住宅: 时点完成\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.zzHkAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(_vm.hkDataByTime.zzSdRate)\n                                      ) +\n                                      \"%）\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 动态货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.zzDynamicAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 时点目标\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.zzSdTargetAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.zzSdTargetRate\n                                        )\n                                      ) +\n                                      \"%）\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 目标货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.zzTargetAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\n                                  \"td\",\n                                  { staticClass: \"progress-text pl-date\" },\n                                  [\n                                    _vm._v(\n                                      \"清盘日期 \" +\n                                        _vm._s(\n                                          _vm.$formatNull(\n                                            _vm.hkDataByTime.zzLiquidateDate\n                                          )\n                                        )\n                                    ),\n                                  ]\n                                ),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\"td\", { staticClass: \"progress-label\" }, [\n                                  _vm._v(\n                                    \"非住: 时点完成\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzHkAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzSdRate\n                                        )\n                                      ) +\n                                      \"%）\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 动态货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzDynamicAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 时点目标\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzSdTargetAmount\n                                        )\n                                      ) +\n                                      \"亿（\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzSdTargetRate\n                                        )\n                                      ) +\n                                      \"%）\"\n                                  ),\n                                ]),\n                                _c(\"td\", { staticClass: \"progress-text\" }, [\n                                  _vm._v(\n                                    \"| 目标货值\" +\n                                      _vm._s(\n                                        _vm.$toFixed2(\n                                          _vm.hkDataByTime.noZzTargetAmount\n                                        )\n                                      ) +\n                                      \"亿\"\n                                  ),\n                                ]),\n                              ]),\n                              _c(\"tr\", [\n                                _c(\n                                  \"td\",\n                                  { staticClass: \"progress-text pl-date\" },\n                                  [\n                                    _vm._v(\n                                      \" 清盘日期 \" +\n                                        _vm._s(\n                                          _vm.$formatNull(\n                                            _vm.hkDataByTime.noZzLiquidateDate\n                                          )\n                                        )\n                                    ),\n                                  ]\n                                ),\n                              ]),\n                            ]),\n                          ]),\n                        ]),\n                      ]\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n            ]),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loadingYearProgress,\n                    expression: \"loadingYearProgress\",\n                  },\n                ],\n                staticClass: \"year-progress card mb-16 mt-16\",\n              },\n              [\n                _c(\"div\", { staticClass: \"card-title\" }, [\n                  _vm._v(\" 年度销售进度 \"),\n                  _vm.hasPermi([\"sales:year:price\"])\n                    ? _c(\"span\", { staticClass: \"total-target\" }, [\n                        _vm._v(\n                          \"年度住宅均价 ¥ \" +\n                            _vm._s(_vm.$toFixed2(_vm.yearQyData.zzUnitPrice)) +\n                            \"元\"\n                        ),\n                      ])\n                    : _vm._e(),\n                ]),\n                _c(\"div\", { staticClass: \"flex-container\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"target-title\" }, [\n                        _vm._v(\n                          \"年度签约目标 ¥ \" +\n                            _vm._s(_vm.$toFixed2(_vm.yearQyData.targetAmount)) +\n                            \" 亿\"\n                        ),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:year:qy\"]),\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"progress-timeline\" }, [\n                            _c(\"div\", { staticClass: \"time-line-wrapper\" }, [\n                              _c(\"span\", { staticClass: \"label\" }, [\n                                _vm._v(\"全盘\"),\n                              ]),\n                              _c(\"div\", { staticClass: \"timeline number\" }, [\n                                _c(\"div\", {\n                                  staticClass: \"marker blue\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.yearQyData.ratio\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress blue year-tooltip-1\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(_vm.yearQyData.ratio),\n                                      \"year-tooltip-1\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        \"销售进度：\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.yearQyData.totalAmount\n                                            )\n                                          ) +\n                                          \" 亿 (\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(_vm.yearQyData.ratio)\n                                          ) +\n                                          \"%)\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"down\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(_vm.yearQyData.ratio)\n                                  ),\n                                }),\n                                _c(\"div\", {\n                                  staticClass: \"marker yellow\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.yearQyData.dayRatio\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress yellow year-tooltip-2\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.yearQyData.dayRatio\n                                      ),\n                                      \"year-tooltip-2\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        \"时间进度：\" +\n                                          _vm._s(_vm.yearQyData.month) +\n                                          \"月 (\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.yearQyData.dayRatio\n                                            )\n                                          ) +\n                                          \"%)\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"up\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(_vm.yearQyData.dayRatio)\n                                  ),\n                                }),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"details\" }, [\n                              _c(\"table\", [\n                                _c(\"tr\", [\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \"住宅: 年度签约目标\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearQyData.zzTargetAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-text\" }, [\n                                    _vm._v(\n                                      \" | 累计签约金额\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearQyData.zzTotalAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-text\" }, [\n                                    _vm._v(\n                                      \" | \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(_vm.yearQyData.zzRatio)\n                                        ) +\n                                        \"%\"\n                                    ),\n                                  ]),\n                                ]),\n                                _c(\"tr\", [\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \"非住: 年度签约目标\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearQyData.noZzTargetAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \" | 累计签约金额\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearQyData.noZzTotalAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \" | \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearQyData.noZzRatio\n                                          )\n                                        ) +\n                                        \"%\"\n                                    ),\n                                  ]),\n                                ]),\n                              ]),\n                            ]),\n                          ]),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"target-title\" }, [\n                        _vm._v(\n                          \"年度回款目标 ¥ \" +\n                            _vm._s(\n                              _vm.$toFixed2(_vm.yearHkData.targetAmount) || \"--\"\n                            ) +\n                            \" 亿\"\n                        ),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:year:hk\"]),\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"progress-timeline\" }, [\n                            _c(\"div\", { staticClass: \"time-line-wrapper\" }, [\n                              _c(\"span\", { staticClass: \"label\" }, [\n                                _vm._v(\"全盘\"),\n                              ]),\n                              _c(\"div\", { staticClass: \"timeline number\" }, [\n                                _c(\"div\", {\n                                  staticClass: \"marker blue\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.yearHkData.ratio\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress blue year-hk-tooltip-1\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(_vm.yearHkData.ratio),\n                                      \"year-hk-tooltip-1\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        \"销售进度：\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.yearHkData.totalAmount\n                                            )\n                                          ) +\n                                          \" 亿 (\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(_vm.yearHkData.ratio)\n                                          ) +\n                                          \"%)\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"down\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(_vm.yearHkData.ratio)\n                                  ),\n                                }),\n                                _c(\"div\", {\n                                  staticClass: \"marker yellow\",\n                                  style: {\n                                    left: `${_vm.filterRatioMax(\n                                      _vm.yearHkData.dayRatio\n                                    )}%`,\n                                  },\n                                }),\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass:\n                                      \"tooltip-progress yellow year-hk-tooltip-2\",\n                                    style: _vm.getTooltipStyle(\n                                      _vm.filterRatioMax(\n                                        _vm.yearHkData.dayRatio\n                                      ),\n                                      \"year-hk-tooltip-2\"\n                                    ),\n                                  },\n                                  [\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        \"时间进度：\" +\n                                          _vm._s(_vm.yearHkData.month) +\n                                          \"月 (\" +\n                                          _vm._s(\n                                            _vm.$toFixed2(\n                                              _vm.yearHkData.dayRatio\n                                            )\n                                          ) +\n                                          \"%)\"\n                                      ),\n                                    ]),\n                                  ]\n                                ),\n                                _c(\"div\", {\n                                  staticClass: \"up\",\n                                  style: _vm.getArrowStyle(\n                                    _vm.filterRatioMax(_vm.yearHkData.dayRatio)\n                                  ),\n                                }),\n                              ]),\n                            ]),\n                            _c(\"div\", { staticClass: \"details\" }, [\n                              _c(\"table\", [\n                                _c(\"tr\", [\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \"住宅: 年度回款目标\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearHkData.zzTargetAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-text\" }, [\n                                    _vm._v(\n                                      \" | 累计回款金额\" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearHkData.zzTotalAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-text\" }, [\n                                    _vm._v(\n                                      \" | \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(_vm.yearHkData.zzRatio)\n                                        ) +\n                                        \"%\"\n                                    ),\n                                  ]),\n                                ]),\n                                _c(\"tr\", [\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \"非住: 年度回款目标 \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearHkData.noZzTargetAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \" | 累计回款金额 \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearHkData.noZzTotalAmount\n                                          )\n                                        ) +\n                                        \" 亿\"\n                                    ),\n                                  ]),\n                                  _c(\"td\", { staticClass: \"progress-label\" }, [\n                                    _vm._v(\n                                      \" | \" +\n                                        _vm._s(\n                                          _vm.$toFixed2(\n                                            _vm.yearHkData.noZzRatio\n                                          )\n                                        ) +\n                                        \"%\"\n                                    ),\n                                  ]),\n                                ]),\n                              ]),\n                            ]),\n                          ]),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ]),\n              ]\n            ),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loadingPayments,\n                    expression: \"loadingPayments\",\n                  },\n                ],\n                staticClass: \"receiving-payments mb-16\",\n              },\n              [\n                _c(\n                  \"div\",\n                  { staticClass: \"card content-block\" },\n                  [\n                    _c(\"div\", { staticClass: \"card-title-2\" }, [\n                      _vm._v(\"款齐\"),\n                    ]),\n                    _c(\n                      \"Empty\",\n                      { attrs: { \"no-authority\": _vm.hasPermi([\"sales:kq\"]) } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"basic-info-item flex-container\" },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"circle-chart chart-size\" },\n                              [\n                                _c(\n                                  \"section\",\n                                  { staticClass: \"card-with-title\" },\n                                  [\n                                    _c(\"Chart\", {\n                                      staticClass: \"chart-size\",\n                                      attrs: { option: _vm.kqProgressOption },\n                                    }),\n                                    _c(\n                                      \"section\",\n                                      { staticClass: \"chart-title-block\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-1\",\n                                            style: {\n                                              color:\n                                                _vm.kqProgressOption.title\n                                                  .textStyle.color,\n                                            },\n                                          },\n                                          [\n                                            _vm._v(\n                                              \" \" +\n                                                _vm._s(\n                                                  _vm.kqProgressOption.title\n                                                    .text\n                                                ) +\n                                                \" \"\n                                            ),\n                                          ]\n                                        ),\n                                        _c(\"div\", { staticClass: \"title-2\" }, [\n                                          _vm._v(\n                                            _vm._s(\n                                              _vm.kqProgressOption.title.subtext\n                                            )\n                                          ),\n                                        ]),\n                                      ]\n                                    ),\n                                  ],\n                                  1\n                                ),\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              { staticClass: \"card-wrapper\" },\n                              _vm._l(_vm.cardData, function (item) {\n                                return _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"data-card\",\n                                    style: {\n                                      backgroundColor: item.color,\n                                      boxShadow: `0px 8px 20px 0px ${item.color}`,\n                                    },\n                                  },\n                                  [\n                                    _c(\"div\", { staticClass: \"card-label\" }, [\n                                      _vm._v(_vm._s(item.label)),\n                                    ]),\n                                    _c(\"br\"),\n                                    _c(\"div\", { staticClass: \"card-value\" }, [\n                                      _c(\"span\", { staticClass: \"currency\" }, [\n                                        _vm._v(\"¥ \"),\n                                      ]),\n                                      _c(\"span\", [\n                                        _vm._v(\n                                          _vm._s(_vm.$toFixed2(item.value)) +\n                                            \" 亿\"\n                                        ),\n                                      ]),\n                                    ]),\n                                  ]\n                                )\n                              }),\n                              0\n                            ),\n                          ]\n                        ),\n                      ]\n                    ),\n                  ],\n                  1\n                ),\n              ]\n            ),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loadingTables,\n                    expression: \"loadingTables\",\n                  },\n                ],\n                staticClass: \"table-container mb-16\",\n              },\n              [\n                _c(\"div\", { staticClass: \"card-title mb-16\" }, [\n                  _vm._v(\"销售分析\"),\n                ]),\n                _c(\n                  \"Empty\",\n                  { attrs: { \"no-authority\": _vm.hasPermi([\"sales:data\"]) } },\n                  [\n                    _c(\"CardTabSales\", {\n                      staticClass: \"mb-14\",\n                      attrs: { tabs: _vm.queryTypeSignList },\n                      on: { click: _vm.handleTabSign },\n                      model: {\n                        value: _vm.queryTypeSign,\n                        callback: function ($$v) {\n                          _vm.queryTypeSign = $$v\n                        },\n                        expression: \"queryTypeSign\",\n                      },\n                    }),\n                    _c(\n                      \"el-table\",\n                      {\n                        staticClass: \"project-table mb-16\",\n                        staticStyle: { width: \"100%\" },\n                        attrs: {\n                          data: _vm.qyData,\n                          border: true,\n                          \"header-cell-style\": {\n                            background: \"#E1EFFD\",\n                            color: \"#666666\",\n                            fontWeight: \"400\",\n                            padding: \"0.5rem 0\",\n                            height: \"2rem\",\n                            fontSize: \"0.875rem\",\n                            lineHeight: \"1.5rem\",\n                          },\n                          \"cell-style\": {\n                            borderColor: \"rgba(0,106,255,0.2);\",\n                            fontWeight: \"400\",\n                            padding: \"0.5rem 0\",\n                            height: \"2rem\",\n                            fontSize: \"0.875rem\",\n                            lineHeight: \"1.5rem\",\n                          },\n                        },\n                      },\n                      [\n                        _c(\n                          \"el-table-column\",\n                          { attrs: { align: \"center\", label: \"累计签约\" } },\n                          [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"金额\",\n                                prop: \"qyAmount\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                              scopedSlots: _vm._u([\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" +\n                                          _vm._s(\n                                            _vm.queryTypeSign === 1 ||\n                                              _vm.queryTypeSign === 2\n                                              ? (\n                                                  scope.row.qyAmount / 10000\n                                                ).toFixed(2) + \"万\"\n                                              : (\n                                                  scope.row.qyAmount / 100000000\n                                                ).toFixed(2) + \"亿\"\n                                          ) +\n                                          \" \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ]),\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"面积\",\n                                prop: \"qyArea\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                              scopedSlots: _vm._u([\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _c(\"span\", [\n                                        _vm._v(\n                                          _vm._s(\n                                            _vm.$toFixed2(scope.row.qyArea)\n                                          )\n                                        ),\n                                      ]),\n                                    ]\n                                  },\n                                },\n                              ]),\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"套数\",\n                                prop: \"qyNum\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中住宅套数\",\n                                prop: \"zzNum\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中退房套数\",\n                                prop: \"backNum\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中换房套数\",\n                                prop: \"changeNum\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                        _c(\n                          \"el-table-column\",\n                          {\n                            attrs: { align: \"center\", label: \"累计到访转化率\" },\n                          },\n                          [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"到访人数\",\n                                prop: \"dfNum\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"转化率\",\n                                prop: \"ratio\",\n                                align: \"center\",\n                                \"min-width\": \"12.5%\",\n                              },\n                              scopedSlots: _vm._u([\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" +\n                                          _vm._s(\n                                            _vm.$toFixed2(scope.row.ratio)\n                                          ) +\n                                          \"% \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ]),\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"el-table\",\n                      {\n                        staticClass: \"mb-16 project-table\",\n                        staticStyle: { width: \"100%\" },\n                        attrs: {\n                          data: _vm.rgData,\n                          \"header-cell-style\": {\n                            background: \"#E1EFFD\",\n                            color: \"#666666\",\n                            fontWeight: \"400\",\n                            padding: \"0.5rem 0\",\n                            height: \"2rem\",\n                            fontSize: \"0.875rem\",\n                            lineHeight: \"1.2rem\",\n                          },\n                          \"cell-style\": {\n                            borderColor: \"rgba(0,106,255,0.2);\",\n                            fontWeight: \"400\",\n                            padding: \"0.5rem 0\",\n                            height: \"2rem\",\n                            fontSize: \"0.875rem\",\n                            lineHeight: \"1.2rem\",\n                          },\n                        },\n                      },\n                      [\n                        _c(\n                          \"el-table-column\",\n                          {\n                            attrs: {\n                              align: \"center\",\n                              label: \"累计认购\",\n                              colspan: 6,\n                            },\n                          },\n                          [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"金额\",\n                                prop: \"rgAmount\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                              scopedSlots: _vm._u([\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" +\n                                          _vm._s(\n                                            _vm.queryTypeSign === 1 ||\n                                              _vm.queryTypeSign === 2\n                                              ? (\n                                                  scope.row.rgAmount / 10000\n                                                ).toFixed(2) + \"万\"\n                                              : (\n                                                  scope.row.rgAmount / 100000000\n                                                ).toFixed(2) + \"亿\"\n                                          ) +\n                                          \" \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ]),\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"面积\",\n                                prop: \"rgArea\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"套数\",\n                                prop: \"tsNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中住宅套数\",\n                                prop: \"zzNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中退房套数\",\n                                prop: \"backNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中换房套数\",\n                                prop: \"changeNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                        _c(\n                          \"el-table-column\",\n                          {\n                            attrs: {\n                              align: \"center\",\n                              label: \"截至当前认未签\",\n                              colspan: 5,\n                            },\n                          },\n                          [\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"金额\",\n                                prop: \"noQyTotalAmount\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                              scopedSlots: _vm._u([\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" +\n                                          _vm._s(\n                                            (\n                                              scope.row.noQyTotalAmount /\n                                              100000000\n                                            ).toFixed(2) + \"亿\"\n                                          ) +\n                                          \" \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ]),\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"面积\",\n                                prop: \"noQyTotalArea\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"套数\",\n                                prop: \"noQyTsNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                            _c(\"el-table-column\", {\n                              attrs: {\n                                label: \"其中住宅套数\",\n                                prop: \"noQyZzNum\",\n                                align: \"center\",\n                                \"min-width\": \"10\",\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"cost flex-container\",\n                    staticStyle: { \"min-height\": \"26.5rem\" },\n                  },\n                  [\n                    _c(\n                      \"div\",\n                      { staticClass: \"flex-item chart\" },\n                      [\n                        _c(\"div\", { staticClass: \"card-title mb-10\" }, [\n                          _vm._v(\"签约业态分布\"),\n                        ]),\n                        _c(\n                          \"Empty\",\n                          {\n                            attrs: {\n                              \"no-authority\": _vm.hasPermi([\"sales:bussDist\"]),\n                            },\n                          },\n                          [_c(\"Chart\", { attrs: { option: _vm.qyYTOption } })],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"flex-item\",\n                        staticStyle: { \"min-width\": \"0\" },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"card-title mb-10\" }, [\n                          _vm._v(\"货值分布表\"),\n                        ]),\n                        _c(\n                          \"Empty\",\n                          {\n                            attrs: {\n                              \"no-authority\": _vm.hasPermi([\n                                \"sales:goodsValue\",\n                              ]),\n                            },\n                          },\n                          [\n                            _c(\n                              \"el-table\",\n                              {\n                                staticClass: \"project-table\",\n                                staticStyle: {\n                                  width: \"100%\",\n                                  \"max-height\": \"30rem\",\n                                },\n                                attrs: {\n                                  data: _vm.analysisData,\n                                  border: true,\n                                  \"span-method\": _vm.handleSpanMethod,\n                                  height: \"'30rem'\",\n                                  \"header-cell-style\": {\n                                    background: \"#E1EFFD\",\n                                    color: \"#666666\",\n                                    fontWeight: \"400\",\n                                    padding: \"0.2rem 0\",\n                                    fontSize: \"0.875rem\",\n                                    height: \"2rem\",\n                                  },\n                                  \"cell-style\": {\n                                    borderColor: \"rgba(0,106,255,0.2);\",\n                                    fontWeight: \"400\",\n                                    padding: \"0.2rem 0\",\n                                    height: \"2rem\",\n                                    fontSize: \"0.875rem\",\n                                  },\n                                },\n                              },\n                              [\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    label: \"业态\",\n                                    prop: \"projectType\",\n                                    align: \"center\",\n                                    \"min-width\": \"8\",\n                                  },\n                                }),\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    label: \"户型\",\n                                    prop: \"hxName\",\n                                    align: \"center\",\n                                    \"min-width\": \"12\",\n                                  },\n                                }),\n                                _c(\n                                  \"el-table-column\",\n                                  {\n                                    attrs: { label: \"总套数\", align: \"center\" },\n                                  },\n                                  [\n                                    _c(\"el-table-column\", {\n                                      attrs: {\n                                        label: \"合计\",\n                                        prop: \"totalNum\",\n                                        align: \"center\",\n                                        \"min-width\": \"8\",\n                                      },\n                                    }),\n                                    _c(\n                                      \"el-table-column\",\n                                      {\n                                        attrs: {\n                                          label: \"其中\",\n                                          align: \"center\",\n                                        },\n                                      },\n                                      [\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"顶层\",\n                                            prop: \"allTopNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"底层\",\n                                            prop: \"allBottomNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    label: \"总货值(万元)\",\n                                    prop: \"allHzAmount\",\n                                    \"min-width\": \"12\",\n                                    align: \"center\",\n                                  },\n                                }),\n                                _c(\n                                  \"el-table-column\",\n                                  {\n                                    attrs: {\n                                      label: \"已售套数\",\n                                      align: \"center\",\n                                    },\n                                  },\n                                  [\n                                    _c(\"el-table-column\", {\n                                      attrs: {\n                                        label: \"合计\",\n                                        prop: \"ysNum\",\n                                        align: \"center\",\n                                        \"min-width\": \"8\",\n                                      },\n                                    }),\n                                    _c(\n                                      \"el-table-column\",\n                                      {\n                                        attrs: {\n                                          label: \"其中\",\n                                          align: \"center\",\n                                        },\n                                      },\n                                      [\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"顶层\",\n                                            prop: \"ysTopNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"底层\",\n                                            prop: \"ysBottomNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    label: \"已售货值(万元)\",\n                                    prop: \"ysHzAmount\",\n                                    \"min-width\": \"12\",\n                                    align: \"center\",\n                                  },\n                                }),\n                                _c(\n                                  \"el-table-column\",\n                                  {\n                                    attrs: {\n                                      label: \"未售套数\",\n                                      align: \"center\",\n                                    },\n                                  },\n                                  [\n                                    _c(\"el-table-column\", {\n                                      attrs: {\n                                        label: \"合计\",\n                                        prop: \"wsNum\",\n                                        align: \"center\",\n                                        \"min-width\": \"8\",\n                                      },\n                                    }),\n                                    _c(\n                                      \"el-table-column\",\n                                      {\n                                        attrs: {\n                                          label: \"其中\",\n                                          align: \"center\",\n                                        },\n                                      },\n                                      [\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"已供未售\",\n                                            prop: \"ygwsNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"未供\",\n                                            prop: \"wgNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"顶层\",\n                                            prop: \"wsTopNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            label: \"底层\",\n                                            prop: \"wsBottomNum\",\n                                            align: \"center\",\n                                            \"min-width\": \"8\",\n                                          },\n                                        }),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _c(\"el-table-column\", {\n                                  attrs: {\n                                    label: \"未售货值(万元)\",\n                                    prop: \"wsHzAmount\",\n                                    \"min-width\": \"12\",\n                                    align: \"center\",\n                                  },\n                                }),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ]\n                ),\n              ],\n              1\n            ),\n            _c(\n              \"div\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.loadingTrends,\n                    expression: \"loadingTrends\",\n                  },\n                ],\n                staticClass: \"trend mb-16\",\n              },\n              [\n                _c(\"div\", { staticClass: \"card-title mb-16\" }, [\n                  _vm._v(\"趋势分析\"),\n                ]),\n                _c(\"CardTabSales\", {\n                  staticClass: \"mb-14\",\n                  attrs: { tabs: _vm.queryTypeTrendList },\n                  on: { click: _vm.handleTrendTab },\n                  model: {\n                    value: _vm.queryTypeTrend,\n                    callback: function ($$v) {\n                      _vm.queryTypeTrend = $$v\n                    },\n                    expression: \"queryTypeTrend\",\n                  },\n                }),\n                _c(\"div\", { staticClass: \"flex-container\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"chart-title mb-25\" }, [\n                        _vm._v(\"到访趋势\"),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:dfTrend\"]),\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"trend-chart-title\" }, [\n                            _vm._v(_vm._s(_vm.dfOptionName)),\n                          ]),\n                          _c(\"Chart\", {\n                            staticClass: \"trend-chart\",\n                            attrs: { option: _vm.dfOption },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"chart-title mb-25\" }, [\n                        _vm._v(\"认购趋势\"),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:rgTrend\"]),\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"trend-chart-title\" }, [\n                            _vm._v(_vm._s(_vm.rgOptionName)),\n                          ]),\n                          _c(\n                            \"section\",\n                            { staticClass: \"flex-row-container\" },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"vertical-tabs\" },\n                                _vm._l(_vm.tabs, function (tab, index) {\n                                  return _c(\n                                    \"div\",\n                                    {\n                                      key: index,\n                                      class: [\n                                        \"tab\",\n                                        {\n                                          active:\n                                            _vm.activeTabRGTrend === index,\n                                        },\n                                      ],\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.selectTabRGTrend(index)\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"div\", { staticClass: \"tab-text\" }, [\n                                        _vm._v(_vm._s(tab)),\n                                      ]),\n                                    ]\n                                  )\n                                }),\n                                0\n                              ),\n                              _c(\"Chart\", {\n                                staticClass: \"trend-chart\",\n                                attrs: { option: _vm.rgOption },\n                              }),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"section\",\n                            { staticClass: \"flex-row-container\" },\n                            [\n                              _c(\"CardTabSales\", {\n                                staticClass: \"mb-14\",\n                                attrs: { tabs: _vm.projectTypeList },\n                                on: { click: _vm.handleRgTab },\n                                model: {\n                                  value: _vm.rgProjectType,\n                                  callback: function ($$v) {\n                                    _vm.rgProjectType = $$v\n                                  },\n                                  expression: \"rgProjectType\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"chart-title mb-25\" }, [\n                        _vm._v(\"签约趋势\"),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:qyTrend\"]),\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"trend-chart-title\" }, [\n                            _vm._v(_vm._s(_vm.qyOptionName)),\n                          ]),\n                          _c(\n                            \"section\",\n                            { staticClass: \"flex-row-container\" },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"vertical-tabs\" },\n                                _vm._l(_vm.tabs, function (tab, index) {\n                                  return _c(\n                                    \"div\",\n                                    {\n                                      key: index,\n                                      class: [\n                                        \"tab\",\n                                        {\n                                          active:\n                                            _vm.activeTabQYTrend === index,\n                                        },\n                                      ],\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.selectTabQYTrend(index)\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"div\", { staticClass: \"tab-text\" }, [\n                                        _vm._v(_vm._s(tab)),\n                                      ]),\n                                    ]\n                                  )\n                                }),\n                                0\n                              ),\n                              _c(\"Chart\", {\n                                staticClass: \"trend-chart\",\n                                attrs: { option: _vm.qyOption },\n                              }),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"section\",\n                            { staticClass: \"flex-row-container\" },\n                            [\n                              _c(\"CardTabSales\", {\n                                staticClass: \"mb-14\",\n                                attrs: { tabs: _vm.projectTypeList },\n                                on: { click: _vm.handleQyTab },\n                                model: {\n                                  value: _vm.qyProjectType,\n                                  callback: function ($$v) {\n                                    _vm.qyProjectType = $$v\n                                  },\n                                  expression: \"qyProjectType\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"flex-item\" },\n                    [\n                      _c(\"div\", { staticClass: \"chart-title mb-25\" }, [\n                        _vm._v(\"回款趋势\"),\n                      ]),\n                      _c(\n                        \"Empty\",\n                        {\n                          attrs: {\n                            \"no-authority\": _vm.hasPermi([\"sales:hkTrend\"]),\n                          },\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"trend-chart-container\" },\n                            [\n                              _c(\"div\", { staticClass: \"trend-chart-title\" }, [\n                                _vm._v(_vm._s(_vm.hkOptionName)),\n                              ]),\n                              _c(\"Chart\", {\n                                staticClass: \"trend-chart\",\n                                attrs: { option: _vm.hkOption },\n                              }),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"section\",\n                            { staticClass: \"flex-row-container\" },\n                            [\n                              _c(\"CardTabSales\", {\n                                staticClass: \"mb-14\",\n                                attrs: { tabs: _vm.projectTypeList },\n                                on: { click: _vm.handleHKTab },\n                                model: {\n                                  value: _vm.hkProjectType,\n                                  callback: function ($$v) {\n                                    _vm.hkProjectType = $$v\n                                  },\n                                  expression: \"hkProjectType\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ]),\n              ],\n              1\n            ),\n          ]),\n        ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;;AAAA,IAAIA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIE,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAmC,CAAC,EAAE,CACxE,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,GACnCH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRI,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,4CAA4C,CAAC;MAC1DC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACpDR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CACvD,CAAC,GACF,CAACT,GAAG,CAACU,OAAO,GACZT,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRI,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,4CAA4C,CAAC;MAC1DC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACrD,CAAC,GACFR,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAACe,YAAY;MACvBC,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACS,EAAE,CAAC,WAAW,CAAC,EACnBT,GAAG,CAACI,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,GAC7BH,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,YAAY,GACVT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACkB,SAAS,CAACC,WAAW,IAAI,IAAI,CAAC,GACzC,GACJ,CAAC,CACF,CAAC,GACFnB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,cAAc,CAAC;IAC/C;EACF,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAC7C,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MACLgB,MAAM,EAAErB,GAAG,CAACsB;IACd;EACF,CAAC,CAAC,EACFrB,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,SAAS;IACtBoB,KAAK,EAAE;MACLC,KAAK,EACHxB,GAAG,CAACsB,gBAAgB,CACjBG,KAAK,CAACC,SAAS,CACfF;IACP;EACF,CAAC,EACD,CACExB,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACsB,gBAAgB,CACjBG,KAAK,CAACE,IACX,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,EACD1B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACsB,gBAAgB,CACjBG,KAAK,CAACG,OACX,CACF,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACD3B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJ,IAAI,GACFT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CACVY,aACL,CACF,CAAC,GACD,IACJ,CAAC,CAEL,CAAC,EACD7B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJ,IAAI,GACFT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CACVa,QACL,CACF,CAAC,GACD,IACJ,CAAC,CAEL,CAAC,EACD9B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CAACH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CAACH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,CAAC,EACFR,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CACVc,eACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,EACD/B,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CACVe,iBACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,CACF,CAAC,EACFhC,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CAACgB,UAChB,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,EACDjC,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CACViB,YACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,CACF,CAAC,EACFlC,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CAACkB,MAChB,CACF,CAAC,GAAG,GACN,CAAC,CAEL,CAAC,EACDnC,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkB,SAAS,CAACmB,QAChB,CACF,CAAC,GAAG,GACN,CAAC,CAEL,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFpC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,cAAc,CAAC;IAC/C;EACF,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAC7C,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MACLgB,MAAM,EAAErB,GAAG,CAACsC;IACd;EACF,CAAC,CAAC,EACFrC,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,SAAS;IACtBoB,KAAK,EAAE;MACLC,KAAK,EACHxB,GAAG,CAACsC,gBAAgB,CACjBb,KAAK,CAACC,SAAS,CACfF;IACP;EACF,CAAC,EACD,CACExB,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACsC,gBAAgB,CACjBb,KAAK,CAACE,IACX,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,EACD1B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACsC,gBAAgB,CACjBb,KAAK,CAACG,OACX,CACF,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACD3B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJ,IAAI,GACFT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CACVT,aACL,CACF,CAAC,GACD,IACJ,CAAC,CAEL,CAAC,EACD7B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJ,IAAI,GACFT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CACVC,QACL,CACF,CAAC,GACD,IACJ,CAAC,CAEL,CAAC,EACDvC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACuC,SAAS,CAACE,MAChB,CAAC,GAAG,GACN,CAAC,CAEL,CAAC,EACDxC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC,EACDR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT;EACJ,CAAC,EACD,CACEF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CAACH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CAACH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,CAAC,EACFR,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CACVP,eACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,EACD/B,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CACVN,iBACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,CACF,CAAC,EACFhC,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CAACG,UAChB,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,EACDzC,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CACVI,YACL,CACF,CAAC,GAAG,IACN,CAAC,CAEL,CAAC,CACF,CAAC,EACF1C,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDR,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CAACH,MAChB,CACF,CAAC,GAAG,GACN,CAAC,CAEL,CAAC,EACDnC,EAAE,CACA,IAAI,EACJ;IACEE,WAAW,EAAE;EACf,CAAC,EACD,CACEH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuC,SAAS,CAACF,QAChB,CACF,CAAC,GAAG,GACN,CAAC,CAEL,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CAEN,CAAC,EACDpC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACS,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAAC4C,0BAA0B;MACrC5B,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAC3B,uBAAuB,CACxB;IACH;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,oBAAoB;IACjCoB,KAAK,EAAE;MACL,2BAA2B,EAAEvB,GAAG,CAC7B6C,cAAc,CAACC,QAAQ,OAAAC,MAAA,CAClB/C,GAAG,CAAC6C,cAAc,CAACC,QAAQ,eACzB;MACV,0BAA0B,EAAE9C,GAAG,CAAC6C,cAAc,CAC3CG,aAAa,OAAAD,MAAA,CACR/C,GAAG,CAAC6C,cAAc,CAACG,aAAa;IAE1C;EACF,CAAC,EACD,CACE/C,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAAC6C,cAAc,CAACM,SACrB,CAAC;IACH;EACF,CAAC,CAAC,EACFlD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,sCAAsC;IACxCoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAAC6C,cAAc,CAACM,SACrB,CAAC,EACD,gBACF;EACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,MAAM,GACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACd,QACrB,CACF,CAAC,GACD,IAAI,GACJ/B,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACM,SACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFlD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACf,aACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACQ,aACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDpD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,MAAM;IACnBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAAC6C,cAAc,CAACM,SACrB,CACF;EACF,CAAC,CAAC,EACFlD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,eAAe;IAC5BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAAC6C,cAAc,CAACU,eACrB,CAAC;IACH;EACF,CAAC,CAAC,EACFtD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,wCAAwC;IAC1CoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAAC6C,cAAc,CAACU,eACrB,CAAC,EACD,gBACF;EACF,CAAC,EACD,CACEtD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,MAAM,GACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CACfW,iBACL,CACF,CAAC,GACD,IAAI,GACJxD,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACU,eACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACY,YACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDxD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,IAAI;IACjBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAAC6C,cAAc,CAACU,eACrB,CACF;EACF,CAAC,CAAC,CAEN,CAAC,CACF,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,UAAU,GACRT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACX,UACrB,CACF,CAAC,GACD,IAAI,GACJlC,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACa,QACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFzD,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACb,eACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACF/B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACc,gBACrB,CACF,CAAC,GACD,IAAI,GACJ3D,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACe,cACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF3D,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACgB,cACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACF5D,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC8D,WAAW,CACb9D,GAAG,CAAC6C,cAAc,CAACkB,eACrB,CACF,CACJ,CAAC,CAEL,CAAC,CACF,CAAC,EACF9D,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,UAAU,GACRT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACmB,YACrB,CACF,CAAC,GACD,IAAI,GACJhE,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACoB,UACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFhE,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACZ,iBACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACFhC,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACqB,kBACrB,CACF,CAAC,GACD,IAAI,GACJlE,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACsB,gBACrB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFlE,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAAC6C,cAAc,CAACuB,gBACrB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFnE,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACnCH,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC8D,WAAW,CACb9D,GAAG,CAAC6C,cAAc,CAACwB,iBACrB,CACF,CACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,EACDpE,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAACsE,0BAA0B;MACrCtD,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAC3B,uBAAuB,CACxB;IACH;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,oBAAoB;IACjCoB,KAAK,EAAE;MACL,2BAA2B,EAAEvB,GAAG,CAACuE,YAAY,CAC1CzB,QAAQ,OAAAC,MAAA,CACH/C,GAAG,CAACuE,YAAY,CAACzB,QAAQ,eACvB;MACV,0BAA0B,EAAE9C,GAAG,CAACuE,YAAY,CACzCvB,aAAa,OAAAD,MAAA,CACR/C,GAAG,CAACuE,YAAY,CAACvB,aAAa;IAExC;EACF,CAAC,EACD,CACE/C,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACuE,YAAY,CAACpB,SACnB,CAAC;IACH;EACF,CAAC,CAAC,EACFlD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,oCAAoC;IACtCoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACuE,YAAY,CAACpB,SACnB,CAAC,EACD,cACF;EACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,MAAM,GACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAAC/B,QACnB,CACF,CAAC,GACD,IAAI,GACJxC,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACpB,SACnB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFlD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACzC,aACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAAClB,aACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDpD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,MAAM;IACnBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACuE,YAAY,CAACpB,SACnB,CACF;EACF,CAAC,CAAC,EACFlD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,eAAe;IAC5BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACuE,YAAY,CAAChB,eACnB,CAAC;IACH;EACF,CAAC,CAAC,EACFtD,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,sCAAsC;IACxCoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACuE,YAAY,CAAChB,eACnB,CAAC,EACD,cACF;EACF,CAAC,EACD,CACEtD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,MAAM,GACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACf,iBACnB,CACF,CAAC,GACD,IAAI,GACJxD,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAAChB,eACnB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE,CACRD,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACd,YACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDxD,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,IAAI;IACjBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACuE,YAAY,CAAChB,eACnB,CACF;EACF,CAAC,CAAC,CAEN,CAAC,CACF,CAAC,EACFtD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,UAAU,GACRT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAAC7B,UACnB,CACF,CAAC,GACD,IAAI,GACJ1C,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACuE,YAAY,CAACb,QAAQ,CACzC,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFzD,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACvC,eACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACF/B,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACZ,gBACnB,CACF,CAAC,GACD,IAAI,GACJ3D,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACX,cACnB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF3D,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACV,cACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACF5D,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEH,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC8D,WAAW,CACb9D,GAAG,CAACuE,YAAY,CAACR,eACnB,CACF,CACJ,CAAC,CAEL,CAAC,CACF,CAAC,EACF9D,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,UAAU,GACRT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAAC5B,YACnB,CACF,CAAC,GACD,IAAI,GACJ3C,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACN,UACnB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFhE,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACtC,iBACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,EACFhC,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACL,kBACnB,CACF,CAAC,GACD,IAAI,GACJlE,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACJ,gBACnB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFlE,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACuE,YAAY,CAACH,gBACnB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACFnE,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEH,GAAG,CAACS,EAAE,CACJ,QAAQ,GACNT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC8D,WAAW,CACb9D,GAAG,CAACuE,YAAY,CAACF,iBACnB,CACF,CACJ,CAAC,CAEL,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,EACFpE,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAACwE,mBAAmB;MAC9BxD,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACS,EAAE,CAAC,UAAU,CAAC,EAClBT,GAAG,CAACI,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAC9BH,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACyE,UAAU,CAACtD,WAAW,CAAC,CAAC,GACjD,GACJ,CAAC,CACF,CAAC,GACFnB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACyE,UAAU,CAAChB,YAAY,CAAC,CAAC,GAClD,IACJ,CAAC,CACF,CAAC,EACFxD,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACyE,UAAU,CAACC,KACjB,CAAC;IACH;EACF,CAAC,CAAC,EACFzE,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,sCAAsC;IACxCoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACyE,UAAU,CAACC,KAAK,CAAC,EACxC,gBACF;EACF,CAAC,EACD,CACEzE,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACE,WACjB,CACF,CAAC,GACD,MAAM,GACN3E,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACyE,UAAU,CAACC,KAAK,CACpC,CAAC,GACD,IACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDzE,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,MAAM;IACnBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACyE,UAAU,CAACC,KAAK,CACzC;EACF,CAAC,CAAC,EACFzE,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,eAAe;IAC5BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACyE,UAAU,CAACG,QACjB,CAAC;IACH;EACF,CAAC,CAAC,EACF3E,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,wCAAwC;IAC1CoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACyE,UAAU,CAACG,QACjB,CAAC,EACD,gBACF;EACF,CAAC,EACD,CACE3E,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACyE,UAAU,CAACI,KAAK,CAAC,GAC5B,KAAK,GACL7E,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACG,QACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACD3E,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,IAAI;IACjBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACyE,UAAU,CAACG,QAAQ,CAC5C;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACF3E,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,YAAY,GACVT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACZ,cACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF5D,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACK,aACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF7E,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACyE,UAAU,CAACM,OAAO,CACtC,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACF9E,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,YAAY,GACVT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACL,gBACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFnE,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACO,eACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF/E,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACyE,UAAU,CAACQ,SACjB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,EACDhF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACkF,UAAU,CAACzB,YAAY,CAAC,IAAI,IAChD,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFxD,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,aAAa;IAC1BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACkF,UAAU,CAACR,KACjB,CAAC;IACH;EACF,CAAC,CAAC,EACFzE,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,yCAAyC;IAC3CoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACkF,UAAU,CAACR,KAAK,CAAC,EACxC,mBACF;EACF,CAAC,EACD,CACEzE,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACP,WACjB,CACF,CAAC,GACD,MAAM,GACN3E,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACkF,UAAU,CAACR,KAAK,CACpC,CAAC,GACD,IACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACDzE,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,MAAM;IACnBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACkF,UAAU,CAACR,KAAK,CACzC;EACF,CAAC,CAAC,EACFzE,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,eAAe;IAC5BoB,KAAK,EAAE;MACL0B,IAAI,KAAAF,MAAA,CAAK/C,GAAG,CAACkD,cAAc,CACzBlD,GAAG,CAACkF,UAAU,CAACN,QACjB,CAAC;IACH;EACF,CAAC,CAAC,EACF3E,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EACT,2CAA2C;IAC7CoB,KAAK,EAAEvB,GAAG,CAACoD,eAAe,CACxBpD,GAAG,CAACkD,cAAc,CAChBlD,GAAG,CAACkF,UAAU,CAACN,QACjB,CAAC,EACD,mBACF;EACF,CAAC,EACD,CACE3E,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJ,OAAO,GACLT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACkF,UAAU,CAACL,KAAK,CAAC,GAC5B,KAAK,GACL7E,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACN,QACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,CAEN,CAAC,EACD3E,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,IAAI;IACjBoB,KAAK,EAAEvB,GAAG,CAACsD,aAAa,CACtBtD,GAAG,CAACkD,cAAc,CAAClD,GAAG,CAACkF,UAAU,CAACN,QAAQ,CAC5C;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACF3E,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,OAAO,EAAE,CACVA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,YAAY,GACVT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACrB,cACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF5D,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,WAAW,GACTT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACJ,aACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF7E,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC7B,GAAG,CAACkF,UAAU,CAACH,OAAO,CACtC,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,EACF9E,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,aAAa,GACXT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACd,gBACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACFnE,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,YAAY,GACVT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACF,eACjB,CACF,CAAC,GACD,IACJ,CAAC,CACF,CAAC,EACF/E,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC1CH,GAAG,CAACS,EAAE,CACJ,KAAK,GACHT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CACX7B,GAAG,CAACkF,UAAU,CAACD,SACjB,CACF,CAAC,GACD,GACJ,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC,EACDhF,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAACmF,eAAe;MAC1BnE,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,UAAU,CAAC;IAAE;EAAE,CAAC,EACzD,CACEH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiC,CAAC,EACjD,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAC1C,CACEF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,YAAY;IACzBE,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAACoF;IAAiB;EACxC,CAAC,CAAC,EACFnF,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAoB,CAAC,EACpC,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,SAAS;IACtBoB,KAAK,EAAE;MACLC,KAAK,EACHxB,GAAG,CAACoF,gBAAgB,CAAC3D,KAAK,CACvBC,SAAS,CAACF;IACjB;EACF,CAAC,EACD,CACExB,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACoF,gBAAgB,CAAC3D,KAAK,CACvBE,IACL,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,EACD1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACoF,gBAAgB,CAAC3D,KAAK,CAACG,OAC7B,CACF,CAAC,CACF,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACD3B,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACqF,EAAE,CAACrF,GAAG,CAACsF,QAAQ,EAAE,UAAUC,IAAI,EAAE;IACnC,OAAOtF,EAAE,CACP,KAAK,EACL;MACEE,WAAW,EAAE,WAAW;MACxBoB,KAAK,EAAE;QACLiE,eAAe,EAAED,IAAI,CAAC/D,KAAK;QAC3BiE,SAAS,sBAAA1C,MAAA,CAAsBwC,IAAI,CAAC/D,KAAK;MAC3C;IACF,CAAC,EACD,CACEvB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACsE,IAAI,CAACG,KAAK,CAAC,CAAC,CAC3B,CAAC,EACFzF,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACtCH,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFR,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAAC6B,SAAS,CAAC0D,IAAI,CAACzE,KAAK,CAAC,CAAC,GAC/B,IACJ,CAAC,CACF,CAAC,CACH,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACDb,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAAC2F,aAAa;MACxB3E,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,YAAY,CAAC;IAAE;EAAE,CAAC,EAC3D,CACEH,EAAE,CAAC,cAAc,EAAE;IACjBE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MAAEuF,IAAI,EAAE5F,GAAG,CAAC6F;IAAkB,CAAC;IACtCC,EAAE,EAAE;MAAEC,KAAK,EAAE/F,GAAG,CAACgG;IAAc,CAAC;IAChCC,KAAK,EAAE;MACLnF,KAAK,EAAEd,GAAG,CAACkG,aAAa;MACxBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBpG,GAAG,CAACkG,aAAa,GAAGE,GAAG;MACzB,CAAC;MACDpF,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFf,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,qBAAqB;IAClCkG,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BjG,KAAK,EAAE;MACLkG,IAAI,EAAEvG,GAAG,CAACwG,MAAM;MAChBC,MAAM,EAAE,IAAI;MACZ,mBAAmB,EAAE;QACnBC,UAAU,EAAE,SAAS;QACrBlF,KAAK,EAAE,SAAS;QAChBmF,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE;MACd,CAAC;MACD,YAAY,EAAE;QACZC,WAAW,EAAE,sBAAsB;QACnCL,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE;MACd;IACF;EACF,CAAC,EACD,CACE9G,EAAE,CACA,iBAAiB,EACjB;IAAEI,KAAK,EAAE;MAAE4G,KAAK,EAAE,QAAQ;MAAEvB,KAAK,EAAE;IAAO;EAAE,CAAC,EAC7C,CACEzF,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAEnH,GAAG,CAACoH,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLvH,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACkG,aAAa,KAAK,CAAC,IACrBlG,GAAG,CAACkG,aAAa,KAAK,CAAC,GACrB,CACEqB,KAAK,CAACC,GAAG,CAACzF,QAAQ,GAAG,KAAK,EAC1B0F,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAClB,CACEF,KAAK,CAACC,GAAG,CAACzF,QAAQ,GAAG,SAAS,EAC9B0F,OAAO,CAAC,CAAC,CAAC,GAAG,GACrB,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAEnH,GAAG,CAACoH,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLtH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACS,EAAE,CACJT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC0F,KAAK,CAACC,GAAG,CAACE,MAAM,CAChC,CACF,CAAC,CACF,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFzH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MAAE4G,KAAK,EAAE,QAAQ;MAAEvB,KAAK,EAAE;IAAU;EAC7C,CAAC,EACD,CACEzF,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,MAAM;MACbwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,KAAK;MACZwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAEnH,GAAG,CAACoH,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLvH,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAAC6B,SAAS,CAAC0F,KAAK,CAACC,GAAG,CAAC9C,KAAK,CAC/B,CAAC,GACD,IACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDzE,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,qBAAqB;IAClCkG,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BjG,KAAK,EAAE;MACLkG,IAAI,EAAEvG,GAAG,CAAC2H,MAAM;MAChB,mBAAmB,EAAE;QACnBjB,UAAU,EAAE,SAAS;QACrBlF,KAAK,EAAE,SAAS;QAChBmF,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE;MACd,CAAC;MACD,YAAY,EAAE;QACZC,WAAW,EAAE,sBAAsB;QACnCL,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE;MACd;IACF;EACF,CAAC,EACD,CACE9G,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACL4G,KAAK,EAAE,QAAQ;MACfvB,KAAK,EAAE,MAAM;MACbkC,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACE3H,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAEnH,GAAG,CAACoH,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLvH,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJjB,GAAG,CAACkG,aAAa,KAAK,CAAC,IACrBlG,GAAG,CAACkG,aAAa,KAAK,CAAC,GACrB,CACEqB,KAAK,CAACC,GAAG,CAACK,QAAQ,GAAG,KAAK,EAC1BJ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAClB,CACEF,KAAK,CAACC,GAAG,CAACK,QAAQ,GAAG,SAAS,EAC9BJ,OAAO,CAAC,CAAC,CAAC,GAAG,GACrB,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACL4G,KAAK,EAAE,QAAQ;MACfvB,KAAK,EAAE,SAAS;MAChBkC,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACE3H,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,iBAAiB;MACvBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf,CAAC;IACDE,WAAW,EAAEnH,GAAG,CAACoH,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAYC,KAAK,EAAE;QACnB,OAAO,CACLvH,GAAG,CAACS,EAAE,CACJ,GAAG,GACDT,GAAG,CAACiB,EAAE,CACJ,CACEsG,KAAK,CAACC,GAAG,CAACM,eAAe,GACzB,SAAS,EACTL,OAAO,CAAC,CAAC,CAAC,GAAG,GACjB,CAAC,GACD,GACJ,CAAC,CACF;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC,EACFxH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,QAAQ;MACfwB,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhH,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCkG,WAAW,EAAE;MAAE,YAAY,EAAE;IAAU;EACzC,CAAC,EACD,CACEpG,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CH,GAAG,CAACS,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,gBAAgB,CAAC;IACjD;EACF,CAAC,EACD,CAACH,EAAE,CAAC,OAAO,EAAE;IAAEI,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAAC+H;IAAW;EAAE,CAAC,CAAC,CAAC,EACpD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD9H,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBkG,WAAW,EAAE;MAAE,WAAW,EAAE;IAAI;EAClC,CAAC,EACD,CACEpG,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CH,GAAG,CAACS,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAC3B,kBAAkB,CACnB;IACH;EACF,CAAC,EACD,CACEH,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,eAAe;IAC5BkG,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACb,YAAY,EAAE;IAChB,CAAC;IACDjG,KAAK,EAAE;MACLkG,IAAI,EAAEvG,GAAG,CAACgI,YAAY;MACtBvB,MAAM,EAAE,IAAI;MACZ,aAAa,EAAEzG,GAAG,CAACiI,gBAAgB;MACnCpB,MAAM,EAAE,SAAS;MACjB,mBAAmB,EAAE;QACnBH,UAAU,EAAE,SAAS;QACrBlF,KAAK,EAAE,SAAS;QAChBmF,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBE,QAAQ,EAAE,UAAU;QACpBD,MAAM,EAAE;MACV,CAAC;MACD,YAAY,EAAE;QACZG,WAAW,EAAE,sBAAsB;QACnCL,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,EACD,CACE7G,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MAAEqF,KAAK,EAAE,KAAK;MAAEuB,KAAK,EAAE;IAAS;EACzC,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXuB,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,SAAS;MAChBwB,IAAI,EAAE,aAAa;MACnB,WAAW,EAAE,IAAI;MACjBD,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACLqF,KAAK,EAAE,MAAM;MACbuB,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXuB,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,UAAU;MACjBwB,IAAI,EAAE,YAAY;MAClB,WAAW,EAAE,IAAI;MACjBD,KAAK,EAAE;IACT;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACLqF,KAAK,EAAE,MAAM;MACbuB,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CACA,iBAAiB,EACjB;IACEI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXuB,KAAK,EAAE;IACT;EACF,CAAC,EACD,CACEhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,MAAM;MACbwB,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,IAAI;MACXwB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,QAAQ;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhH,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqF,KAAK,EAAE,UAAU;MACjBwB,IAAI,EAAE,YAAY;MAClB,WAAW,EAAE,IAAI;MACjBD,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDhH,EAAE,CACA,KAAK,EACL;IACEU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAEd,GAAG,CAACkI,aAAa;MACxBlH,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CAAC,cAAc,EAAE;IACjBE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MAAEuF,IAAI,EAAE5F,GAAG,CAACmI;IAAmB,CAAC;IACvCrC,EAAE,EAAE;MAAEC,KAAK,EAAE/F,GAAG,CAACoI;IAAe,CAAC;IACjCnC,KAAK,EAAE;MACLnF,KAAK,EAAEd,GAAG,CAACqI,cAAc;MACzBlC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBpG,GAAG,CAACqI,cAAc,GAAGjC,GAAG;MAC1B,CAAC;MACDpF,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACsI,YAAY,CAAC,CAAC,CACjC,CAAC,EACFrI,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAACuI;IAAS;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDtI,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACwI,YAAY,CAAC,CAAC,CACjC,CAAC,EACFvI,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChCH,GAAG,CAACqF,EAAE,CAACrF,GAAG,CAAC4F,IAAI,EAAE,UAAU6C,GAAG,EAAEC,KAAK,EAAE;IACrC,OAAOzI,EAAE,CACP,KAAK,EACL;MACEoH,GAAG,EAAEqB,KAAK;MACVC,KAAK,EAAE,CACL,KAAK,EACL;QACEC,MAAM,EACJ5I,GAAG,CAAC6I,gBAAgB,KAAKH;MAC7B,CAAC,CACF;MACD5C,EAAE,EAAE;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAY+C,MAAM,EAAE;UACvB,OAAO9I,GAAG,CAAC+I,gBAAgB,CAACL,KAAK,CAAC;QACpC;MACF;IACF,CAAC,EACD,CACEzI,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACwH,GAAG,CAAC,CAAC,CACpB,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDxI,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAACgJ;IAAS;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACD/I,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,cAAc,EAAE;IACjBE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MAAEuF,IAAI,EAAE5F,GAAG,CAACiJ;IAAgB,CAAC;IACpCnD,EAAE,EAAE;MAAEC,KAAK,EAAE/F,GAAG,CAACkJ;IAAY,CAAC;IAC9BjD,KAAK,EAAE;MACLnF,KAAK,EAAEd,GAAG,CAACmJ,aAAa;MACxBhD,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBpG,GAAG,CAACmJ,aAAa,GAAG/C,GAAG;MACzB,CAAC;MACDpF,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDf,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACoJ,YAAY,CAAC,CAAC,CACjC,CAAC,EACFnJ,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChCH,GAAG,CAACqF,EAAE,CAACrF,GAAG,CAAC4F,IAAI,EAAE,UAAU6C,GAAG,EAAEC,KAAK,EAAE;IACrC,OAAOzI,EAAE,CACP,KAAK,EACL;MACEoH,GAAG,EAAEqB,KAAK;MACVC,KAAK,EAAE,CACL,KAAK,EACL;QACEC,MAAM,EACJ5I,GAAG,CAACqJ,gBAAgB,KAAKX;MAC7B,CAAC,CACF;MACD5C,EAAE,EAAE;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAY+C,MAAM,EAAE;UACvB,OAAO9I,GAAG,CAACsJ,gBAAgB,CAACZ,KAAK,CAAC;QACpC;MACF;IACF,CAAC,EACD,CACEzI,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACwH,GAAG,CAAC,CAAC,CACpB,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDxI,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAACuJ;IAAS;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDtJ,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,cAAc,EAAE;IACjBE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MAAEuF,IAAI,EAAE5F,GAAG,CAACiJ;IAAgB,CAAC;IACpCnD,EAAE,EAAE;MAAEC,KAAK,EAAE/F,GAAG,CAACwJ;IAAY,CAAC;IAC9BvD,KAAK,EAAE;MACLnF,KAAK,EAAEd,GAAG,CAACyJ,aAAa;MACxBtD,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBpG,GAAG,CAACyJ,aAAa,GAAGrD,GAAG;MACzB,CAAC;MACDpF,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDf,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFR,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MACL,cAAc,EAAEL,GAAG,CAACI,QAAQ,CAAC,CAAC,eAAe,CAAC;IAChD;EACF,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAwB,CAAC,EACxC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAAC0J,YAAY,CAAC,CAAC,CACjC,CAAC,EACFzJ,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,aAAa;IAC1BE,KAAK,EAAE;MAAEgB,MAAM,EAAErB,GAAG,CAAC2J;IAAS;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACD1J,EAAE,CACA,SAAS,EACT;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,cAAc,EAAE;IACjBE,WAAW,EAAE,OAAO;IACpBE,KAAK,EAAE;MAAEuF,IAAI,EAAE5F,GAAG,CAACiJ;IAAgB,CAAC;IACpCnD,EAAE,EAAE;MAAEC,KAAK,EAAE/F,GAAG,CAAC4J;IAAY,CAAC;IAC9B3D,KAAK,EAAE;MACLnF,KAAK,EAAEd,GAAG,CAAC6J,aAAa;MACxB1D,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBpG,GAAG,CAAC6J,aAAa,GAAGzD,GAAG;MACzB,CAAC;MACDpF,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACP,CAAC;AACJ,CAAC;AACD,IAAI8I,eAAe,GAAA/J,OAAA,CAAA+J,eAAA,GAAG,EAAE;AACxBhK,MAAM,CAACiK,aAAa,GAAG,IAAI", "ignoreList": []}]}