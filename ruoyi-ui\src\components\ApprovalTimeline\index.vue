<template>
  <div class="approval-timeline">
    <div class="timeline-header">
      <img src="@/assets/rant/rant-progress.png" class="icon">
      审批流程
    </div>
    <div class="timeline-content">
      <el-timeline v-if="approvalList && approvalList.length > 0">
        <el-timeline-item v-for="(item, index) in approvalList" :key="index"
          :color="getStatusColor(item.approveStatus)" :icon="getStatusIcon(item.approveStatus)" size="large"
          placement="bottom">
          <el-card class="timeline-card">
            <section class="status-header" v-if="item.approveTitle !== '发起' && item.approveTitle !== '结束'">
              <el-tag :type="getStatusType(item.approveStatus)" size="mini" class="status-tag">
                {{ getStatusText(item.approveStatus) }}
              </el-tag>
            </section>
            <section class="flex-row">
              <section class="avatar">
                {{(item.approveNickName == null || item.approveNickName == '') ? "系统" :  item.approveNickName.slice(1, item.approveNickName.length) }}
              </section>
              <section class="content">
                <div class="card-header">
                  <div class="step-info">
                    <span class="step-title">{{ item.approveNickName }}({{ item.approveTitle || '--' }})</span>
                    <span class="value">{{ item.finishTime || '--' }}</span>
                  </div>
                </div>
                <div class="card-content" v-if="item.approveTitle !== '结束'">
                  <div class="dept-info">
                    <span class="label">部门：</span>
                    <span class="value">{{ item.approveUserDept || '--' }}</span>
                  </div>
                  <div class="opinion-info" v-if="item.approveTitle !== '发起' && item.approveTitle !== '结束'">
                    <span class="label">审批意见：</span>
                    <div class="opinion-content">{{ item.approveDesc || '--' }}</div>
                  </div>
                </div>
              </section>
            </section>

          </el-card>
        </el-timeline-item>
      </el-timeline>
      <div v-else class="no-data">
        <i class="el-icon-document"></i>
        <p>暂无审批流程数据</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ApprovalTimeline",
  props: {
    approvalList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      statusList: [1,2,3]
    }
  },
  methods: {
    // 格式化时间显示
    formatTime(time) {
      if (!time) return '';
      return this.parseTime(time, '{m}-{d} {h}:{i}');
    },

    // 格式化完整时间
    formatFullTime(time) {
      if (!time) return '--';
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        1: '#E6A23C', // 审批中 - 橙色
        2: '#67C23A', // 同意 - 绿色
        3: '#F56C6C'  // 驳回 - 红色
      };
      return colorMap[status] || '#67C23A';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        1: 'el-icon-time',   // 审批中
        2: 'el-icon-success',     // 同意
        3: 'el-icon-error'    // 驳回
      };
      return iconMap[status] || 'el-icon-success';
    },

    // 获取状态类型（用于el-tag）
    getStatusType(status) {
      // 审批状态(1-审批中 2-同意 3-驳回)")
      const typeMap = {
        1: 'warning',   // 处理中
        2: 'success',  // 已通过
        3: 'danger',   // 已驳回
      };
      return typeMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        1: '审批中',
        2: '同意',
        3: '驳回'
      };
      return textMap[status] || '--';
    }
  }
};
</script>

<style lang="scss" scoped>
.approval-timeline {
  .timeline-header {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 13px 0px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .timeline-content {
    padding: 0 16px;

    .timeline-card {
      margin-bottom: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      .card-header {
        .step-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .step-title {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
          }

          .status-tag {
            border-radius: 12px;
          }
        }
      }

      .card-content {
        font-size: 14px;

        >div {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #666666;
            // min-width: 80px;
            flex-shrink: 0;
          }

          .value {
            color: #333333;
            font-weight: 500;
          }
        }

        .opinion-info {
          flex-direction: column;
          align-items: flex-start;

          .opinion-content {
            margin-top: 4px;
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            color: #333333;
            width: 100%;
            box-sizing: border-box;
            line-height: 1.5;
          }
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 40px 0;
      color: #999999;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      p {
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #999999;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 16px;
}

.flex-row {
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1990ff;
  color: #fff;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
  font-weight: 600;
  margin-right: 30px;
}

.content {
  flex: 1;
}
</style>
