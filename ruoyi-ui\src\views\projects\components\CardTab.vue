<script>
export default {
  name: 'CardT<PERSON>',
  props: {
    tabs: {
      type: Array,
      default: null,
    },
    size: {
      type: String,
      default: null,
    },
    value: {
      type: [String, Number],
      default: null,
    }
  },
  methods: {
    handleClick(item) {
      this.$emit('input', item)
      this.$emit('click', item)
    }
  }
}
</script>

<template>
  <div class="card-tab">
    <div
      class="card-tab-item"
      v-for="item in tabs"
      :class="{ 'active': value === item }"
      @click="handleClick(item)"
    >
      {{ item }}
  </div>
  </div>
</template>


<style scoped lang="scss">
.card-tab {
  display: flex;
  align-items: center;
  border: 0.0625rem solid #CC1414;
  width: fit-content;

  .card-tab-item {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: #000000;
    line-height: 1.375rem;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 0.125rem 1rem;
    border-right: 0.0625rem solid #CC1414;
    cursor: pointer;
    &:last-child {
      border-right: none;
    }

    &.active {
      color: #FCF9F9;
      background: #CC1414;
    }
  }
}
</style>
