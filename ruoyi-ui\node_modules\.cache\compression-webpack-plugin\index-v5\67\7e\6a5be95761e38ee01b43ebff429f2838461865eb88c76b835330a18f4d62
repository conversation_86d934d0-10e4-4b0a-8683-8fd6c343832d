
ee109ef7caa2ecd1def0a5f2b796aa7cccb22a67	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.5585e5aefa97e81a8a1e.hot-update.js\",\"contentHash\":\"e4f6cd0b9d9ee7e08e67298d2f6ed7e1\"}","integrity":"sha512-/KYyiPf++fkzjqWhuozjQz/SFC13YVJ5UspAAvpnpb9D0zpj0LMvzLMRFY/657gRiIds6k/57HtpEROQvbrG9A==","time":1754312230079,"size":77866}