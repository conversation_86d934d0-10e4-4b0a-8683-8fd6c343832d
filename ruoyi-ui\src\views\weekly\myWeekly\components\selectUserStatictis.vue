<template>
  <!-- 授权用户 -->
  <el-dialog
    title="选择用户"
    :visible.sync="visible"
    width="800px"
    top="5vh"
    append-to-body
  >
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        @row-click="clickRow"
        ref="table"
        :data="userList"
        height="300px"
        stripe
        v-loading="loading"
      >
        <el-table-column width="55" label="选择">
          <template slot-scope="scope">
            <el-checkbox
              v-if="selectMultiple"
              v-model="userIds"
              :label="scope.row.userId"
              @change="selectRow(scope.row)"
            />
            <el-radio v-else :label="scope.row.userName" v-model="selectUserName">
              {{ "" }}
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column
          label="用户名称"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户昵称"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="邮箱"
          prop="email"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="手机"
          prop="phonenumber"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_normal_disable"
              :value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
// import { listUser } from "@/api/system/user"
import { listUserNew as listUser } from "@/api/weekly/reportInfo"

export default {
  dicts: ['sys_normal_disable'],
  props: {
    // 角色编号
    roleId: {
      type: [Number, String]
    },
    selectMultiple: { // 是否多选
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        userName: undefined,
        phonenumber: undefined
      },
      loading: false,
      selectUserName: '',
      selectData: null,
      selectUserArr: [], // 选中用户数组
      selectMultipleUserIds: [], // 多选用户id
    }
  },
  methods: {
    selectRow (row) {
      if (this.selectMultiple) {
        const index = this.selectMultipleUserIds.indexOf(row.userId);
        if (index > -1) {
          this.userIds.splice(index, 1);
          this.selectMultipleUserIds.splice(index, 1);
        } else {
          this.selectMultipleUserIds.push(row.userId);
          this.selectUserArr.push(row);
        }
      }
    },
    clearSelect () {
      this.selectUserName = ''
      this.selectData = null
    },
    // 显示弹框
    show () {
      this.selectUserName = this.roleId
      this.getList()
      this.visible = true
    },
    clickRow (row) {
      this.selectUserName = row.userName
      this.selectData = row
    },
    // 查询表数据
    getList () {
      this.loading = true
      listUser(this.queryParams).then(res => {
        this.loading = false
        this.userList = res.rows
        this.selectData = this.userList.filter(item => {
          return  (item.userName == this.selectUserName||item.userId==this.selectUserName)
        })[0]
        this.total = res.total
        //反赋值
        this.selectUserName=this.selectData?this.selectData.userName:''
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 选择授权用户操作 */
    handleSelectUser () {
      if(this.selectMultiple){
        this.$emit('feedbackEmit', this.selectUserArr)
      }else {
        this.$emit('feedbackEmit', this.selectData)
      }
      this.visible = false
    }
  }
};
</script>
