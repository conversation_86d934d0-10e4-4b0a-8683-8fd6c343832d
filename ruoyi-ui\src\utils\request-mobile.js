import axios from 'axios'
import { To<PERSON>, Dialog } from 'vant'
import {getMobileToken, setMobileToken, redirectToOauth2Url, getAuthType, getAuthTypeOfMobile} from '@/utils/auth'
import { wechatLogin } from '@/api/weekly/mobile-reportInfo'
import errorCode from '@/utils/errorCode'
import { blobValidate, tansParams } from "@/utils/ruoyi"
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// Create axios instance for mobile
const mobileService = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 60000 // Shorter timeout for mobile
})

// Request interceptor
mobileService.interceptors.request.use(config => {
  const isToken = (config.headers || {}).isToken === false
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false

  if (getMobileToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getMobileToken()
  }

  // Handle GET request params
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.params = {}
    config.url = url
  }

  // Handle duplicate submission for POST/PUT
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const sessionObj = cache.session.getJSON('sessionObj')

    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url
      const s_data = sessionObj.data
      const s_time = sessionObj.time
      const interval = 1000

      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = '请求处理中，请稍候...'
        console.warn(`[${s_url}]: ${message}`)
        return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  return config
}, error => {
  console.log(error)
  return Promise.reject(error)
})

// Response interceptor
mobileService.interceptors.response.use(res => {
  const code = res.data.code || 200
  const msg = errorCode[code] || res.data.msg || errorCode['default']

  // Handle binary data
  if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
    return res.data
  }

  // Handle different response codes
  if (code === 401 || code === 402) {
    let {type, code} = getAuthTypeOfMobile();

    if (code) {
      // Attempt to refresh token using WeChat login
      return wechatLogin({"code": code, "type": type}).then(loginRes => {
        if (loginRes?.token?.access_token) {
          // Update token
          setMobileToken(loginRes.token.access_token)

          // Retry original request with new token
          const config = res.config
          config.headers['Authorization'] = 'Bearer ' + loginRes?.token?.access_token
          return mobileService(config)
        }
        return Promise.reject('Token refresh failed')
      }).catch(err => {
        Toast.fail('登录已过期，请重新登录')
        return Promise.reject(err)
      })
    } else {
      redirectToOauth2Url();
      // Toast.fail('登录已过期，请重新登录')
      // return Promise.reject('No auth code available')
    }
  } else if (code === 403) {
    Dialog.alert({
      title: '系统提示',
      message: '您没有该系统权限，请联系管理员添加',
    })
  } else if (code === 500) {
    Toast.fail(msg)
    return Promise.reject(new Error(msg))
  } else if (code !== 200) {
    Toast.fail(msg)
    return Promise.reject('error')
  } else {
    return res.data
  }
}, error => {
  console.log('err' + error)
  let { message } = error

  if (message === "Network Error") {
    message = "网络连接异常"
  } else if (message.includes("timeout")) {
    message = "请求超时"
  } else if (message.includes("Request failed with status code")) {
    message = "接口" + message.substr(message.length - 3) + "异常"
  }

  Toast.fail({
    message: message,
    duration: 3000
  })
  return Promise.reject(error)
})

// Mobile download method
export function mobileDownload(url, params, filename) {
  Toast.loading({
    message: '正在下载...',
    forbidClick: true,
    duration: 0
  })

  return mobileService.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  }).then(async (data) => {
    const isLogin = await blobValidate(data)
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text()
      const rspObj = JSON.parse(resText)
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      Toast.fail(errMsg)
    }
    Toast.clear()
  }).catch((r) => {
    console.error(r)
    Toast.fail('下载失败')
  })
}

export default mobileService
