
8a6f127b806daf098e7a145af89e2e8f021b9d03	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e78e7048652a23f95f617300686da1df\"}","integrity":"sha512-cxyOXgynVuR75BPrpesQAJ/r8cLwOo3wsa2gDyriILB92M+zH37lAYhhIVT8WWk+N0R9Jl96qb6gjdgLbKzoNA==","time":1754312231234,"size":12045454}