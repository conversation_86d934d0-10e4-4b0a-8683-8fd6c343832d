<template>
  <div class="track-node-diagram" style="position: relative;">
    <canvas
      ref="canvas"
      :width="canvasWidth"
      :height="calculatedCanvasHeight"
      @mousemove="handleMouseMove"
      @mouseleave="hideTooltip"
    ></canvas>

    <!-- 使用相对定位父容器和absolute定位tooltip -->
    <div
      v-if="showTooltip"
      class="node-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-item header">
        <span class="title">{{ activeNode.trackMapName }}</span>
        <span class="status" :style="{color: statusStyle(activeNode.status)}">{{ activeNode.status || '-' }}</span>
      </div>
      <div class="tooltip-item">
        <span class="label">责任部门：</span>
        <span>{{ activeNode.deptName }}</span>
      </div>
      <div class="tooltip-item">
        <span class="label">计划完成日期：</span>
        <span>{{ activeNode.planFinishDate }}</span>
      </div>
      <div class="tooltip-item">
        <span class="label">实际完成日期：</span>
        <span>{{ activeNode.actualFinishDate }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TrackNodeDiagramCanvas',
  props: {
    canvasWidth: {
      type: Number,
      default: 1800
    },
    nodeSpacing: {
      type: Number,
      default: 158
    },
    nodeData: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      startX: 100,
      rowHeight: 180,
      edgeExtension: 50,
      showTooltip: false,
      activeNode: null,
      tooltipX: 0,
      tooltipY: 0,
      nodePositions: [],
    }
  },
  mounted() {
    this.initCanvas()
  },
  methods: {
    statusStyle(status){
      if(status === '按期完成'){
        return '#24B253'
      }
      else if(status === '延期完成'){
        return '#FF8000'
      }
      else if(status === '延期未完成'){
        return '#FF4040'
      }
      else if(status === '进行中'){
        return '#006AFF'
      }
      else{
        return '#DDDDDD'
      }
    },
    dotTypeDotColorFilter(dotType){
      // 圆点类型（1-灰点、2-绿点、3-橙点、4-红点、5-蓝圈）
      if(dotType === 1){
        return '#DDDDDD'
      }
      else if(dotType === 2){
        return '#24B253'
      }
      else if(dotType === 3){
        return '#FF8000'
      }
      else if(dotType === 4){
        return '#FF4040'
      }
      else if(dotType === 5){
        return '#006AFF'
      }
      else{
        return '#DDDDDD'
      }
    },
    dotTypetrackMapNameColorFilter(dotType){
      if(dotType === 1){
        return '#222222'
      }
      else if(dotType === 2){
        return '#24B253'
      }
      else if(dotType === 3){
        return '#FF8000'
      }
      else if(dotType === 4){
        return '#FF4040'
      }
      else if(dotType === 5){
        return '#006AFF'
      }
      else{
        return '##222222'
      }
    },
    initCanvas() {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')
      console.log('TrackNodeDiagram-----canvasWidth-----', this.canvasWidth, this.nodeSpacing)
      // 设置缩放比例以处理高清屏
      const dpr = window.devicePixelRatio || 1
      console.log('TrackNodeDiagram-----canvasWidth-----dpr', this.canvasWidth, this.nodeSpacing, dpr)
      canvas.style.width = this.canvasWidth + 'px'
      canvas.style.height = this.calculatedCanvasHeight + 'px'
      canvas.width = this.canvasWidth * dpr
      canvas.height = this.calculatedCanvasHeight * dpr
      ctx.scale(dpr, dpr)

      this.drawNodes(ctx)
    },

    drawNodes(ctx) {
      this.nodePositions = [] // 重置节点位置数组
      const scaleFactor = this.canvasWidth / 1440;
      const startY = 60

      // 1. 先计算所有节点位置
      this.nodeData.forEach((node, index) => {
        // 计算节点所在行
        const row = Math.floor(index / this.nodesPerRow)
        // 计算节点所在列
        let col = row % 2 === 0
          ? index % this.nodesPerRow  // 偶数行：从左到右
          : this.nodesPerRow - 1 - (index % this.nodesPerRow) // 奇数行：从右到左

        // const x = this.startX * scaleFactor + (col * this.nodeSpacing)
        // const y = startY + (row * this.rowHeight * scaleFactor)
        const x = (this.startX * scaleFactor + (col * this.nodeSpacing));
        const y = startY * scaleFactor + (row * this.rowHeight * scaleFactor);
        // console.log('TrackNodeDiagram-----x-----y', x, y)
        this.nodePositions.push({
          x,
          y,
          radius: 6 * scaleFactor,
          node
        })
      })

      console.log('nodeData', this.nodeData)
      // this.rowHeight = this.rowHeight * scaleFactor;
      // 2. 绘制所有连接线
      const rows = Math.ceil(this.nodeData.length / this.nodesPerRow)
      // 遍历每一行，除了最后一行
      for (let row = 0; row < rows - 1; row++) {
        const currentY = startY * scaleFactor + (row * this.rowHeight * scaleFactor)
        const nextY = startY * scaleFactor + ((row + 1) * this.rowHeight * scaleFactor)
        const lastX = this.startX * scaleFactor + ((this.nodesPerRow - 1) * this.nodeSpacing)
        const firstX = this.startX * scaleFactor
        const radius = this.rowHeight / 5 * scaleFactor

        // 计算剩余节点以确定是否需要连接
        const remainingNodes = this.nodeData.length - ((row + 1) * this.nodesPerRow)
        const needsConnection = remainingNodes > 0

        // 处理偶数行
        if (row % 2 === 0 && needsConnection) {
          const nextRowFirstIndex = (row + 1) * this.nodesPerRow
          // const nextNode = this.nodeData[nextRowFirstIndex]
          const nextNode = this.nodeData[(row + 1) * this.nodesPerRow - 1]
          const lineColor = this.getLineColor(nextNode)

          const extendedX = lastX + this.edgeExtension * scaleFactor

          // 绘制向右的水平线
          ctx.beginPath()
          ctx.moveTo(lastX + 6 * scaleFactor, currentY)
          ctx.lineTo(extendedX, currentY)
          ctx.strokeStyle = lineColor
          ctx.lineWidth = 2 * scaleFactor
          ctx.stroke()

          // 绘制连接到下一行的弧线
          ctx.beginPath()
          ctx.moveTo(extendedX, currentY)
          ctx.arcTo(
            extendedX + radius , currentY,
            extendedX + radius , currentY + radius ,
            radius
          )
          ctx.arcTo(
            extendedX + radius , nextY,
            extendedX, nextY,
            radius
          )
          ctx.lineTo(lastX + 6 * scaleFactor, nextY)
          ctx.strokeStyle = lineColor
          ctx.stroke()
        }
        // 处理奇数行
        else if (row % 2 === 1 && needsConnection) {
          const nextRowLastIndex = Math.min((row + 2) * this.nodesPerRow - 1, this.nodeData.length - 1)
          // const nextNode = this.nodeData[nextRowLastIndex]
          const nextNode = this.nodeData[(row + 1) * this.nodesPerRow - 1]
          const lineColor = this.getLineColor(nextNode)

          const extendedX = firstX - this.edgeExtension * scaleFactor

          // 绘制向左的水平线
          ctx.beginPath()
          ctx.moveTo(firstX - 6 * scaleFactor, currentY)
          ctx.lineTo(extendedX, currentY)
          ctx.strokeStyle = lineColor
          ctx.lineWidth = 2 * scaleFactor
          ctx.stroke()

          // 绘制连接到下一行的弧线
          ctx.beginPath()
          ctx.moveTo(extendedX, currentY)
          ctx.arcTo(
            extendedX - radius , currentY,
            extendedX - radius , currentY + radius,
            radius
          )
          ctx.arcTo(
            extendedX - radius, nextY,
            extendedX, nextY,
            radius
          )
          ctx.lineTo(firstX - 6 * scaleFactor, nextY)
          ctx.strokeStyle = lineColor
          ctx.stroke()
        }
      }

      // 3. 绘制水平连接线
      this.nodePositions.forEach((position, index) => {
        const row = Math.floor(index / this.nodesPerRow)
        const col = row % 2 === 0
          ? index % this.nodesPerRow
          : this.nodesPerRow - 1 - (index % this.nodesPerRow)

        const isBlueCircle = position.node.dotType === 5
        const stopBeforeCircle = isBlueCircle
        const circleGap = 8

        if (row % 2 === 0) {  // 偶数行
          if (col > 0) {
            const prevNodeIndex = index - 1
            const prevNode = this.nodeData[prevNodeIndex]
            ctx.beginPath()
            ctx.moveTo(position.x - this.nodeSpacing + 6 * scaleFactor, position.y)
            ctx.lineTo(stopBeforeCircle ? position.x - circleGap * scaleFactor : position.x - 6 * scaleFactor, position.y)
            ctx.strokeStyle = this.getLineColor(prevNode)  // 使用左侧节点的颜色
            ctx.lineWidth = 2 * scaleFactor
            ctx.stroke()
          }
        } else {  // 奇数行
          if (col < this.nodesPerRow - 1 && index < this.nodeData.length - 1) {
            const prevNodeIndex = index - 1
            const prevNode = this.nodeData[prevNodeIndex]
            ctx.beginPath()
            ctx.moveTo(position.x + this.nodeSpacing - 6 * scaleFactor, position.y)
            ctx.lineTo(stopBeforeCircle ? position.x + circleGap * scaleFactor : position.x + 6 * scaleFactor, position.y)
            ctx.strokeStyle = this.getLineColor(prevNode)  // 使用左侧节点的颜色
            ctx.lineWidth = 2 * scaleFactor
            ctx.stroke()
          }
        }
      })

      // 4. 绘制节点与文本
      this.nodePositions.forEach((position, index) => {
        const { x, y, node } = position

        const isLargeNode = node.trackMapName === '工程规划许可证' ||
                            node.trackMapName === '工程施工许可证' ||
                            node.trackMapName === '展示区开放' ||
                            node.trackMapName === '预售许可证' ||
                            (node.trackMapName.includes('主体结构封顶') && node.trackMapName.includes('全部')) ||
                            node.trackMapName === '工程竣工备案' ||
                            node.trackMapName === '合同交付'

        const radius = (isLargeNode ? 10 : 6) * scaleFactor
        const dotColor = this.dotTypeDotColorFilter(node.dotType)

        if (dotColor === '#006AFF') {
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.strokeStyle = dotColor;
            ctx.lineWidth = isLargeNode ? 4 * scaleFactor : 2 * scaleFactor;
            ctx.stroke();
        } else {
            if (dotColor !== '#DDDDDD') {
                ctx.beginPath();
                ctx.arc(x, y, radius + 2 * scaleFactor, 0, Math.PI * 2);
                ctx.fillStyle = '#FFFFFF';
                ctx.fill();
            }
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fillStyle = dotColor;
            ctx.fill();
        }

        ctx.fillStyle = '#666666'
        // ctx.font = '12px PingFang SC'
        ctx.font = `${12 * scaleFactor}px PingFang SC`
        ctx.textAlign = 'center'

        if(index === 0){
          ctx.fillStyle = this.dotTypetrackMapNameColorFilter(node.dotType)
          ctx.fillText(node.trackMapName, x, (y - 40 * scaleFactor))
          ctx.fillText(node.actualFinishDate || '', x, (y - 20 * scaleFactor))
          ctx.fillStyle = '#666666'
          ctx.fillText('计划完成日期', x, (y + 50 * scaleFactor))
          ctx.fillText('实际完成日期', x, (y + 70 * scaleFactor))
          ctx.fillText('用时', x, (y + 90 * scaleFactor))
        }
        else{
          if(node.trackMapName === '工程规划许可证' || node.trackMapName === '工程施工许可证'
          || node.trackMapName === '展示区开放' || node.trackMapName === '预售许可证'
          || (node.trackMapName.includes('主体结构封顶') && node.trackMapName.includes('全部'))
          || node.trackMapName === '工程竣工备案'
          || node.trackMapName === '合同交付'
          ){
            ctx.fillStyle = '#222222'
            // ctx.font = '16px PingFang SC'
            ctx.font = `${16 * scaleFactor}px PingFang SC`
            ctx.fontWeight = 'bold'
          }

          ctx.fillStyle = this.dotTypetrackMapNameColorFilter(node.dotType)
          ctx.fillText(node.trackMapName, x, (y - 40 * scaleFactor))
          ctx.fillStyle = '#666666'
          // ctx.font = '12px PingFang SC'
          ctx.font = `${12 * scaleFactor}px PingFang SC`
          ctx.textAlign = 'center'
          ctx.fillText(node.planFinishDate || '--', x, (y + 50 * scaleFactor))
          ctx.fillText(node.actualFinishDate || '--', x, (y + 70 * scaleFactor))
          ctx.fillText(!!node.days ? node.days + '天' : '--', x, (y + 90 * scaleFactor))
        }

      })
    },

    handleMouseMove(event) {
      const scaleFactor = this.canvasWidth / 1440;
      const rect = this.$refs.canvas.getBoundingClientRect()

      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      const hoveredNode = this.nodePositions.find(node => {
        const distance = Math.sqrt(
          Math.pow(x - node.x, 2) +
          Math.pow(y - node.y, 2)
        )
        return distance <= node.radius + 2 * scaleFactor
      })

      console.log('mousemove------------hoveredNode', hoveredNode)

      if (hoveredNode) {
        this.showTooltip = true
        this.activeNode = hoveredNode.node

        // 如果节点的 x 坐标大于或等于 "画布宽度减去节点间距" 的位置，就认为这个节点在最右侧列
        const isRightmostColumn = (hoveredNode.x >= this.canvasWidth - this.nodeSpacing);

        this.tooltipX = isRightmostColumn
          ? x - (200 * scaleFactor) // 200 is approximate tooltip width
          : x + (15 * scaleFactor)
        this.tooltipY = y + (15 * scaleFactor)
        console.log('mousemove------------tooltipX', this.tooltipX)
        console.log('mousemove------------tooltipY', this.tooltipY)
      } else {
        this.hideTooltip()
      }
    },
    handleMouseMove2(event) {
      const scaleFactor = this.canvasWidth / 1440;
      const rect = this.$refs.canvas.getBoundingClientRect()

      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      const hoveredNode = this.nodePositions.find(node => {
        const distance = Math.sqrt(
          Math.pow(x - node.x, 2) +
          Math.pow(y - node.y, 2)
        )
        return distance <= node.radius + 2 * scaleFactor
      })

      if (hoveredNode) {
        this.showTooltip = true
        this.activeNode = hoveredNode.node
        this.tooltipX = x + 15 * scaleFactor
        this.tooltipY = y + 15 * scaleFactor
      } else {
        this.hideTooltip()
      }
    },

    hideTooltip() {
      this.showTooltip = false
      this.activeNode = null
    },

    getLineColor(node) {
      let color;
      if(node.lineColour === 1){
        color = '#006AFF'
      }
      else if(node.lineColour === 2){
        color = '#DDDDDD'
      }
      return color;
    }
  },
  computed: {
    nodesPerRow() {
      return 9
    },
    totalRows() {
      return Math.ceil(this.nodeData.length / this.nodesPerRow)
    },
    calculatedCanvasHeight() {
      const designWidth = 1440; // Design width
      const scaleFactor = this.canvasWidth / 1440;
      const designHeight = (this.totalRows * this.rowHeight) + 50; // Original height calculation

      // Calculate the responsive height based on the current canvas width
      return designHeight * scaleFactor;
    },
    tooltipStyle() {
      return {
        position: 'absolute',
        left: `${this.tooltipX}px`,
        top: `${this.tooltipY}px`,
        zIndex: 1000
      }
    }
  }
}
</script>

<style scoped lang="scss">
.track-node-diagram {
  width: 100%;
  overflow: auto;
  padding: 0 0.75rem;
  position: relative;
}

canvas {
  display: block;
  margin: 0 auto;
}

.node-tooltip {
  position: absolute;
  background: white;
  border-radius: 0.25rem;
  padding: 1rem;
  min-width: 12.5rem;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.1);
  z-index: 1000;
  pointer-events: none;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 0.875rem;
  color: #333333;
  line-height: 1.375rem;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.tooltip-item {
  margin-bottom: 0.5rem;
  &.header{
    display: flex;
    justify-content: space-between;
    gap: 6.25rem;
  }
  .title{
    font-weight: 600;
    font-size: 0.875rem;
    color: #333333;
  }
}

.tooltip-item .label {
  color: #999;
}
</style>
