import request from '@/utils/request'
// 获取全景计划项目分期集合
function costAndOutput(projectCode) {
  return request({
    url: `/project/cost/index/costAndOutput/${projectCode}`,
    method: 'get',
  })
}

function dynamicCost(projectCode) {
  return request({
    url: `/project/cost/dynamic/trend/${projectCode}`,
    method: 'get',
  })
}

function detail(projectCode) {
  return request({
    url: `/project/cost/data/detail/${projectCode}`,
    method: 'get',
  })
}

function changeVisa(projectCode) {
  return request({
    url: `/project/cost/index/changeVisa/${projectCode}`,
    method: 'get',
  })
}


export default {
  costAndOutput,
  dynamicCost,
  detail,
  changeVisa
}
