# FileLink 组件文档

## 版本信息
- **当前版本**: v5.4
- **最后更新**: 2024-01-XX
- **更新内容**: 修复PDF文件损坏错误，实现智能文件访问策略（直接URL优先，API流备用）

## 概述
FileLink 是一个用于显示和预览文件链接的 Vue.js 组件。它提供了文件预览和下载功能，支持多种文件格式的在线预览。

## 特性
- 📄 支持多种文件格式预览：PDF、Word、Excel、PowerPoint、图片等
- 🔗 智能文件名显示，支持长文件名截断
- 📥 一键下载功能
- 🔒 安全的文件流处理
- 🎨 现代化的预览界面
- ⚡ 基于 officetohtml.js 的高性能预览

## 安装与配置

### 1. 依赖库
确保以下依赖库已在 `public/index.html` 中正确引入：

```html
<!-- jQuery -->
<script src="<%= BASE_URL %>include/jquery/jquery-3.3.1.min.js"></script>

<!-- PDF.js -->
<script src="<%= BASE_URL %>include/pdf/pdf.js"></script>

<!-- Mammoth.js (Word文档) -->
<script src="<%= BASE_URL %>include/docx/mammoth.browser.min.js"></script>

<!-- PPTXjs (PowerPoint) -->
<script src="<%= BASE_URL %>include/PPTXjs/js/jszip.min.js"></script>
<script src="<%= BASE_URL %>include/PPTXjs/js/filereader.js"></script>
<script src="<%= BASE_URL %>include/PPTXjs/js/d3.min.js"></script>
<script src="<%= BASE_URL %>include/PPTXjs/js/nv.d3.min.js"></script>
<script src="<%= BASE_URL %>include/PPTXjs/js/pptxjs.js"></script>
<script src="<%= BASE_URL %>include/PPTXjs/js/divs2slides.js"></script>

<!-- Handsontable (Excel) -->
<script src="<%= BASE_URL %>include/SheetJS/handsontable.full.min.js"></script>

<!-- ViewerJS (图片) -->
<script src="<%= BASE_URL %>include/verySimpleImageViewer/js/imageViewer.min.js"></script>

<!-- XLSX -->
<script src="<%= BASE_URL %>include/SheetJS/xlsx.full.min.js"></script>

<!-- officeToHtml.js -->
<link rel="stylesheet" href="<%= BASE_URL %>include/officeToHtml/officeToHtml.css">
<script src="<%= BASE_URL %>include/officeToHtml/officeToHtml.js"></script>
```

### 2. 组件注册
在 `main.js` 中注册组件：

```javascript
import FileLink from '@/components/FileLink.vue'
Vue.component('FileLink', FileLink)
```

## 使用方法

### 基本用法
```vue
<template>
  <div>
    <!-- 单个文件链接 -->
    <FileLink :file="fileObject" />
    
    <!-- 文件列表 -->
    <div v-for="file in fileList" :key="file.id">
      <FileLink :file="file" @preview="handlePreview" @download="handleDownload" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileObject: {
        name: '示例文档.docx',
        url: 'https://example.com/files/document.docx'
      },
      fileList: [
        { id: 1, name: '报告.pdf', url: 'https://example.com/files/report.pdf' },
        { id: 2, name: '数据.xlsx', url: 'https://example.com/files/data.xlsx' },
        { id: 3, name: '演示.pptx', url: 'https://example.com/files/presentation.pptx' }
      ]
    }
  },
  methods: {
    handlePreview(event) {
      console.log('预览文件:', event)
    },
    handleDownload(event) {
      console.log('下载文件:', event)
    }
  }
}
</script>
```

## Props

### file (必需)
- **类型**: `Object`
- **说明**: 文件对象
- **属性**:
  - `name` (String): 文件名
  - `url` (String): 文件URL

```javascript
// 示例
const file = {
  name: '示例文档.docx',
  url: 'https://example.com/files/document.docx'
}
```

## 事件

### preview
文件预览时触发
- **参数**: 
  ```javascript
  {
    fileUrl: String,    // 完整的文件访问URL
    fileName: String,   // 文件名
    fileType: String    // 文件类型
  }
  ```

### download
文件下载时触发
- **参数**:
  ```javascript
  {
    fileUrl: String,    // 文件URL
    fileName: String    // 文件名
  }
  ```

## 支持的文件类型

### 文档类型
- **PDF**: `.pdf`
- **Word**: `.doc`, `.docx`
- **Excel**: `.xls`, `.xlsx`
- **PowerPoint**: `.ppt`, `.pptx`

### 图片类型
- **常见格式**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`, `.svg`

### 文本类型
- **纯文本**: `.txt`, `.md`, `.json`, `.xml`, `.csv`

## 技术实现

### 文件访问机制
组件根据文件类型采用不同的预览方式：

1. **图片文件**: 直接使用 `<img>` 标签显示，简单高效
   ```vue
   <img 
     :src="file.url" 
     alt="图片预览" 
     style="max-width: 100%; max-height: 600px; object-fit: contain;"
     @load="previewLoading = false"
     @error="handlePreviewError"
   />
   ```

2. **Office文档**: 使用 officetohtml.js 进行预览，采用智能访问策略
   ```javascript
   // 优先尝试直接文件URL
   async tryDirectFilePreview() {
     const response = await fetch(this.file.url, { method: 'HEAD' })
     if (response.ok) {
       // 直接使用原始URL
       $container.officeToHtml({ url: this.file.url, ...options })
     } else {
       throw new Error('文件不可直接访问')
     }
   }
   
   // 备用方案：通过API获取文件流
   async tryBlobFilePreview() {
     const response = await getFile(this.file.url)
     const blob = new Blob([response], { type: mimeType })
     const blobUrl = window.URL.createObjectURL(blob)
     $container.officeToHtml({ url: blobUrl, ...options })
   }
   ```

3. **下载处理**: 通过后端API获取文件流，创建blob URL进行下载

### 预览配置
每种文件类型都有专门的预览配置：

```javascript
// PDF 配置
pdfSetting: {
  thumbnailViewBtn: true,
  searchBtn: true,
  nextPreviousBtn: true,
  pageNumberTxt: true,
  totalPagesLabel: true,
  zoomBtns: true,
  scaleSelector: true,
  printBtn: true,
  downloadBtn: true
}

// Word 文档配置
docxSetting: {
  includeEmbeddedStyleMap: true,
  includeDefaultStyleMap: true,
  ignoreEmptyParagraphs: false,
  isRtl: "auto"
}

// Excel 配置
sheetSetting: {
  allowEmpty: true,
  autoColumnSize: true,
  columnSorting: true,
  contextMenu: true,
  copyable: true,
  readOnly: true,
  rowHeaders: true,
  colHeaders: true,
  width: '100%',
  height: 600
}
```

## 样式定制

### CSS 类名
- `.file-link-container`: 文件链接容器
- `.file-name`: 文件名链接
- `.filename-part`: 文件名主体部分
- `.extension-part`: 文件扩展名部分
- `.download-icon`: 下载图标
- `.preview-container`: 预览容器
- `.office-preview-container`: Office文档预览容器

### 自定义样式示例
```css
/* 自定义文件链接样式 */
.file-link-container .file-name {
  color: #1890ff;
  font-weight: 500;
}

.file-link-container .download-icon:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 自定义预览对话框 */
.officetohtml-dialog .el-dialog {
  border-radius: 8px;
}

.officetohtml-dialog .el-dialog__header {
  background: #f5f5f5;
  border-radius: 8px 8px 0 0;
}
```

## 故障排除

### 常见问题

1. **提示 "The file is not supported!"**
   - 检查文件URL是否可访问
   - 确认后端API `/rant/common/file/stream` 正常工作
   - 验证文件格式是否在支持列表中

2. **预览界面空白**
   - 检查浏览器控制台是否有JavaScript错误
   - 确认所有依赖库正确加载
   - 验证 officetohtml.js 插件是否正确初始化

3. **下载功能异常**
   - 检查网络连接
   - 确认后端文件流API正常
   - 验证文件权限设置

4. **样式显示异常**
   - 检查CSS文件是否正确加载
   - 确认Element UI样式没有冲突
   - 验证自定义样式优先级

### 调试技巧

1. **启用调试日志**:
   ```javascript
   // 在浏览器控制台中查看详细日志
   console.log('开始预览文件:', this.file.name)
   console.log('文件URL:', this.file.url)
   console.log('完整文件URL:', fullFileUrl)
   ```

2. **检查依赖加载**:
   ```javascript
   // 在控制台检查依赖是否加载
   console.log('jQuery:', typeof window.$)
   console.log('officeToHtml:', typeof window.$.fn.officeToHtml)
   ```

3. **验证文件访问**:
   ```javascript
   // 直接在浏览器中访问文件URL
   // 应该能够下载或预览文件
   ```

## 版本历史

### v5.4 (当前版本)
- 修复PDF文件"Invalid or corrupted PDF file"错误
- 实现智能文件访问策略：优先直接URL，失败时使用API流
- 改进错误处理和资源管理
- 优化文件预览的兼容性和稳定性

### v5.3
- 修复PDF等文档预览"The file is not supported!"问题
- 使用直接文件URL替代blob URL进行文档预览
- 图片预览使用简单的img标签实现
- 优化文件访问机制和错误处理

### v5.2
- 图片预览使用简单的img标签实现
- 保持Office文档使用officetohtml.js预览
- 优化预览性能和用户体验
- 分离图片和文档预览逻辑

### v5.1
- 修复 officetohtml.js 文件支持问题
- 使用直接文件URL而非blob URL
- 优化错误处理和调试信息
- 改进文件访问机制

### v5.0
- 集成 officetohtml.js 实现文件预览
- 支持 PDF、Office 文档、图片等多种格式
- 添加专业的预览界面
- 实现本地依赖库部署

### v4.0
- 使用 file-viewer3 组件
- Element UI 对话框集成
- 改进的预览界面

### v3.0
- 集成 gxd-file-preview 插件
- 简化组件实现
- 支持更多文件格式

### v2.0
- 实现文件流安全处理
- 添加 Blob URL 支持
- 改进下载功能

### v1.0
- 初始版本
- 基本的文件链接和预览功能
- ImagePreview 组件集成

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看浏览器控制台的错误信息
2. 确认所有依赖库正确加载
3. 检查后端API接口是否正常
4. 参考本文档的故障排除部分

## 相关资源

- [officetohtml.js 官方文档](https://officetohtml.js.org/)
- [Element UI 官方文档](https://element.eleme.cn/)
- [Vue.js 官方文档](https://cn.vuejs.org/) 