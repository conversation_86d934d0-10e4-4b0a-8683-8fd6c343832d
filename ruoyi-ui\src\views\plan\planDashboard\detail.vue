<script>

import ResultFile from "@/views/plan/components/ResultFile/index.vue";
import PlanAndNodeInfo from "./PlanAndNodeInfo/index.vue";
import ProgressFeedback from "@/views/plan/components/ProgressFeedback/index.vue";
import FinishedFeedback from "@/views/plan/components/FinishedFeedback/index.vue";
import {finishFeedback, progressFeedback, getFeedback, getPlanStatus, flowCreate} from "@/api/plan/feedback";
import {planDetail} from "@/api/plan/plan";

/*import {
  getApproveFlow,
} from "@/api/plan/feedback";*/
import {formatDateWithTime} from '@/utils';
export default {
  components: {PlanAndNodeInfo, ResultFile},
  data() {
    return {
      rowData: {},
      feedbackOpen: false,
      resultOpen: false,
      feedbackDetail: {},
      currentTabComponent: null,
      feedBackForm: {},
    }
  },
  created() {
    this.getPlanDetail();
  },
  methods: {
    getPlanDetail() {
      planDetail(this.$route.query.id).then(response => {
        this.rowData = response.data;
      })
    },
    tableBthShow(btnType, row){
      // status为 0 1 4   同时 feedback_flow_status 为0 3 4时，显示过程反馈  完成反馈
      // status 为 2  3时，显示 查看
      // 计划查看 里面的计划查看计划节点的时候，有些节点是可以反馈的，
      // 用这个去判断是否可以反馈 再根据现在反馈的判断去展示 过程反馈 和完成反馈 按钮
      switch (btnType) {
        case 'update':
          return row.isEnableFeedback && (row.status === 0 || row.status === 1 || row.status === 4)
            && (row.feedbackFlowStatus === 0 || row.feedbackFlowStatus === 3 || row.feedbackFlowStatus === 4);
        case 'refreshFlowStatus': // 刷新状态
          return row.feedbackFlowStatus === 1
        default:
          return false;
      }
    },
    // 获取反馈节点详情
    handleGetFeedback(row, feedbackType) {
      console.log(row)
      this.feedBackForm.feedbackType = feedbackType;
      getFeedback(this.$route.query.id, row.id).then(response => {
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList || [];
        this.feedBackForm.nodeId = response.data.id;
        this.feedBackForm.feedbackProgress = response.data.feedbackProgress || 0; // 如果没有反馈进度为null，表单校验报错
        this.feedBackForm.expectedCompletionDate = response.data.expectedCompletionDate ?  formatDateWithTime(new Date(response.data.expectedCompletionDate)) : formatDateWithTime(new Date());
        this.feedBackForm.actualCompletionDate = response.data.actualCompletionDate ? formatDateWithTime(new Date(response.data.actualCompletionDate)) : formatDateWithTime(new Date());
        this.feedBackForm.notes = response.data.notes || '';
        // 1:过程反馈，2：完成反馈
        if(feedbackType === 2){
          this.feedBackForm.feedbackProgress = 100;
        }
        this.$refs.feedback.handleSetForm(this.feedBackForm);
        this.$refs.feedback.handleSetNodeOutcomeDocumentList(this.nodeOutcomeDocumentList);
      })
    },
    // 过程反馈、完成反馈打开弹窗
    handleFeedback(type, slotProps, feedbackType) { // 处理反馈
      if(type === 'progressFeedback') {
        this.currentTabComponent = ProgressFeedback
      } else if(type === 'finishedFeedback') {
        this.currentTabComponent = FinishedFeedback
      }
      this.feedbackOpen = true;
      this.handleGetFeedback(slotProps.row, feedbackType);
    },
    handleResultOpen(open){ // 打开、关闭成果文件选择弹窗
      this.resultOpen = open;
    },
    submitForm() {
      const obj = {
        path: "/planDetailBlank"
      };
      this.$forceUpdate();
      this.$refs.feedback.$refs["form"].validate(valid => {
        if (valid && this.$refs.feedback.fileValidate()) {
          // 过程反馈、完成反馈接口判断
          let form = this.$refs.feedback.form;
          let nodeOutcomeDocumentList = this.$refs.feedback.handleGetNodeOutcomeDocumentList();
          let feedbackPost = form.feedbackType === 1 ? progressFeedback : finishFeedback; // 1 过程反馈，2 完成反馈
          feedbackPost({
            ...form,
            planId: this.$route.query.id,
            nodeOutcomeDocumentList
          }).then(response => {
            // this.$modal.msgSuccess("节点反馈成功");

            //保存成功后发起流程
            flowCreate({
              ...this.feedBackForm,
              planId: this.$route.query.id,
            }).then(response => {
              this.$refs.planAndNodeInfoRef.getList(); // 刷新计划节点列表
              window.open(response.data)
              this.feedbackOpen = false;
              this.$modal.msgSuccess("节点反馈成功");
              this.$tab.closeOpenPage(obj);
              /*this.$router.push({
                path: "/plan/dashboard"
              });*/
              this.loading = false;
            }).catch(() => {
              this.loading = false;
            })
            ;
            // this.queryParams.pageNum = 1;
            // this.getList();
          });
        }
      });
    },
    handleResultFileConfirm(){ // 获取成果文件选择弹窗选中的文件
      this.handleResultOpen(false);
      this.selectedFileList = this.$refs.resultFile.getSelectedFileList();
      this.submitResultFile();
    },
    refreshApproval(row) {
      getPlanStatus(row.id).then(res => {
        this.loading = false
        this.getPlanDetail();
        this.$refs.planAndNodeInfoRef.getList();
        this.$forceUpdate();
      }).catch(err => {
        this.loading = false
      })
    },
    //获取审批流
   /* fetchApprovalUser () {
      this.loading = true
      getApproveFlow(this.$route.query.id).then(res => {
        this.loading = false
        window.open(res.data)
        /!*if (res.data) {
          //可编辑页面需要先打开一个页面用于登陆
          if (res.data.indexOf('collaboration/collaboration.do') > 0) {
            let newWindow = window.open('http://iam.cscec1b.net/cas/login?service=https%3A%2F%2Foa.cscec1b.net%2Fseeyon%2Fcaslogin%2Fsso', '_blank')
            setTimeout(function () {
              newWindow.location.replace(res.data)
            }, 2000)
          } else {
            window.open(res.data)
          }
        }*!/
      }).catch(err => {
        this.loading = false
      })
    },*/
  },

}
</script>
<template>
  <div class="app-container">
<!--   fetchApprovalUser 计划名称超链接汇报审批-->
<!--    @fetchApprovalUser="fetchApprovalUser"-->
    <PlanAndNodeInfo :info="rowData" :statusShow="true" ref="planAndNodeInfoRef">
<!--      过程反馈 都先注释掉-->
<!--      <template slot="progressFeedback" slot-scope="slotProps">
        <el-button
          v-if="tableBthShow('update', slotProps.row)"
          size="mini"
          type="text"
          icon="el-icon-view"
          @click="handleFeedback('progressFeedback', slotProps, 1)"
        >过程反馈</el-button>
      </template>-->
      <template slot= "progressFeedback" slot-scope="slotProps">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-edit-outline"
          v-if="tableBthShow('update', slotProps.row)"
          @click="handleFeedback('finishedFeedback', slotProps, 2)"
        >
          汇报
        </el-button>
      </template>
        <template slot= "finishedFeedback" slot-scope="slotProps">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            v-if="tableBthShow('refreshFlowStatus', slotProps.row)"
            @click="refreshApproval(slotProps.row)"
          >
            刷新
          </el-button>
      </template>
    </PlanAndNodeInfo>
    <!-- 反馈弹窗 -->
    <el-dialog  title="节点反馈维护" :visible.sync="feedbackOpen" width="1200px" append-to-body>
      <component v-bind:is="currentTabComponent" ref="feedback" :feedbackDetail="feedbackDetail" @handleResultOpen="handleResultOpen"></component>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">发起审批</el-button>
        <el-button @click="feedbackOpen = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--  成果文件选择  -->
    <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
      <ResultFile ref="resultFile"></ResultFile>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResultFileConfirm">确 认</el-button>
        <el-button @click="handleResultOpen(false)">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<style scoped lang="scss">

</style>
