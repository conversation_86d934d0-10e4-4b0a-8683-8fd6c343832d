<script>
// 成果文件选择
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import Template from "@/views/plan/template/index.vue";
import {statusOption, fillFlagOption} from '@/constant';
import {listConfig} from "@/api/plan/config";
export default {
  components: {Template, StatusTag},
  data(){
    return {
      resultFileTotal: 0, // 成果文件总条数
      resultFileLoading: false, // 成果文件加载
      queryResultFileParams: {
        pageNum: 1,
        pageSize: 200,
        status: 0
      },
      resultFileList: [
        /*{
          "searchValue": null,
          "createBy": null,
          "createTime": "2023-12-29 06:53:47",
          "updateBy": null,
          "updateTime": "2023-12-29 06:53:57",
          "remark": null,
          "params": {},
          "id": "72c0fbce7a1a447db3a8664508c59304",
          "type": "会议报告",
          "name": "会议报告",
          "fillFlag": 0,
          "status": 0,
          "delFlag": "0"
        },*/
      ], // 成果文件列表

    }
  },
  computed: {
    statusOption() {
      return statusOption
    },
    fillFlagOption() {
      return fillFlagOption
    },
  },
  mounted() {
    this.getResultFileNodeList();
  },
  methods: {
    handleFileSelectionChange(selection) {
      this.selectedFileList = selection;
    },
    /** 查询成果设置列表 */
    getResultFileNodeList() {
      this.resultFileLoading = true;
      listConfig(this.queryResultFileParams).then(response => {
        this.resultFileList = response.rows;
        this.resultFileTotal = response.total;
        this.resultFileLoading = false;
      });
    },
    getSelectedFileList(){
      return this.selectedFileList;
    },
  }
}
</script>

<template>
  <section>
    <el-table v-loading="resultFileLoading" :data="resultFileList" @selection-change="handleFileSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index"/>
      <el-table-column label="成果类别" align="center" prop="type"/>
      <el-table-column label="成果名称" align="center" prop="name"/>
      <el-table-column label="是否必填" align="center" prop="fillFlag">
        <template slot-scope="scope">
          <status-tag :status="scope.row.fillFlag" :options="fillFlagOption"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="statusOption"/>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt20"
      v-show="resultFileTotal>0"
      :total="resultFileTotal"
      :page.sync="queryResultFileParams.pageNum"
      :limit.sync="queryResultFileParams.pageSize"
      @pagination="getResultFileNodeList"
    />
  </section>

</template>

<style scoped lang="scss">

</style>
