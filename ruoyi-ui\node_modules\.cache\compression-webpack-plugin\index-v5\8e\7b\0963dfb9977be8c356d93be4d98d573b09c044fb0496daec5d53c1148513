
f9f35b3b083ae20949fe24d99881ff948ad3e463	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"c7e46a86200f5303b24d36657a7ed7f0\"}","integrity":"sha512-yn3WYDr5u5eocyJOr66iTtFhNrsfunfOi7PL7XDzSM+6kL92DfVk7RcJ7jxKihyrzTEAK2Ns2TbM8oEt0a3wWw==","time":1754311448790,"size":12044054}