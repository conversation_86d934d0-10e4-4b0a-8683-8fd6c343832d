{"remainingRequest": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue?vue&type=template&id=724887a0&scoped=true", "dependencies": [{"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue", "mtime": 1754312350764}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743920556726}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1743920558214}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743920555710}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}