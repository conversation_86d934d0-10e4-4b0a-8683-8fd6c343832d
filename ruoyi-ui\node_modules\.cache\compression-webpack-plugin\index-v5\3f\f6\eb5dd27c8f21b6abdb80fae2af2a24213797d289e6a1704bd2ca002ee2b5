
7a91b3cfcfc91b3d0e9a85d65d85f444e4ad9221	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.1a52f18c5e110b3ae115.hot-update.js\",\"contentHash\":\"1308fe0bcef0f317f6a0c03f1966ee95\"}","integrity":"sha512-NfEIMAszvvzZ1mz/+d7E/05vagaeKEput/IoqdqwFDyvNEW4FESMU/esgPwos61l3rMNtiqmM9NHnUWEttPtvQ==","time":1754311996051,"size":77918}