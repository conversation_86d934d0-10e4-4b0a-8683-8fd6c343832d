<!--初审页面-->
<template>
  <div class="app-container app-container-feedback" v-loading="loading">
    <div class="step-box">
      <rant-step :stepActive="stepActive" />
    </div>

    <div class="rant-container">
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="rant-form-title">
            <img src="@/assets/rant/rant-info.png" class="icon">
            基本信息
          </div>

          <van-form class="rant-detail" ref="form" @submit="onSubmit">
            <div class="form-group">
              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>来源</div>
                <div class="form-value clickable" @click="selectUserFun('ranter')" :class="{'custom-disabled-mobile': isRead}">
                  {{ form.ranterName || '请选择来源' }}
                </div>
              </div>
              <div class="form-error content-error" v-if="errors.ranter">
                <i class="error-icon"></i>{{ errors.ranter }}
              </div>
              <van-field v-show="false" v-model="form.ranter" name="ranter"
                :rules="[{ required: true, message: '请选择来源' }]" />

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>分类</div>
                <div class="form-value clickable" @click="!isRead && (showClassifyPicker = true)" :class="{'custom-disabled-mobile': isRead}">
                  <template v-if="form.rantClassify">
                    <van-icon name="more-o" /> {{ form.rantClassify }}
                  </template>
                  <template v-else><van-icon name="more-o" />请选择吐槽分类</template>
                </div>
              </div>
              <div class="form-error content-error" v-if="errors.rantClassify">
                <i class="error-icon"></i>{{ errors.rantClassify }}
              </div>
              <van-field v-show="false" v-model="form.rantClassify" name="rantClassify"
                :rules="[{ required: true, message: '请选择吐槽分类' }]" />

              <div class="form-item">
                <div class="form-label">计划完成时间</div>
                <div class="form-value align-right clickable" @click="!isRead && (showDatePicker = true)" :class="{'custom-disabled-mobile': isRead}">
                  <van-icon name="clock-o" /> {{ form.planTime || '请选择计划完成时间' }}
                </div>
              </div>

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>责任人</div>
                <div class="form-value clickable" @click="selectUserFun('responsibilityer')" :class="{'custom-disabled-mobile': isRead}">
                  <van-icon name="manager-o" />{{ form.responsiblePersonName || '请选择责任人' }}
                </div>
              </div>
              <div class="form-error content-error" v-if="errors.responsiblePerson">
                <i class="error-icon"></i>{{ errors.responsiblePerson }}
              </div>
              <van-field v-show="false" v-model="form.responsiblePerson" name="responsiblePerson"
                :rules="[{ required: true, message: '请选择责任人' }]" />

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>责任部门</div>
                <div class="form-value clickable" @click="!isRead && (showDeptPicker = true)" :class="{'custom-disabled-mobile': isRead}">
                  <van-icon name="user-o" />{{ form.deptName || '请选择责任部门' }}
                </div>
              </div>
              <div class="form-error content-error" v-if="errors.deptId">
                <i class="error-icon"></i>{{ errors.deptId }}
              </div>
              <van-field v-show="false" v-model="form.deptId" name="deptId"
                :rules="[{ required: true, message: '请选择责任部门' }]" />

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>部门负责人</div>
                <div class="form-value">{{ form.respDeptResponsiblerName || '未设置' }}</div>
              </div>
              <div class="form-error content-error" v-if="errors.respDeptResponsibler">
                <i class="error-icon"></i>{{ errors.respDeptResponsibler }}
              </div>
              <van-field v-show="false" v-model="form.respDeptResponsibler" name="respDeptResponsibler"
                :rules="[{ required: true, message: '请确保已选择有负责人的部门' }]" />
            </div>
          </van-form>
        </div>
      </div>
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10"><span class="required-mark">*</span>内容</div>
            <div class="">
              <textarea v-model="form.rantContent" class="progress-textarea" rows="4" placeholder="请输入内容" :disabled="isRead"></textarea>
            </div>
            <div class="form-error content-error" v-if="errors.rantContent">
              <i class="error-icon"></i>{{ errors.rantContent }}
            </div>
          </div>
        </div>
      </div>
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10">措施</div>
            <div class="">
              <textarea v-model="form.solution" class="progress-textarea" rows="4" placeholder="请输入" :disabled="isRead" @focus="handleTextareaFocus" @blur="handleTextareaBlur"></textarea>
            </div>
          </div>
        </div>
      </div>
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10">审核意见</div>
            <div class="">
              <textarea v-model="form.approveDesc" class="progress-textarea" rows="4" placeholder="请输入审核意见" :disabled="isRead" @focus="handleTextareaFocus" @blur="handleTextareaBlur"></textarea>
            </div>
            <div class="form-error content-error" v-if="errors.approveDesc">
              <i class="error-icon"></i>{{ errors.approveDesc }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 - 审批 -->
    <div class="bottom-buttons" v-if="status == 6 && !isRead">
<!--      v-if="form.isRead == 2"-->
      <van-button class="custom-btn-submit" @click="submitForm(6)">同 意</van-button>
      <van-button class="custom-btn-danger" @click="submitForm(7)">驳 回</van-button>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="date" title="选择日期" :min-date="minDate" @confirm="confirmDate"
        @cancel="showDatePicker = false" />
    </van-popup>

    <!-- 分类选择弹窗 -->
    <van-popup v-model="showClassifyPicker" position="bottom">
      <van-picker :columns="classifyOptions" @confirm="onClassifyConfirm" @cancel="showClassifyPicker = false"
        show-toolbar title="选择分类" />
    </van-popup>

    <!-- 部门选择弹窗 -->
    <van-popup v-model="showDeptPicker" position="bottom">
      <van-picker :columns="deptPickerOptions" @confirm="onDeptConfirm" @cancel="showDeptPicker = false" show-toolbar
        title="选择责任部门" />
    </van-popup>

    <!-- 选择用户组件 -->
    <select-user :show.sync="showUserSelector" :multiple="isMultiple" :value="[]" @select="onUserSelected"
      @close="onUserSelectorClose" />
  </div>
</template>

<script>
// http://localhost/rant/todoCenterAddFeedback?todoNoticeId=20
// http://localhost/wechatE/mobile/rant/todoCenterAddFeedback?todoNoticeId=20
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/

import { close } from '@/utils';
import {
  rantDaiBanInfo,
  myRantApprove
} from '@/api/rantMobile/matters'
import SelectUser from '@/views/rantMobile/components/SelectUserMobile.vue'
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listRespdet, getRespdet } from '@/api/rantMobile/respdet'
import RantStep from '@/views/rant/components/RantStep/index.vue'
import mixin from '../mixins'
// 导入Vant组件
import {
  Form,
  Field,
  Button,
  Popup,
  DatetimePicker,
  Picker,
  Toast,
  Icon
} from 'vant';

export default {
  name: 'MyRant',
  mixins: [mixin],
  components: {
    SelectUser,
    StatusTag,
    Treeselect,
    RantStep,
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Picker.name]: Picker,
    [Icon.name]: Icon
  },
  data() {
    return {
      stepActive: 2, // 当前步骤
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: true,
      isHavaSave: true,
      isMultiple: false,
      status: null,
      // 责任部门列表
      deptOptions: [],
      // 日期选择器
      showDatePicker: false,
      currentDate: new Date(),
      minDate: new Date(2000, 0, 1),
      // 分类选择器
      showClassifyPicker: false,
      classifyOptions: [],
      // 部门选择器
      showDeptPicker: false,
      deptPickerOptions: [],
      // 表单错误信息
      errors: {
        ranter: '',
        rantClassify: '',
        responsiblePerson: '',
        rantContent: '',
        deptId: '',
        respDeptResponsibler: '',
        approveDesc: ''
      },
      // 当前滚动位置
      currentScrollTop: 0,
      // 表单参数
      form: {
        "todoNoticeId": null,
        "id": null,
        "ranter": null, // 来源:人(多个用","隔开)
        "ranterName": null, // 来源:人(多个用","隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "status": 0, // 状态（6-同意，7-驳回）
        "approveDesc": '', // 审批意见
      },
      /*  // 表单校验
       rules: {
         ranter: [
           { required: true, message: '来源不能为空', trigger: 'blur' }
         ],
         rantClassify: [
           { required: true, message: '吐槽分类不能为空', trigger: 'blur' }
         ],
         responsiblePerson: [
           { required: true, message: '责任人不能为空', trigger: 'blur' }
         ],
         rantContent: [
           { required: true, message: '吐槽内容不能为空', trigger: 'blur' }
         ],
         deptId: [
           { required: true, message: '责任部门不能为空', trigger: 'blur' }
         ],
         respDeptResponsiblerName: [
           { required: true, message: '部门负责人不能为空', trigger: 'blur' }
         ],
         approveDesc: [
           { required: true, message: '审核意见不能为空', trigger: 'blur' }
         ]
       }, */
      type: '',
      rantId: '',
      showUserSelector: false,
      selectDeptId: null,
      isRead: false
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    }
  },
  watch: {
    'form.ranter': function(newVal) {
      if (newVal) this.errors.ranter = '';
    },
    'form.rantClassify': function(newVal) {
      if (newVal) this.errors.rantClassify = '';
    },
    'form.responsiblePerson': function(newVal) {
      if (newVal) this.errors.responsiblePerson = '';
    },
    'form.deptId': function(newVal) {
      if (newVal) this.errors.deptId = '';
    },
    'form.respDeptResponsibler': function(newVal) {
      if (newVal) this.errors.respDeptResponsibler = '';
    },
    'form.rantContent': function(newVal) {
      if (newVal) this.errors.rantContent = '';
    },
    'form.approveDesc': function(newVal) {
      if (newVal) this.errors.approveDesc = '';
    }
  },
  created() {
    this.todoNoticeId = this.$route.query.todoNoticeId
    this.form.todoNoticeId = this.todoNoticeId;
    this.fetchDaiBanInfo(this.todoNoticeId)
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, 'id')
      // 处理部门选择器数据
      this.prepareDeptPickerOptions(this.deptOptions)
    })
    this.filterRantClassify('rant_classify')
  },

  mounted() {
    // 添加滚动监听，当用户手动滚动时移除聚焦状态
    this.scrollHandler = () => {
      const container = document.querySelector('.app-container-feedback');
      if (container && container.classList.contains('textarea-focused')) {
        // 检查是否有输入框仍然聚焦
        const focusedTextarea = document.activeElement;
        if (!focusedTextarea || focusedTextarea.tagName !== 'TEXTAREA') {
          container.classList.remove('textarea-focused');
        }
      }
    };
    window.addEventListener('scroll', this.scrollHandler, { passive: true });
  },

  beforeDestroy() {
    // 清理滚动监听
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
    }
  },
  methods: {
    fetchDaiBanInfo(todoNoticeId) {
      this.loading = true;
      rantDaiBanInfo(todoNoticeId).then((res) => {
        if (res.code === 200) {
          this.form.id = res.data?.id;
          this.form.ranter = res.data?.ranter;
          this.form.ranterName = res.data?.ranterName;
          this.form.rantClassify = res.data?.rantClassify;
          this.form.rantContent = res.data?.rantContent;
          this.form.planTime = res.data?.planTime;
          this.form.deptId = res.data?.deptId;
          this.form.deptName = res.data?.deptName;
          this.form.responsiblePerson = res.data?.responsiblePerson;
          this.form.responsiblePersonName = res.data?.responsiblePersonName;
          this.form.respDeptResponsibler = res.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = res.data?.respDeptResponsiblerName;
          this.form.approveDesc = res.data?.approveDesc;
          this.status = res.data?.status;
          this.form.solution = res.data?.solution;
          this.form.isRead = res.data?.isRead;
          this.isRead = res.data?.isRead;
          // 初始化日期选择器当前值
          if (this.form.planTime) {
            const parts = this.form.planTime.split('-')
            if (parts.length === 3) {
              this.currentDate = new Date(parts[0], parts[1] - 1, parts[2])
            }
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        Toast.fail('获取数据失败');
      })
    },

    // 确认日期选择
    confirmDate(value) {
      this.form.planTime = this.formatDate(value);
      this.showDatePicker = false;
    },

    // 格式化日期为字符串 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 确认分类选择
    onClassifyConfirm(value) {
      console.log('选择的分类', value);
      // Vant Picker返回的是整个选项对象，所以我们需要从中提取value
      if (typeof value === 'object') {
        this.form.rantClassify = value.value;
      } else {
        this.form.rantClassify = value;
      }
      this.showClassifyPicker = false;
    },

    // 准备部门选择器数据
    prepareDeptPickerOptions(deptList) {
      console.log('准备部门选择器数据:', deptList);
      const flattenDepts = (list, prefix = '') => {
        return list.reduce((acc, dept) => {
          // 使用name字段，因为后端返回的是name而不是label
          const deptName = prefix ? `${prefix} / ${dept.name || dept.label}` : (dept.name || dept.label);
          acc.push({
            text: deptName,
            value: dept.id
          });

          if (dept.children && dept.children.length > 0) {
            acc = acc.concat(flattenDepts(dept.children, deptName));
          }

          return acc;
        }, []);
      };

      this.deptPickerOptions = flattenDepts(deptList);
      console.log('部门选择器数据:', this.deptPickerOptions);
    },

    // 确认部门选择
    onDeptConfirm(value) {
      console.log('选择的部门:', value);
      this.form.deptId = value.value;
      this.form.deptName = value.text;
      this.showDeptPicker = false;
      this.selectDept(value.value);
    },

    // 选择部门负责人
    selectUserFun(type) {
      if(this.isRead) {
        return;
      }
      this.selectUserType = type;

      if (type === 'ranter') {
        this.roleName = 'rantManager';
        this.isMultiple = true;
        this.selectDeptId = undefined;
        this.showUserSelector = true;
      } else if (type === 'responsibilityer') {
        // 如果已经从API获取了deptId但尚未在界面选择
        // 也允许选择责任人
        const hasDeptId = this.form.deptId || (this.deptPickerOptions && this.deptPickerOptions.length > 0);

        if (!hasDeptId) {
          Toast('请先选择责任部门');
          return false;
        }

        this.isMultiple = false;
        this.selectDeptId = this.form.deptId;
        console.log('选择责任人，部门ID:', this.selectDeptId);
        this.showUserSelector = true;
      }
    },

    // 选择用户回调
    selectUserData(data) {
      if (this.selectUserType === 'ranter') {
        this.form.ranter = data.userId;
        this.form.ranterName = data.nickName;
      } else if (this.selectUserType === 'responsibilityer') {
        this.form.responsiblePerson = data.userId;
        this.form.responsiblePersonName = data.nickName;
      }
    },

    // 新增用户选择处理方法
    onUserSelected(selectedUsers) {
      console.log('选择的用户:', selectedUsers);
      if (selectedUsers && selectedUsers.length > 0) {
        const user = selectedUsers[0];
        this.selectUserData({
          userId: user.userId,
          nickName: user.nickName
        });
      }
    },

    // 用户选择器关闭
    onUserSelectorClose() {
      this.showUserSelector = false;
    },

    // 选择责任部门回调
    selectDept(deptId) {
      if (!deptId) return;
      getRespdet(deptId).then(response => {
        console.log('获取部门负责人信息:', response);
        if (response.code === 200 && response.data) {
          this.form.respDeptResponsibler = response.data.respPeople || response.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = response.data.respPeopleName || response.data.respDeptResponsiblerName;
        }
      }).catch(err => {
        Toast('获取部门负责人信息失败');
      });
    },

    // 表单提交
    submitForm(status) {
      // 清空所有错误信息
      Object.keys(this.errors).forEach(key => {
        this.errors[key] = '';
      });
      let isValid = true;


      if (status === 6) { // 同意

        if (!this.form.ranter) {
          this.errors.ranter = '请选择来源';
          isValid = false;
        }

        if (!this.form.rantClassify) {
          this.errors.rantClassify = '请选择吐槽分类';
          isValid = false;
        }

        if (!this.form.responsiblePerson) {
          this.errors.responsiblePerson = '请选择责任人';
          isValid = false;
        }

        if (!this.form.deptId) {
          this.errors.deptId = '请选择责任部门';
          isValid = false;
        }

        if (!this.form.respDeptResponsibler) {
          this.errors.respDeptResponsibler = '请确保已选择有负责人的部门';
          isValid = false;
        }

        if (!this.form.rantContent) {
          this.errors.rantContent = '请输入内容';
          isValid = false;
        }
      }

      // 状态为7(驳回)时，审核意见必填
      else if (status === 7 && !this.form.approveDesc) {
        this.errors.approveDesc = '驳回时审核意见必填';
        isValid = false;
      }

      if (!isValid) {
        return;
      }

      this.form.status = status;
      this.loading = true;

      myRantApprove(this.form).then(response => {
        if (response.code === 200) {
          Toast.success(status === 6 ? '已同意，即将关闭页面' : '已驳回，即将关闭页面');
          setTimeout(() => {
            close();
          }, 1500);
        } else {
          Toast.fail(response.msg || '操作失败');
        }
      }).catch(() => {
        Toast.fail('操作失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // Vant Form 提交方法
    onSubmit(values) {
      console.log('提交表单', values);
    },

    // 处理文本框获得焦点事件
    handleTextareaFocus(event) {
      // 保存当前滚动位置
      this.currentScrollTop = document.documentElement.scrollTop || document.body.scrollTop;

             // 添加CSS类来增加底部空间
       const container = document.querySelector('.app-container-feedback');
       if (container) {
         // 检测是否是审核意见输入框
         const isApproveDesc = event.target.value === this.form.approveDesc;

         if (isApproveDesc) {
           // 审核意见输入框需要更多空间
           container.classList.add('textarea-focused-bottom');
         } else {
           container.classList.add('textarea-focused');
         }
       }

             // 延迟执行，等待DOM更新和浏览器默认滚动完成后再调整
        setTimeout(() => {
          // 计算输入框位置
          const textarea = event.target;
          const rect = textarea.getBoundingClientRect();
          const viewportHeight = window.innerHeight;

                     // 计算合适的滚动位置
           // 检测是否是审核意见输入框（通过检查是否绑定了approveDesc）
           const isApproveDesc = textarea.getAttribute('v-model') === 'form.approveDesc' ||
                                textarea.value === this.form.approveDesc;

           // 获取页面总高度和输入框在页面中的相对位置
           const pageHeight = document.documentElement.scrollHeight;
           const textareaPageTop = rect.top + window.pageYOffset;
           const relativePosition = textareaPageTop / pageHeight;

           let targetTop;

           // 如果是审核意见或者输入框在页面底部80%以下的位置
           if (isApproveDesc || relativePosition > 0.8) {
             // 底部输入框：移动到屏幕上方5%的位置，提供最大滚动距离
             targetTop = rect.top + window.pageYOffset - (viewportHeight * 0.05);
             console.log('检测到底部输入框，使用最大滚动距离');
           } else if (rect.top > viewportHeight * 0.5) {
             // 普通下半屏输入框：移动到屏幕上方10%
             targetTop = rect.top + window.pageYOffset - (viewportHeight * 0.1);
           } else {
             // 上半屏输入框：移动到屏幕上方15%
             targetTop = rect.top + window.pageYOffset - (viewportHeight * 0.15);
           }

           // 对于底部输入框，确保最少滚动400px
           const currentScrollTop = window.pageYOffset;
           const minScrollDistance = (isApproveDesc || relativePosition > 0.8) ? 400 : 200;
           if (targetTop - currentScrollTop < minScrollDistance && targetTop > currentScrollTop) {
             targetTop = currentScrollTop + minScrollDistance;
           }

           // 调试信息（可在生产环境中移除）
           console.log('滚动计算:', {
             '是否审核意见': isApproveDesc,
             '输入框顶部位置': rect.top,
             '页面相对位置': (relativePosition * 100).toFixed(1) + '%',
             '当前滚动位置': currentScrollTop,
             '视窗高度': viewportHeight,
             '计算目标位置': targetTop,
             '滚动距离': targetTop - currentScrollTop,
             '最小滚动距离': minScrollDistance
           });

           // 平滑滚动到目标位置
           window.scrollTo({
             top: Math.max(0, targetTop),
             behavior: 'smooth'
           });
        }, 300); // 增加延迟以等待CSS动画完成
    },

    // 处理文本框失去焦点事件
    handleTextareaBlur(event) {
      // 移除CSS类来恢复底部空间
      const container = document.querySelector('.app-container-feedback');
      if (container) {
        container.classList.remove('textarea-focused');
        container.classList.remove('textarea-focused-bottom');
      }

      // 延迟一点时间再滚动回原位置，避免键盘收起时的闪烁
      setTimeout(() => {
        // 可以选择是否滚动回原位置
        // window.scrollTo({
        //   top: this.currentScrollTop,
        //   behavior: 'smooth'
        // });
      }, 100);
    },

    // 取消按钮
    cancel() {
      close();
    },

    // ... 保留其他原有方法
    handleTree(data, id, parentId, children, rootId) {
      id = id || 'id'
      parentId = parentId || 'parentId'
      children = children || 'children'
      rootId = rootId || Math.min.apply(Math, data.map(item => { return item[parentId] })) || 0
      //对源数据深度克隆
      const cloneData = JSON.parse(JSON.stringify(data))
      //循环所有项
      const treeData = cloneData.filter(father => {
        let branchArr = cloneData.filter(child => {
          //返回每一项的子级数组
          return father[id] === child[parentId]
        });
        branchArr.length > 0 ? father.children = branchArr : '';
        //返回第一层
        return father[parentId] === rootId;
      });
      return treeData != '' ? treeData : data;
    },

    normalizer(node) {
      return {
        id: node.id,
        label: node.name || node.deptName, // 使用name字段，兼容deptName
        value: node.id,
        children: node.children
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../css/common.scss';

.form-item {
  margin-bottom: 0px !important;
}

.app-container-feedback {
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
  padding: 20px 16px;
  padding-bottom: 60px;
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  /* 防止过度滚动 */
  overscroll-behavior: contain;
  /* 为输入框获得焦点时留出额外空间 */
  margin-bottom: 0;
  transition: margin-bottom 0.3s ease;

  &.textarea-focused {
    margin-bottom: 60vh; /* 当输入框获得焦点时增加底部空间 */
    padding-bottom: 150px;
  }

  &.textarea-focused-bottom {
    margin-bottom: 80vh; /* 底部输入框需要更多空间 */
    padding-bottom: 200px;
  }
}

.step-box {
  margin-bottom: 16px;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .custom-btn-submit {
    width: 160px;
    height: 40px;
    background: #3673FF;
    color: #FFFFFF;
    border-radius: 20px;
  }

  .custom-btn-danger {
    width: 160px;
    height: 40px;
    background: #FFFFFF;
    color: #ee0a24;
    border: 1px solid #ee0a24;
    border-radius: 20px;
  }
}

.form-value {
  &.align-right {
    color: #3673FF;
    text-align: right;
  }
}

:deep(.van-field__control) {
  background-color: #F1F7FE;
  min-height: 30px;
  font-size: 14px;
  color: #333;
}

.clickable {
  position: relative;
  padding-right: 20px;

  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-top: 1px solid #999;
    border-right: 1px solid #999;
    transform: translateY(-50%) rotate(45deg);
  }
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.progress-textarea {
  width: 100%;
  border: 1px solid rgba(55, 109, 247, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F1F7FE;
  min-height: 80px;
  font-size: 14px;
  color: #333333;
  outline: none;
  resize: vertical;
  /* 防止iOS自动缩放 */
  font-size: 16px;
  /* 优化滚动行为 */
  scroll-margin-top: 20vh;
  scroll-margin-bottom: 20vh;

  &:focus {
    border-color: #3673FF;
    /* 防止默认的滚动行为过于激进 */
    scroll-behavior: smooth;
  }
}

.form-value {
  text-align: right !important;
}

.required-mark {
  color: #ee0a24;
  margin-right: 2px;
}

/* 表单错误消息样式 */
:deep(.van-field__error-message) {
  color: #ee0a24;
  font-size: 12px;
  text-align: left;
  margin-top: 4px;
}

.form-item {
  position: relative;
  margin-bottom: 20px;
}

.form-error {
  color: #ee0a24;
  font-size: 12px;
  margin-top: 2px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.content-error {
  padding-left: 0;
}

.error-icon {
  display: inline-block;
  // width: 12px;
  // height: 12px;
  margin-right: 5px;
  position: relative;

  &:before {
    content: "!";
    position: absolute;
    left: 4px;
    top: -1px;
    color: white;
    font-size: 9px;
    font-weight: bold;
  }

  /* &:after {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: #ee0a24;
    border-radius: 50%;
  } */
}
</style>
