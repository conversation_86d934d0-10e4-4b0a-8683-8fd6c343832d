<template>
    <div class="weekly-index">
        <div class="content">
            <WeeklyApproveForm ref="weeklyFormRef" :action="type"></WeeklyApproveForm>
        </div>
        <div class="bottom-button">
          <van-button type="primary" class="primary-btn btn" @click="handleReject">驳回</van-button>
          <van-button type="primary" v-if="weeklyForm.approveStatus === 1 || weeklyForm.approveStatus === 5" class="primary-btn btn" @click="handleApprove">同意</van-button>
        </div>
    </div>
</template>
<script>
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}
// http://localhost/wechatE/mobile/weeklyInput
import WeeklyApproveForm from './components/weeklyApproveForm.vue'
import { getInfo, approveWeekly, weeklyReject } from "@/api/weekly/mobile-reportInfo";
import { Cell, CellGroup, Col, Row, Field, Form, FormItem, Input, Button,Popup,Picker, Icon, Sticky} from "vant";
export default {
    name: "WeeklyIndex",
    components: {
        WeeklyApproveForm,
        [Cell.name]: Cell,
        [CellGroup.name]: CellGroup,
        [Col.name]: Col,
        [Row.name]: Row,
        [Field.name]: Field,
        [Button.name]: Button,
        [Popup.name]: Popup,
        [Picker.name]: Picker,
        [Icon.name]: Icon,
        [Sticky.name]: Sticky,
    },
    data() {
        return {
          form: {},
          loading: false,
          type: "", // 新增还是编辑模式
          id: "", // 周报ID
          weeklyForm: {},
        };
    },
  created() {
    // 获取路由参数
    const { id } = this.$route.params;
    this.id = id;
  },
  mounted() {
    this.getWeeklyDetail();
  },
  methods: {
    handleReject() {
      this.loading = true;
      if (!this.$refs.weeklyFormRef.getFormData().approveDesc) {
        this.$modal.msgWarning("请输入审阅意见");
        this.loading = false;
        return;
      }
      weeklyReject({
        id: this.$route.params.id,
        approveDesc: this.$refs.weeklyFormRef.getFormData().approveDesc,
      })
        .then((response) => {
          this.$modal.msgSuccess("驳回成功");
          this.closeCurrentPage(this.fromPath);

        })
        .catch(() => {
          // this.$modal.msgError("审阅失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleApprove() {
      this.loading = true;
      approveWeekly({
        id: this.$route.params.id,
        approveDesc: this.weeklyForm.approveDesc,
      })
        .then((response) => {
          this.$modal.msgSuccess("审阅成功");
          // this.handleGetData();
          // 从待办中心审阅，审阅完成后只刷新当前页面
          // 从我的审阅进入审阅，审阅完成后关闭当前页面并返回到我的审阅，刷新我的审阅列表
         /*  if (this.fromPath) {
            this.closeCurrentPage(this.fromPath);
          }
          else {
            close();
          } */
          this.closeCurrentPage(this.fromPath);
        })
        .catch(() => {
          // this.$modal.msgError("审阅失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getWeeklyDetail() {
      this.loading = true;
      try {
        const { data } = await getInfo(this.id);
        this.weeklyForm = data;
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        this.weeklyForm.reflectInfo = data.reflectInfo || {type: 4, progressScheme: "" };
        this.$refs.weeklyFormRef.updateFormData(data);
      } catch (error) {
        // this.$modal.msgError("获取详情失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    closeCurrentPage() {
      // this.$tab.closeOpenPage({ path: "/weekly/myWeekly" });
      this.$router.push("/wechatE/mobile/approveList");
    },
    cancelHandle() {
      this.closeCurrentPage();
    }
  },
};
</script>
<style scoped lang="scss">
:deep(.van-button--primary) {
  background-color: #007aff;
  border-color: #007aff;
}
.weekly-index {
    width: 100%;
    // height: 100%;
    min-height: 100vh;
    overflow-y: auto;
    background-image: url("~@/assets/weeklyMobile/weeklybac.png");
    background-size: cover;
    background-repeat: no-repeat;

    .header {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 17px;
        color: #333333;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        padding: 11px 0;
        //margin-bottom: 60px;
    }
    .content{
        padding: 16px 10px;
        .content-item{
            .content-item-image{
                width: 100%;
                margin-bottom: 42px;
            }
        }
    }
    .bottom-button{
      padding: 20px 10px;
      display: flex;
      gap : 10px;
      &.primary-btn{
        background: #3673FF;
      }
      .btn{
        flex: 1;
      }
    }
}
</style>
