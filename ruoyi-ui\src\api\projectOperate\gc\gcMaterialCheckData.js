import request from '@/utils/request'

// 查询材料飞检列表
export function listData(query) {
  return request({
    url: '/project/gcMaterialCheckData/list',
    method: 'get',
    params: query
  })
}

// 查询材料飞检详细
export function getData(id) {
  return request({
    url: '/project/gcMaterialCheckData/' + id,
    method: 'get'
  })
}

// 新增材料飞检
export function addData(data) {
  return request({
    url: '/project/gcMaterialCheckData',
    method: 'post',
    data: data
  })
}

// 修改材料飞检
export function updateData(data) {
  return request({
    url: '/project/gcMaterialCheckData',
    method: 'put',
    data: data
  })
}

// 删除材料飞检
export function delData(id) {
  return request({
    url: '/project/gcMaterialCheckData/' + id,
    method: 'delete'
  })
}

// 文件导入
export function importFile(data) {
  return request({
    url: '/project/gcMaterialCheckData/import',
    method: 'post',
    data: data
  })
}

// 过程评估导入数据提交
export function importDataSubmit(data) {
  return request({
    url: '/project/gcMaterialCheckData/importDataSubmit',
    method: 'post',
    data: data
  })
}
