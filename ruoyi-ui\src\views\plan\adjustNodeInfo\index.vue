<template>
  <div>
    <div class="app-container" :style="{ '--color': theme }">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="节点名称" prop="nodeName">
          <el-input
            v-model="queryParams.nodeName"
            placeholder="请输入节点名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          <el-input
            style="position: absolute; right: -100000px;"
            v-model="queryParams.nodeName"
            placeholder="请输入节点名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
<!--          人员调整 新增和增量导入都隐藏-->
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-if="adjustType != 2"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            >导出</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload"
            size="mini"
            :disabled="planUseTemplate"
            @click="importFile(1)"
            >覆盖导入</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload"
            size="mini"
            @click="importFile(2)"
            v-if="adjustType != 2"
          >增量导入</el-button
          >
        </el-col>

        <el-col :span="3">
          <el-button
            icon="el-icon-plus"
            type="primary"
            plain
            size="mini"
            @click="selectNode"
            >选择节点</el-button
          >
        </el-col>

        <el-col :span="11">
          <div class="custom--title icon-primary"><span>当前计划：</span>{{ $route.query.planName }}</div>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <div v-loading="loading">
        <el-table
          :data="nodeFilterList"
          @selection-change="handleSelectionChange"
          ref="table"
          max-height="500"
        >
          <el-table-column type="selection" width="45" align="center" />
          <el-table-column label="序号" align="center" prop="id" width="60">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <status-tag
                :status="scope.row.status"
                :options="nodeStatusOption"
              />
            </template>
          </el-table-column>
          <el-table-column label="节点名称" align="center" prop="nodeName" />
          <el-table-column
            v-if="adjustType == 1"
            label="计划开始时间"
            align="center"
            prop="startTime"
            width="150"
          >
            <template slot-scope="scope">
              <div class="time">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="scope.row.startTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="计划开始时间"
                  @change="changeData($event,scope.$index,1)"

                >
                </el-date-picker>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="adjustType == 1"
            label="计划完成时间"
            align="center"
            prop="endTime"
            width="150"
          >
            <template slot-scope="scope">
              <div class="time">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="scope.row.endTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="计划完成时间"
                  @change="changeData($event,scope.$index,2)"
                >
                </el-date-picker>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作状态" align="center" prop="adjustType">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.adjustType == 1"
                >修改</el-tag
              >
              <el-tag v-if="scope.row.adjustType == 2">新增</el-tag>
              <el-tag type="info" v-if="scope.row.adjustType == 0"
                >未调整</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="责任部门" align="center" prop="departmentArr" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.departmentArr" :disabled="adjustType != 1" multiple placeholder="请选择" size="small"  @change="selectChange($event,scope.$index)">
                <el-option
                  v-for="item in departmentsData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            label="反馈人"
            align="center"
            prop="feedbackUserName"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.feedbackUserName"
                class="pointer"
                @click="clickFeedback(scope.$index)"
              >
                <el-link type="primary">
                  {{ scope.row.feedbackUserName }}
                </el-link>
              </div>
              <el-button v-else type="text" @click="clickFeedback(scope.$index)"
                >选择</el-button >
            </template>
          </el-table-column>
          <el-table-column label="是否可用" align="center" prop="isValid">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.isValid"
                clearable
                :disabled="(scope.row.isGrey == 0 && scope.row.isValid == 1) || adjustType == 2"
              >
                <el-option
                  v-for="option in isValidOption"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            class-name="hidden-column"
            min-width="150"
            label-class-name="hidden-column"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row, scope.$index)"
                >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-close"
                @click="handleRemove(scope.row, scope.$index)"
                >移除
              </el-button>
              <i
                v-if="scope.$index !== 0"
                class="el-icon-top iconStyle"
                @click="moveUp(scope.$index)"
              >
              </i>
              <i
                class="el-icon-bottom iconStyle"
                style="margin-left: 5px"
                @click="moveDown(scope.$index)"
                v-if="scope.$index < nodeFilterList.length - 1"
              ></i>
            </template>
          </el-table-column>
        </el-table>

        <select-user
          ref="userRef"
          :roleId="roleName"
          @feedbackEmit="selectFeedbackData"
        />
        <input
          type="file"
          ref="fileInput"
          style="display: none"
          @change="handleFileChange"
        />
        <div
          slot="footer"
          class="dialog-footer"
          style="margin-top: 10px; margin-left: 20px"
        >
          <el-button
            v-if="planStatus != 5"
            type="primary"
            plain
            @click="saveFun"
            style="margin-right: 10px"
            >保存</el-button
          >
            <el-button slot="reference" type="primary" @click="submitApprovalHandle">提交</el-button>
          <el-button
            type="warning"
            plain
            @click="handleClose"
            style="margin-left: 10px"
            >关闭</el-button
          >
        </div>
      </div>
    </div>
    <add-node-form
      ref="nodeFormRef"
      @cancel="cancel"
      @handleFormFun="handleFormFun"
      @clickDepart="clickDepart"
      @clickFeedback="selectFeedbackFun"
      @clearFeedbackUserInfo="clearFeedbackUserInfo"
      :departmentsData="departmentsData"
      :adjustType="adjustType"
    ></add-node-form>
    <select-node-info
      ref="nodeInfoRef"
      :defaultSelect="defaultSelect"
      :adjustId="adjustId"
      @handleData="handleData"
    ></select-node-info>
    <node-sort-before-submit ref="nodeSortRef" @submit="submitApproval"></node-sort-before-submit>
  </div>
</template>

<script>
import {
  adjustList,
  adjustSave,
  adjustSubmit,
  importAdjustNodeFile
} from "@/api/plan/node"
import { getAdjustInfo } from '@/api/plan/info'
import { getRespPeople } from '@/api/plan/dept'
import SelectTree from '@/components/SelectTree.vue'
import {isValidOption} from '@/views/plan/constant'
import variables from '@/assets/styles/element-variables.scss'
import SelectUser from "../components/SelectUser/index.vue"
import SelectNodeInfo from "./selectNodeInfo.vue"
import AddNodeForm from "./addNodeForm.vue"
import { nodeStatusOption } from '@/constant'
import StatusTag from "@/views/plan/components/StatusTag/index.vue"
import NodeSortBeforeSubmit from "@/views/plan/components/NodeSortBeforeSubmit/index.vue"
export default {
  name: "adjustNodeInfo",
  components: {
    SelectTree,
    SelectUser,
    SelectNodeInfo,
    AddNodeForm,
    StatusTag,
    NodeSortBeforeSubmit
  },
  props: [],
  // dicts: ['levels'],
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      // 是否显示弹出层
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 20,
        nodeLevel: null,
        nodeName: null,
        planId: null,
      },
      editIndex: null,
      clickIndex: null,
      /*专业成果*/
      resultList: [],
      showResultDialog: false,
      ids2: [],
      //责任人
      respectPeople: [],
      resultConfigIds: [],
      theme: variables.theme,
      showDepart: false,
      planId: null,
      selectData: [],
      selectIds: [],
      planStatus: 0,
      departmentsData:[],
      paramData:null,
      planUseTemplate: false, // 计划是否引用模板
      adjustType: 1,
      adjustId: null,
    }
  },
  created () {
    this.planStatus = this.$route.query.planStatus
    this.show()
    this.getDepartFun()
    this.nodeInfo()
    /*调整类型 （1-节点调整  2-人员调整）
    当选择人员调整时，弹出界面，两个时间字段隐藏，且只改人员，
    其他字段置灰，且不生成新的版本号，还是原来版本号；当选择节点调整时，生成新的版本号，逻辑跟现在一致；*/
    this.adjustType = this.$route.query.adjustType
  },
  computed: {
    //提交的时候 删除的数据界面不显示
    nodeFilterList: function () {
      return this.nodeList
    },
    isValidOption: () => isValidOption,
    roleName: function () {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        return this.$refs.nodeFormRef.form && this.$refs.nodeFormRef.form.feedbackUser
      } else { // 处理表单里面数据
        // console.log('computed===',this.nodeList[this.clickIndex])
        if(!this.nodeList[this.clickIndex])return;
        if (typeof (this.clickIndex) == 'number' && this.nodeList[this.clickIndex].feedbackUserName) {
          return this.nodeList[this.clickIndex].feedbackUser
        }
      }
    },
    defaultSelect: function () {
      return this.nodeList.map(item => item.nodeCode)
    },
    nodeStatusOption: function () {
      return nodeStatusOption
    }
  },
  watch: {},
  methods: {
    changeData(val,index,type){
      this.clickIndex=index
      let item=this.nodeList[this.clickIndex]
      if(!item.startTime||!item.endTime)return null;
      let date1 = new Date(item.startTime);
      let date2 = new Date(item.endTime);
      if (type==1&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('开始时间不能大于结束时间')
        item.startTime=null
      } else if (type==2&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('结束时间不能小于开始时间')
        item.endTime=null
      }
    },
    ///节点信息
    nodeInfo(){
      getAdjustInfo(this.$route.query.planId).then(response => {
        let data=response.data
        this.adjustId = data.adjustId;
        this.paramData = {
          cityCompanyNum: data.deptNum,//城市公司
          projectId: data.projectId,//项目
          stageId: data.stageId, //分期
          respDept: "" //部门
        }
        //  判断计划是否引用模板
        // this.planUseTemplate = !!data.templateId
      })
    },
    //获取默认反馈人
    getDefautlFeedback(type){
      getRespPeople({...this.paramData}).then(response => {
        let data=response.data
        if(type){ //更新列表的反馈人
          this.nodeList[this.clickIndex].feedbackUser=data.respPeople
          this.nodeList[this.clickIndex].feedbackUserName = data.respPeopleName
          this.nodeList=[...this.nodeList]
        }else{
        this.$refs.nodeFormRef.form.feedbackUser = data.respPeople //特殊情况传的ID 正常传的账户
        this.$refs.nodeFormRef.form.feedbackUserName = data.respPeopleName
        }
      })
    },
    selectChange(val,index){
      this.clickIndex=index;
      let node=this.nodeList[this.clickIndex]
      if(val.length==0||val.length==1||(node.department.indexOf(val[0])>0)){ //没选 或者多选变更了第一个选择
        this.paramData.respDept=val[0]||'';
        this.getDefautlFeedback(1)
      }
      if (typeof (this.clickIndex) == 'number') {
        node.departmentNameArr=this.departmentsData.reduce((pre,curr,index)=>{
          if(val.includes(curr.value)){
            pre.push(curr.label)
          }
          return pre;
            },[])
        node.department = val.join(',')
        node.departmentNames = node.departmentNameArr.join(',')
      }

    },
    getDepartFun(){
      this.getDicts('resp_department').then(res => {
        let data = res.data
        return this.departmentsData = data.map(item => {
            return {
              label: item.dictLabel,
              value: item.dictValue,
            }
          }
        )
      }).catch(err => {
        console.log(err)
      })},
    submitApprovalHandle(){
      if(!this.nodeList.length){
        this.$message.warning('数据为空')
        return
      }
      if (this.validFun()) {
        return
      }
      const updateData = this.nodeFilterList.filter(item => item.adjustType === 1) // 修改节点
      // 如果先保存，那么新增节点就有id，但是节点的状态还是新增，这里要处理
      const addData = this.nodeFilterList.filter(item => item.adjustType === 2 && !item.id) // 新增节点
      this.$refs.nodeSortRef.show(this.queryParams.planId, updateData, addData)

      /*this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitApproval()
      }).catch(() => {
      });*/
    },
    //提交审批
    submitApproval (planNodeList) {
      this.loading = true
      adjustSubmit({
        id: this.queryParams.planId,
        planNodeList,
        adjustType: this.adjustType
      }).then(res => {
        this.$refs.nodeSortRef.setSubmitLoading(false);
        this.loading = false
        this.$message.success("提交成功")
        this.$bus.$emit('refreshAdjustList')
        //可编辑页面需要先打开一个页面用于登陆
        window.open(res.data)
        this.cancel()
        //关闭当前页面
        this.handleClose()
      }).catch(err => {
        // this.$emit('cancel')
        this.$refs.nodeSortRef.setSubmitLoading(false);
        this.loading = false
      })
    },
    validFun () {
      let nodes = this.nodeList
      let isReturn = false
      for (let i = 0; i < nodes.length; i++) {
        let index = i + 1

        if (!nodes[i].startTime) {
          isReturn = true
          this.$message.warning('序号为' + index + '开始时间为空')
          break
        }
        if (!nodes[i].endTime) {
          isReturn = true
          this.$message.warning('序号为' + index + '结束时间为空')
          break
        }
        if (!nodes[i].department) {
          isReturn = true
          this.$message.warning('序号为' + index + '责任部门为空')
          break
        }
        if (!nodes[i].feedbackUserName) {
          isReturn = true
          this.$message.warning('序号为' + index + '反馈人为空')
          break
        }
      }
      return isReturn
    },
    saveFun () {
      if(!this.nodeList.length){
        this.$message.warning('数据为空')
        return
      }
      // //保存至本地
     /* if (this.validFun()) {
        return
      }*/
      this.loading = true
      adjustSave({
        id: this.queryParams.planId,
        planNodeList: this.nodeList,
        adjustType: this.adjustType
      }).then(res => {
        this.loading = false
        // 获取当前URL
        let url = window.location.href
        // 修改浏览器历史记录中的当前状态，并同时改变地址栏的URL，而不会刷新页面
        history.pushState(null, null, url.replace('planId=' + this.queryParams.planId, 'planId=' + res.data))
        this.queryParams.planId = res.data
        //刷新调整列表
        this.getList()
      }).catch(err => {
        console.log(err)
        this.loading = false
      })
    },
    handleData (data) {
      this.nodeList = [...this.nodeList, ...data]
    },
    selectNode () {
      this.$refs.nodeInfoRef.show()
    },
    handleFormFun (data) {
      let node=JSON.parse(JSON.stringify(data))
      node.departmentNameArr=this.departmentsData.reduce((pre,curr,index)=>{
          if(node.departmentArr.includes(curr.value)){
            pre.push(curr.label)
          }
          return pre;
        },[])
        node.department = node.departmentArr.join(',')
        node.departmentNames = node.departmentNameArr.join(',')

      //表单组件 更改table 数据
      //修改
      if (data.id != null) {
        this.nodeList.splice(this.editIndex, 1, node)
      } else {
        //新增后又编辑
        if (typeof (this.editIndex) == 'number') {
          this.nodeList.splice(this.editIndex, 1, node)
        } else {
          //新增
          this.nodeList.push(node)
        }
      }
      this.editIndex = null
    },
    clearFeedbackUserInfo(){
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.$refs.nodeFormRef.form.feedbackUser = ''
        this.$refs.nodeFormRef.form.feedbackUserName = ''
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].feedbackUser = ''
          this.nodeList[this.clickIndex].feedbackUserName = ''
        }
      }
    },
    selectFeedbackData (data) {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.$refs.nodeFormRef.form.feedbackUser = data.userName
        this.$refs.nodeFormRef.form.feedbackUserName = data.nickName
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].feedbackUser = data.userName
          this.nodeList[this.clickIndex].feedbackUserName = data.nickName
        }
      }
    },
    selectDepartData (data) {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.$refs.nodeFormRef.form.departmentNames = data.map(item => item.departName).join(',')
        this.$refs.nodeFormRef.form.department = data.map(item => item.department).join(',')
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].department = data.map(item => item.department).join(',')
          this.nodeList[this.clickIndex].departmentNames = data.map(item => item.departName).join(',')
        }
      }
    },
    // 导入节点
    importFile (type) {
      this.importType=type
      this.$refs.fileInput.click() // 触发文件输入的点击事件
    },
    handleFileChange (event) {
      const files = event.target.files
      const formData = new FormData()
      formData.append('file', files[0])
      formData.append('type', this.importType)
      formData.append('planId', this.queryParams.planId)
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null
      this.loading=true
      importAdjustNodeFile(formData).then(response => {
        this.loading=false
        let da = response.data

        if(this.importType==1){
          da.map(item=>{
            if (item.adjustType == null || item.adjustType ==undefined) {
              item.adjustType=2
            }
            // item.status=0
            item.departmentArr=item.department.length?item.department.split(','):[]
          })
          this.nodeList = []
          this.nodeList.push(...da)
        }else{
          da.map(item=>{
             item.adjustType=2
            // item.status=0
            item.departmentArr=item.department.length?item.department.split(','):[]
          })
          this.nodeList=[...this.nodeList,...da]
        }
      }).catch(err=>{
        this.loading=false
      })
    },
    moveUp (index) {
      if (index > 0) {
        const row = this.nodeFilterList.splice(index, 1)[0]
        this.nodeFilterList.splice(index - 1, 0, row)
      }
    },
    moveDown (index) {
      if (index < this.nodeFilterList.length - 1) {
        const row = this.nodeFilterList.splice(index, 1)[0]
        this.nodeFilterList.splice(index + 1, 0, row)
      }
    },
    clickDepart (val) {
      this.paramData.respDept=val[0]||'';
      if(val.length==0||val.length==1||this.singlevalue1!=val[0]){ //没选 或者多选变更了第一个选择
        this.getDefautlFeedback()
      }
      this.singlevalue1=val[0]
    },
    clickFeedback (index) {
      this.clickIndex = index
      setTimeout(() => {
        this.selectFeedbackFun()
      }, 800)
    },
    selectFeedbackFun () {
      this.$refs.userRef.show()
    },
    getList () {
      this.loading = true
      adjustList(this.queryParams).then(response => {
        let da = response.rows
        da.map(item=>{
          item.departmentArr=item.department.length?item.department.split(','):[]
        })
        this.nodeList=[...da]
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.editIndex = null
    },
    /** 搜索按钮操作 */
    handleQuery () {
      // this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.multiple = selection.length > 0 ? false : true
      this.ids = selection.map(item => item.id)
      this.selectData = [...selection]
    },

    /** 新增按钮操作 */
    async handleAdd () {
      await this.getDepartFun()
      this.$refs.nodeFormRef.show(0)
      this.editIndex = true
    },
    /** 修改按钮操作 */
    async handleUpdate (row, index) {
      await this.getDepartFun()
      let data = JSON.parse(JSON.stringify(row))
      this.$refs.nodeFormRef.show(1, data)
      this.editIndex = index
      this.handleFormData()
    },
    handleFormData(){
      let form=this.$refs.nodeFormRef.form
      // this.form.departmentArr departmentNames
      form.departmentNameArr=this.departmentsData.filter(item=>form.departmentArr.includes(item.value))
      form.department=form.departmentArr.join(',')
      form.departmentNames=form.departmentNameArr.join(',')
    },
    handleRemove (row, index) {
      let message = '是否移除计划节点名称为' + row.nodeName + '的数据项？'
      let that = this
      this.$modal.confirm(message).then(function () {
        let nodes = that.nodeList
        that.nodeList = nodes.filter((item,indexK) => indexK != index)
      }).catch((err) => { })
    },

    /** 导出按钮操作 */
    handleExport () {
      this.download('plan/node/exportAdjustmentNodeList', {
        'nodeList': JSON.stringify(this.nodeList),
        'adjustType': this.adjustType
      }, `node_${new Date().getTime()}.xlsx`)
    },
    show () {
      this.clickIndex = null
      this.queryParams.planId = this.$route.query.planId
      this.getList()
    },
    //关闭当前页面
    handleClose () {
      const obj = {
        path: "/plan/plans/adjustment"
      }
      this.$tab.closeOpenPage(obj)
    },
  },
};
</script>
<style>
.hidden-column {
  border: none;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";

.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 140px;
}

.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
  .custom--title{
    padding: 3px;
    font-weight: 700;
    color: #409eff;
    //background-color: #f8f8f9;

}
</style>
