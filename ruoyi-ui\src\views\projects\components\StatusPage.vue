<template>
    <div class="content-container">
      <!-- 无权限状态 -->
      <div v-if="!hasPermission && permissionCode" class="empty-status">
        <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
        <div class="desc">暂无权限</div>
        <div class="desc">请联系管理员</div>
      </div>

      <!-- 有权限时显示的内容 -->
      <div v-else>
        <!-- 开发中状态 -->
        <div v-if="isDeveloping" class="empty-status">
          <img src="@/views/projects/assets/images/developing.png" alt="开发中">
          <div class="desc">正在开发中</div>
          <div class="desc">敬请期待</div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="!hasData" class="empty-status">
          <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
          <div class="desc">暂无数据</div>
        </div>

        <!-- 有数据时显示实际内容 -->
        <slot v-else></slot>
      </div>
    </div>
  </template>

  <script>
  export default {
    name: 'StatusPage',
    props: {
      // 权限代码
      permissionCode: {
        type: String,
        default: ''
      },
      // 是否有权限
      hasPermission: {
        type: Boolean,
        default: true
      },
      // 是否开发中
      isDeveloping: {
        type: Boolean,
        default: false
      },
      // 是否有数据
      hasData: {
        type: Boolean,
        default: true
      }
    }
  }
  </script>

  <style scoped>
  .content-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .desc{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 26px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }

  .empty-status {
    text-align: center;

    img {
      margin-top: 120px;
      width: 300px;
      height: 200px;
    }
  }
  </style>
