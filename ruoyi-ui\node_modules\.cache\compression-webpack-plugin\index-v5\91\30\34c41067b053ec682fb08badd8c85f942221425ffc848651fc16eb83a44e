
30c97e0373c884ce101f6a49b95871e3fc214003	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4fcdf54967a5a94087e64cd430328aea\"}","integrity":"sha512-qioWXxUhl8ZYQLazMKn7+V0bJqM6lEOMq25uqSyQJZ2eZePA1iRQ17VsAiEt3B+gpU+0H5DTuG/C1OEHbV5wqg==","time":1754311996857,"size":12043704}