<script>
export default {
  name: 'BaseInfoTable',
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    tableData() {
      // 定义基础行配置
      const baseRows = [
        [
          { label: '案名', key: 'name' },
          { label: '地块名', key: 'plotName' },
          { label: '项目公司注册名', key: 'companyRegisterName' },
          { label: '法人/总经理及归属公司', key: 'belongCompanyName' }
        ],
        // 特殊行单独处理
        [
          { label: '股权占比', key: 'shareholdRate' },
          { label: '操盘条线', key: 'operateStripline', keyColSpan: 3},
          { label: '并表方', key: 'combineTableContent'}
        ],
        [
          { label: '拿地时间', key: 'takeLandDate' },
          { label: '首次开盘时间', key: 'firstOpenDate' },
          { label: '首批竣备时间', key: 'firstCompletedDate' },
          { label: '首批合同交付时间', key: 'firstDeliverDate' }
        ],
        [
          { label: '土地费用\n(亿，不含契税)', key: 'landCost', labelRowSpan: 2, keyRowSpan: 2},
          { label: '计容面积\n(m²，不含地下)', key: 'capacityArea' },
          { label: '可售面积\n(m²，不含地下)', key: 'canSalesArea' },
          { label: '限价政策', key: 'constraintPricePolicy', labelRowSpan: 2, keyRowSpan: 2}
        ],
        [
          { label: '计容楼面价(元)', key: 'capacityPrice' },
          { label: '可售楼面价(元)', key: 'canSalesPrice' },
        ],
        [
          { label: '用地面积(㎡)', key: 'useLandArea' },
          { label: '容积率/限高(米)', key: 'plotRatio' },
          { label: '配套情况', key: 'supportSituation' },
          { label: '竣备交付形式', key: 'completedDeliverModality' },
        ],
      ]

      return baseRows.map(row =>
        row.map(item => ({
          label: item.label,
          key: item.key,
          value: this.baseInfo[item.key],
          keyColSpan: item.keyColSpan,
          keyRowSpan: item.keyRowSpan,
          labelRowSpan: item.labelRowSpan
        }))
      )
    }
}
}
</script>

<template>
  <table class="base-info-table">
    <colgroup>
      <col class="label-col">
      <col class="value-col">
      <col class="label-col">
      <col class="value-col">
    </colgroup>
    <tr v-for="(row, rowIndex) in tableData" :key="rowIndex" class="table-row">
      <template v-for="(item, itemIndex) in row">
        <th
          :key="`label-${rowIndex}-${itemIndex}`"
          class="table-cell-label"
          :rowspan="item.labelRowSpan"
          :class="{ 'share': item.keyRowSpan }"
        >
          {{ item.label }}
        </th>
        <td
          :key="`value-${rowIndex}-${itemIndex}`"
          class="table-cell-value"
          :colspan="item.keyColSpan"
          :rowspan="item.keyRowSpan"
          :class="{
            'value1': item.keyColSpan,
            'share-value': item.keyRowSpan
          }"
        >
          {{ item.value }}
        </td>
      </template>
    </tr>
  </table>
</template>

<style scoped lang="scss">
.base-info-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border: 0.0625rem solid #E5E5E5;

  .label-col {
    width: 7%; // 6.25rem;
    font-family: PingFang SC, PingFang SC;
  }

  .value-col {
    // width: calc((100% - 12.5rem) / 2);
    width: 17%;
  }

  th, td{
    font-weight: 400;
    font-size: 0.75rem;
    color: #333333;
    line-height: 0.875rem;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  th {
    // width: 7.5rem;
    width: 7%;
    padding: 0.75rem 0.5rem;
    border: 0.0625rem solid #E5E5E5;
    white-space: pre-line;
    background: rgba(0,106,255,0.1);
    text-align: center;
    line-height: 1.25rem;
  }

  td {
    width: 17%;
    padding: 0.75rem 0.5rem;
    border: 0.0625rem solid #E5E5E5;
    word-break: break-all;
    white-space: normal;
    text-align: left;
    line-height: 1.25rem;
  }
}
</style>
