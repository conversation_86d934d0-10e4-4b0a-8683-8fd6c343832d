<template>
  <div v-loading="loading" class="weekly-approve">
    <el-form :model="weeklyForm" label-width="120px" class="weekly-form">
      <div class="head-block">
        <!-- 标题部分 -->
        <div class="weekly-title">
          {{ currentYear }}年第{{ weekNumber }}周（{{ dateRange }}）个人总结
        </div>

        <!-- 个人信息部分 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="custom-label">员工姓名：</span>
            <span class="custom-text">{{ weeklyForm.nickName }}</span>

          </el-col>
          <el-col :span="8">
            <span class="custom-label">员工所属部门：</span>
            <span class="custom-text">{{ weeklyForm.deptName }}</span>
          </el-col>
          <el-col :span="8">
            <span class="custom-label">员工职位：</span>
            <span class="custom-text">{{ weeklyForm.postName }}</span>
          </el-col>
        </el-row>
      </div>


      <div class="weekly-content">
        <!-- 本周工作总结 -->
        <div>
          <div class="section-title">
            本周工作总结
          </div>
        </div>
        <el-table :data="weeklyForm.workSummaryList" border>
          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="工作板块" prop="workMatter" width="350" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.workMatter" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.progressScheme" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="完成及预警情况" prop="completeStatus" width="150" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.completeStatus" readonly></el-input>
              <!--            <el-select v-model="scope.row.completeStatus" placeholder="请选择" disabled>-->
              <!--              <el-option-->
              <!--                v-for="dict in dict.type.work_complete_status"-->
              <!--                :key="dict.value"-->
              <!--                :label="dict.label"-->
              <!--                :value="dict.value"-->
              <!--              ></el-option>-->
              <!--            </el-select>-->
            </template>
          </el-table-column>
        </el-table>

        <!-- 下周工作计划 -->
        <div>
          <div class="section-title">下周工作计划</div>
        </div>
        <el-table :data="weeklyForm.workPlanList" border>
          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="工作板块" prop="workMatter" width="350" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.workMatter" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.progressScheme" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="完成及预警情况" prop="completeStatus" width="150" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.completeStatus" readonly></el-input>
              <!--            <el-select v-model="scope.row.completeStatus" placeholder="请选择" disabled>-->
              <!--              <el-option-->
              <!--                v-for="dict in dict.type.work_complete_status"-->
              <!--                :key="dict.value"-->
              <!--                :label="dict.label"-->
              <!--                :value="dict.value"-->
              <!--              ></el-option>-->
              <!--            </el-select>-->
            </template>
          </el-table-column>
        </el-table>
        <div>
          <div class="section-title">每周反思</div>
        </div>
        <el-input v-model="weeklyForm.reflectInfo.progressScheme" type="textarea" resize="none" readonly autosize="true"
            style="min-height: 56px;line-height: 56px;border: 1px solid #dfe6ec;display: flex;align-items: center;">
        </el-input>
        <!-- 需部门支持事项 -->
        <div>
          <div class="section-title">需部门支持事项</div>
        </div>
        <el-table :data="weeklyForm.workSupportList" border>
          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="工作板块" prop="workMatter" width="350" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.workMatter" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.progressScheme" type="textarea" readonly autosize="true"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="完成及预警情况" prop="completeStatus" width="150" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.completeStatus" readonly></el-input>
              <!--            <el-select v-model="scope.row.completeStatus" placeholder="请选择" disabled>-->
              <!--              <el-option-->
              <!--                v-for="dict in dict.type.work_complete_status"-->
              <!--                :key="dict.value"-->
              <!--                :label="dict.label"-->
              <!--                :value="dict.value"-->
              <!--              ></el-option>-->
              <!--            </el-select>-->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="approve-row-block">
        <div>
          <div class="section-title">审阅意见</div>
        </div>
        <el-row class="approve-row">
          <!--  <el-col :span="8">
            <el-form-item label="审批人" prop="approveUserName">
                <el-input
                v-model="weeklyForm.approveUserName"
                clearable
                disabled
              />
            </el-form-item>
        </el-col> -->
          <!-- @Excel(name = "审批状态", readConverterExp = "1=待阅,2=已阅, 5=部分已阅") -->

          <el-col :span="24">
            <el-input class="custom-border" v-model="weeklyForm.approveDesc" type="textarea" resize="none"
              :disabled="weeklyForm.approveStatus === 2" placeholder="请输入审阅意见" :autosize="{ minRows: 3 }"></el-input>
          </el-col>
        </el-row>
        <el-row class="button-row">
          <el-button class="custom-btn" type="danger" @click="handleReject" :loading="loading">驳回</el-button>
          <el-button class="custom-btn confirm" v-if="weeklyForm.approveStatus === 1 || weeklyForm.approveStatus === 5"
            type="primary" @click="handleApprove" :loading="loading">同意</el-button>
        </el-row>
      </div>
    </el-form>
  </div>
</template>

<script>
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close()
  }
}

import { getInfo, approveWeekly, weeklyReject } from "@/api/weekly/reportInfo";
export default {
  name: "weeklyApprove",
  dicts: ["work_complete_status"],
  data() {
    return {
      loading: true,
      labelPosition: "right",
      weeklyForm: {
        id: null,
        year: null, // 年份不能为空
        week: null, // 第几周不能为空
        userId: "", // 员工编码不能为空
        nickName: "", // 员工名称不能为空
        deptId: null, // 部门ID不能为空
        deptName: "", // 部门名称不能为空
        postName: "", // 岗位名称
        startDate: "", // 周开始日期不能为空
        endDate: "", // 周结束日期不能为空
        approveUserCode: "", // 审批人编码不能为空
        approveUserName: "", // 审批人名称不能为空
        circulateUserCodes: "", // 传阅人（多个用逗号隔开）
        userCode: "", // 员工编码不能为空
        // 本周工作总结
        workSummaryList: [
          {
            workMatter: "", // 工作板块
            progressScheme: "", // 工作事项/完成进度
            completeStatus: "", // 完成及预警情况
            type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
            serialNumber: 1, // 序号
          },
        ],
        // 下周工作计划
        workPlanList: [
          {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 2,
            serialNumber: 1,
          },
        ],
        // 需部门支持事项
        workSupportList: [
          {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 3,
            serialNumber: 1,
          },
        ],
        fromPath: "", // 来源路径
        reflectInfo: { // 1=本周工作总结,2=下周工作计划,3=需部门支持事项,4=每周反思
          type: 4,
          progressScheme: null
        },
      },
    };
  },
  computed: {
    currentYear() {
      return this.weeklyForm.year;
    },
    weekNumber() {
      // 这里需要实现获取当前周数的逻辑
      return this.weeklyForm.week;
    },
    dateRange() {
      // 这里需要实现获取日期范围的逻辑
      // return `${this.weeklyForm.startDate}-${this.weeklyForm.endDate}`;
      return this.formatDateRange(this.weeklyForm.startDate, this.weeklyForm.endDate);
    },
    department() {
      return this.weeklyForm.deptName;
    },
  },
  created() {
    if (this.$route.query.from) {
      this.fromPath = this.$route.query.from;
    }
    this.handleGetData();
  },
  methods: {
    formatDateRange(startDate, endDate) {
      return `${new Date(startDate).getMonth() + 1}月${new Date(startDate).getDate()}日-${new Date(endDate).getMonth() + 1}月${new Date(endDate).getDate()}日`;
    },
    closeCurrentPage() {
      this.$tab.closeOpenPage({ path: this.fromPath });
    },
    handleReject() {
      this.loading = true;
      if (!this.weeklyForm.approveDesc) {
        this.$modal.msgWarning("请输入审阅意见");
        this.loading = false;
        return;
      }
      weeklyReject({
        id: this.$route.params.id,
        approveDesc: this.weeklyForm.approveDesc,
      })
        .then((response) => {
          this.$modal.msgSuccess("驳回成功");
          this.handleGetData();
          // 从待办中心审阅，审阅完成后只刷新当前页面
          // 从我的审阅进入审阅，审阅完成后关闭当前页面并返回到我的审阅，刷新我的审阅列表
          if (this.fromPath) {
            this.closeCurrentPage(this.fromPath);
          }
          else {
            close();
            /*window.opener = null;
            window.open("", "_self");
            window.close()*/
          }
        })
        .catch(() => {
          // this.$modal.msgError("审阅失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleApprove() {
      this.loading = true;
      approveWeekly({
        id: this.$route.params.id,
        approveDesc: this.weeklyForm.approveDesc,
      })
        .then((response) => {
          this.$modal.msgSuccess("审阅成功");
          this.handleGetData();
          // 从待办中心审阅，审阅完成后只刷新当前页面
          // 从我的审阅进入审阅，审阅完成后关闭当前页面并返回到我的审阅，刷新我的审阅列表
          if (this.fromPath) {
            this.closeCurrentPage(this.fromPath);
          }
          else {
            close();
            /*window.opener = null;
            window.open("", "_self");
            window.close()*/
          }
        })
        .catch(() => {
          // this.$modal.msgError("审阅失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleGetData() {
      const id = this.$route.params.id;
      this.loading = true;
      getInfo(id).then((response) => {
        if(!response.data.reflectInfo){
          response.data.reflectInfo = {};
        }
        this.weeklyForm = response.data;
        this.loading = false;
      });
    },
    getFormData() {
      return this.weeklyForm;
    },
    submitForm() {
      // 实现提交表单的逻辑
      console.log("提交表单", this.weeklyForm);
    },
    setFormData(data) {
      console.log("setFormData", data);
      if (data) {
        this.weeklyForm = {
          ...this.weeklyForm,
          year: data.year,
          week: data.week,
          userId: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          userCode: data.userCode,
          workSummaryList: data.workSummaryList || [
            {
              workMatter: "", // 工作板块
              progressScheme: "", // 工作事项/完成进度
              completeStatus: "", // 完成及预警情况
              type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
              serialNumber: 1, // 序号
            },
          ],
        };
      }
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.el-textarea textarea) {
  border: none;
}

:deep(.el-input input) {
  border: none;
}

:deep(.custom-border.el-textarea textarea) {
  min-height: 80px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #DDDDDD;
}

.button-row {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.custom-btn {
  width: 280px;
  height: 42px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 19px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-top: 24px;

  &.confirm {
    border-radius: 4px 4px 4px 4px;

  }
}

.weekly-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.head-block {
  background: linear-gradient(270deg, rgba(54, 115, 255, 0.05) 0%, rgba(54, 115, 255, 0.2) 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.custom-input {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #DDDDDD;
}

.custom-label,
.custom-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.info-row {
  :deep(.el-form-item__label) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.weekly-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}

.section-title {
  /* background-color: #f0f2f5; */
  padding: 10px 0;
  margin: 0 10px;
  /* margin: 20px 0 0; */
  font-weight: 600;
  text-align: left;
  /* border-bottom: 1px solid #fff; */
  display: inline-flex;
  align-items: flex-start;
  margin-bottom: 18px;
  position: relative;

  &::before {
    content: " ";
    display: inline-block;
    height: 10px;
    background: #D8E4FB;
    // margin-right: 10px;
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    z-index: -1;
  }
}

.submit-section {
  margin-top: 20px;
  text-align: center;
}

/* 添加深度选择器来修改 element-plus 表头样式 */
/* :deep(.el-table th) {
  background-color: #f0f2f5 !important;
} */

.weekly-form {
  max-height: calc(100vh - 100px);
  /* 可以根据实际需要调整减去的高度 */
  overflow-y: auto;
  padding: 20px;
}

.approve-row {
  // margin-top: 20px;
  text-align: center;
}

/* 添加以下样式来修复 label 和 input 换行的问题 */
.weekly-form :deep(.el-form-item) {
  display: flex;
  margin-bottom: 0px;
}

.weekly-form :deep(.el-form-item__label) {
  float: none;
  display: inline-flex;
  align-items: center;
}

.weekly-form :deep(.el-form-item__content) {
  flex: 1;
  margin-left: 0 !important;
}

.section-content {
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;

  .section-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
  }
}

:deep(.el-table__header-wrapper .el-table__cell) {
  background: rgba(54, 115, 255, 0.05);
  border-radius: 0px 0px 0px 0px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.weekly-form :deep(th.el-table__cell) {
  padding: 0;
}

.info-row {
  /* margin-bottom: 10px; */
}

.mb-20 {
  margin-bottom: 20px !important;
}

.font-size-15 {
  font-size: 15px;
}

.sortable-table .el-table__row {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.8;
  background: #f0f9eb;
}

.el-table__body tr {
  cursor: move;
}
</style>
