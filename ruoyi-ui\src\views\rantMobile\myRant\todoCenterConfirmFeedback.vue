<template>
  <div class="app-container app-container-feedback" v-loading="loading">
    <div class="step-box">
      <rant-step :stepActive="stepActive" />
    </div>

    <div class="rant-container">
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="rant-form-title">
            <img src="@/assets/rant/rant-info.png" class="icon">
            基本信息
          </div>

          <van-form class="rant-detail">
            <div class="form-group">
             <!--  <div class="form-item">
                <div class="form-label">来源</div>
                <div class="form-value">{{ form.ranterName || '未设置' }}</div>
              </div> -->

              <div class="form-item">
                <div class="form-label">分类</div>
                <div class="form-value">
                  <template v-if="form.rantClassify">
                    {{ form.rantClassify }}
                  </template>
                  <template v-else>未设置</template>
                </div>
              </div>

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>计划完成时间</div>
                <div class="form-value align-right clickable" @click="!isRead && (showDatePicker = true)" :class="{'custom-disabled-mobile': isRead}">
                  <van-icon name="clock-o" />{{ form.planTime || '请选择计划完成时间' }}
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">责任人</div>
                <div class="form-value">{{ form.responsiblePersonName || '未设置' }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">责任部门</div>
                <div class="form-value">{{ form.deptName || '未设置' }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">部门负责人</div>
                <div class="form-value">{{ form.respDeptResponsiblerName || '未设置' }}</div>
              </div>

             <!--  <div class="form-item">
                <div class="form-label">内容</div>
                <div class="form-value content-text">{{ form.rantContent || '未设置' }}</div>
              </div> -->

              <!--  <div class="form-item">
                <div class="form-label">措施</div>
                <div class="form-value">
                  <textarea
                    v-model="form.solution"
                    class="progress-textarea"
                    rows="4"
                    placeholder="请输入措施"
                  ></textarea>
                </div>
              </div> -->
            </div>

          </van-form>

        </div>
      </div>
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10">内容</div>
            <div class="">
              <textarea v-model="form.rantContent" disabled class="progress-textarea custom-disabled-mobile" rows="4" placeholder="请输入" ></textarea>
            </div>
          </div>
        </div>
      </div>
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10"><span class="required-mark">*</span>措施</div>
            <div class="">
              <textarea v-model="form.solution" class="progress-textarea" rows="4" placeholder="请输入" :disabled="isRead"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons" v-if="status == 6">
<!--      v-if="form.isRead == 2"-->
      <van-button class="custom-btn-submit" @click="submitForm" v-if="!isRead">确 认</van-button>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="date" title="选择日期" :min-date="minDate" @confirm="confirmDate"
        @cancel="showDatePicker = false" />
    </van-popup>
  </div>
</template>

<script>
// http://localhost/rant/todoCenterConfirmFeedback?todoNoticeId=20
// http://localhost/wechatE/mobile/rant/todoCenterConfirmFeedback?todoNoticeId=60
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/

import { close } from '@/utils';
import {
  rantDaiBanInfo,
  myRantConfirm
} from '@/api/rantMobile/matters'
// import { getInfo } from '@/api/login'
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listRespdet, getRespdet } from '@/api/rantMobile/respdet'
// import { listDept, listDeptExcludeChild } from '@/api/system/dept'
import RantStep from '@/views/rant/components/RantStep/index.vue'

// 导入Vant组件
import {
  Form,
  Field,
  Button,
  Popup,
  DatetimePicker,
  Toast,
  Icon
} from 'vant';

export default {
  name: 'MyRant',
  components: {
    StatusTag,
    Treeselect,
    RantStep,
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Icon.name]: Icon
  },
  // dicts: ['rant_classify'],
  data() {
    return {
      stepActive: 3, // 当前步骤
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: false,
      isMultiple: false,
      // 责任部门列表
      deptOptions: [],
      status: null,
      // 日期选择器
      showDatePicker: false,
      currentDate: new Date(),
      minDate: new Date(2000, 0, 1),
      // 表单参数
      form: {
        "todoNoticeId": null,
        "id": null,
        "ranter": null, // 来源:人(多个用","隔开)
        "ranterName": null, // 来源:人(多个用","隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "approveDesc": '', // 审批意见
      },
      // 表单校验
      rules: {
        planTime: [
          { required: true, message: '计划完成时间不能为空', trigger: 'blur' }
        ],
        solution: [
          { required: true, message: '措施不能为空', trigger: 'blur' }
        ]
      },
      type: '',
      rantId: '',
      isRead: false
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    }
  },
  created() {
    this.todoNoticeId = this.$route.query.todoNoticeId
    this.form.todoNoticeId = this.todoNoticeId;
    this.fetchDaiBanInfo(this.todoNoticeId)
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, 'id')
    })
  },
  methods: {
    fetchDaiBanInfo(todoNoticeId) {
      this.loading = true;
      rantDaiBanInfo(todoNoticeId).then((res) => {
        if (res.code === 200) {
          this.form.id = res.data?.id;
          this.form.ranter = res.data?.ranter;
          this.form.ranterName = res.data?.ranterName;
          this.form.rantClassify = res.data?.rantClassify;
          this.form.rantContent = res.data?.rantContent;
          this.form.planTime = res.data?.planTime;
          this.form.deptId = res.data?.deptId;
          this.form.deptName = res.data?.deptName;
          this.form.responsiblePerson = res.data?.responsiblePerson;
          this.form.responsiblePersonName = res.data?.responsiblePersonName;
          this.form.respDeptResponsibler = res.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = res.data?.respDeptResponsiblerName;
          this.form.approveDesc = res.data?.approveDesc;
          this.form.solution = res.data?.solution;
          this.status = res.data?.status;
          this.form.isRead = res.data?.isRead;
          this.isRead = res.data?.isRead;
          // 初始化日期选择器当前值
          if (this.form.planTime) {
            const parts = this.form.planTime.split('-')
            if (parts.length === 3) {
              this.currentDate = new Date(parts[0], parts[1] - 1, parts[2])
            }
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        Toast.fail('获取数据失败');
      })
    },

    // 确认日期选择
    confirmDate(value) {
      this.form.planTime = this.formatDate(value);
      this.showDatePicker = false;
    },

    // 格式化日期为字符串 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 表单提交
    submitForm() {
      if (!this.form.solution) {
        Toast('请输入措施');
        return;
      }

      if (!this.form.planTime) {
        Toast('请选择计划完成时间');
        return;
      }

      this.loading = true;

      myRantConfirm({
        todoNoticeId: this.form.todoNoticeId,
        id: this.form.id,
        planTime: this.form.planTime,
        solution: this.form.solution,
      }).then((response) => {
        if (response.code === 200) {
          Toast.success('责任人确认成功，即将关闭页面');
          setTimeout(() => {
            close();
          }, 1500);
        } else {
          Toast.fail(response.msg || '责任人确认失败');
        }
      }).catch(() => {
        Toast.fail('操作失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      close();
    },

    // ... 保留其他原有方法
    normalizer(node) {
      return {
        id: node.id,
        label: node.name || node.deptName, // 使用name字段，兼容deptName
        value: node.id,
        children: node.children
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import '../css/common.scss';

.app-container-feedback {
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
  padding: 20px 16px;
  padding-bottom: 60px;
}

.step-box {
  margin-bottom: 16px;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;

  .custom-btn-submit {
    width: 200px;
    height: 40px;
    background: #3673FF;
    color: #FFFFFF;
    border-radius: 20px;
  }
}

.form-value {
  &.align-right {
    color: #3673FF;
    text-align: right;
  }
}

.clickable {
  position: relative;
  padding-right: 20px;

  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-top: 1px solid #999;
    border-right: 1px solid #999;
    transform: translateY(-50%) rotate(45deg);
  }
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.progress-textarea {
  width: 100%;
  border: 1px solid rgba(55, 109, 247, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F1F7FE;
  min-height: 80px;
  font-size: 14px;
  color: #333333;
  outline: none;
  resize: vertical;
}

.form-value {
  text-align: right !important;
}
.required-mark {
  color: #ee0a24;
  margin-right: 2px;
}
</style>
