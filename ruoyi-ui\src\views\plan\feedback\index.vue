<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-row >
        <el-col :span="8">
          <el-form-item label="城市公司" prop="deptNum" label-width="100px">
            <el-select v-model="queryParams.deptNum" placeholder="请选择城市公司" clearable @change="handleQueryProject">
              <el-option v-for="dict in deptList"
                         :key="dict.deptNum"
                         :label="dict.deptName"
                         :value="dict.deptNum"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称" prop="projectName" label-width="100px">
            <!--        <el-input
                      v-model="queryParams.projectName"
                      placeholder="请输入项目名称"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />-->
            <el-select
              v-model="queryParams.projectName"
              placeholder="请选择项目"
              clearable
              filterable
              @change="getPlanNameList"
            >
              <el-option
                v-for="dict in storeProjectList"
                :key="dict.name"
                :label="dict.name"
                :value="dict.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch">
          <el-form-item label="计划名称" prop="planName" label-width="100px">
            <!--            <el-input
                          v-model="queryParams.planName"
                          placeholder="请输入计划名称"
                          clearable
                          @keyup.enter.native="handleQuery"
                        />-->
            <el-select v-model="queryParams.planName">
              <el-option
                v-for="planName in planNameList"
                :key="planName"
                :label="planName"
                :value="planName"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch">
          <el-form-item label="分期" prop="stageId" label-width="100px">
            <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                       clearable
                       filterable>
              <el-option
                v-for="dict in stages"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch" label-width="100px">
          <el-form-item label="标段" prop="lot" v-show="showAllSearch">
          <el-select v-model="queryParams.lot" placeholder="请选择标段"
                     clearable
                     filterable>
            <el-option
              v-for="dict in projectLot"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch">
          <el-form-item label="节点状态" prop="status" label-width="100px">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择节点状态"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="dict in nodeStatusOption"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch">
          <el-form-item label="节点名称" prop="nodeName" label-width="100px">
            <el-input
              v-model="queryParams.nodeName"
              placeholder="请输入节点名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="showAllSearch">
          <el-form-item label="责任部门" prop="department" label-width="100px">
            <el-select clearable v-model="queryParams.department" placeholder="请选择责任部门">
              <el-option v-for="dict in dict.type.resp_department"
                         :key="dict.value"
                         :label="dict.label"
                         :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!--          <el-form-item label="反馈人" prop="feedbackPeopleName">
                      <el-input
                        v-model="queryParams.feedbackPeopleName"
                        placeholder="请输入反馈人"
                        clearable
                        @keyup.enter.native="handleQuery"
                      />
                    </el-form-item>-->
          <el-form-item label="反馈人" prop="feedbackPeopleName" v-show="showAllSearch" label-width="100px">
            <div class="flex-row">
              <div class="feedback-people-container" v-if="!!feedBackUserInfo && !!feedBackUserInfo.nickName">
                <div>{{ feedBackUserInfo ? feedBackUserInfo.nickName : '' }}</div>
                <i class="el-icon-circle-close" @click="clearFeedbackUserInfo"></i>
              </div>
              <el-button type="primary" @click="selectFeedbackFun" size="mini"
              >选择
              </el-button
              >
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              type="primary"
              :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
              size="mini"
              @click="toggleAllSearch"
            >更多筛选
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" fixed="left"/>
      <el-table-column label="序号" align="center" type="index" fixed="left"/>
      <el-table-column label="节点状态" align="center" prop="status" fixed="left" width="100">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column label="计划名称" align="center" prop="planName" min-width="130" fixed="left">

<!--        <template slot-scope="scope">
          <el-link class="wrap-line" v-if="tableBthShow('getApproveFlow', scope.row)" type="primary"
                   @click="fetchApprovalUser(scope.row)">{{ scope.row.planName || '无' }}
          </el-link>
          <span v-else class="wrap-line" @click="$copyToClipboard(scope.row.planName)">{{ scope.row.planName || '无' }}</span>
        </template>-->
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="130">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.projectName)">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点名称" align="center" prop="nodeName">
        <template slot-scope="scope">
          <el-link class="wrap-line" v-if="tableBthShow('getApproveFlow', scope.row)" type="primary"
                   @click="fetchApprovalUser(scope.row)">{{ scope.row.nodeName || '无' }}
          </el-link>
          <span v-else class="wrap-line" @click="$copyToClipboard(scope.row.nodeName)">{{ scope.row.nodeName || '无' }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="完成标准" align="center" prop="completeCriteria"/>-->
      <el-table-column label="计划开始时间" width="130" align="center" prop="startTime"/>
      <el-table-column label="计划完成时间" width="130" align="center" prop="endTime"/>
      <!--<el-table-column label="预计完成时间" width="100" align="center" prop="expectedCompletionDate"/>-->
      <!--      <el-table-column label="流程反馈状态" width="100" align="center" prop="feedbackFlowStatus">
              <template slot-scope="scope">
                <status-tag :status="scope.row.feedbackFlowStatus" :options="feedbackFlowStatusOption"/>
              </template>
            </el-table-column>-->
      <!--      <el-table-column label="实际完成时间" width="100" align="center" prop="actualCompletionDate"/>-->
      <el-table-column label="实际偏差" width="100" align="center" prop="deviationNum">
        <template slot-scope="scope">
          {{!!scope.row.deviationNum ? scope.row.deviationNum : '--'}}
        </template>
      </el-table-column>
      <el-table-column label="责任部门" align="center" prop="department"/>
      <!--      <el-table-column label="反馈人" align="center" prop="feedbackPeopleName"/>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="230" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看
          </el-button>
          <el-button
            v-if="tableBthShow('update', scope.row)"
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            @click="handleUpdate(scope.row, 2)"
            v-hasPermi="['plan:feedback:edit']"
          >汇报
          </el-button>
          <el-button
            v-if="tableBthShow('refreshFlowStatus', scope.row)"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="refreshApproval(scope.row, scope.$index)"
          >刷新
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page-sizes="[200, 500, 800, 1000]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!--反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <div class="feedback-container">
        <div class="feedback-detail">
          <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
          <el-form class="feedback-detail">
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目名称：" prop="projectName">
                  {{feedbackDetail.projectName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目分期：" prop="stageId">
                  <dict-tag :options="dict.type.stages" :value="feedbackDetail.stageId"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="节点顺序：" prop="nodeIndex">
                  {{feedbackDetail.nodeIndex || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="节点名称：" prop="nodeName">
                  {{feedbackDetail.nodeName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="计划开始时间：" prop="startTime">
                  {{feedbackDetail.startTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划完成时间：" prop="endTime">
                  {{feedbackDetail.endTime || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="完成时间：" prop="actualCompletionDate">
                  {{feedbackDetail.actualCompletionDate || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际偏差：" prop="deviationNum">
                  {{feedbackDetail.deviationNum || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="完成标准：" prop="completeCriteria">
                  {{feedbackDetail.completeCriteria || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="成果文件：" prop="resultFileName">
                  {{feedbackDetail.resultFileName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="节点描述：" prop="nodeDesc">
                  {{feedbackDetail.nodeDesc || '--'}}
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
        </div>
        <div class="feecback-form" v-if="dialogType === 'update'">
          <div class="el-icon-s-comment feedback-form-title icon-primary">节点反馈</div>
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-row>
              <!--     过程反馈有百分比，完成反馈没有百分比-->
              <el-col :span="12" v-if="form.feedbackType === 1">
                <el-form-item label="完成百分比" prop="feedbackProgress">
                  <el-slider :max='100' :step='1' show-input v-model="form.feedbackProgress"></el-slider>
                </el-form-item>
              </el-col>
              <!--    根据反馈类型，确定显示预计完成时间or实际完成时间          -->
              <el-col :span="12" v-if="form.feedbackType !== 2">
                <el-form-item label="预计完成时间" prop="expectedCompletionDate">
                  <el-date-picker v-model="form.expectedCompletionDate" type="date" format="yyyy-MM-dd"
                                  value-format="yyyy-MM-dd"
                                  @input="(value)=>setDatePikcer(value, 'expectedCompletionDate')"
                                  :style="{width: '100%'}" placeholder="请选择预计完成时间" clearable>
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.feedbackType == 2">
                <el-form-item label="实际完成时间" prop="actualCompletionDate">
                  <el-date-picker v-model="form.actualCompletionDate" type="date" format="yyyy-MM-dd"
                                  value-format="yyyy-MM-dd"
                                  @input="(value)=>setDatePikcer(value, 'actualCompletionDate')"
                                  :style="{width: '100%'}" placeholder="请选择实际完成时间" clearable>
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.feedbackType == 2">
                <section>
                  <span class="ml5 mr5">时间前推</span>
                  <el-input v-model="calcDate" style="display: inline-block;width: 80px;"></el-input>
                  <span class="mr5">天</span>
                  <el-button class="ml5 mr5" type="primary" @click="handleSetDate">设置</el-button>
                </section>

              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="进度说明" prop="notes">
                <el-input v-model="form.notes" type="textarea" placeholder="请输入进度说明"
                          :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
              </el-form-item>
            </el-row>
          </el-form>
        </div>
        <div class="feedback-file">
          <div class="el-icon-files feedback-form-title icon-primary">成果文件</div>
          <el-row :gutter="10" class="mb8" v-if="dialogType === 'update'">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-setting"
                size="mini"
                @click="handleResultOpen"
              >节点列表选择
              </el-button>
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleNodeDocument"
              >新增
              </el-button>
            </el-col>
          </el-row>
          <el-table :data="nodeOutcomeDocumentList" @selection-change="handleNodeDocumentChange" ref="nodeOut"
                    style="width: 100%;">
            <el-table-column type="index" width="55" align="center"/>
            <el-table-column label="成果类型" align="center" prop="type" width="150">
              <template slot-scope="scope">
                <span v-if="dialogType === 'view'">{{scope.row.type || '--'}}</span>
                <el-input v-else-if="dialogType === 'update'" v-model="scope.row.type"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="成果文件" align="center" prop="annexUrl" width="200">
              <template slot-scope="scope">
                <section class="file-wrapper" v-if="dialogType === 'update'">
                  <label v-if="scope.row.fileList.length === 0" :for="'uploadFile_'+scope.$index"
                         class="el-icon-upload cursor icon-primary"
                         :class="{'custom-warning': !!scope.row.fileListError}">
                    {{scope.row.fileListError || '请上传文件'}}
                  </label>
                  <section v-else>
                    <div v-for="(file, index) in scope.row.fileList" :key="index" class="file-show">
                      <a :href="file.url" class="link" target="_blank" :title="file.name">{{file.name || '--'}}</a>
                      <i class="el-icon-circle-close cursor link" @click="handleDeleteAnnexUrl(scope.row, index)"></i>
                    </div>
                  </section>
                </section>
                <section v-else-if="dialogType === 'view'" style="display: flex;flex-direction: column;">
                  <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" :download="file.name"
                     class="link" target="_blank">
                    {{file.name || '--'}}</a>
                </section>
                <input type="file" class="display-none" multiple :id="'uploadFile_'+scope.$index"
                       @change="(event) => uploadFileHandle(event, scope.row, scope.$index)"/>
              </template>
            </el-table-column>
            <el-table-column label="上传时间" align="center" prop="createTime">
              <template slot-scope="scope">
                <span v-if="dialogType === 'view'">{{scope.row.createTime || '--'}}</span>
                <el-input v-else-if="dialogType === 'update'" v-model="scope.row.createTime" disabled=""></el-input>
              </template>
            </el-table-column>
            <el-table-column label="备注说明" align="center" prop="outcomeDocumentsDesc">
              <template slot-scope="scope">
                <span v-if="dialogType === 'view'">{{scope.row.outcomeDocumentsDesc || '--'}}</span>
                <el-input v-else-if="dialogType === 'update'" v-model="scope.row.outcomeDocumentsDesc"></el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="dialogType === 'update'" label="操作" align="center"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <!--已有成果文件，禁止删除-->
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  :disabled="!!scope.row.id && !!scope.row.fileList && scope.row.fileList.length !== 0"
                  @click="handleNodeDocumentDelete(scope.row, scope.$index)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="dialogType !== 'view'">确 认</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
      <el-table v-loading="resultFileLoading" :data="resultFileList" @selection-change="handleFileSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" align="center" type="index"/>
        <el-table-column label="成果类别" align="center" prop="type"/>
        <el-table-column label="成果名称" align="center" prop="name"/>
        <el-table-column label="是否必填" align="center" prop="fillFlag">
          <template slot-scope="scope">
            <status-tag :status="scope.row.fillFlag" :options="fillFlagOption"/>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <status-tag :status="scope.row.status" :options="statusOption"/>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        class="mt20"
        v-show="resultFileTotal>0"
        :total="resultFileTotal"
        :page.sync="queryResultFileParams.pageNum"
        :limit.sync="queryResultFileParams.pageSize"
        @pagination="getResultFileNodeList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResultFile" v-if="dialogType !== 'view'">确 认</el-button>
        <el-button @click="cancelFile">取 消</el-button>
      </div>
    </el-dialog>
    <select-user
      ref="userRef"
      :roleId="feedBackUserInfo.userName"
      @feedbackEmit="selectFeedbackData"
    />
  </div>
</template>
<script>
  import {
    listFeedback,
    getFeedback,
    delFeedback,
    progressFeedback,
    finishFeedback,
    getPlanStatus,
    getApproveFlow,
    allPlanName,
    flowCreate
  } from '@/api/plan/feedback'
  import { mapState } from 'vuex'
  import {
    uploadFile,
    uploadFileMultiple
  } from '@/api/plan/common'
  import Template from '@/views/plan/template/index.vue'
  import { nodeStatusOption, feedbackFlowStatusOption, statusOption, fillFlagOption } from '@/constant'
  import StatusTag from '@/views/plan/components/StatusTag/index.vue'
  import { listConfig } from '@/api/plan/config'
  import SelectUser from '@/views/plan/components/SelectUser/index.vue'
  import mixin from '@/mixins'

  export default {
    mixins: [mixin],
    name: 'Feedback',
    components: { SelectUser, Template, StatusTag },
    dicts: ['stages', 'resp_department' /*责任部门*/],
    data() {
      return {
        planNameList: [], // 计划名称列表
        feedBackUserInfo: { userName: '' },
        calcDate: 0,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 计划反馈表格数据
        feedbackList: [],
        // 弹出层标题
        title: '节点反馈维护',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 200,
          nodeCode: null,
          feedbackPeople: null,
          projectId: null, // 项目id
          stageId: null,
          completeTime: null,
          notes: null,
          annexUrl: null,
          tenantId: null,
          creator: null,
          updater: null,
          deleted: null,
          deptNum: null, //城市公司
          planId: null, // 计划id
          status: [0, 1, 4] // 节点状态
        },
        // 表单参数
        form: {
          expectedCompletionDate: null,
          actualCompletionDate: null
        }, // 反馈表单-节点反馈内容
        nodeOutcomeDocumentList: [], // 反馈成果文件
        // 表单校验
        rules: {
          feedbackType: [{
            required: true,
            message: '反馈内容不能为空',
            trigger: 'change'
          }],
          expectedCompletionDate: [{
            required: true,
            message: '预计完成时间不能为空',
            trigger: 'change'
          }],
          actualCompletionDate: [{
            required: true,
            message: '实际完成时间不能为空',
            trigger: 'change'
          }]
        },
        feedbackTypeOptions: [{
          'label': '过程反馈',
          'value': 1
        }, {
          'label': '完成反馈',
          'value': 2
        }],
        feedbackDetail: {}, // 反馈详情
        dialogType: 'update', // 新增、修改'upddate'，查看'view'
        resultFileList: [
          /*{
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-29 06:53:47",
            "updateBy": null,
            "updateTime": "2023-12-29 06:53:57",
            "remark": null,
            "params": {},
            "id": "72c0fbce7a1a447db3a8664508c59304",
            "type": "会议报告",
            "name": "会议报告",
            "fillFlag": 0,
            "status": 0,
            "delFlag": "0"
          },
          {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-31 09:55:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "id": "72c0fbce7a1a447db3agdf86558c59304",
            "type": "验收报告",
            "name": "验收报告",
            "fillFlag": 0,
            "status": 1,
            "delFlag": "0"
          }*/
        ], // 成果文件列表
        selectedFileList: [], // 已选成果文件列表
        resultOpen: false, // 成果文件选择弹出层
        resultFileTotal: 0, // 成果文件总条数
        resultFileLoading: false, // 成果文件加载
        queryResultFileParams: {
          pageNum: 1,
          pageSize: 200,
          status: 0
        },
        stages:[],
        projectLot:[],
      }
    },
    computed: {
      deptList() {
        return this.$store.state.plan.deptList
      },
      latestVersionList() {
        return this.$store.state.plan.latestVersionList
      },
      nodeStatusOption() {
        return nodeStatusOption
      },
      statusOption() {
        return statusOption
      },
      fillFlagOption() {
        return fillFlagOption
      },
      feedbackFlowStatusOption() {
        return feedbackFlowStatusOption
      },
      ...mapState('plan', { storeProjectList: 'projectList' })
    },
    async created() {
      this.getList()
      //获取城市公司
      this.$store.dispatch('plan/fetchDeptList')
      this.getPlanNameList()
      this.getdictsHandle()
      this.$bus.$on('refreshFeedbackList', () => {
        this.getList()
      })
    },
    methods: {
      getdictsHandle(){
        // this.getDicts('departs').then(res=>{
        //   let data=res.data
        //   this.departs=data.map(item=>{
        //     return {
        //       label:item.dictLabel,
        //       value:item.dictValue,
        //       raw:item
        //     }}
        //   )
        // }).catch(err=>{
        //   console.log(err)
        // })
        this.getDicts('stages').then(res=>{
          let data=res.data
          this.stages=data.map(item=>{
            return {
              label:item.dictLabel,
              value:item.dictValue,
              raw:item
            }}
          )
        }).catch(err=>{
          console.log(err)
        })


        this.getDicts('project_lot').then(res=>{
          let data=res.data
          this.projectLot=data.map(item=>{
            return {
              label:item.dictLabel,
              value:item.dictValue,
              raw:item
            }}
          )
        }).catch(err=>{
          console.log(err)

        })

      },
      getPlanNameList() {
        // deptNum 城市名称
        // projectName 项目名称
        let query = {
          deptNum: this.queryParams.deptNum,
          projectName: this.queryParams.projectName
        }
        allPlanName(query).then(res => {
          this.planNameList = res.data
        })
      },
      clearFeedbackUserInfo() {
        this.$refs.userRef.clearSelect()
        this.feedBackUserInfo = { userName: '' }
        this.queryParams.feedbackUser = ''
      },
      selectFeedbackFun() {
        this.$refs.userRef.show()
      },
      selectFeedbackData(data) {
        this.feedBackUserInfo = data
        this.queryParams.feedbackUser = data.userName
      },
      handleQueryProject(value) {
        this.$store.dispatch('plan/fetchProjectList', { name: '', company: value })
        this.getPlanNameList()
      },
      // 刷新审批状态
      refreshApproval(row, index) {
        this.loading = true
        getPlanStatus(row.id).then(res => {
          this.loading = false
          // this.infoList[index].status = res.data
          // this.infoList = [...this.infoList]
          this.queryParams.pageNum = 1
          this.getList()
        }).catch(err => {
          this.loading = false
        })
      },
      // 查看审批流
      fetchApprovalUser(row) {
        this.loading = true
        getApproveFlow(row.id).then(res => {
          // this.dialogVisible = true
          // this.approveUserInfo = res.data
          this.loading = false
          if (res.data) {
            window.open(res.data)
          }
        }).catch(err => {
          this.loading = false

        })
      },
      tableBthShow(btnType, row) {
        // status为 0 1 4   同时 feedback_flow_status 为0 3 4时，显示过程反馈  完成反馈
        // status 为 2  3时，显示 查看
        // 审批中的 刷新状态
        // 查看审批流的 除了0  都显示
        switch (btnType) {
          case 'view':
            return row.status === 2 || row.status === 3
          case 'update':
            return (row.status === 0 || row.status === 1 || row.status === 4)
              && (row.feedbackFlowStatus === 0 || row.feedbackFlowStatus === 3 || row.feedbackFlowStatus === 4)
          case 'getApproveFlow': // 查看审批流
            return row.feedbackFlowStatus !== 0
          case 'refreshFlowStatus': // 刷新状态
            return row.feedbackFlowStatus === 1
          default:
            return false
        }
      },
      handleSetDate() {
        let date = new Date(this.form.actualCompletionDate)
        // this.form.actualCompletionDate = new Date(this.form.actualCompletionDate).setDate(new Date(this.form.actualCompletionDate).getDate() - this.calcDate);
        this.form.actualCompletionDate = new Date(date.setDate(date.getDate() - this.calcDate))
        this.$forceUpdate()
      },
      /** 查询成果设置列表 */
      getResultFileNodeList() {
        this.resultFileLoading = true
        listConfig(this.queryResultFileParams).then(response => {
          this.resultFileList = response.rows
          this.resultFileTotal = response.total
          this.resultFileLoading = false
        })
      },
      handleResultOpen() {
        this.resultOpen = true
        this.getResultFileNodeList()
      },
      submitResultFile() {
        this.resultOpen = false
        for (let item of this.selectedFileList) {
          item.resultConfigId = item.id
          delete item.id
        }
        this.nodeOutcomeDocumentList = this.nodeOutcomeDocumentList.concat(
          this.selectedFileList.map(item => {
            return {
              ...item,
              createTime: null,
              fileList: []
            }
          }))
      },
      cancelFile() {
        this.resultOpen = false
      },
      handleFileSelectionChange(selection) {
        this.selectedFileList = selection
      },
      /* async downloadFile(url, fileName) {
         const response = await fetch(url);
         const blob = await response.blob(); // 将响应转换为 Blob
         const downloadUrl = window.URL.createObjectURL(blob);
         const link = document.createElement('a');
         link.href = downloadUrl;
         link.download = fileName;
         link.click();
         window.URL.revokeObjectURL(downloadUrl);
       },*/
      handleFYRadioChange(val) {
        this.rules.expectedCompletionDate[0].required = val !== 2
        this.form.feedbackType = val
      },
      handleDeleteAnnexUrl(row, index) {
        // row.annexUrl = null;
        // row.createTime = null;
        row.fileList.splice(index, 1)
        if (row.fileList.length === 0) {
          row.createTime = null
        }
      },
      uploadFileHandle(event, row, index) {
        const files = event.target.files
        if (!files) { //
          // 没有选择文件
          return
        }
        //  模拟数据
        // {
        //   type: null,
        //   createTime: null,
        //   annexUrl: null,
        //   outcomeDocumentsDesc: null,
        //   annexName: null
        // }
        // this.nodeOutcomeDocumentList[index].annexName = file.name;
        // this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
        const formData = new FormData()
        for (let item of files) {
          formData.append('files', item)
        }
        uploadFileMultiple(formData).then(response => {
          // this.nodeOutcomeDocumentList[index].annexUrl = response.data.url;
          // this.nodeOutcomeDocumentList[index].ossFileName = response.data.name;
          this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
          this.nodeOutcomeDocumentList[index].fileList = response.data
          this.$forceUpdate()
        })
          .catch(error => {
            console.error(error)
          })
      },
      /** 查询计划反馈列表 */
      getList() {
        this.loading = true
        console.log(this.queryParams)
        listFeedback({ ...this.queryParams, status: this.queryParams.status?.join() }).then(response => {
          this.feedbackList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          nodeCode: null,
          feedbackPeople: null,
          projectId: null,
          stageId: null,
          completeTime: null,
          notes: null,
          annexUrl: null,
          tenantId: null,
          creator: null,
          createTime: null,
          updater: null,
          updateTime: null,
          deleted: null,
          planId: null,
          feedbackType: 1
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      handleNodeDocumentChange(selection) { // 成果文件选中

      },
      handleNodeDocument() { // 成果文件新增
        /*成果类型String type;
        成果名称String name;
        创建时间"yyyy-MM-dd HH:mm:ss" Date createTime;
        专业成果文件和证明材料描述 String outcomeDocumentsDesc;
        附件 String annexUrl;
        是否必填（0-是 1-否）Integer fillFlag;
        文件名称 annexName;
        文件上传后的附件名称 ossFileName
        文件集合fileList
        */
        this.nodeOutcomeDocumentList.push({
          type: null,
          createTime: null,
          annexUrl: null,
          outcomeDocumentsDesc: null,
          annexName: null,
          ossFileName: null,
          fileList: []
        })
      },
      handleNodeDocumentDelete(row, index) { // 成果文件删除
        this.nodeOutcomeDocumentList.splice(index, 1)
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '节点反馈维护'
      },
      handleView(row) {
        this.$router.push({
          path: '/plan/feedbackDetail',
          query: {
            id: row.id,
            planId: row.planId
          }
        })
      },
      setDatePikcer(value, formKey) { // el-date-picker绑定form-date无效，使用此方法
        this.form[formKey] = value
        this.$forceUpdate()
      },
      /** 修改按钮操作 */
      handleUpdate(row, feedbackType) {
        console.log(row)
        // 1:过程反馈，2：完成反馈
        this.rules.expectedCompletionDate[0].required = feedbackType === 1
        this.rules.actualCompletionDate[0].required = feedbackType === 2
        this.dialogType = 'update'
        this.reset()
        const id = row.id || this.ids
        this.form.feedbackType = feedbackType
        this.$router.push({
          path: '/plan/feedbackUpdate',
          query: {
            id: row.id,
            planId: row.planId,
            feedbackType: feedbackType
          }
        })
        /*getFeedback(id).then(response => {
          this.feedbackDetail = response.data;
          this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList || [];
          this.form.nodeId = response.data.id;
          this.form.feedbackProgress = response.data.feedbackProgress || 0; // 如果没有反馈进度为null，表单校验报错
          this.form.expectedCompletionDate = response.data.expectedCompletionDate || new Date();
          this.form.actualCompletionDate = response.data.actualCompletionDate || new Date();
          this.form.notes = response.data.notes;
          this.title = "节点反馈维护";
          if(feedbackType === 2){
            this.form.feedbackProgress = 100;
          }
          this.$router.push({
            path: '/plan/feedbackUpdate',
            query: {
              id: row.id,
              feedback: JSON.stringify({
                feedbackDetail: this.feedbackDetail,
                form: this.form,
                nodeOutcomeDocumentList: this.nodeOutcomeDocumentList
              }),
            }
          });
          // this.open = true;
        });*/
      },
      fileValidate() {
        // 校验成果文件选择文件是否上传
        // fillFlag 0 必填，1 非必填
        let list = this.nodeOutcomeDocumentList.map(item => {
          if (item.fillFlag === 0 && item.fileList.length === 0) {
            item.fileListError = '成果文件不能为空'
          } else {
            item.fileListError = null
          }
          return item
        })
        this.nodeOutcomeDocumentList.splice(0, this.nodeOutcomeDocumentList.length, ...list)
        return this.nodeOutcomeDocumentList.every(item => !item.fileListError)
      },
      /** 提交按钮 */
      submitForm() {
        this.$forceUpdate()
        this.$refs['form'].validate(valid => {
          if (valid && this.fileValidate()) {
            // 过程反馈、完成反馈接口判断
            let feedbackPost = this.form.feedbackType === 1 ? progressFeedback : finishFeedback // 1 过程反馈，2 完成反馈
            feedbackPost({
              ...this.form,
              planId: this.$route.query.planId,
              nodeOutcomeDocumentList: this.nodeOutcomeDocumentList
            }).then(response => {
              //保存成功后发起流程
              flowCreate({
                ...this.form
              }).then(response => {
                //保存成功后发起流程
                window.open(response.data)
                this.$modal.msgSuccess('节点反馈成功')
                this.open = false
                this.queryParams.pageNum = 1
                this.getList()
              })
            })
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids
        this.$modal.confirm('是否确认删除计划反馈编号为"' + ids + '"的数据项？').then(function() {
          return delFeedback(ids)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        }).catch(() => {
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('plan/feedback/export', {
          ...this.queryParams
        }, `feedback_${new Date().getTime()}.xlsx`)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .feedback-container {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;

    .feedback-form-title {
      margin-bottom: 20px;
      background-color: #f8f8f9;
      padding: 10px;
      width: 100%;
    }

    .icon-primary {
      color: #409eff;
    }

    .cursor {
      cursor: pointer;
    }

    .display-none {
      display: none !important;
    }

    .file-wrapper {
      .el-icon-circle-close {
        margin-left: 10px;
      }
    }

    .link {
      color: #409eff;
      display: inline-block; /* 或 block，取决于布局需求 */
      max-width: 150px; /* 设置最大宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏溢出的文本 */
      text-overflow: ellipsis; /* 显示省略号 */
      direction: ltr; /* 从右到左的文本方向 */
      text-align: left; /* 文本对齐方式 */
      &:hover {
        color: #1ab394;
      }

      margin-bottom: 3px;
    }
  }

  .transfer-item {
    display: flex;

    .item {
      text-align: center;
      margin-right: 5px;
    }
  }

  .file-show {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

</style>
