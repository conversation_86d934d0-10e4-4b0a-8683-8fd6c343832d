<template>
  <div class="my-weekly">

    <!-- 添加顶部tab切换 -->
    <div class="my-weekly-tabs">
      <van-tabs v-model:active="activeTab" @click="onClickTab">
        <van-tab v-for="(key) of Object.keys(approveStatusFilter)" :key="key" :title="approveStatusFilter[key]" :name="key"></van-tab>
      </van-tabs>
    </div>

    <div class="filter-container">
      <!-- <span class="filter-label">筛选</span> -->
     <!--  <van-button
        type="default"
        size="small"
        @click="showDeptPicker = true"
        class="dept-select-btn"
        icon="arrow-down"
        icon-position="right"
      >
        {{ selectedDeptName || "部门" }}
      </van-button> -->
      <div
        @click="showDeptPicker = true"
        class="dept-select-btn"
      >
        {{ selectedDeptName || "部门" }}
      </div>
     <!--  <van-button
        type="default"
        size="small"
        @click="handleWeekDropdown"
        class="dept-select-btn"
        icon="arrow-down"
        icon-position="right"
      >
        {{ filterWeekLabel(selectedWeek) }}
      </van-button> -->
      <van-dropdown-menu ref="dropdownMenuRef" class="dept-select-btn year">
        <van-dropdown-item v-model="year" :options="yearOptions" />
      </van-dropdown-menu>
      <van-dropdown-menu ref="dropdownMenuRef" class="dept-select-btn">
        <van-dropdown-item v-model="selectedWeek" :options="weekOptions" @change="onWeekChange"/>
      </van-dropdown-menu>
      <!-- <van-button
        type="default"
        size="small"
        @click="showUserPickerVisible = true"
        class="dept-select-btn"
        icon="arrow-down"
        icon-position="right"
      >
        {{ "员工" }}
      </van-button> -->
      <div
        @click="showUserPickerVisible = true"
        class="dept-select-btn"
      >
        {{ "员工" }}
      </div>
    </div>
    <div class="my-weekly-statics">
      <div class="statics-item">
        <div class="statics-item-value">{{ unsubmitNum }}</div>
        <div class="statics-item-title">{{activeTab == 0 ? '未填报数' : '已填报数'}}</div>
      </div>
      <div class="statics-item">
        <div class="statics-item-value">{{ totalNum }}</div>
        <div class="statics-item-title">需填报总数</div>
      </div>
    </div>
    <div class="weekly-table">
      <LoadMoreTable
        ref="tableRef"
        :auto-load="true"
        :page-size="20"
        :fetchApi="staListAPI"
        :queryParams="queryParams"
        :weekOptions="weekOptions"
      />
    </div>
    <!-- 选择部门 -->
    <van-popup
      v-model="showDeptPicker"
      position="left "
      :style="{ width: '90%', height: '100%' }"
    >
      <div class="dept-tree-container">
        <div class="dept-tree-header">
          <div class="dept-tree-title">选择部门</div>
          <van-icon name="cross" @click="showDeptPicker = false" />
        </div>
        <div class="dept-tree-content">
          <el-tree
            class="tree-container"
            :data="deptTreeObj"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            @node-click="handleNodeClick"
            :default-expanded-keys="defaultExpandedKeys"
          />
        </div>
        <div class="dept-tree-footer">
          <van-button type="primary" block @click="showDeptPicker = false"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>
    <!-- 选择周期 -->
   <!--  <van-popup
      v-model="showPeriodPicker"
      position="bottom"
      :style="{ height: '45%' }"
    >
      <van-picker
        show-toolbar
        :columns="weekOptions"
        :default-index="currentWeek"
        @confirm="onWeekConfirm"
        @cancel="onWeekCancel"
      />
    </van-popup> -->
    <!-- 选择员工 -->
    <select-user-mobile
      ref="userRef"
      :multiple="true"
      :show="showUserPickerVisible"
      :value="selectedUsers"
      @select="handleSelectUser"
      @close="showUserPickerVisible = false"
    />
  </div>
</template>

<script>
import {
  NavBar,
  DropdownMenu,
  DropdownItem,
  PullRefresh,
  List,
  Tag,
  Button,
  SwipeCell,
  Dialog,
  Popup,
  Picker,
  TreeSelect,
  Icon,
  Tabs,
  Tab,
  Toast,
} from "vant";
// import { approveList, listInfo } from "@/api/weekly/mobile-reportInfo.js";
import {
  approveList,
  listInfo,
  submitDraft,
  delInfo,
  callbackWeekly,
  deptTree,
  statNum,
  staList,
} from "@/api/weekly/mobile-reportInfo.js";
import LoadMoreTable from "./components/loadMoreTable.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SelectUserMobile from "./components/SelectUserStatictisMobile.vue";
import {formatDate as formatDateUtil} from '@/utils/index'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import localeData from 'dayjs/plugin/localeData'

// 注册 weekOfYear 插件
dayjs.extend(weekOfYear)
dayjs.extend(localeData)
// 设置每周从周一开始
dayjs.locale('zh-cn', {
  weekStart: 1
})
export default {
  name: "MyWeekly",
  components: {
    [NavBar.name]: NavBar,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
    [Tag.name]: Tag,
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Dialog.name]: Dialog,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    LoadMoreTable,
    [TreeSelect.name]: TreeSelect,
    [Icon.name]: Icon,
    SelectUserMobile,
    [Tabs.name]: Tabs,
    [Tab.name]: Tab,
    [Toast.name]: Toast,
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      year: new Date().getFullYear().toString(),
      weekType: "全部周",
      yearOptions: this.generateYearOptions(),
      weekOptions: [],
      queryParams: {
        // pageNum: 1, // 在table组件中处理pageNum
        // pageSize: 20,
        userCode: undefined, // 员工编码
        deptId: undefined, // 部门ID
        // startDate: undefined, //
        // endDate: undefined, //
        approveStatus: undefined,
      },
      totalNum: 0,
      unsubmitNum: 0,
      staListAPI: staList,
      deptTreeObj: [],
      showDeptPicker: false,
      deptColumns: [],
      selectedDeptName: "",
      selectedWeek: "",
      deptTreeItems: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      defaultExpandedKeys: [],
      showPeriodPicker: false,
      showUserPickerVisible: false,
      selectedUsers: [],
      activeTab: 0,

      weeklyForm: {
        reportReviewDetails: [],
        selectUserIds: [],
      },
      approveStatusFilter: {
        0: '未填报',
        1: '已填报'
      },
      currentWeek: ''
    };
  },
  created() {
    this.generateWeekOptions();
    // 初始化查询参数
    this.queryParams.year = this.year;
    this.queryParams.week = "";
    this.queryParams.approveStatus = this.activeTab;
    this.fetchDeptTree();

    // 获取当前是第几周
    this.currentWeek = this.getCurrentWeekOfYear();
    this.selectedWeek = this.currentWeek.toString();
    this.queryParams.week = this.selectedWeek === '全部周' ? '' : this.selectedWeek;
    this.fetchStatNum();
  },
  methods: {

    filterWeekLabel(week){
      if(week === '全部周'){
        return week;
      }
      else if(!!week){
        return `第${week}周`;
      }
      return "周期";
    },
    refreshTable() {
      console.log("refreshTable");
      this.fetchStatNum();
      // this.queryParams.pageNum = 1;
      // const item = this.weekOptions.find(item => item.value == this.selectedWeek)
      // this.queryParams.startDate = item?.startDay;
      // this.queryParams.endDate = item?.endDay;
      this.$refs.tableRef.initLoadData();
    },
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.selectedDeptName = data.label;
      this.refreshTable();
      this.showDeptPicker = false;
    },
    onClickTab(tab) {
      console.log("tab", tab);
      // Update to use tab.name instead of this.activeTab
      this.queryParams.approveStatus = tab;
      this.refreshTable();
    },
    handleSelectUser(data) {
      // 保存选中的用户
      this.selectedUsers = data;

      // 更新审阅人显示名称
      this.approveUserNamesArray = data.map((item) => item.nickName);

      // 更新表单数据
      this.weeklyForm.reportReviewDetails = data.map((item) => ({
        /*
          按照组件格式在form表单中保存选中的用户，注意实际在取值时在submitForm做了转换
          approveUserCode: item.userName
          approveUserName: item.nickName
        */
        userId: item.userId,
        userName: item.userName,
        nickName: item.nickName,
      }));

      // 更新选中的用户ID数组
      this.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );

      this.queryParams.userCode = this.selectUserIds.join(",");
      this.refreshTable();
    },
    onWeekChange(value){ // value 是当前选中的周 1,2等值
      console.log("value", value);
      this.selectedWeek = value;
      this.queryParams.week = this.selectedWeek === '全部周' ? '' : this.selectedWeek;
      console.log('this.queryParams.week---', this.queryParams.week)
      this.refreshTable();
    },
    onWeekConfirm(value) {
      console.log("value", value);
      // this.queryParams.week = value.value;
      this.showPeriodPicker = false;
      this.selectedWeek = value.value;
      this.queryParams.week = this.selectedWeek === '全部周' ? '' : this.selectedWeek;
      console.log('this.queryParams.week---', this.queryParams.week)
      this.refreshTable();
    },
    onWeekCancel() {
      this.showPeriodPicker = false;
    },
    // 设置默认展开的节点
    setDefaultExpandedKeys() {
      // 如果有数据，默认展开第一层的子节点（即第二层级）

      if (this.deptTreeObj && this.deptTreeObj.length > 0) {
        // 获取第一层节点的ID
        const firstLevelNode = this.deptTreeObj[0];
        if (firstLevelNode.children && firstLevelNode.children.length > 0) {
          // 将第一层节点的ID添加到默认展开的keys中
          this.defaultExpandedKeys = [firstLevelNode.id];
          console.log("defaultExpandedKeys", this.defaultExpandedKeys);
        }
      }
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 加载部门树
    fetchDeptTree() {
      deptTree().then((res) => {
        console.log("deptTree", res);
        if (res.code === 200) {
          this.deptTreeObj = res.data;
          this.deptColumns = this.generateDeptColumns(res.data);
          this.deptTreeItems = this.generateDeptTreeItems(res.data);
          this.setDefaultExpandedKeys();
          console.log("deptColumns", this.deptColumns, this.deptTreeObj);
        } else {
          this.deptTreeObj = [];
          this.deptColumns = [];
          this.deptTreeItems = [];
        }
      });
    },
    // 加载统计数据
    fetchStatNum() {
      Toast.loading({
        message: 'Loading...',
        forbidClick: true,
      });
      const params = {
        year: this.year,
        week: this.selectedWeek === '全部周' ? '' : this.selectedWeek,
        deptId: this.queryParams.deptId,
        userCode: this.queryParams.userCode,
        approveStatus: this.queryParams.approveStatus
      };
      statNum(params).then((res) => {
        if (res.code === 200) {
          this.totalNum = res.data.totalNum;
          this.unsubmitNum = res.data.num;
        } else {
          this.totalNum = 0;
          this.unsubmitNum = 0;
        }
      }).finally(() => {
        Toast.clear();
      });
    },
    // 生成年份选项
    generateYearOptions() {
      const years = [
        /* {
          text: "全部年",
          value: "",
        },*/
      ];
      const currentYear = new Date().getFullYear();
      // 从系统设计运行时间2025年开始
      for (let i = 2025; i <= currentYear; i++) {
        const year = i++;
        years.push({
          // text: `${year}年`,
          text: `${year}`,
          value: year.toString(),
        });
      }
      return years;
    },
    // 生成周选项
    generateWeekOptions() {
      const year = parseInt(this.year);
      const weeks = [];
      // 去掉全部周
      // weeks.push({ text: "全部周", value: "全部周" });

      // 获取该年第一天
      const firstDay = new Date(year, 0, 1);
      // 获取该年最后一天
      const lastDay = new Date(year, 11, 31);

      let currentDate = firstDay;
      let weekNum = 1;

      while (currentDate <= lastDay) {
        // 获取本周的开始日期（周一）
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1);

        // 获取本周的结束日期（周日）
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);

        // 格式化日期
        const startStr = this.formatDate(weekStart);
        const endStr = this.formatDate(weekEnd);

        weeks.push({
          text: `第${weekNum}周 (${startStr}-${endStr})`,
          value: weekNum.toString(),
          startDay: dayjs(weekStart).format('YYYY-MM-DD'), // 使用dayjs格式化为年-月-日
          endDay: dayjs(weekEnd).format('YYYY-MM-DD'), // 使用dayjs格式化为年-月-日
          weekNum,
          weekDay: `${startStr}-${endStr}`, // 周时间范围
          weekNumPeriod: `${startStr}-${endStr}`
        });

        // 移到下一周的第一天
        currentDate.setDate(currentDate.getDate() + 7);
        weekNum++;
      }

      this.weekOptions = weeks;
      console.log("weekOptions", this.weekOptions);
    },
    // 格式化日期
    formatDate(date) {
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${month}/${day}`;
    },
    generateDeptColumns(deptTree) {
      const columns = [];
      const generateColumns = (node, level = 0) => {
        const column = {
          text: node.label,
          value: node.id,
          children: [],
        };
        if (node.children && node.children.length > 0) {
          column.children = node.children.map((child) =>
            generateColumns(child, level + 1)
          );
        }
        return column;
      };
      columns.push(generateColumns(deptTree[0]));
      return columns;
    },
    generateDeptTreeItems(deptTree) {
      // TreeSelect 组件需要特定的数据结构
      const formatDeptTree = (nodes) => {
        if (!nodes || !nodes.length) return [];

        return nodes.map((node) => {
          const item = {
            text: node.label,
            id: node.id,
          };

          if (node.children && node.children.length) {
            item.children = formatDeptTree(node.children);
          }

          return item;
        });
      };

      return formatDeptTree(deptTree);
    },
    onDeptConfirm(value, index) {
      this.showDeptPicker = false;
    },
    // 获取当前是一年中的第几周
    getCurrentWeekOfYear() {
      // 设置周一为每周的第一天，ISO标准
      return dayjs().week();
    },
    // 获取默认选中的周期索引
    getDefaultWeekIndex() {
      // 如果已经选择了周期，则返回该周期在weekOptions中的索引
      if (this.selectedWeek) {
        return this.weekOptions.findIndex(item => item.value === this.selectedWeek);
      }

      // 否则返回当前周在weekOptions中的索引
      const currentWeek = this.getCurrentWeekOfYear().toString();
      const index = this.weekOptions.findIndex(item => item.value === currentWeek);

      // 如果找不到当前周，则默认选中第一项（全部周）
      return index > 0 ? index : 0;
    },
  },
};
</script>

<style lang="scss" scoped>
 :deep(.van-dropdown-menu__item) {
    .van-dropdown-menu__title::after {
      border-color: transparent transparent #606266 #606266;
      right: 15px;
    }
  }
:deep(.dept-select-btn .van-dropdown-menu__bar){
  width: 100%;
  height: 100%;
  background-color: unset;
  border-radius: 44px;
}
:deep(.dept-select-btn.year .van-dropdown-menu__title::after){
  right: -4px;
}
:deep(.dept-select-btn .van-ellipsis){
  font-weight: 500;
  background-color: unset;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
:deep(.van-van-dropdown-menu__title::after){
  display: none;
}
:deep(.van-button__text){
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.weekly-operate {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  .weekly-operate-item {
    padding: 3px 15px;
    border: 1px solid #007aff;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    color: #3673ff;
  }
}
:deep(.van-tabs__nav) {
  background-color: unset;
}
:deep(.van-button--normal) {
  padding: 0 10px;
}
:deep(.van-button--primary) {
  background-color: #007aff;
  border-color: #007aff;
}
:deep(.van-nav-bar) {
  background-color: unset;
}
.my-weekly {
  background-image: url("~@/assets/weeklyMobile/weeklybac.png");
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;

  :deep(.van-tabs) {
    .van-tabs__wrap {
      background-color: transparent;
    }

    .van-tab {
      color: #666;
      font-size: 15px;
    }

    .van-tab--active {
      color: #007aff;
      font-weight: 500;
    }

    .van-tabs__line {
      background-color: #007aff;
    }
  }
  .my-weekly-tabs {
    padding: 0px 40px;
  }

  .filter-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 10px 0 10px;
    .dept-select-btn {
      width: 33%;
      flex: 1;
      flex-shrink: 0;
      height: 28px;
      border-radius: 44px 44px 44px 44px;
      border: 1px solid #99b8ff;
      font-size: 14px;
      background-color: unset;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      color: #666666;
      text-align: center;
      line-height: 28px;
      /*  line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none; */
    }
    // background-color: #eef2f8;
    /* .filter-label {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 20px;
    }
    .custom-dropdown {
      flex: 1;
      :deep(.van-dropdown-menu__bar) {
        height: 36px;
        background-color: unset;
        box-shadow: none;
      }

      :deep(.van-dropdown-menu__item) {
        background-color: unset;
        margin: 0 4px;
        padding: 0 8px;
        border-radius: 44px 44px 44px 44px;
        border: 1px solid #99b8ff;

        .van-dropdown-menu__title {
          font-size: 14px;
          color: #606266;
        }

        .van-dropdown-menu__title::after {
          border-color: transparent transparent #606266 #606266;
        }
      }
    } */
  }
  .my-weekly-statics {
    margin: 8px 10px 12px;
    padding: 18px;
    display: flex;
    justify-content: space-around;
    background-color: #ebf0f8;
    border-radius: 8px;
    .statics-item {
      .statics-item-title {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 18px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .statics-item-value {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 26px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .weekly-table {
    padding: 0 10px;
    box-sizing: border-box;
    // margin-bottom: 60px;
  }
}
.dept-select-btn {
  margin-left: 10px;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  padding: 0 4px;
}

.dept-tree-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dept-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.dept-tree-title {
  font-size: 16px;
  font-weight: 500;
}

.dept-tree-content {
  flex: 1;
  overflow-y: auto;
}

.dept-tree-footer {
  padding: 15px;
  border-top: 1px solid #eee;
}
</style>
