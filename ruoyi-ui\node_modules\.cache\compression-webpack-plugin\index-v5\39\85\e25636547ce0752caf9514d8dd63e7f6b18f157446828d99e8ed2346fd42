
72ac8de894aa347b886d1ed91095b975232c6e96	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"89a390d54d48edadc54fd69eea585126\"}","integrity":"sha512-qwutE0A/RpWUcXpPxwpL+4dDox4QaIHXbF32F73OLRBEXTXjGzCg0vAVe/5vZtxjsaOpqt0cESXdrFd+N4VP0w==","time":1754311735774,"size":12044674}