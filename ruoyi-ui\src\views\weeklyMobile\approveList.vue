<template>
  <div class="my-weekly">
    <!--    <van-nav-bar title="我的审阅" left-arrow @click-left="onClickLeft" />-->

    <div class="filter-container">
      <van-dropdown-menu class="custom-dropdown">
        <van-dropdown-item v-model="year" :options="yearOptions"/>
        <van-dropdown-item v-model="weekType" :options="weekOptions"/>
      </van-dropdown-menu>
    </div>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list-container">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <van-swipe-cell v-for="(item, index) in list" :key="index">
          <div class="weekly-card" @click="handleDetail(item)">
            <div class="weekly-title">
              <div>{{ item.year }}年{{ item.week }}周</div>
              <div class="weekly-status">
                <van-tag :type="getStatusType(item.pushType)">{{
                    getStatusText(item.pushType)
                  }}
                </van-tag>
              </div>
            </div>
            <div class="weekly-info">
              <div class="info-item">
                填报周期：{{ item.startDate }} ~ {{ item.endDate }}
              </div>
              <div class="info-item">
                审阅日期：{{ item.nodeApprovalDate || "--" }}
              </div>
            </div>
            <div class="weekly-operate">
              <div
                v-if="item.pushType === 0"
                class="weekly-operate-item"
                @click.stop="handleApprove(item)"
              >
                审阅
              </div>
            </div>
          </div>
          <template #right>
            <!--  <van-button
              square
              text="删除"
              type="danger"
              class="delete-button"
              @click.stop="handleDelete(item)"
            /> -->
          </template>
        </van-swipe-cell>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import {
  NavBar,
  DropdownMenu,
  DropdownItem,
  PullRefresh,
  List,
  Tag,
  SwipeCell,
} from "vant";
import {approveDaiBanList} from "@/api/weekly/mobile-reportInfo.js";
// import { approveDaiBanList } from "@/api/weekly/reportInfo.js";

export default {
  name: "MyWeekly",
  components: {
    [NavBar.name]: NavBar,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
    [Tag.name]: Tag,
    [SwipeCell.name]: SwipeCell
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      year: new Date().getFullYear().toString(),
      weekType: "全部周",
      yearOptions: this.generateYearOptions(),
      weekOptions: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        year: "",
        week: "",
      },
    };
  },
  created() {
    this.generateWeekOptions();
    // 初始化查询参数
    this.queryParams.year = this.year;
    this.queryParams.week = "";
  },
  methods: {
    handleDetail(row) {
      this.$router.push({
        path: `/wechatE/mobile/approveDetail/${row.id}`,
      });
    },
    handleApprove(row) {
      this.$router.push({
        path: `/wechatE/mobile/weeklyApprove/${row.id}`,
      });
    },
    getStatusType(status) {
      switch (status) {
        case 0:
          return "primary";
        case 1:
          return "success";
        default:
          return "success";
      }
    },
    getStatusText(status) {
      // 审阅状态（0-待阅 1-已阅）
      const statusMap = {
        0: "待阅",
        1: "已阅",
      };
      return statusMap[status] || "未知";
    },
    onClickLeft() {
      this.$router.back();
    },
    // 生成年份选项
    generateYearOptions() {
      const years = [
        /* {
           text: "全部年",
           value: "",
         },*/
      ];
      const currentYear = new Date().getFullYear();
      // 从系统设计运行时间2025年开始
      for (let i = 2025; i <= currentYear; i++) {
        const year = i++;
        years.push({
          text: `${year}年`,
          value: year.toString(),
        });
      }
      console.log(years);
      return years;
    },
    // 生成周选项
    generateWeekOptions() {
      const year = parseInt(this.year);
      const weeks = [];
      weeks.push({text: "全部周", value: "全部周"});

      // 获取该年第一天
      const firstDay = new Date(year, 0, 1);
      // 获取该年最后一天
      const lastDay = new Date(year, 11, 31);

      let currentDate = firstDay;
      let weekNum = 1;

      while (currentDate <= lastDay) {
        // 获取本周的开始日期（周一）
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1);

        // 获取本周的结束日期（周日）
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);

        // 格式化日期
        const startStr = this.formatDate(weekStart);
        const endStr = this.formatDate(weekEnd);

        weeks.push({
          text: `第${weekNum}周 (${startStr}-${endStr})`,
          value: weekNum.toString(),
        });

        // 移到下一周的第一天
        currentDate.setDate(currentDate.getDate() + 7);
        weekNum++;
      }

      this.weekOptions = weeks;
    },
    // 格式化日期
    formatDate(date) {
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${month}/${day}`;
    },
    async onLoad() {
      if (this.refreshing) {
        console.log("refreshing----------", this.refreshing);
        this.list = [];
        this.refreshing = false;
      }

      try {
        console.log(222, "this.list.length---", this.list.length, "this.queryParams.pageNum----", this.queryParams.pageNum);
        const res = await approveDaiBanList(this.queryParams);
        // 临时处理列表高度变化两次请求引起的数据重复问题
        console.log(111, "this.queryParams.pageNum-----", this.queryParams.pageNum);
        this.list.push(...res.rows);
        // 根据id过滤掉重复数据
        this.list = Array.from(new Set(this.list.map(item => item.id))).map(id => this.list.find(item => item.id === id));
        this.loading = false;

        if (this.list.length >= res.total) {
          this.finished = true;
        }
        this.queryParams.pageNum++;
      } catch (error) {
        this.loading = false;
        this.finished = true;
      }
    },
    onRefresh() {
      this.finished = false;
      this.queryParams.pageNum = 1;
      this.onLoad();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.finished = false;
      this.list = [];
      this.onLoad();
    },
  },
  watch: {
    year(newVal) {
      this.weekType = "全部周";
      this.generateWeekOptions();
      this.queryParams.year = newVal;
      this.queryParams.week = "";
      this.handleQuery();
    },
    weekType(newVal, oldVal) {
      // 年份变化时会触发 weekType 变化，此时不需要重复请求
      if (oldVal === "全部周" && newVal === "全部周") {
        return;
      }
      this.queryParams.week = newVal === "全部周" ? "" : newVal;
      this.handleQuery();
    },
  },
};
</script>

<style lang="scss" scoped>
.weekly-operate {
  display: flex;
  gap: 10px;
  justify-content: flex-end;

  .weekly-operate-item {
    padding: 3px 15px;
    border: 1px solid #007aff;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    color: #3673ff;
  }
}

:deep(.van-button--normal) {
  padding: 0 10px;
}

:deep(.van-button--primary) {
  background-color: #007aff;
  border-color: #007aff;
}

:deep(.van-nav-bar) {
  background-color: unset;
}

.my-weekly {
  background-image: url("~@/assets/weeklyMobile/weeklybac.png");
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
}

.list-container {
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.filter-container {
  display: flex;
  align-items: center;
  padding: 20px 10px 0 10px;
  // background-color: #eef2f8;
  .filter-label {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-right: 20px;
  }
}

.custom-dropdown {
  flex: 1;

  :deep(.van-dropdown-menu__bar) {
    height: 36px;
    background-color: unset;
    box-shadow: none;
  }

  :deep(.van-dropdown-menu__item) {
    background-color: unset;
    margin: 0 4px;
    padding: 0 8px;
    border-radius: 44px 44px 44px 44px;
    border: 1px solid #99b8ff;

    .van-dropdown-menu__title {
      font-size: 14px;
      color: #606266;
    }

    .van-dropdown-menu__title::after {
      border-color: transparent transparent #606266 #606266;
    }
  }
}

.weekly-card {
  margin: 12px 10px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;

  .weekly-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 8px;
  }

  .weekly-info {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 18px;

    .info-item {
      margin-bottom: 4px;
    }
  }

  .weekly-status {
    //margin-top: 8px;
    text-align: right;
  }
}

.delete-button {
  height: 100%;
  width: 65px;
  color: #fff;
}
</style>
