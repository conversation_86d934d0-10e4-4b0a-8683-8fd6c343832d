import auth from '@/plugins/auth'
import router, {constantRoutes, dynamicRoutes} from '@/router'
import {getRouters} from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import InnerLink from '@/layout/components/InnerLink'

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = constantRoutes.concat(routes)
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes)
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [{
        path: 'index',
        meta: { title: '统计报表', icon: 'dashboard' }
      }]
      state.topbarRouters = routes.concat(index);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      // 周报管理 ---周报管理：我的周报，我的审阅在src/store/modules/permission.js中通过静态路由配置
      //  我的周报，我的审阅不受权限控制

      state.sidebarRouters = routes
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes({ commit }) {
      return new Promise(resolve => {
        // 向后端请求路由数据
        getRouters().then(res => {
          const sdata = JSON.parse(JSON.stringify(res.data))
          const rdata = JSON.parse(JSON.stringify(res.data))
          const sidebarRoutes = filterAsyncRouter(sdata)
          const rewriteRoutes = filterAsyncRouter(rdata, false, true)
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
          console.log('asyncRoutes----------', asyncRoutes)
          console.log('rewriteRoutes----------', rewriteRoutes)
          console.log('sidebarRoutes----------', sidebarRoutes)
          router.addRoutes(asyncRoutes);
          commit('SET_ROUTES', rewriteRoutes)

         
      // 查找动态路由中name为Weekly的路由
      let weeklyDynamicRoute = null;
      for(let item of sidebarRoutes) {
        if(item.name === 'Weekly') {
          weeklyDynamicRoute = item;
          break;
        }
      }
      
      // 查找静态路由中name为Weekly的路由
      let weeklyStaticRoute = null;
      for(let item of constantRoutes) {
        if(item.name === 'Weekly') {
          weeklyStaticRoute = item;
          break;
        }
      }
      
      // 如果找到了两个路由，将动态路由的子路由添加到静态路由中
      if(weeklyDynamicRoute && weeklyStaticRoute) {
        // 确保静态路由有children数组
        if(!weeklyStaticRoute.children) {
          weeklyStaticRoute.children = [];
        }
        
        // 将动态路由的子路由添加到静态路由中
        // 可以选择性地过滤或处理子路由
        if(weeklyDynamicRoute.children && weeklyDynamicRoute.children.length > 0) {
          // 可以在这里对子路由进行处理，例如过滤掉某些路由
          const processedChildren = weeklyDynamicRoute.children.map(child => {
            // 处理每个子路由，例如添加额外属性
            return { ...child, meta: { ...child.meta, dynamicAdded: true } };
          });
          
          // 将处理后的子路由添加到静态路由中
          weeklyStaticRoute.children = [...weeklyStaticRoute.children, ...processedChildren];
        }
      }

          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))
          commit('SET_DEFAULT_ROUTES', sidebarRoutes)
          commit('SET_TOPBAR_ROUTES', sidebarRoutes)
          resolve(rewriteRoutes)
        })
      })
    }
  }
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else if (route.component === 'InnerLink') {
        route.component = InnerLink
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView' && !lastRouter) {
        el.children.forEach(c => {
          c.path = el.path + '/' + c.path
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
    }
    children = children.concat(el)
  })
  return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = []
  routes.forEach(route => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route)
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route)
      }
    }
  })
  return res
}

export const loadView = (view) => {
  if (process.env.NODE_ENV === 'development') {
    return (resolve) => require([`@/views/${view}`], resolve)
  } else {
    // 使用 import 实现生产环境的路由懒加载
    return () => import(`@/views/${view}`)
  }
}

export default permission
