<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="城市公司" prop="cityId">
        <!--        <el-input
                  v-model="queryParams.cityName"
                  placeholder="请输入城市公司名称"
                  clearable
                  size="small"
                  @keyup.enter.native="handleQuery"
                />-->
        <el-select
          v-model="queryParams.cityId"
          placeholder="请选择城市公司"
          @change="handleCitySearch"
          clearable
          class="width-100">
          <el-option
            v-for="item in cites"
            :key="item.cityId"
            :label="item.cityName"
            :value="item.cityId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目" prop="projectCode">
<!--        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />-->
        <el-select
          v-model="queryParams.projectCode"
          placeholder="请选择项目"
          @change="handleProjectSearch"
          clearable
          class="width-100"
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.displayName"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:info:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:info:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:info:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="城市公司" align="center" prop="cityName"/>
      <el-table-column label="项目名称" align="center" prop="projectName"/>
      <el-table-column label="项目编码" align="center" prop="projectCode"/>
      <el-table-column label="案名" align="center" prop="name"/>
      <el-table-column label="地块名" align="center" prop="plotName"/>
      <el-table-column label="拿地时间" align="center" prop="takeLandDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.takeLandDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次开盘时间" align="center" prop="firstOpenDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.firstOpenDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次竣备时间" align="center" prop="firstCompletedDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.firstCompletedDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首批合同交付时间" align="center" prop="firstDeliverDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.firstDeliverDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleConfigBuilding(scope.row)"
            v-hasPermi="['project:info:buildingConfig']"
          >楼栋配置
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:info:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listInfo, getInfo, addInfo, updateInfo, delInfo} from "@/api/projectOperate/projectInfo";
import API from '@/views/projects/api'
export default {
  name: "Project",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      projectNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目基本信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        projectCode: null,
        projectName: null,
        cityId: null,
        cityName: null,
        name: null,
        plotName: null,
        companyRegisterName: null,
        belongCompanyName: null,
        shareholdRate: null,
        operateStripline: null,
        combineTableContent: null,
        takeLandDate: null,
        firstOpenDate: null,
        firstCompletedDate: null,
        firstDeliverDate: null,
        landCost: null,
        capacityArea: null,
        canSalesArea: null,
        constraintPricePolicy: null,
        capacityPrice: null,
        canSalesPrice: null,
        useLandArea: null,
        plotRatio: null,
        supportSituation: null,
        completedDeliverModality: null,
        buildingPic: null,
        birdPic: null,
        locationPic: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 城市公司列表
      cites: [],
      // 项目列表
      projects: [],
      detailDisabled: false
    };
  },
  created() {
    this.getList();
    this.getCities();
  },
  mounted() {
    this.$bus.$on('refreshProjectOperateInfo', () => {
      this.getList()
    })
  },
  methods: {
    handleCitySearch(val) {
      this.projects = null;
      this.queryParams.projectCode = null;
      !!this.queryParams.cityId ? this.getProjects(this.queryParams.cityId) : null;
      this.handleQuery();
    },
    handleProjectSearch(val) {
      this.handleQuery()
    },
    handleCityChange() {
      this.form.cityName = this.cites.find(item => item.cityId === this.form.cityId).cityName;
      this.projects = null;
      this.form.projectName = null;
      this.form.projectCode = null;
      this.getProjects(this.form.cityId)
    },
    handleProjectChange() {
      const project = this.projects.find(item => item.projectCode === this.form.projectCode)
      this.form.projectName = project.displayName
    },
    getProjects(cityId) {
      API.Common.getProject(cityId).then(res => {
        if (res.code === 200) {
          this.projects = res.data
        }
        else {
          this.$message.error(res.message || '获取项目信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取项目信息失败')
      })
    },
    getCities() {
      API.Common.getCity().then(res => {
        if (res.code === 200) {
          this.cites = res.data
        }
        else {
          this.$message.error(res.message || '获取城市公司信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取城市公司信息失败')
      })
    },
    /** 查询项目基本信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectCode: null,
        projectName: null,
        cityId: null,
        cityName: null,
        name: null,
        plotName: null,
        companyRegisterName: null,
        belongCompanyName: null,
        shareholdRate: null,
        operateStripline: null,
        combineTableContent: null,
        takeLandDate: null,
        firstOpenDate: null,
        firstCompletedDate: null,
        firstDeliverDate: null,
        landCost: null,
        capacityArea: null,
        canSalesArea: null,
        constraintPricePolicy: null,
        capacityPrice: null,
        canSalesPrice: null,
        useLandArea: null,
        plotRatio: null,
        supportSituation: null,
        completedDeliverModality: null,
        buildingPic: null,
        birdPic: null,
        locationPic: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.projectId)
      this.projectNames = selection.map(item => item.projectName)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.detailDisabled = false;
      // this.reset();
      // this.open = true;
      // this.title = "添加项目基本信息";
      // this.getCities();
      this.$router.push('/projectOperate/add')
    },
    // 楼栋配置
    handleConfigBuilding(row){
      this.$router.push(`/projectOperate/configBuilding/${row.projectId}`)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.detailDisabled = false;
      this.reset();
      const projectId = row.projectId || this.ids
      // getInfo(projectId).then(response => {
      //   this.getProjects(response.data.cityId)
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改项目基本信息";
      // });
      // this.getCities();
      this.$router.push(`/projectOperate/edit/${projectId}`)
    },
    handleDetail(row) {
      this.detailDisabled = true;
      this.reset();
      const projectId = row.projectId || this.ids
      // getInfo(projectId).then(response => {
      //   this.getProjects(response.data.cityId)
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "项目基本信息详情";
      // });
      // this.getCities();
      this.$router.push(`/projectOperate/detail/${projectId}`)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.projectId != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectIds = row.projectId || this.ids;
      const projectNames = row.projectName || this.projectNames
      this.$modal.confirm('是否确认删除项目名称为"' + projectNames + '"的数据项？').then(function () {
        return delInfo(projectIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('plan/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped lang="scss">
.upload{
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-align: center;
  line-height: 100px;
}
.width-100{
  width: 100%!important;
}
</style>
