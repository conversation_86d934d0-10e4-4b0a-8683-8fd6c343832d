<script>
import Template from "@/views/plan/template/index.vue";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import {getNodeDetail, intervene} from "@/api/plan/intervene";
import {statusOption, fillFlagOption} from '@/constant';
import {listConfig} from "@/api/plan/config";
import SelectTree from "@/components/SelectTree.vue";
import NodeForm from "@/views/plan/components/NodeForm/index.vue";
import SelectUser from "@/views/plan/components/SelectUser/index.vue";
import {uploadFile, uploadFileMultiple} from "@/api/plan/common";
import {getRespPeople} from "@/api/plan/dept";
export default {
  dicts: ['stages'/*分期*/, 'resp_department' /*责任部门*/],
  components: {SelectUser, NodeForm, SelectTree, Template, StatusTag},
  data(){
    return {
      loading: false,
      form: {},
      nodeOutcomeDocumentList: [], // 反馈成果文件
      // 表单校验
      rules: {
        feedbackType: [{
          // End Generation Here
          required: true,
          message: '反馈内容不能为空',
          trigger: 'blur'
        }],
        /*feedbackProgress: [{
          required: true,
          message: '完成百分比不能为空',
          trigger: 'change'
        }],*/
        startTime: [{
          required: true,
          message: '计划开始时间不能为空',
          trigger: 'change'
        }],
        endTime: [{
          required: true,
          message: '计划完成时间不能为空',
          trigger: 'change'
        }],
        department: [{
          required: true,
          message: '责任部门不能为空',
          trigger: 'blur'
        }],
        /*responsibilityPeople: [{
          required: true,
          message: '责任人不能为空',
          trigger: 'blur'
        }],*/
        completeCriteria: [{
          required: true,
          message: '完成标准不能为空',
          trigger: 'blur'
        }],
        actualCompletionDate: [{
          required: true,
          message: '实际完成时间不能为空',
          trigger: 'change'
        }],
      },
      feedbackTypeOptions: [{
        "label": "过程反馈",
        "value": 1
      }, {
        "label": "完成反馈",
        "value": 2
      }],
      selectedFileList: [], // 已选成果文件列表
      resultOpen: false, // 成果文件选择弹出层
      resultFileTotal: 0, // 成果文件总条数
      resultFileLoading: false, // 成果文件加载
      queryResultFileParams: {
        pageNum: 1,
        pageSize: 200,
        status: 0
      },
      selectDepartIds: [], // 选择的部门
      resultFileList: [
        /*{
          "searchValue": null,
          "createBy": null,
          "createTime": "2023-12-29 06:53:47",
          "updateBy": null,
          "updateTime": "2023-12-29 06:53:57",
          "remark": null,
          "params": {},
          "id": "72c0fbce7a1a447db3a8664508c59304",
          "type": "会议报告",
          "name": "会议报告",
          "fillFlag": 0,
          "status": 0,
          "delFlag": "0"
        }*/
      ], // 成果文件列表
      dialogType: 'update',
      /*departmentDialogTitle: '责任部门列表',
      departmentDialogShow: false,*/
      roleName: null,
      uploadLoading: false,
    }
  },
  computed: {
    deptList() {
      return this.$store.state.plan.deptList
    },
    statusOption() {
      return statusOption
    },
    fillFlagOption() {
      return fillFlagOption
    }
  },
  mounted() {
    this.handleGetNodeDetail();
  },
  methods: {
    clearFeedbackUserInfo(){
      this.$refs.userRef.clearSelect()
      this.form.feedbackUser = '';
      this.form.feedbackUserName = '';
    },
    handleDeleteAnnexUrl(row) {
      row.annexUrl = null;
      row.createTime = null;
    },
    selectFeedbackData (data) {
      this.form.feedbackUser = data.userName;
      this.form.feedbackUserName = data.nickName;
      // console.log('data-------',data);
      /*if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.form.feedbackUser = data.userName
        this.form.feedbackUserName = data.nickName
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].feedbackUser = data.userName
          this.nodeList[this.clickIndex].feedbackUserName = data.nickName

        }
      }*/
    },
    selectFeedbackFun () {
      this.$refs.userRef.show()
    },
    cancelIntervene(){ // 关闭计划干预
      this.$tab.closeOpenPage({
        path: "/plan/project/intervene"
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          intervene({
            ...this.form,
            department: this.form.department.join(','), // 责任部门
            nodeOutcomeDocumentList: this.nodeOutcomeDocumentList
          }).then(response => {
            this.$modal.msgSuccess("节点干预成功");
            const obj = {
              path: "/plan/project/intervene"
            };

            this.$tab.closeOpenPage(obj)
            /*this.queryParams.pageNum = 1;
            this.getList();*/
          });
        }
      });
    },
    selectDepartIdsFilter(row){
      this.departmentArr = row.department ? row.department.split(',') : [];
      this.departmentNameArr = row.departmentName ?  row.departmentName.split(',') : [];
      let arr = this.departmentArr.map((item, index) => {
        return {
          department: item,
          departName: this.departmentNameArr[index]
        }
      })
      this.selectDepartIds = this.selectDepartIds.splice(0, this.selectDepartIds.length, ...arr);
      this.selectDepartIds = arr;
      // console.log(this.selectDepartIds, this.departmentArr, arr);

    },
    departNameShow(form){
      return !!form.departmentName;
    },
    selectDepartFun (val) {
     /* 城市公司编码  cityCompanyNum;
      顶目id  projectId;
      分期id stageld;
      责任部门  respDept;*/
      console.log('val------',val);
      if(val.length === 0) {
        this.form.feedbackUserName = '';
        this.form.feedbackUser = '';
        return;
      }
      val[0] ?
      this.getDefautlFeedback({
        cityCompanyNum: this.form.deptNum,
        projectId: this.form.projectId,
        stageId: this.form.stageId,
        respDept: val[0]
      }) : null
    },
    //获取默认反馈人
    getDefautlFeedback(data){
      getRespPeople(data).then(res => {
        this.form.feedbackUser = res.data.respPeople //特殊情况传的ID 正常传的账户
        this.form.feedbackUserName = res.data.respPeopleName
        /*let data=response.data
        if(type){ //更新列表的反馈人
          this.nodeList[this.clickIndex].feedbackUser=data.respPeople
          this.nodeList[this.clickIndex].feedbackUserName = data.respPeopleName
          this.nodeList=[...this.nodeList]
        }else{
          this.form.feedbackUser = data.respPeople //特殊情况传的ID 正常传的账户
          this.form.feedbackUserName = data.respPeopleName
        }*/
      })
    },
    /*selectDepartData (data) {
      console.log('data---------',data);
      this.form.department = data.map(item => item.department).join(',');
      this.form.departmentName = data.map(item => item.departName).join(',');
      this.selectDepartIdsFilter(this.form);
      this.$forceUpdate();
    },*/
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleFileSelectionChange(selection) {
      this.selectedFileList = selection;
      for (let item of this.selectedFileList) {
        delete item.id;
      }

    },
    submitResultFile() {
      this.resultOpen = false;
      this.nodeOutcomeDocumentList = this.nodeOutcomeDocumentList.concat(
        this.selectedFileList.map(item => {
          return {
            ...item,
            createTime: null
          };
        }));
    },
    /** 查询成果设置列表 */
    getResultFileNodeList() {
      this.resultFileLoading = true;
      listConfig(this.queryResultFileParams).then(response => {
        this.resultFileList = response.rows;
        this.resultFileTotal = response.total;
        this.resultFileLoading = false;
        this.resultFileTotal = response.total;
      });
    },
    handleGetNodeDetail(){
      this.loading = true;
      const id = this.$route.query.id;
      const planId = this.$route.query.planId;
      const status = this.$route.query.status;
      getNodeDetail(planId, id).then(response => {
        this.form = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList;
        this.form.nodeId = response.data.id;
        this.form.department = response.data.department.split(',') || [];
        if(status === 2 || status === 3) {
          // 根据节点状态，已完成的默认选择完成反馈，显示实际完成时间
          // 未完成的显示完成百分比和预计完成时间
          this.form.feedbackType = 2;
        }
        else{
          this.form.feedbackType = 1;
        }
        this.selectDepartIdsFilter(this.form);
        this.$forceUpdate();
        this.open = true;
        this.title = "节点干预";
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleFYRadioChange(val) {
      this.form.expectedCompletionDate =  val === 2 ? 100 : 0;
      this.form.feedbackType = val;
      this.$forceUpdate();
    },
    handleResultOpen() {
      this.resultOpen = true;
      this.getResultFileNodeList();
    },
    handleNodeDocument() { // 成果文件新增
      /*成果类型String type;
      成果名称String name;
      创建时间"yyyy-MM-dd HH:mm:ss" Date createTime;
      专业成果文件和证明材料描述 String outcomeDocumentsDesc;
      附件 String annexUrl;
      是否必填（0-是 1-否）Integer fillFlag;
      文件名称 annexName;
      文件上传后的附件名称 ossFileName
      */
      this.nodeOutcomeDocumentList.push({
        type: null,
        createTime: null,
        annexUrl: null,
        outcomeDocumentsDesc: null,
        annexName: null,
        ossFileName: null,
      });
    },
    handleNodeDocumentChange(selection) { // 成果文件选中

    },
    cancelFile() {
      this.resultOpen = false;
    },
    handleNodeDocumentDelete(row, index) { // 成果文件删除
      this.nodeOutcomeDocumentList.splice(index, 1);
    },
    uploadFileHandle(event, row, index) {
      const file = event.target.files[0];
      if (!file) {
        // 没有选择文件
        return;
      }
      //  模拟数据
      // {
      //   type: null,
      //   createTime: null,
      //   annexUrl: null,
      //   outcomeDocumentsDesc: null,
      //   annexName: null
      // }
      this.uploadLoading = true;
      // this.nodeOutcomeDocumentList[index].annexName = file.name;
      // this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
      const formData = new FormData();
      formData.append('files', file);
      uploadFileMultiple(formData).then(response => {
        this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
        this.nodeOutcomeDocumentList[index].fileList = response.data;

        this.nodeOutcomeDocumentList[index].annexName = file.name;
        this.nodeOutcomeDocumentList[index].annexUrl = response?.data[0].url;
        /*this.nodeOutcomeDocumentList[index].ossFileName = response.data.name;*/
        this.nodeOutcomeDocumentList.splice(index, 1, this.nodeOutcomeDocumentList[index]);
        this.$forceUpdate();
        console.log('nodeOutcomeDocumentList', this.nodeOutcomeDocumentList[index]);
        this.uploadLoading = false;
      })
        .catch(error => {
          console.error(error);
          this.uploadLoading = false;
        });
    },
    downloadFile(url, filename) {

      return;
      let link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
</script>
<template>
<section class="app-container" v-loading="loading">
  <el-form ref="form" :model="form" :rules="rules" label-width="120px">
    <div class="feedback-container">
      <div class="feedback-detail">
        <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="projectName">
              {{ form.projectName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目分期：" prop="stageId">
              <dict-tag :options="dict.type.stages" :value="form.stageId"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="节点名称：" prop="nodeName">
              {{ form.nodeName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成标准：" prop="completeCriteria">
              <el-input v-model="form.completeCriteria"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--责任部门、责任人-->
        <el-row>
          <el-col :span="12">
            <el-form-item label="责任部门：" prop="department">
              <section class="flex-align-center">
<!--                      <span class="dialog-select" @click="selectDepartFun(form)">
                        {{departNameShow(form) ? form.departmentName : '请选择'}}
                        <i class="el-icon-search"></i>
                      </span>-->
              <el-select clearable multiple v-model="form.department" placeholder="请选择" @change="selectDepartFun">
                  <el-option v-for="dict in dict.type.resp_department"
                             :key="dict.value"
                             :label="dict.label"
                             :value="dict.value"
                  ></el-option>
                </el-select>
              </section>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">
            <el-form-item label="责任人：" prop="responsibilityPeople">
              {{ form.responsibilityPeopleName || '&#45;&#45;'}}
            </el-form-item>
          </el-col>-->
          <el-col :span="12">
            <el-form-item label="反馈人" prop="feedbackUserName">
              <div style="width: 220px" class="flex-row">
                <div class="feedback-people-container" v-if="!!form.feedbackUserName">
                  <div style="margin-right: 15px">
                    {{ form.feedbackUserName }}
                  </div>
                  <i class="el-icon-circle-close" @click="clearFeedbackUserInfo"></i>
                </div>

                <el-button type="primary" @click="selectFeedbackFun" size="mini"
                >选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划开始时间：" prop="startTime">
              <el-date-picker v-model="form.startTime" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="请选择计划开始时间" clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划完成时间：" prop="endTime">
              <el-date-picker v-model="form.endTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择计划完成时间" clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="feedback-form">
        <div class="el-icon-s-comment feedback-form-title icon-primary">反馈信息</div>
        <!--根据节点状态，已完成的默认选择完成反馈，显示实际完成时间-->
        <!--未完成的显示完成百分比和预计完成时间-->
        <el-row>
          <el-form-item label="反馈类型" prop="feedbackType">
            <el-radio-group v-model="form.feedbackType" size="medium" @input="handleFYRadioChange">
              <el-radio v-for="(item, index) in feedbackTypeOptions" :key="index" :label="item.value">{{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-row>
<!--        <el-row>
          <el-col :span="12"  v-if="form.feedbackType !== 2">
            <el-form-item label="完成百分比" prop="feedbackProgress" style="width: 100%;">
              <el-slider :max='100' :step='1' show-input v-model="form.feedbackProgress"></el-slider>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.feedbackType == 1">
            <el-form-item label="预计完成时间" prop="expectedCompletionDate">
              <el-date-picker v-model="form.expectedCompletionDate" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="请选择预计完成时间" clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-else>
            <el-form-item label="实际完成时间" prop="actualCompletionDate">
              <el-date-picker v-model="form.actualCompletionDate" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="请选择实际完成时间" clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>-->
        <el-row>
          <el-col :span="12" v-if="form.feedbackType === 2">
            <el-form-item label="实际完成时间" prop="actualCompletionDate">
              <el-date-picker v-model="form.actualCompletionDate" type="date" format="yyyy-MM-dd"
                              value-format="yyyy-MM-dd" placeholder="请选择实际完成时间" clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="进度说明" prop="notes">
            <el-input v-model="form.notes" type="textarea" placeholder="请输入进度说明"
                      :autosize="{minRows: 4, maxRows: 4}" ></el-input>
          </el-form-item>
        </el-row>
        <!--          </el-form>-->
      </div>
      <div class="feedback-file">
        <div class="el-icon-files feedback-form-title icon-primary">成果文件</div>
        <el-row :gutter="10" class="mb8" v-if="dialogType === 'update'">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-setting"
              size="mini"
              @click="handleResultOpen"
            >节点列表选择
            </el-button>
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleNodeDocument"
            >新增
            </el-button>
          </el-col>
        </el-row>
        <el-table v-loading="uploadLoading" :data="nodeOutcomeDocumentList" @selection-change="handleNodeDocumentChange" style="width: 100%;">
          <el-table-column type="index" width="55" align="center"/>
          <el-table-column label="成果类型" align="center" prop="type" width="150">
            <template slot-scope="scope">
              <span v-if="dialogType === 'view'">{{ scope.row.type || '--' }}</span>
              <el-input v-else-if="dialogType === 'update'" v-model="scope.row.type"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="成果文件" align="center" prop="annexUrl" width="150">
            <template slot-scope="scope">
              <section class="file-wrapper" v-if="dialogType === 'update'">
                <!-- 如果文件链接不存在，显示文件上传-->
                <label v-if="!scope.row.annexUrl" :for="'uploadFile_'+scope.$index"
                       class="el-icon-upload cursor icon-primary">
                  {{ !scope.row.annexUrl ? '请选择文件上传' : scope.row.annexName }}
                </label>
                <!--        文件链接存在，显示link标签，点击可下载文件        -->
                <a v-else :href="scope.row.annexUrl" class="link" :download="scope.row.annexUrl">
                  {{ scope.row.annexName || '--' }}
                </a>
                <!--        文件链接存在，显示删除标志        -->
                <i class="el-icon-circle-close cursor link" :class="{'display-none': !scope.row.annexUrl}"
                   @click="handleDeleteAnnexUrl(scope.row)"></i>
              </section>
              <section v-else-if="dialogType === 'view'">
                <a :href="scope.row.annexUrl" :download="scope.row.annexName" class="link"
                   target="_blank">{{ scope.row.annexName || '--' }}</a>
              </section>
              <input type="file" class="display-none" :id="'uploadFile_'+scope.$index"
                     @change="(event) => uploadFileHandle(event, scope.row, scope.$index)"/>
            </template>
          </el-table-column>
          <el-table-column label="上传时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span v-if="dialogType === 'view'">{{ scope.row.createTime || '--' }}</span>
              <el-input v-else-if="dialogType === 'update'" v-model="scope.row.createTime" disabled=""></el-input>
            </template>
          </el-table-column>
          <el-table-column label="备注说明" align="center" prop="outcomeDocumentsDesc">
            <template slot-scope="scope">
              <span v-if="dialogType === 'view'">{{ scope.row.outcomeDocumentsDesc || '--' }}</span>
              <el-input v-else-if="dialogType === 'update'" v-model="scope.row.outcomeDocumentsDesc"></el-input>
            </template>
          </el-table-column>
          <el-table-column v-if="dialogType === 'update'" label="操作" align="center"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleNodeDocumentDelete(scope.row, scope.$index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <section class="form-footer">
          <el-button @click="cancelIntervene">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 认</el-button>
        </section>
      </div>
    </div>
  </el-form>
  <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
    <el-table v-loading="resultFileLoading" :data="resultFileList" @selection-change="handleFileSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" align="center" type="index"/>
      <el-table-column label="成果类别" align="center" prop="type"/>
      <el-table-column label="成果名称" align="center" prop="name"/>
      <el-table-column label="是否必填" align="center" prop="fillFlag">
        <template slot-scope="scope">
          <status-tag :status="scope.row.fillFlag" :options="fillFlagOption"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="statusOption"/>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="mt20"
      v-show="resultFileTotal>0"
      :total="resultFileTotal"
      :page.sync="queryResultFileParams.pageNum"
      :limit.sync="queryResultFileParams.pageSize"
      @pagination="getResultFileNodeList"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitResultFile">确 认</el-button>
      <el-button @click="cancelFile">取 消</el-button>
    </div>
  </el-dialog>
<!--  <select-depart
    ref="departRef"
    :selectDepartIds="selectDepartIds"
    @departEmit="selectDepartData"
  />-->
  <select-user
    ref="userRef"
    :roleId="form.feedbackUser"
    @feedbackEmit="selectFeedbackData"
  />
</section>
</template>

<style lang="scss" scoped>
.feedback-container {
  //max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;

  .feedback-form-title {
    margin-bottom: 20px;
    background-color: #f8f8f9;
    padding: 10px;
    width: 100%;
  }

  .icon-primary {
    color: #409eff;
  }

  .cursor {
    cursor: pointer;
  }

  .display-none {
    display: none !important;
  }

  .file-wrapper {
    .el-icon-circle-close {
      margin-left: 10px;
    }
  }

  .link {
    color: #409eff;
  }

  .form-footer{
    margin-bottom: 0;
    margin-top: 30px;
    display: flex;
    justify-content: center;
    .el-form-item__content{
      margin-left: 0!important;
    }
  }
}

.transfer-item {
  display: flex;

  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

</style>
