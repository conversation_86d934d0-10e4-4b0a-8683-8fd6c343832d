export default {
  data() {
    return {
      isEnable: null,
      limitDay: null,
    };
  },
  computed: {
    pickerOptions() {
      return this.isEnable === 1 ? {
        disabledDate: (time) => {
          const oneWeekAgo = new Date();
          oneWeekAgo.setTime(oneWeekAgo.getTime() - this.limitDay * 24 * 3600 * 1000);
          return time.getTime() < oneWeekAgo.getTime();
        }
      } : {};
    }
  },
  async created() {
    await this.getLimitDayIsEnable();
  },
  methods: {
    async getLimitDayIsEnable() {
      let isEnable = await this.getConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
      let limitDay = await this.getConfigKey("rant.finish.date.limit.day"); // 设置限制天数
      this.isEnable = parseInt(isEnable.msg);
      this.limitDay = parseInt(limitDay.msg);
    },
  }
}
