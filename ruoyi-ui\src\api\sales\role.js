import request from '@/utils/request'

// 查询销售看板角色信息列表
export function listRole(query) {
  return request({
    url: '/sales/role/list',
    method: 'get',
    params: query
  })
}

// 查询销售看板角色信息详细
export function getRole(roleId) {
  return request({
    url: '/sales/role/' + roleId,
    method: 'get'
  })
}

// 新增销售看板角色信息
export function addRole(data) {
  return request({
    url: '/sales/role',
    method: 'post',
    data: data
  })
}

// 修改销售看板角色信息
export function updateRole(data) {
  return request({
    url: '/sales/role',
    method: 'put',
    data: data
  })
}

// 删除销售看板角色信息
export function delRole(roleId) {
  return request({
    url: '/sales/role/' + roleId,
    method: 'delete'
  })
}

// 角色状态修改
export function changeRoleStatus (roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/sales/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询角色已授权用户列表
export function allocatedUserList (query) {
  return request({
    url: '/sales/role/allocatedUser',
    method: 'get',
    params: query
  })
}

// 获取所有未分配的用户
export function unallocatedUserList (query) {
  return request({
    url: '/sales/role/noAllocatedUser',
    method: 'get',
    params: query
  })
}

// 取消用户授权角色
export function authUserCancel (data) {
  return request({
    url: '/sales/role/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权角色
export function authUserCancelAll (data) {
  return request({
    url: '/sales/role/authUser/batchCancel',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll (data) {
  return request({
    url: '/sales/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}
