<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="城市公司" prop="deptNum">
        <el-select
          v-model="queryParams.deptNum"
          placeholder="请选择城市公司"
          clearable
          @change="handleQueryProject"
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict.deptNum"
            :label="dict.deptName"
            :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目" prop="projectName">
<!--        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称"></el-input>-->
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in projectList"
            :key="dict.name"
            :label="dict.name"
            :value="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分期" prop="stageId">
      <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                 clearable
                 filterable>
        <el-option
          v-for="dict in dict.type.stages"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
    </el-form-item>
      <el-form-item label="标段" prop="lot"  v-show="showAllSearch">
        <el-select v-model="queryParams.lot" placeholder="请选择分期"
                   clearable
                   filterable>
          <el-option
            v-for="dict in dict.type.project_lot"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="计划名称" prop="planName" v-show="showAllSearch">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置</el-button>
        <el-button
          type="primary"
          :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          size="mini"
          @click="toggleAllSearch"
        >更多筛选</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      :row-class-name="() => 'custom-row'"
      @row-click="showPlanAndNodeDialogFun"
    >
      <el-table-column label="序号" align="center" prop="id" width="55" fixed="left">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" fixed="left">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="planStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column label="城市公司" align="center" prop="deptName" fixed="left">
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" fixed="left">
      </el-table-column>
      <el-table-column label="分期" align="center" prop="stageId">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stages" :value="scope.row.stageId" />
        </template>
      </el-table-column>
      <el-table-column label="标段" align="center" prop="lot">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.project_lot" :value="scope.row.lot" />
        </template>
      </el-table-column>
      <el-table-column
        label="计划编码"
        align="center"
        prop="planCode"
      >
        <template slot-scope="scope">
          <span class="wrap-line">{{ scope.row.planCode || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
      >
        <template slot-scope="scope">
          <span class="wrap-line">{{ scope.row.planName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="版本号"
        align="center"
        prop="version"
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding"
        fixed="right"
      >
        <template slot-scope="scope">
<!--          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
          >查看</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click.stop="fetchApprovalUser(scope.row)"
          >查看审批流</el-button>
          <el-button
            v-if="tableBthShow('update', scope.row)"
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            @click.stop="handleUpdate(scope.row, 2)"
            v-hasPermi="['plan:feedback:edit']"
          >汇报
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
<!--    <el-dialog
      title="计划节点明细"
      :visible.sync="showPlanAndNodeDialog"
      width="1200px"
      append-to-body
      v-if="showPlanAndNodeDialog"
    >
      <PlanAndNodeInfo :data="rowData">
        <template slot="progressFeedback" slot-scope="slotProps">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            v-if="tableBthShow('update', slotProps.row)"
            @click="handleFeedback('progressFeedback', slotProps, 1)"
          >过程反馈</el-button>
        </template>
        <template slot= "finishedFeedback" slot-scope="slotProps">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            v-if="tableBthShow('update', slotProps.row)"
            @click="handleFeedback('finishedFeedback', slotProps, 2)"
          >完成反馈</el-button>
        </template>
      </PlanAndNodeInfo>
    </el-dialog>
    &lt;!&ndash; 反馈弹窗 &ndash;&gt;
    <el-dialog  title="节点反馈维护" :visible.sync="feedbackOpen" width="1200px" append-to-body>
      <component v-bind:is="currentTabComponent" ref="feedback" :feedbackDetail="feedbackDetail" @handleResultOpen="handleResultOpen"></component>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 认</el-button>
        <el-button @click="feedbackOpen = false;">取 消</el-button>
      </div>
    </el-dialog>
    &lt;!&ndash;  成果文件选择  &ndash;&gt;
    <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
      <ResultFile ref="resultFile"></ResultFile>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResultFileConfirm">确 认</el-button>
        <el-button @click="handleResultOpen(false)">取 消</el-button>
      </div>
    </el-dialog>-->
  </div>
</template>

<script>
import {validList} from "@/api/plan/plan";
import {
  finishFeedback,
  getFeedback,
  progressFeedback,
} from "@/api/plan/feedback";
import {
  getApproveFlow
} from "@/api/plan/info"
import ProgressFeedback from "@/views/plan/components/ProgressFeedback/index.vue";
import FinishedFeedback from "@/views/plan/components/FinishedFeedback/index.vue";
import PlanAndNodeInfo from "@/views/plan/components/PlanAndNodeInfo/index.vue";
import ResultFile from "@/views/plan/components/ResultFile/index.vue";
import Template from "@/views/plan/template/index.vue";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import { planStatusOption } from "@/constant";
import { mapState } from 'vuex';
import mixin from "@/mixins";
import {formatDateWithTime} from '@/utils';
export default {
  mixins: [mixin],
  name: "Dashboard",
  dicts: ['departs', 'stages', 'plan_status', 'project_lot'],
  components: {
    StatusTag, Template,
    ProgressFeedback,
    FinishedFeedback,
    PlanAndNodeInfo,
    ResultFile
  },
  data () {
    return {
      // showAllSearch: false,
      currentTabComponent: null,
      feedbackOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        deptNum: null,
        projectId: null,
        stageId: null,
        lot: null,
        status: null,
        planName: null,
      },
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划编制表格数据
      infoList: [],
      // projectList: [],
      rowData: null,
      showPlanAndNodeDialog: false,
      // 遮罩层
      loading: true,
      feedbackDetail: {},
      feedBackForm: {},
      nodeOutcomeDocumentList: [],
      resultOpen: false,
    }
  },
  mounted () {
    this.getList()
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList')
  },
  computed: {
    planStatusOption() {
      return planStatusOption;
    },
    cityCompanys () {
      return this.$store.state.plan.deptList
    },
    ...mapState('plan', ['projectList'])
  },
  methods: {
    handleUpdate(row, feedbackType){
      // 1:过程反馈，2：完成反馈
      /*this.rules.expectedCompletionDate[0].required = feedbackType === 1;
      this.rules.actualCompletionDate[0].required = feedbackType === 2;
      this.rules.feedbackProgress[0].required = feedbackType === 1;
      this.dialogType = 'update';
      this.reset();
      const id = row.id || this.ids
      this.form.feedbackType = feedbackType;*/
      this.$router.push({
        path: '/plan/feedbackUpdate',
        query: {
          id: row.id,
          planId: row.planId,
          feedbackType: feedbackType
        }
      });
    },
    /*moreFilter () {
      this.showAllSearch = !this.showAllSearch
    },*/
    handleQueryProject(value){
      this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
    },
    //获取审批流
    fetchApprovalUser (row) {
      this.loading = true
      getApproveFlow(row.id).then(res => {
        this.loading = false
        window.open(res.data)
      }).catch(err => {
        this.loading = false
      })
    },
    tableBthShow(btnType, row){
      // status为 0 1 4   同时 feedback_flow_status 为0 3 4时，显示过程反馈  完成反馈
      // status 为 2  3时，显示 查看
     // 计划查看 里面的计划查看计划节点的时候，有些节点是可以反馈的，
      // 用这个去判断是否可以反馈 再根据现在反馈的判断去展示 过程反馈 和完成反馈 按钮
      switch (btnType) {
        case 'update':
          return row.isEnabledFeedback && (row.status === 0 || row.status === 1 || row.status === 4)
            && (row.feedbackFlowStatus === 0 || row.feedbackFlowStatus === 3 || row.feedbackFlowStatus === 4);
        default:
          return false;
      }
    },
    submitForm() {
      this.$forceUpdate();
      this.$refs.feedback.$refs["form"].validate(valid => {
        if (valid && this.$refs.feedback.fileValidate()) {
          // 过程反馈、完成反馈接口判断
          let form = this.$refs.feedback.form;
          let nodeOutcomeDocumentList = this.$refs.feedback.handleGetNodeOutcomeDocumentList();
          let feedbackPost = form.feedbackType === 1 ? progressFeedback : finishFeedback; // 1 过程反馈，2 完成反馈
          feedbackPost({
            ...form,
            nodeOutcomeDocumentList
          }).then(response => {
            this.$modal.msgSuccess("节点反馈成功");
            this.feedbackOpen = false;
            this.queryParams.pageNum = 1;
            this.getList();
          });
        }
      });
    },
    submitResultFile() { // 提交成果文件
      for (let item of this.selectedFileList) {
        item.resultConfigId = item.id;
        delete item.id;
      }

      let nodeOutcomeDocumentList = this.$refs.feedback.handleGetNodeOutcomeDocumentList();
      nodeOutcomeDocumentList = nodeOutcomeDocumentList.concat(
        this.selectedFileList.map(item => {
          return {
            ...item,
            createTime: null,
            fileList: [],
          };
        }));
      this.$refs.feedback.handleSetNodeOutcomeDocumentList(nodeOutcomeDocumentList);
    },
    handleResultFileConfirm(){ // 获取成果文件选择弹窗选中的文件
      this.handleResultOpen(false);
      this.selectedFileList = this.$refs.resultFile.getSelectedFileList();
      this.submitResultFile();
    },
    handleResultOpen(open){ // 打开、关闭成果文件选择弹窗
      this.resultOpen = open;
    },
    // 获取反馈节点详情
    handleGetFeedback(row, feedbackType) {
      this.feedBackForm.feedbackType = feedbackType;
      getFeedback(row.id).then(response => {
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList || [];
        this.feedBackForm.nodeId = response.data.id;
        this.feedBackForm.feedbackProgress = response.data.feedbackProgress || 0; // 如果没有反馈进度为null，表单校验报错
        this.feedBackForm.expectedCompletionDate = response.data.expectedCompletionDate ? formatDateWithTime(response.data.expectedCompletionDate) : formatDateWithTime(new Date());
        this.feedBackForm.actualCompletionDate = response.data.actualCompletionDate ? formatDateWithTime(response.data.actualCompletionDate) : formatDateWithTime(new Date());
        this.feedBackForm.notes = response.data.notes || '';
        // 1:过程反馈，2：完成反馈
        if(feedbackType === 2){
          this.feedBackForm.feedbackProgress = 100;
        }
        this.$refs.feedback.handleSetForm(this.feedBackForm);
        this.$refs.feedback.handleSetNodeOutcomeDocumentList(this.nodeOutcomeDocumentList);
      })
    },
    // 过程反馈、完成反馈打开弹窗
    handleFeedback(type, slotProps, feedbackType) { // 处理反馈
      if(type === 'progressFeedback') {
        this.currentTabComponent = ProgressFeedback
      } else if(type === 'finishedFeedback') {
        this.currentTabComponent = FinishedFeedback
      }
      this.feedbackOpen = true;
      this.handleGetFeedback(slotProps.row, feedbackType);
    },
    showPlanAndNodeDialogFun (row, column) {
     /* if (column.label === '计划编码') {
        this.$copyToClipboard(row.planCode)
        return
      }
      else if (column.label === '项目名称') {
        this.$copyToClipboard(row.projectName)
        return
      }
      else if (column.label === '计划名称') {
        this.$copyToClipboard(row.planName)
        return
      }*/
      this.rowData = row;
      /*this.$router.push({
        path: '/plan/planDetail',
        query: {
          id: row.id
        }
      });*/

      const routeData = this.$router.resolve({
        path: '/planDetailBlank',
        query: {
          id: row.id
        }
      });
      window.open(routeData.href, '_blank');
    },
    /** 查询计划编制列表 */
    getList () {
      this.loading = true
      validList(this.queryParams).then(response => {
        this.infoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        deptNum: null,
        professionId: null, // 专业
        projectId: null, //项目
        stageId: null, //分期
        templateId: null,
        planName: null, //项目名称
        planExplain: ''
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
  }
};
</script>
<style lang="scss">
  .el-table__body tr.custom-row:hover > td {
    cursor: pointer;
  }
</style>
