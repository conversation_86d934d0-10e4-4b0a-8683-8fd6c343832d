<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="年份" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第几周" prop="week">
        <el-input
          v-model="queryParams.week"
          placeholder="请输入周数"
          clearable
          size="small"
          type="number"
        />
      </el-form-item>
      <!-- <el-form-item label="填报周期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="审批人" align="center" prop="approveUserName" width="100" /> -->
      <!-- 1=待阅,2=已阅 -->
      <!--  <el-form-item
        label="审批状态"
        align="center"
        prop="approveStatus"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.approveStatus === 1 ? "待阅" : "已阅" }}</span>
        </template>
      </el-form-item>
      <el-form-item
        label="审批日期"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.approveTime
              ? parseTime(scope.row.approveTime, "{y}-{m}-{d}")
              : "--"
          }}</span>
        </template>
      </el-form-item>
      <el-form-item label="审批描述" align="center" prop="approveDesc">
        <template slot-scope="scope">
          <span>{{ scope.row.approveDesc || "--" }}</span>
        </template>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          :loading="addLoading"
          >新增</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          plain-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['plan:info:edit']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="danger"-->
      <!--          plain-->
      <!--          icon="el-icon-delete"-->
      <!--          size="mini"-->
      <!--          :disabled="multiple"-->
      <!--          @click="handleDelete"-->
      <!--          v-hasPermi="['plan:info:remove']"-->
      <!--        >删除</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--          v-hasPermi="['plan:info:export']"-->
      <!--        >导出</el-button>-->
      <!--      </el-col>-->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="年份" align="center" prop="year"  />
      <el-table-column label="第几周" align="center" prop="week"  />
      <el-table-column
        label="填报周期"
        align="center"
        prop="startDate"
      >
        <template slot-scope="scope">
          <span
            >{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}~{{
              parseTime(scope.row.endDate, "{y}-{m}-{d}")
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="审阅人"
        align="center"
        prop="approveUserName"
      />
      <!-- 1=待阅,2=已阅 -->
      <el-table-column
        label="审阅状态"
        align="center"
        prop="approveStatus"
      >
      <!-- 审阅状态（0-草稿 1-待阅 2-已阅 3-已撤回 4=已驳回 5-部分已阅）） -->
        <template slot-scope="scope">
          <span>{{ getApproveStatusText(scope.row.approveStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审阅日期"
        align="center"
        prop="approveTime"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.approveTime
              ? parseTime(scope.row.approveTime, "{y}-{m}-{d}")
              : "--"
          }}</span>
        </template>
      </el-table-column>
      <!-- 审阅状态（1-待阅 2-已阅 3-已撤回 ） -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button
            v-if="scope.row.approveStatus === 3 || scope.row.approveStatus === 0 || scope.row.approveStatus === 4"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.approveStatus === 1"
            size="mini"
            type="text"
            icon="el-icon-d-arrow-left"
            @click="handleCallback(scope.row)"
            >撤回</el-button
          >
          <el-button
            v-if="scope.row.approveStatus === 0"
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click="handleSubmitDraft(scope.row)"
            v-loading="loading"
            >提交</el-button
          >
          <el-button
            v-if="scope.row.approveStatus === 0"
               size="mini"
               type="text"
               icon="el-icon-delete"
               @click="handleDelete(scope.row)"
             >删除</el-button>
<!--            <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改周报信息主对话框 -->
    <!-- 添加或修改周报信息主对话框 -->
    <!-- <el-dialog :visible.sync="open" width="80vw" append-to-body>
      <WeeklyForm />
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="年份" prop="year">
          <el-input v-model="form.year" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="第几周" prop="week">
          <el-input v-model="form.week" placeholder="请输入第几周" />
        </el-form-item>
        <el-form-item label="员工编码" prop="userCode">
          <el-input v-model="form.userCode" placeholder="请输入员工编码" />
        </el-form-item>
        <el-form-item label="员工名称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入员工名称" />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="员工岗位" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入员工岗位" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker clearable size="small"
                          v-model="form.startDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker clearable size="small"
                          v-model="form.endDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批人编码" prop="approveUserCode">
          <el-input v-model="form.approveUserCode" placeholder="请输入审批人编码" />
        </el-form-item>
        <el-form-item label="审批人名称" prop="approveUserName">
          <el-input v-model="form.approveUserName" placeholder="请输入审批人名称" />
        </el-form-item>
        <el-form-item label="审批日期" prop="approveTime">
          <el-date-picker clearable size="small"
                          v-model="form.approveTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择审批日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批描述" prop="approveDesc">
          <el-input v-model="form.approveDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="传阅人" prop="circulateUserIds">
          <el-input v-model="form.circulateUserIds" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import WeeklyForm from "./components/weeklyForm.vue";
import {
  getInfo,
  listInfo,
  updateInfo,
  delInfo,
  addInfo,
  addInit,
  callbackWeekly,
  submitDraft
} from "@/api/weekly/reportInfo.js";

export default {
  name: "Info",
  components: {
    WeeklyForm,
  },
  data() {
    return {
      addLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 周报信息主表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        year: null,
        week: null,
        userCode: null,
        nickName: null,
        deptId: null,
        postName: null,
        dateRange: [],
        status: null,
        approveUserCode: null,
        approveUserName: null,
        approveTime: null,
        approveStatus: null,
        approveDesc: null,
        circulateUserIds: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      submitLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSubmitDraft(row) {
      this.loading = true
      submitDraft({id: row.id}).then((response) => {
        this.$modal.msgSuccess("提交成功");
        this.getList();
      }).finally(() => {
        this.loading = false
      })
    },
    handleDetail(row) {
      // console.log("row", row);
      // this.$router.push({ path: `/weekly/weeklyDetail/${row.id}` });
      this.$router.push({
        path: `/weekly/weeklyDetail/${row.id}`,
        query: { from: this.$route.path }
      });
    },
    handleInit() {
      this.addLoading = true;
      addInit()
        .then((response) => {
          if (response.code === 200) {
            const data = response.data;
            // 存储数据到 Vuex
            this.$store.dispatch("weekly/setWeeklyFormData", data);
            // 然后跳转
            this.$router.push({ path: "/weekly/weeklyInput" });
          } else if (response.code === 500) {
            this.$message.error(response.msg);
          }
        })
        .finally(() => {
          this.addLoading = false;
        });
    },
    /** 查询周报信息主列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        year: null,
        week: null,
        userCode: null,
        nickName: null,
        deptId: null,
        postName: null,
        startDate: null,
        endDate: null,
        status: null,
        approveUserCode: null,
        approveUserName: null,
        approveTime: null,
        approveStatus: null,
        approveDesc: null,
        circulateUserIds: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      const queryData = { ...this.queryParams };

      if (queryData.dateRange && queryData.dateRange.length === 2) {
        queryData.startDate = queryData.dateRange[0];
        queryData.endDate = queryData.dateRange[1];
      } else {
        queryData.startDate = null;
        queryData.endDate = null;
      }

      // Remove dateRange from query params
      delete queryData.dateRange;

      listInfo(queryData).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      /*  this.open = true;
      this.title = "添加周报信息主"; */
      this.handleInit();
    },
    handleCallback(row) {
      // console.log("row", row);
      this.loading = true
      callbackWeekly({id: row.id}).then((response) => {
        this.$modal.msgSuccess("撤回成功");
        this.getList();
      }).finally(() => {
        this.loading = false
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      /* getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改周报信息主";
      }); */
      this.$router.push({ path: "/weekly/weeklyInput", query: { id: id, type: 'update' } });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const year = row.year
      const week = row.week
      this.loading = true
      this.$modal
        .confirm('是否确认删除' + year + '年第' + week + '周周报信息？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "plan/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },
    // 获取审阅状态文本
    // 审阅状态（0-草稿 1-待阅 2-已阅 3-已撤回 4=已驳回 5-部分已阅）
    getApproveStatusText(status) {
      const statusMap = {
        0: '草稿',
        1: '待阅',
        2: '已阅',
        3: '已撤回',
        4: '已驳回',
        5: '部分已阅'
      }
      return statusMap[status] || '未知'
    },
  },
};
</script>
