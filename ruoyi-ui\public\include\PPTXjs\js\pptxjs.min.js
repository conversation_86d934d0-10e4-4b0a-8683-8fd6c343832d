/**
 * https://meshesha.github.io/pptxjs
 * V1.11.0
 */
!function (r) { r.fn.pptxToHtml = function (t) { var e = r(this), s = e.attr("id"), i = !1, o = new Array, l = null, n = "", d = 0, h = 1, p = 0, c = 0, f = !1, u = {}, L = r.extend(!0, { pptxFileUrl: "", fileInputId: "", slidesScale: "", slideMode: !1, slideType: "divs2slidesjs", revealjsPath: "", keyBoardShortCut: !1, mediaProcess: !0, jsZipV2: !1, slideModeConfig: { first: 1, nav: !0, navTxtColor: "black", keyBoardShortCut: !0, showSlideNum: !0, showTotalSlideNum: !0, autoSlide: !0, randomAutoSlide: !1, loop: !1, background: !1, transition: "default", transitionTime: 1 }, revealjsConfig: {} }, t); function v(a) { for (var t, n, d = new JSZip, h = function (a) { var r = [], t = new Date; if (null !== a.file("docProps/thumbnail.jpeg")) { var e = da(a.file("docProps/thumbnail.jpeg").asArrayBuffer()); r.push({ type: "pptx-thumb", data: e }) } var s = function (a) { for (var r = g(a, "[Content_Types].xml").Types.Override, t = [], e = [], s = 0; s < r.length; s++)switch (r[s].attrs.ContentType) { case "application/vnd.openxmlformats-officedocument.presentationml.slide+xml": t.push(r[s].attrs.PartName.substr(1)); break; case "application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml": e.push(r[s].attrs.PartName.substr(1)) }return { slides: t, slideLayouts: e } }(a), i = function (a) { var r = g(a, "ppt/presentation.xml")["p:presentation"]["p:sldSz"].attrs; return p = 96 * parseInt(r.cx) / 914400, c = 96 * parseInt(r.cy) / 914400, { width: p, height: c } }(a); l = function (a) { var r = g(a, "ppt/_rels/presentation.xml.rels").Relationships.Relationship, t = void 0; if (r.constructor === Array) { for (var e = 0; e < r.length; e++)if ("http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" === r[e].attrs.Type) { t = r[e].attrs.Target; break } } else "http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" === r.attrs.Type && (t = r.attrs.Target); if (void 0 === t) throw Error("Can't open theme file."); return g(a, "ppt/" + t) }(a), tableStyles = g(a, "ppt/tableStyles.xml"), r.push({ type: "slideSize", data: i }); for (var o = s.slides.length, n = 0; n < o; n++) { var d = s.slides[n], h = b(a, d, n, i); r.push({ type: "slide", data: h }), r.push({ type: "progress-update", data: 100 * (n + 1) / o }) } r.push({ type: "globalCSS", data: D() }); var f = new Date; return r.push({ type: "ExecutionTime", data: f - t }), r }(d = d.load(a)), u = 0; u < h.length; u++)switch (h[u].type) { case "slide": e.append(h[u].data); break; case "pptx-thumb": break; case "slideSize": p = h[u].data.width, c = h[u].data.height; break; case "globalCSS": e.append("<style>" + h[u].data + "</style>"); break; case "ExecutionTime": ta(o), sa(r(".block")), sa(r("table td")), i = !0, L.slideMode && !f ? (f = !0, m(s, L)) : L.slideMode || r(".slides-loadnig-msg").remove(); break; case "progress-update": t = h[u].data, n = void 0, (n = r(".slides-loading-progress-bar")).width(t + "%"), n.html("<span style='text-align: center;'>Loading...(" + t + "%)</span>") }(!L.slideMode || L.slideMode && "revealjs" == L.slideType) && (null === document.getElementById("all_slides_warpper") && r("#" + s + " .slide").wrapAll("<div id='all_slides_warpper' class='slides'></div>"), L.slideMode && "revealjs" == L.slideType && r("#" + s).addClass("reveal")); var v = L.slidesScale, M = ""; if ("" != v) { var k = parseInt(v) / 100; L.slideMode && "revealjs" != L.slideType && (M = "transform:scale(" + k + "); transform-origin:top") } var y = r("#" + s + " .slide").height(), w = r("#" + s + " .slide").length, I = "" != v ? k : 1; r("#all_slides_warpper").attr({ style: M + ";height: " + w * y * I + "px" }) } function m(a, t) { if ("" == t.slideType || "divs2slidesjs" == t.slideType) { var e = r("#" + a + " .slide").height(); r("#" + a + " .slide").hide(), setTimeout(function () { var s = t.slideModeConfig; r(".slides-loadnig-msg").remove(), r("#" + a).divs2slides({ first: s.first, nav: s.nav, showPlayPauseBtn: t.showPlayPauseBtn, navTxtColor: s.navTxtColor, keyBoardShortCut: s.keyBoardShortCut, showSlideNum: s.showSlideNum, showTotalSlideNum: s.showTotalSlideNum, autoSlide: s.autoSlide, randomAutoSlide: s.randomAutoSlide, loop: s.loop, background: s.background, transition: s.transition, transitionTime: s.transitionTime }); var i = t.slidesScale, o = ""; if ("" != i) { var l = parseInt(i) / 100; o = "transform:scale(" + l + "); transform-origin:top" } var n = "" != i ? l : 1; r("#all_slides_warpper").attr({ style: o + ";height: " + 1 * e * n + "px" }) }, 1500) } else if ("revealjs" == t.slideType) { r(".slides-loadnig-msg").remove(); var s = ""; s = "" != t.revealjsPath ? t.revealjsPath : "./revealjs/reveal.js", r.getScript(s, function (a, r) { "success" == r && Reveal.initialize(t.revealjsConfig) }) } } function g(a, r) { var t = ca(a.file(r).asText(), { simplify: 1 }); return void 0 !== t["?xml"] ? t["?xml"] : t } function b(a, r, t, e) { var s = g(a, r.replace("slides/slide", "slides/_rels/slide") + ".rels").Relationships.Relationship, i = "", o = {}; if (s.constructor === Array) for (var d = 0; d < s.length; d++)switch (s[d].attrs.Type) { case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout": i = s[d].attrs.Target.replace("../", "ppt/"); break; case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/image": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart": case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink": default: o[s[d].attrs.Id] = { type: s[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: s[d].attrs.Target.replace("../", "ppt/") } } else i = s.attrs.Target.replace("../", "ppt/"); var h = g(a, i), p = M(h), c = h["p:sldLayout"]["p:clrMapOvr"]["a:overrideClrMapping"]; void 0 !== c && (n = c.attrs); var f = "", u = {}; if ((s = g(a, i.replace("slideLayouts/slideLayout", "slideLayouts/_rels/slideLayout") + ".rels").Relationships.Relationship).constructor === Array) for (d = 0; d < s.length; d++)switch (s[d].attrs.Type) { case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster": f = s[d].attrs.Target.replace("../", "ppt/"); break; default: u[s[d].attrs.Id] = { type: s[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: s[d].attrs.Target.replace("../", "ppt/") } } else f = s.attrs.Target.replace("../", "ppt/"); var v = g(a, f), m = J(v, ["p:sldMaster", "p:txStyles"]), b = M(v), y = "", w = {}; if ((s = g(a, f.replace("slideMasters/slideMaster", "slideMasters/_rels/slideMaster") + ".rels").Relationships.Relationship).constructor === Array) for (d = 0; d < s.length; d++)switch (s[d].attrs.Type) { case "http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme": y = s[d].attrs.Target.replace("../", "ppt/"); break; default: w[s[d].attrs.Id] = { type: s[d].attrs.Type.replace("http://schemas.openxmlformats.org/officeDocument/2006/relationships/", ""), target: s[d].attrs.Target.replace("../", "ppt/") } } else y = s.attrs.Target.replace("../", "ppt/"); void 0 !== y && (l = g(a, y)); var I = g(a, r), P = I["p:sld"]["p:cSld"]["p:spTree"], C = { zip: a, slideLayoutTables: p, slideMasterTables: b, slideResObj: o, slideMasterTextStyles: m, layoutResObj: u, masterResObj: w }, x = function (a, r, t, e) { var s, i = J(a, ["p:sld", "p:cSld", "p:bg", "p:bgPr"]), o = J(a, ["p:sld", "p:cSld", "p:bg", "p:bgRef"]); if (void 0 !== i) { var n = q(i); if ("SOLID_FILL" == n) { var d = i["a:solidFill"], h = Q(d), p = Y(d); s = "background: rgba(" + F(h) + "," + p + ");" } else "GRADIENT_FILL" == n ? s = N(i, void 0, t) : "PIC_FILL" == n && (s = O(i, "slideBg", e)) } else if (void 0 !== o) { if (void 0 !== o["a:srgbClr"]) b = J(o, ["a:srgbClr", "attrs", "val"]); else if (void 0 !== o["a:schemeClr"]) { var c = J(o, ["a:schemeClr", "attrs", "val"]); b = W("a:" + c, t) } var f = Number(o.attrs.idx); if (0 == f || 1e3 == f); else if (f > 0 && f < 1e3); else if (f > 1e3) { var u = f - 1e3, L = l["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:bgFillStyleLst"], v = []; Object.keys(L).forEach(function (a) { var r = L[a]; if ("attrs" != a) if (r.constructor === Array) for (var t = 0; t < r.length; t++) { var e = {}; e[a] = r[t], e.idex = r[t].attrs.order, v.push(e) } else { var e = {}; e[a] = r, e.idex = r.attrs.order, v.push(e) } }); var m = v.slice(0); m.sort(function (a, r) { return a.idex - r.idex }); var g = m[u - 1], n = q(g); if ("SOLID_FILL" == n) { var d = g["a:solidFill"], p = Y(d); s = "background: rgba(" + F(b) + "," + p + ");" } else "GRADIENT_FILL" == n && (s = N(g, b, t)) } } else if (i = J(r, ["p:sldLayout", "p:cSld", "p:bg", "p:bgPr"]), o = J(r, ["p:sldLayout", "p:cSld", "p:bg", "p:bgRef"]), void 0 !== i) { var n = q(i); if ("SOLID_FILL" == n) { var d = i["a:solidFill"], h = Q(d), p = Y(d); s = "background: rgba(" + F(h) + "," + p + ");" } else "GRADIENT_FILL" == n ? s = N(i, void 0, t) : "PIC_FILL" == n && (s = O(i, "layoutBg", e)) } else if (void 0 !== o) s = "background: red;"; else if (i = J(t, ["p:sldMaster", "p:cSld", "p:bg", "p:bgPr"]), o = J(t, ["p:sldMaster", "p:cSld", "p:bg", "p:bgRef"]), void 0 !== i) { var n = q(i); if ("SOLID_FILL" == n) { var d = i["a:solidFill"], h = Q(d), p = Y(d); s = "background: rgba(" + F(h) + "," + p + ");" } else "GRADIENT_FILL" == n ? s = N(i, void 0, t) : "PIC_FILL" == n && (s = O(i, "masterBg", e)) } else if (void 0 !== o) { var b; if (void 0 !== o["a:srgbClr"]) b = J(o, ["a:srgbClr", "attrs", "val"]); else if (void 0 !== o["a:schemeClr"]) { var c = J(o, ["a:schemeClr", "attrs", "val"]); b = W("a:" + c, t) } var f = Number(o.attrs.idx); if (0 == f || 1e3 == f); else if (f > 0 && f < 1e3); else if (f > 1e3) { var u = f - 1e3, L = l["a:theme"]["a:themeElements"]["a:fmtScheme"]["a:bgFillStyleLst"], v = []; Object.keys(L).forEach(function (a) { var r = L[a]; if ("attrs" != a) if (r.constructor === Array) for (var t = 0; t < r.length; t++) { var e = {}; e[a] = r[t], e.idex = r[t].attrs.order, v.push(e) } else { var e = {}; e[a] = r, e.idex = r.attrs.order, v.push(e) } }); var m = v.slice(0); m.sort(function (a, r) { return a.idex - r.idex }); var g = m[u - 1], n = q(g); if ("SOLID_FILL" == n) { var d = g["a:solidFill"], p = Y(d); s = "background: rgba(" + F(b) + "," + p + ");" } else "GRADIENT_FILL" == n ? s = N(g, b, t) : console.log(n) } } return s }(I, h, v, C); if (L.slideMode && "revealjs" == L.slideType) var _ = "<section class='slide' style='width:" + e.width + "px; height:" + e.height + "px;" + x + "'>"; else _ = "<div class='slide' style='width:" + e.width + "px; height:" + e.height + "px;" + x + "'>"; for (var D in P) if (P[D].constructor === Array) for (d = 0; d < P[D].length; d++)_ += k(D, P[D][d], C); else _ += k(D, P[D], C); return L.slideMode && "revealjs" == L.slideType ? _ + "</section>" : _ + "</div>" } function M(a) { var r = a[Object.keys(a)[0]]["p:cSld"]["p:spTree"], t = {}, e = {}, s = {}; for (var i in r) if ("p:nvGrpSpPr" != i && "p:grpSpPr" != i) { var o = r[i]; if (o.constructor === Array) for (var l = 0; l < o.length; l++) { var n = J(p = o[l]["p:nvSpPr"], ["p:cNvPr", "attrs", "id"]), d = J(p, ["p:nvPr", "p:ph", "attrs", "idx"]), h = J(p, ["p:nvPr", "p:ph", "attrs", "type"]); void 0 !== n && (t[n] = o[l]), void 0 !== d && (e[d] = o[l]), void 0 !== h && (s[h] = o[l]) } else { var p; n = J(p = o["p:nvSpPr"], ["p:cNvPr", "attrs", "id"]), d = J(p, ["p:nvPr", "p:ph", "attrs", "idx"]), h = J(p, ["p:nvPr", "p:ph", "attrs", "type"]); void 0 !== n && (t[n] = o), void 0 !== d && (e[d] = o), void 0 !== h && (s[h] = o) } } return { idTable: t, idxTable: e, typeTable: s } } function k(a, r, t) { var e = ""; switch (a) { case "p:sp": e = y(r, t); break; case "p:cxnSp": e = function (a, r) { var t = a["p:nvCxnSpPr"]["p:cNvPr"].attrs.id, e = a["p:nvCxnSpPr"]["p:cNvPr"].attrs.name, s = a.attrs.order; return w(a, void 0, void 0, t, e, void 0, void 0, s, r) }(r, t); break; case "p:pic": e = function (a, r) { var t = "", e = !1, s = a.attrs.order, i = a["p:blipFill"]["a:blip"].attrs["r:embed"], o = r.slideResObj[i].target, l = ha(o).toLowerCase(), n = r.zip, d = n.file(o).asArrayBuffer(), h = "", p = a["p:spPr"]["a:xfrm"], c = 0, f = J(a, ["p:spPr", "a:xfrm", "attrs", "rot"]); void 0 !== f && (c = aa(f)); var u, v, m, g, b, M, k, y = J(a, ["p:nvPicPr", "p:nvPr", "a:videoFile"]), w = !1, I = !1, P = L.mediaProcess; if (void 0 !== y & P) { u = y.attrs["r:link"]; var C = function (a) { return /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/.test(a) }(v = r.slideResObj[u].target); C ? (v = pa(v), I = !0, w = !0, e = !0) : "mp4" != (m = ha(v).toLowerCase()) && "webm" != m && "ogg" != m || (b = n.file(v).asArrayBuffer(), g = ra(m), M = new Blob([b], { type: g }), k = URL.createObjectURL(M), w = !0, e = !0) } var x, _, D, S, z, T, A, R = J(a, ["p:nvPicPr", "p:nvPr", "a:audioFile"]), B = !1; if (void 0 !== R & P && (x = R.attrs["r:link"], _ = r.slideResObj[x].target, "mp3" == (D = ha(_).toLowerCase()) || "wav" == D || "ogg" == D)) { S = n.file(_).asArrayBuffer(), z = new Blob([S]), T = URL.createObjectURL(z); var N = 20 * parseInt(p["a:ext"].attrs.cx), O = p["a:ext"].attrs.cy, F = parseInt(p["a:off"].attrs.x) / 2.5, E = p["a:off"].attrs.y; A = { "a:ext": { attrs: { cx: N, cy: O } }, "a:off": { attrs: { x: F, y: E } } }, B = !0, w = !0, e = !0 } h = ra(l), t = "<div class='block content' style='" + G(P && B ? A : p, void 0, void 0) + j(P && B ? A : p, void 0, void 0) + " z-index: " + s + ";transform: rotate(" + c + "deg);'>", void 0 === y && void 0 === R || !P || !w ? t += "<img src='data:" + h + ";base64," + da(d) + "' style='width: 100%; height: 100%'/>" : (void 0 !== y || void 0 !== R) && P && w && (void 0 === y || I ? void 0 !== y && I && (t += "<iframe   src='" + v + "' controls style='width: 100%; height: 100%'></iframe >") : t += "<video  src='" + k + "' controls style='width: 100%; height: 100%'>Your browser does not support the video tag.</video>", void 0 !== R && (t += '<audio id="audio_player" controls ><source src="' + T + '"></audio>')); !w && e && (t += "<span style='color:red;font-size:40px;position: absolute;'>This media file Not supported by HTML5</span>"); void 0 === y && void 0 === R || P || !w || console.log("Founded supported media file but media process disabled (mediaProcess=false)"); return t += "</div>" }(r, t); break; case "p:graphicFrame": e = function (a, r) { var t = ""; switch (J(a, ["a:graphic", "a:graphicData", "attrs", "uri"])) { case "http://schemas.openxmlformats.org/drawingml/2006/table": t = function (a, r) { var t = a.attrs.order, e = J(a, ["a:graphic", "a:graphicData", "a:tbl"]), s = J(a, ["p:xfrm"]), i = J(a, ["a:graphic", "a:graphicData", "a:tbl", "a:tblPr"]), o = J(a, ["a:graphic", "a:graphicData", "a:tbl", "a:tblGrid", "a:gridCol"]), l = ""; if (void 0 !== i) { var n = i.attrs.rtl; l = 1 == n ? "dir=rtl" : "dir=ltr" } var d = i.attrs.firstRow, h = (i.attrs.firstCol, i.attrs.lastRow, i.attrs.lastCol, i.attrs.bandRow), p = (i.attrs.bandCol, "<table " + l + " style='border-collapse: collapse;" + G(s, void 0, void 0) + j(s, void 0, void 0) + " z-index: " + t + ";'>"), c = e["a:tr"]; if (c.constructor === Array) for (var f = 0; f < c.length; f++) { var u, L = c[f].attrs.h, v = 0, m = ""; void 0 !== L && (v = 96 * parseInt(L) / 914400, m += "height:" + v + "px;"); var g = i["a:tableStyleId"]; if (void 0 !== g) { var b = tableStyles["a:tblStyleLst"]["a:tblStyle"]; if (b.constructor === Array) for (var M = 0; M < b.length; M++)b[M].attrs.styleId == g && (u = b[M]); else b.attrs.styleId == g && (u = b) } if (0 == f && void 0 !== d) { var k = "fff", y = 1; if (void 0 !== u["a:firstRow"]) { var w = J(u, ["a:firstRow", "a:tcStyle", "a:fill", "a:solidFill"]); void 0 !== w && (k = Q(w), y = Y(w)); var I = J(u, ["a:firstRow", "a:tcStyle", "a:tcBdr"]); if (void 0 !== I) { var P = R(I); m += P } J(u, ["a:firstRow", "a:tcTxStyle"]) } m += " background-color:#" + k + "; opacity:" + y + ";" } else if (f > 0 && void 0 !== h) { var k = "fff", y = 1; if (f % 2 == 0) { if (void 0 !== u["a:band2H"]) { var w = J(u, ["a:band2H", "a:tcStyle", "a:fill", "a:solidFill"]); void 0 !== w && (k = Q(w), y = Y(w)); var I = J(u, ["a:band2H", "a:tcStyle", "a:tcBdr"]); if (void 0 !== I) { var P = R(I); m += P } J(u, ["a:band2H", "a:tcTxStyle"]) } } else if (void 0 !== u["a:band1H"]) { var w = J(u, ["a:band1H", "a:tcStyle", "a:fill", "a:solidFill"]); void 0 !== w && (k = Q(w), y = Y(w)); var I = J(u, ["a:band1H", "a:tcStyle", "a:tcBdr"]); if (void 0 !== I) { var P = R(I); m += P } J(u, ["a:band1H", "a:tcTxStyle"]) } m += " background-color:#" + k + "; opacity:" + y + ";" } p += "<tr style='" + m + "'>"; var x = c[f]["a:tc"]; if (x.constructor === Array) for (var _ = 0; _ < x.length; _++) { var D = C(x[_]["a:txBody"], a, void 0, void 0, void 0, r), S = J(x[_], ["attrs", "rowSpan"]), z = J(x[_], ["attrs", "gridSpan"]), T = J(x[_], ["attrs", "vMerge"]), A = J(x[_], ["attrs", "hMerge"]), B = o[_].attrs.w, N = ""; if (void 0 !== B) { var O = 96 * parseInt(B) / 914400; N += "width:" + O + "px;" } var F = x[_]["a:tcPr"]["a:solidFill"], k = "", y = 1; if (void 0 !== F) k = Q(F), y = Y(F); else { var g = i["a:tableStyleId"]; if (void 0 !== g) for (var b = tableStyles["a:tblStyleLst"]["a:tblStyle"], M = 0; M < b.length; M++)b[M].attrs.styleId } "" != k && (N += " background-color:#" + k + ";", N += " opacity" + y + ";"), void 0 !== S ? p += "<td rowspan='" + parseInt(S) + "' style='" + N + "'>" + D + "</td>" : void 0 !== z ? p += "<td colspan='" + parseInt(z) + "' style='" + N + "'>" + D + "</td>" : void 0 === T && void 0 === A && (p += "<td style='" + N + "'>" + D + "</td>") } else { var D = C(x["a:txBody"], a, void 0, void 0, void 0, r), B = o[0].attrs.w, N = ""; if (void 0 !== B) { var O = 96 * parseInt(B) / 914400; N += "width:" + O + "px;" } var F = x["a:tcPr"]["a:solidFill"], k = "", y = 1; void 0 !== F && (k = Q(F), y = Y(F)), "" != k && (N += " background-color:#" + k + ";", N += " opacity" + y + ";"), p += "<td style='" + N + "'>" + D + "</td>" } p += "</tr>" } else { var L = c.attrs.h, v = 0; void 0 !== L ? (v = 96 * parseInt(L) / 914400, p += "<tr style='height:" + v + "px;'>") : p += "<tr>"; var x = c["a:tc"]; if (x.constructor === Array) for (var _ = 0; _ < x.length; _++) { var D = C(x[_]["a:txBody"], a, void 0, void 0, void 0, r), B = o[_].attrs.w, N = ""; if (void 0 !== B) { var O = 96 * parseInt(B) / 914400; N += "width:" + O + "px;" } var F = x[_]["a:tcPr"]["a:solidFill"], k = "", y = 1; void 0 !== F && (k = Q(F), y = Y(F)), "" != k && (N += " background-color:#" + k + ";", N += " opacity" + y + ";"), p += "<td style='" + N + "'>" + D + "</td>" } else { var D = C(x["a:txBody"], a, void 0, void 0, void 0, r), B = o[0].attrs.w, N = ""; if (void 0 !== B) { var O = 96 * parseInt(B) / 914400; N += "width:" + O + "px;" } var F = x[_]["a:tcPr"]["a:solidFill"], k = "", y = 1; void 0 !== F && (k = Q(F), y = Y(F)), "" != k && (N += " background-color:#" + k + ";", N += " opacity" + y + ";"), p += "<td style='" + N + "'>" + D + "</td>" } p += "</tr>" } return p }(a, r); break; case "http://schemas.openxmlformats.org/drawingml/2006/chart": t = function (a, r) { var t = a.attrs.order, e = J(a, ["p:xfrm"]), s = "<div id='chart" + d + "' class='block content' style='" + G(e, void 0, void 0) + j(e, void 0, void 0) + " z-index: " + t + ";'></div>", i = a["a:graphic"]["a:graphicData"]["c:chart"].attrs["r:id"], l = r.slideResObj[i].target, n = J(g(r.zip, l), ["c:chartSpace", "c:chart", "c:plotArea"]), h = null; for (var p in n) switch (p) { case "c:lineChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "lineChart", chartData: Z(n[p]["c:ser"]) } }; break; case "c:barChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "barChart", chartData: Z(n[p]["c:ser"]) } }; break; case "c:pieChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "pieChart", chartData: Z(n[p]["c:ser"]) } }; break; case "c:pie3DChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "pie3DChart", chartData: Z(n[p]["c:ser"]) } }; break; case "c:areaChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "areaChart", chartData: Z(n[p]["c:ser"]) } }; break; case "c:scatterChart": h = { type: "createChart", data: { chartID: "chart" + d, chartType: "scatterChart", chartData: Z(n[p]["c:ser"]) } } }null !== h && o.push(h); return d++, s }(a, r); break; case "http://schemas.openxmlformats.org/drawingml/2006/diagram": t = function (a, r) { a.attrs.order; var t = r.zip, e = J(a, ["p:xfrm"]), s = J(a, ["a:graphic", "a:graphicData", "dgm:relIds", "attrs"]), i = s["r:cs"], o = s["r:dm"], l = s["r:lo"], n = s["r:qs"], d = r.slideResObj[i].target, h = r.slideResObj[o].target, p = r.slideResObj[l].target; dgmQuickStyleFileName = r.slideResObj[n].target; g(t, d); var c = g(t, h), f = (g(t, p), g(t, dgmQuickStyleFileName), ""), u = J(c, ["dgm:dataModel", "dgm:extLst", "a:ext", "dsp:dataModelExt", "attrs"]); if (void 0 !== u) { var L = u.relId; f = r.slideResObj[L].target } var v = ""; "" != f && (v = g(t, f)); var m = J(v, ["dsp:drawing", "dsp:spTree", "dsp:sp"]), b = ""; if (void 0 !== m) for (var M = m.length, k = 0; k < M; k++) { var w = m[k], I = JSON.stringify(w), P = I.replace(/dsp:/g, "p:"), C = JSON.parse(P); b += y(C, r) } return "<div class='block content' style='" + G(e, void 0, void 0) + j(e, void 0, void 0) + "'>" + b + "</div>" }(a, r) }return t }(r, t); break; case "p:grpSp": e = function (a, r) { var t = 96 / 914400, e = a["p:grpSpPr"]["a:xfrm"], s = parseInt(e["a:off"].attrs.x) * t, i = parseInt(e["a:off"].attrs.y) * t, o = parseInt(e["a:chOff"].attrs.x) * t, l = parseInt(e["a:chOff"].attrs.y) * t, n = parseInt(e["a:ext"].attrs.cx) * t, d = parseInt(e["a:ext"].attrs.cy) * t, h = parseInt(e["a:chExt"].attrs.cx) * t, p = parseInt(e["a:chExt"].attrs.cy) * t, c = "<div class='block group' style='z-index: " + a.attrs.order + "; top: " + (i - l) + "px; left: " + (s - o) + "px; width: " + (n - h) + "px; height: " + (d - p) + "px;'>"; for (var f in a) if (a[f].constructor === Array) for (var u = 0; u < a[f].length; u++)c += k(f, a[f][u], r); else c += k(f, a[f], r); return c += "</div>" }(r, t); break; case "mc:AlternateContent": e = y(J(r, ["mc:Fallback", "p:sp"]), t) }return e } function y(a, r) { var t = J(a, ["p:nvSpPr", "p:cNvPr", "attrs", "id"]), e = J(a, ["p:nvSpPr", "p:cNvPr", "attrs", "name"]), s = void 0 === J(a, ["p:nvSpPr", "p:nvPr", "p:ph"]) ? void 0 : J(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "idx"]), i = void 0 === J(a, ["p:nvSpPr", "p:nvPr", "p:ph"]) ? void 0 : J(a, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"]), o = J(a, ["attrs", "order"]), l = void 0, n = void 0; return void 0 !== i ? (l = r.slideLayoutTables.typeTable[i], n = r.slideMasterTables.typeTable[i]) : void 0 !== s && (l = r.slideLayoutTables.idxTable[s], n = r.slideMasterTables.idxTable[s]), void 0 === i && void 0 === (i = J(l, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"])) && (i = J(n, ["p:nvSpPr", "p:nvPr", "p:ph", "attrs", "type"])), w(a, l, n, t, e, s, i, o, r) } function w(a, r, t, e, s, i, o, l, n) { var d = ["p:spPr", "a:xfrm"], h = J(a, d), p = J(r, d), c = J(t, d), f = "", u = J(a, ["attrs", "order"]), L = J(a, ["p:spPr", "a:prstGeom", "attrs", "prst"]), v = J(a, ["p:spPr", "a:custGeom"]), m = !1; "1" !== J(h, ["attrs", "flipV"]) && "1" !== J(h, ["attrs", "flipH"]) || (m = !0); var g, b = aa(J(h, ["attrs", "rot"])), M = J(a, ["p:txXfrm"]); if (void 0 !== M) { var k = J(M, ["attrs", "rot"]); void 0 !== k && (g = aa(k) + 90) } else g = b; if (void 0 !== L || void 0 !== v) { var y = J(h, ["a:off", "attrs"]), w = (parseInt(y.x), parseInt(y.y), J(h, ["a:ext", "attrs"])), x = 96 * parseInt(w.cx) / 914400, _ = 96 * parseInt(w.cy) / 914400; f += "<svg class='drawing' _id='" + e + "' _idx='" + i + "' _type='" + o + "' _name='" + s + "' style='" + G(h, void 0, void 0) + j(h, void 0, void 0) + " z-index: " + l + ";transform: rotate(" + b + "deg);'>", f += "<defs>"; var D = E(a, !0, n), S = !1, T = !1, A = q(J(a, ["p:spPr"])); if ("GRADIENT_FILL" == A) { S = !0; var R = D.color; f += function (a, r, t, e, s) { var i = function (a) { var r = ["0%", "100%"]; if (0 == a) return !0; for (var t = a; t--;) { var e = 100 - 100 / (a + 1) * (t + 1), s = e + "%"; r.splice(-1, 0, s) } return r }(e.length - 2), o = "", l = function (a, r, t) { var e = parseFloat(t), s = parseFloat(r), i = parseFloat(a), o = 2, l = 2, n = e / 2, d = s / 2, h = 2, p = 2, c = 2, f = 2, u = (i % 360 + 360) % 360, L = (360 - u) * Math.PI / 180, v = Math.tan(L), m = d - v * n; 0 == u ? (h = e, p = d, c = 0, f = d) : u < 90 ? (l = e, o = 0) : 90 == u ? (h = n, p = 0, c = n, f = s) : u < 180 ? (l = 0, o = 0) : 180 == u ? (h = 0, p = d, c = e, f = d) : u < 270 ? (l = 0, o = s) : 270 == u ? (h = n, p = s, c = n, f = 0) : (l = e, o = s); var g = o + l / v, h = 2 == h ? v * (g - m) / (Math.pow(v, 2) + 1) : h, p = 2 == p ? v * h + m : p, c = 2 == c ? e - h : c, f = 2 == f ? s - p : f, b = Math.round(c / e * 100 * 100) / 100, M = Math.round(f / s * 100 * 100) / 100, k = Math.round(h / e * 100 * 100) / 100, y = Math.round(p / s * 100 * 100) / 100; return [b, M, k, y] }(t, r, a), n = l[0], d = l[1], h = l[2], p = l[3], c = i.length, f = c < 20 ? 100 : 1e3; o += '<linearGradient id="linGrd_' + s + '"' + (' gradientUnits="userSpaceOnUse" x1="' + n + '%" y1="' + d + '%" x2="' + h + '%" y2="' + p + '%"') + ">\n"; for (var u = 0; u < c; u++)o += '<stop offset="' + Math.round(parseFloat(i[u]) / 100 * f) / f + '" stop-color="' + e[u] + '"', o += "/>\n"; return o += "</linearGradient>\n" }(x, _, D.rot, R, u) } else if ("PIC_FILL" == A) { T = !0, f += function (a, r) { var t = '<pattern id="imgPtrn_' + r + '"  patternContentUnits="objectBoundingBox"  width="1" height="1">'; return a = pa(a), t += '<image  xlink:href="' + a + '" preserveAspectRatio="none" width="1" height="1"></image>', t += "</pattern>" }(D, u) } else "SOLID_FILL" == A || "PATTERN_FILL" == A || "arc" != L && "bracketPair" != L && "bracePair" != L && "leftBracket" != L && "leftBrace" != L && "rightBrace" != L && "rightBracket" != L || (D = "none"); var N = B(a, !0, "shape"), O = J(a, ["p:spPr", "a:ln", "a:headEnd", "attrs"]), F = J(a, ["p:spPr", "a:ln", "a:tailEnd", "attrs"]); if (void 0 !== O && ("triangle" === O.type || "arrow" === O.type) || void 0 !== F && ("triangle" === F.type || "arrow" === F.type)) f += "<marker id='markerTriangle_" + u + "' viewBox='0 0 10 10' refX='1' refY='5' markerWidth='5' markerHeight='5' stroke='" + N.color + "' fill='" + N.color + "' orient='auto-start-reverse' markerUnits='strokeWidth'><path d='M 0 0 L 10 5 L 0 10 z' /></marker>"; f += "</defs>" } if (void 0 !== L && void 0 === v) { switch (L) { case "rect": case "flowChartProcess": case "flowChartPredefinedProcess": case "flowChartInternalStorage": case "actionButtonBlank": f += "<rect x='0' y='0' width='" + x + "' height='" + _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", "flowChartPredefinedProcess" == L ? f += "<rect x='" + x * (1 / 8) + "' y='0' width='" + .75 * x + "' height='" + _ + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />" : "flowChartInternalStorage" == L && (f += " <polyline points='" + x * (1 / 8) + " 0," + x * (1 / 8) + " " + _ + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", f += " <polyline points='0 " + _ * (1 / 8) + "," + x + " " + _ * (1 / 8) + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"); break; case "flowChartCollate": f += "<path d='" + (ka = "M 0,0 L" + x + ",0 L0," + _ + " L" + x + "," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartDocument": f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + (Ro = 17322 * _ / 21600) + " C" + (So = 10800 * x / 21600) + "," + Ro + " " + So + "," + (No = 23922 * _ / 21600) + " 0," + (Bo = 20172 * _ / 21600) + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartMultidocument": f += "<path d='" + (ka = "M0," + (Bo = 3675 * _ / 21600) + " L" + (Ki = 18595 * x / 21600) + "," + Bo + " L" + Ki + "," + (Ro = 18022 * _ / 21600) + " C" + (To = 9298 * x / 21600) + "," + Ro + " " + To + "," + (No = 23542 * _ / 21600) + " 0," + (st = 20782 * _ / 21600) + " zM" + (So = 1532 * x / 21600) + "," + Bo + " L" + So + "," + (ut = 1815 * _ / 21600) + " L" + (zo = 2e4 * x / 21600) + "," + ut + " L" + zo + "," + (mo = 16252 * _ / 21600) + " C" + (ft = 19298 * x / 21600) + "," + mo + " " + Ki + "," + (Di = 16352 * _ / 21600) + " " + Ki + "," + Di + "M" + ($i = 2972 * x / 21600) + "," + ut + " L" + $i + ",0 L" + x + ",0 L" + x + "," + (_i = 14392 * _ / 21600) + " C" + (Ji = 20800 * x / 21600) + "," + _i + " " + zo + "," + (it = 14467 * _ / 21600) + " " + zo + "," + it) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonBackPrevious": var H = _ / 2; f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + (Pe = (U = x / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + "," + H + " L" + (Ce = U + jo) + "," + (we = H - jo) + " L" + Ce + "," + (Ie = H + jo) + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonBeginning": H = _ / 2; f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + (je = (Pe = (U = x / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + (De = (xe = 3 * qe / 4) / 4)) + "," + H + " L" + (Ce = U + jo) + "," + (we = H - jo) + " L" + Ce + "," + (Ie = H + jo) + " zM" + (Ge = Pe + (_e = xe / 8)) + "," + we + " L" + Pe + "," + we + " L" + Pe + "," + Ie + " L" + Ge + "," + Ie + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonDocument": var U = x / 2; H = _ / 2; jo = 3 * (qe = Math.min(x, _)) / 8, f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + (Pe = U - (Uo = 9 * qe / 32)) + "," + (we = H - jo) + " L" + (_e = (Ce = U + Uo) - (xe = 3 * qe / 16)) + "," + we + " L" + Ce + "," + (De = we + xe) + " L" + Ce + "," + (Ie = H + jo) + " L" + Pe + "," + Ie + " zM" + _e + "," + we + " L" + _e + "," + De + " L" + Ce + "," + De + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonEnd": H = _ / 2; f += "<path d='" + (ka = "M0," + _ + " L" + x + "," + _ + " L" + x + ",0 L0,0 z M" + (je = (Pe = (U = x / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + (De = 7 * (xe = 3 * qe / 4) / 8)) + "," + (we = H - jo) + " L" + (Ce = U + jo) + "," + we + " L" + Ce + "," + (Ie = H + jo) + " L" + je + "," + Ie + " z M" + (Ge = Pe + (_e = 3 * xe / 4)) + "," + H + " L" + Pe + "," + we + " L" + Pe + "," + Ie + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonForwardNext": H = _ / 2; f += "<path d='" + (ka = "M0," + _ + " L" + x + "," + _ + " L" + x + ",0 L0,0 z M" + (Ce = (U = x / 2) + (jo = 3 * (qe = Math.min(x, _)) / 8)) + "," + H + " L" + (Pe = U - jo) + "," + (we = H - jo) + " L" + Pe + "," + (Ie = H + jo) + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonHelp": U = x / 2; Z = (we = (H = _ / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + (Ae = 17 * (xe = 3 * qe / 4) / 28), $ = we + 21 * xe / 28, ha = xe / 14; var Q = (ia = (Pe = U - jo) + (ze = 3 * xe / 7)) + (_e = xe / 7), X = (K = we + (Be = 11 * xe / 14)) + (ca = 3 * xe / 28), V = ((oa = Pe + (Te = 4 * xe / 7)) + ia + (Ge = 2 * xe / 7)) / 2; f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + (ta = Pe + (De = 3 * xe / 14)) + "," + (ga = we + Ge) + P(He = ta + Ge, ga, Ge, Ge, 180, 360, !1).replace("M", "L") + P(V, ga, _e, De, 0, 90, !1).replace("M", "L") + P(V, Z, ha, ca, 270, 180, !1).replace("M", "L") + " L" + oa + "," + $ + " L" + ia + "," + $ + " L" + ia + "," + Z + P(Q, Z, _e, De, 180, 270, !1).replace("M", "L") + P(oa, ga, ha, ca, 90, 0, !1).replace("M", "L") + P(He, ga, _e, _e, 0, -180, !1).replace("M", "L") + " zM" + U + "," + K + P(U, X, ca, ca, 270, 630, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonHome": f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (U = x / 2) + "," + (we = (H = _ / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + " L" + (Pe = U - jo) + "," + H + " L" + (W = Pe + (De = (xe = 3 * qe / 4) / 8)) + "," + H + " L" + W + "," + (Ie = H + jo) + " L" + (ta = Pe + 7 * xe / 8) + "," + Ie + " L" + ta + "," + H + " L" + (Ce = U + jo) + "," + H + " L" + (ra = Pe + (Re = 13 * xe / 16)) + "," + (Oe = we + (je = 5 * xe / 16)) + " L" + ra + "," + (Be = we + (_e = xe / 16)) + " L" + (K = Pe + (Te = 11 * xe / 16)) + "," + Be + " L" + K + "," + (Ne = we + (Ge = 3 * xe / 16)) + " z M" + (Z = Pe + (Se = 7 * xe / 16)) + "," + (ga = we + (Ae = 3 * xe / 4)) + " L" + ($ = Pe + (ze = 9 * xe / 16)) + "," + ga + " L" + $ + "," + Ie + " L" + Z + "," + Ie + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonInformation": U = x / 2; W = (we = (H = _ / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + (je = 5 * (xe = 3 * qe / 4) / 16), Z = we + (Se = 3 * xe / 8), $ = we + 13 * xe / 16, K = we + (Be = 7 * xe / 8), ra = (Pe = U - jo) + je, ea = Pe + (ze = 13 * xe / 32), sa = Pe + (Te = 19 * xe / 32), oa = Pe + (Re = 11 * xe / 16); var Y = (Ne = we + (_e = xe / 32)) + (la = 3 * xe / 32); f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + U + "," + we + P(U, Ue = we + jo, jo, jo, 270, 630, !1).replace("M", "L") + " zM" + U + "," + Ne + P(U, Y, la, la, 270, 630, !1).replace("M", "L") + "M" + ra + "," + W + " L" + sa + "," + W + " L" + sa + "," + $ + " L" + oa + "," + $ + " L" + oa + "," + K + " L" + ra + "," + K + " L" + ra + "," + $ + " L" + ea + "," + $ + " L" + ea + "," + Z + " L" + ra + "," + Z + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonMovie": var W, Z, $, K, ra, ta, ea, sa, ia, oa, la, na, da, ha, ca, fa, ua, La, va, ma; U = x / 2; Ie = (H = _ / 2) + (jo = 3 * (qe = Math.min(x, _)) / 8), (we = H - jo) + (K = (Pe = U - jo) + (_e = 1455 * (xe = 3 * qe / 4) / 21600)), f += "<path d='" + (ka = "M0," + _ + " L" + x + "," + _ + " L" + x + ",0 L0,0 zM" + Pe + "," + (na = we + (Re = 5280 * xe / 21600)) + " L" + Pe + "," + (ua = we + (ga = 9555 * xe / 21600)) + " L" + K + "," + ua + " L" + (ra = Pe + (De = 1905 * xe / 21600)) + "," + (fa = we + (Oe = 9067 * xe / 21600)) + " L" + (ta = Pe + (Ge = 2325 * xe / 21600)) + "," + fa + " L" + ta + "," + (ma = we + ($ = 15592 * xe / 21600)) + " L" + (sa = Pe + (Se = 17010 * xe / 21600)) + "," + ma + " L" + sa + "," + (La = we + (W = 13342 * xe / 21600)) + " L" + (ia = Pe + (ze = 19335 * xe / 21600)) + "," + La + " L" + (la = Pe + (Ae = 20595 * xe / 21600)) + "," + (va = we + (Z = 14580 * xe / 21600)) + " L" + (Ce = U + jo) + "," + va + " L" + Ce + "," + (ha = we + (Be = 6630 * xe / 21600)) + " L" + la + "," + ha + " L" + (oa = Pe + (Te = 19725 * xe / 21600)) + "," + (ca = we + (Ne = 7492 * xe / 21600)) + " L" + sa + "," + ca + " L" + sa + "," + ha + " L" + (ea = Pe + (je = 16155 * xe / 21600)) + "," + (da = we + 5730 * xe / 21600) + " L" + ra + "," + da + " L" + K + "," + na + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonReturn": var ga; H = _ / 2; Re = (Pe = (U = x / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + (_e = 7 * (xe = 3 * qe / 4) / 8), Ne = Pe + (je = 3 * xe / 8), Oe = Pe + (Se = xe / 4); Y = (ze = (we = H - jo) + (De = 3 * xe / 4)) - (ga = xe / 8); var ba = Pe + je, Ma = (Ie = H + jo) - je; f += "<path d='" + (ka = "M0," + _ + " L" + x + "," + _ + " L" + x + ",0 L0,0 z M" + (Ce = U + jo) + "," + (Ae = we + Se) + " L" + (Pe + De) + "," + we + " L" + U + "," + Ae + " L" + (Be = Pe + (Ge = 5 * xe / 8)) + "," + Ae + " L" + Be + "," + (Te = we + Ge) + P(He = Be - ga, Te, ga, ga, 0, 90, !1).replace("M", "L") + " L" + Ne + "," + ze + P(Ne, Y, ga, ga, 90, 180, !1).replace("M", "L") + " L" + Oe + "," + Ae + " L" + Pe + "," + Ae + " L" + Pe + "," + Te + P(ba, Te, je, je, 180, 90, !1).replace("M", "L") + " L" + U + "," + Ie + P(U, Ma, je, je, 90, 0, !1).replace("M", "L") + " L" + Re + "," + Ae + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "actionButtonSound": H = _ / 2; f += "<path d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (Pe = (U = x / 2) - (jo = 3 * (qe = Math.min(x, _)) / 8)) + "," + (Ae = (we = H - jo) + (De = 5 * (xe = 3 * qe / 4) / 16)) + " L" + (Be = Pe + De) + "," + Ae + " L" + (Ne = Pe + (Ge = 5 * xe / 8)) + "," + we + " L" + Ne + "," + (Ie = H + jo) + " L" + Be + "," + (Re = we + (je = 11 * xe / 16)) + " L" + Pe + "," + Re + " z M" + (Oe = Pe + (Se = 3 * xe / 4)) + "," + Ae + " L" + (Ce = U + jo) + "," + (Te = we + (_e = xe / 8)) + " M" + Oe + "," + H + " L" + Ce + "," + H + " M" + Oe + "," + Re + " L" + Ce + "," + (we + (ze = 7 * xe / 8))) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "irregularSeal1": case "irregularSeal2": if ("irregularSeal1" == L) var ka = "M" + 10800 * x / 21600 + "," + 5800 * _ / 21600 + " L" + 14522 * x / 21600 + ",0 L" + 14155 * x / 21600 + "," + 5325 * _ / 21600 + " L" + 18380 * x / 21600 + "," + 4457 * _ / 21600 + " L" + 16702 * x / 21600 + "," + 7315 * _ / 21600 + " L" + 21097 * x / 21600 + "," + 8137 * _ / 21600 + " L" + 17607 * x / 21600 + "," + 10475 * _ / 21600 + " L" + x + "," + 13290 * _ / 21600 + " L" + 16837 * x / 21600 + "," + 12942 * _ / 21600 + " L" + 18145 * x / 21600 + "," + 18095 * _ / 21600 + " L" + 14020 * x / 21600 + "," + 14457 * _ / 21600 + " L" + 13247 * x / 21600 + "," + 19737 * _ / 21600 + " L" + 10532 * x / 21600 + "," + 14935 * _ / 21600 + " L" + 8485 * x / 21600 + "," + _ + " L" + 7715 * x / 21600 + "," + 15627 * _ / 21600 + " L" + 4762 * x / 21600 + "," + 17617 * _ / 21600 + " L" + 5667 * x / 21600 + "," + 13937 * _ / 21600 + " L" + 135 * x / 21600 + "," + 14587 * _ / 21600 + " L" + 3722 * x / 21600 + "," + 11775 * _ / 21600 + " L0," + 8615 * _ / 21600 + " L" + 4627 * x / 21600 + "," + 7617 * _ / 21600 + " L" + 370 * x / 21600 + "," + 2295 * _ / 21600 + " L" + 7312 * x / 21600 + "," + 6320 * _ / 21600 + " L" + 8352 * x / 21600 + "," + 2295 * _ / 21600 + " z"; else if ("irregularSeal2" == L) ka = "M" + 11462 * x / 21600 + "," + 4342 * _ / 21600 + " L" + 14790 * x / 21600 + ",0 L" + 14525 * x / 21600 + "," + 5777 * _ / 21600 + " L" + 18007 * x / 21600 + "," + 3172 * _ / 21600 + " L" + 16380 * x / 21600 + "," + 6532 * _ / 21600 + " L" + x + "," + 6645 * _ / 21600 + " L" + 16985 * x / 21600 + "," + 9402 * _ / 21600 + " L" + 18270 * x / 21600 + "," + 11290 * _ / 21600 + " L" + 16380 * x / 21600 + "," + 12310 * _ / 21600 + " L" + 18877 * x / 21600 + "," + 15632 * _ / 21600 + " L" + 14640 * x / 21600 + "," + 14350 * _ / 21600 + " L" + 14942 * x / 21600 + "," + 17370 * _ / 21600 + " L" + 12180 * x / 21600 + "," + 15935 * _ / 21600 + " L" + 11612 * x / 21600 + "," + 18842 * _ / 21600 + " L" + 9872 * x / 21600 + "," + 17370 * _ / 21600 + " L" + 8700 * x / 21600 + "," + 19712 * _ / 21600 + " L" + 7527 * x / 21600 + "," + 18125 * _ / 21600 + " L" + 4917 * x / 21600 + "," + _ + " L" + 4805 * x / 21600 + "," + 18240 * _ / 21600 + " L" + 1285 * x / 21600 + "," + 17825 * _ / 21600 + " L" + 3330 * x / 21600 + "," + 15370 * _ / 21600 + " L0," + 12877 * _ / 21600 + " L" + 3935 * x / 21600 + "," + 11592 * _ / 21600 + " L" + 1172 * x / 21600 + "," + 8270 * _ / 21600 + " L" + 5372 * x / 21600 + "," + 7817 * _ / 21600 + " L" + 4502 * x / 21600 + "," + 3625 * _ / 21600 + " L" + 8550 * x / 21600 + "," + 6382 * _ / 21600 + " L" + 9722 * x / 21600 + "," + 1887 * _ / 21600 + " z"; f += "<path d='" + ka + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartTerminator": var ya = 90; f += "<path d='" + (ka = "M" + (So = 3475 * x / 21600) + ",0 L" + (zo = 18125 * x / 21600) + ",0" + P(zo, _ / 2, So, Ro = 10800 * _ / 21600, Pa = 270, Pa + (Ia = 180), !1).replace("M", "L") + " L" + So + "," + _ + P(So, _ / 2, So, Ro, ya, ya + Ia, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartPunchedTape": Bo = 18 * _ / 20, f += "<path d='" + (ka = "M0," + (Ro = 2 * _ / 20) + P(So = 5 * x / 20, Ro, So, Ro, Ia = 180, 0, !1).replace("M", "L") + P(.75 * x, Ro, So, Ro, Ia, 360, !1).replace("M", "L") + " L" + x + "," + Bo + P(.75 * x, Bo, So, Ro, 0, -Ia, !1).replace("M", "L") + P(So, Bo, So, Ro, 0, Ia, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartOnlineStorage": ya = 90; f += "<path d='" + (ka = "M" + (So = 1 * x / 6) + ",0 L" + x + ",0" + P(x, _ / 2, So, Ro = 3 * _ / 6, Pa = 270, 90, !1).replace("M", "L") + " L" + So + "," + _ + P(So, _ / 2, So, Ro, ya, 270, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartDisplay": f += "<path d='" + (ka = "M0," + (Ro = 3 * _ / 6) + " L" + (So = 1 * x / 6) + ",0 L" + (zo = 5 * x / 6) + ",0" + P(x, _ / 2, So, Ro, Pa = 270, Pa + (Ia = 180), !1).replace("M", "L") + " L" + So + "," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartDelay": ya = 90; f += "<path d='" + (ka = "M0,0 L" + (wa = x / 2) + ",0" + P(wa, ja = _ / 2, wa, ja, Pa = 270, Pa + (Ia = 180), !1).replace("M", "L") + " L0," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "flowChartMagneticTape": var wa = x / 2, Ia = 180, Pa = 270; ya = 90; Ga = (ja = _ / 2) + (zi = ja * Math.sin(Math.PI / 4)); var Ca = 180 * Math.atan(_ / x) / Math.PI; f += "<path d='" + (ka = "M" + wa + "," + _ + P(wa, ja, wa, ja, ya, Ia, !1).replace("M", "L") + P(wa, ja, wa, ja, Ia, Pa, !1).replace("M", "L") + P(wa, ja, wa, ja, Pa, 360, !1).replace("M", "L") + P(wa, ja, wa, ja, 0, Ca, !1).replace("M", "L") + " L" + x + "," + Ga + " L" + x + "," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "ellipse": case "flowChartConnector": case "flowChartSummingJunction": case "flowChartOr": if (f += "<ellipse cx='" + x / 2 + "' cy='" + _ / 2 + "' rx='" + x / 2 + "' ry='" + _ / 2 + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", "flowChartOr" == L) f += " <polyline points='" + x / 2 + " 0," + x / 2 + " " + _ + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", f += " <polyline points='0 " + _ / 2 + "," + x + " " + _ / 2 + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; else if ("flowChartSummingJunction" == L) { U = x / 2, H = _ / 2, wa = x / 2; var xa, _a, Da, Ga, ja = _ / 2, Sa = Math.PI / 4; f += " <polyline points='" + (xa = U - (i = wa * Math.cos(Sa))) + " " + (Da = H - (zi = ja * Math.sin(Sa))) + "," + (_a = U + i) + " " + (Ga = H + zi) + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", f += " <polyline points='" + _a + " " + Da + "," + xa + " " + Ga + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />" } break; case "roundRect": case "round1Rect": case "round2DiagRect": case "round2SameRect": case "snip1Rect": case "snip2DiagRect": case "snip2SameRect": case "flowChartAlternateProcess": case "flowChartPunchedCard": var za, Ta; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && Zo.constructor === Array) for (var Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Na = parseInt(tl.substr(4)) / 5e4) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 5e4) } else if (void 0 !== Zo && Zo.constructor !== Array) { var Ra = J(Zo, ["attrs", "fmla"]); Na = parseInt(Ra.substr(4)) / 5e4, Oa = 0 } var Ba = ""; switch (L) { case "roundRect": case "flowChartAlternateProcess": za = "round", Ta = "cornrAll", void 0 === Na && (Na = .33334), Oa = 0; break; case "round1Rect": za = "round", Ta = "cornr1", void 0 === Na && (Na = .33334), Oa = 0; break; case "round2DiagRect": za = "round", Ta = "diag", void 0 === Na && (Na = .33334), void 0 === Oa && (Oa = 0); break; case "round2SameRect": za = "round", Ta = "cornr2", void 0 === Na && (Na = .33334), void 0 === Oa && (Oa = 0); break; case "snip1Rect": case "flowChartPunchedCard": za = "snip", Ta = "cornr1", void 0 === Na && (Na = .33334), Oa = 0, "flowChartPunchedCard" == L && (Ba = "transform='translate(" + x + ",0) scale(-1,1)'"); break; case "snip2DiagRect": za = "snip", Ta = "diag", void 0 === Na && (Na = 0), void 0 === Oa && (Oa = .33334); break; case "snip2SameRect": za = "snip", Ta = "cornr2", void 0 === Na && (Na = .33334), void 0 === Oa && (Oa = 0) }f += "<path " + Ba + "  d='" + (Xd = function (a, r, t, e, s, i) { var o, l, n, d, h; "cornr1" == i ? (o = 0, l = 0, n = 0, d = t) : "cornr2" == i ? (o = t, l = e, n = e, d = t) : "cornrAll" == i ? (o = t, l = t, n = t, d = t) : "diag" == i && (o = t, l = e, n = t, d = e); "round" == s ? h = "M0," + (r / 2 + r / 2 * (1 - l)) + " Q0," + r + " " + l * (a / 2) + "," + r + " L" + (a / 2 + a / 2 * (1 - n)) + "," + r + " Q" + a + "," + r + " " + a + "," + (r / 2 + r / 2 * (1 - n)) + "L" + a + "," + r / 2 * d + " Q" + a + ",0 " + (a / 2 + a / 2 * (1 - d)) + ",0 L" + a / 2 * o + ",0 Q0,0 0," + r / 2 * o + " z" : "snip" == s && (h = "M0," + o * (r / 2) + " L0," + (r / 2 + r / 2 * (1 - l)) + "L" + l * (a / 2) + "," + r + " L" + (a / 2 + a / 2 * (1 - n)) + "," + r + "L" + a + "," + (r / 2 + r / 2 * (1 - n)) + " L" + a + "," + d * (r / 2) + "L" + (a / 2 + a / 2 * (1 - d)) + ",0 L" + a / 2 * o + ",0 z"); return h }(x, _, Na, Oa, za, Ta)) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "snipRoundRect": var Na = .33334, Oa = .33334; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Na = parseInt(tl.substr(4)) / 5e4) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 5e4) } f += "<path   d='" + (Xd = "M0," + _ + " L" + x + "," + _ + " L" + x + "," + _ / 2 * Oa + " L" + (x / 2 + x / 2 * (1 - Oa)) + ",0 L" + x / 2 * Na + ",0 Q0,0 0," + _ / 2 * Na + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bentConnector2": ka = ""; f += "<path d='" + (ka = m ? "M 0 " + x + " L " + _ + " " + x + " L " + _ + " 0" : "M " + x + " 0 L " + x + " " + _ + " L 0 " + _) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' fill='none' ", void 0 === O || "triangle" !== O.type && "arrow" !== O.type || (f += "marker-start='url(#markerTriangle_" + u + ")' "), void 0 === F || "triangle" !== F.type && "arrow" !== F.type || (f += "marker-end='url(#markerTriangle_" + u + ")' "), f += "/>"; break; case "rtTriangle": f += " <polygon points='0 0,0 " + _ + "," + x + " " + _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "triangle": case "flowChartExtract": case "flowChartMerge": var Fa = .5; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Fa = 96 * parseInt(Ua.substr(4)) / 9144e3); Ba = ""; "flowChartMerge" == L && (Ba = "transform='rotate(180 " + x / 2 + "," + _ / 2 + ")'"), f += " <polygon " + Ba + " points='" + x * Fa + " 0,0 " + _ + "," + x + " " + _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "diamond": case "flowChartDecision": case "flowChartSort": f += " <polygon points='" + x / 2 + " 0,0 " + _ / 2 + "," + x / 2 + " " + _ + "," + x + " " + _ / 2 + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />", "flowChartSort" == L && (f += " <polyline points='0 " + _ / 2 + "," + x + " " + _ / 2 + "' fill='none' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"); break; case "trapezoid": case "flowChartManualOperation": case "flowChartManualInput": var Ea = .2, qa = .7407; if (void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]))) Ea = .5 * (96 * parseInt(Ua.substr(4)) / 9144e3) / qa; var Ha = 0; Ba = ""; "flowChartManualOperation" == L && (Ba = "transform='rotate(180 " + x / 2 + "," + _ / 2 + ")'"), "flowChartManualInput" == L && (Ea = 0, Ha = _ / 5), f += " <polygon " + Ba + " points='" + x * Ea + " " + Ha + ",0 " + _ + "," + x + " " + _ + "," + (1 - Ea) * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "parallelogram": case "flowChartInputOutput": Ea = .25; if (qa = x > _ ? x / _ : _ / x, void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]))) Ea = parseInt(Ua.substr(4)) / 1e5 / qa; f += " <polygon points='" + Ea * x + " 0,0 " + _ + "," + (1 - Ea) * x + " " + _ + "," + x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "pentagon": f += " <polygon points='" + .5 * x + " 0,0 " + .375 * _ + "," + .15 * x + " " + _ + "," + .85 * x + " " + _ + "," + x + " " + .375 * _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "hexagon": case "flowChartPreparation": var Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Qa = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, Ya = 60 * Math.PI / 180; void 0 !== Ua && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); H = _ / 2, ja = _ / 2; Tr = Xa * x / (qe = Math.min(x, _)), zo = x - (So = qe * (Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa) / Va), f += "<path   d='" + (ka = "M0," + H + " L" + So + "," + (Ro = H - (Go = ja * (11085120 / 914400) / Va * Math.sin(Ya))) + " L" + zo + "," + Ro + " L" + x + "," + H + " L" + zo + "," + (Bo = H + Go) + " L" + So + "," + Bo + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "heptagon": f += " <polygon points='" + .5 * x + " 0," + x / 8 + " " + _ / 4 + ",0 " + 5 / 8 * _ + "," + x / 4 + " " + _ + "," + .75 * x + " " + _ + "," + x + " " + 5 / 8 * _ + "," + 7 / 8 * x + " " + _ / 4 + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "octagon": var Wa = .25; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = parseInt(Ua.substr(4)) / 1e5), f += " <polygon points='" + Wa * x + " 0,0 " + Wa * _ + ",0 " + (gr = 1 - Wa) * _ + "," + Wa * x + " " + _ + "," + gr * x + " " + _ + "," + x + " " + gr * _ + "," + x + " " + Wa * _ + "," + gr * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "decagon": f += " <polygon points='" + 3 / 8 * x + " 0," + x / 8 + " " + _ / 8 + ",0 " + _ / 2 + "," + x / 8 + " " + 7 / 8 * _ + "," + 3 / 8 * x + " " + _ + "," + 5 / 8 * x + " " + _ + "," + 7 / 8 * x + " " + 7 / 8 * _ + "," + x + " " + _ / 2 + "," + 7 / 8 * x + " " + _ / 8 + "," + 5 / 8 * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "dodecagon": f += " <polygon points='" + 3 / 8 * x + " 0," + x / 8 + " " + _ / 8 + ",0 " + 3 / 8 * _ + ",0 " + 5 / 8 * _ + "," + x / 8 + " " + 7 / 8 * _ + "," + 3 / 8 * x + " " + _ + "," + 5 / 8 * x + " " + _ + "," + 7 / 8 * x + " " + 7 / 8 * _ + "," + x + " " + 5 / 8 * _ + "," + x + " " + 3 / 8 * _ + "," + 7 / 8 * x + " " + _ / 8 + "," + 5 / 8 * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "star4": case "star5": case "star6": case "star7": case "star8": case "star10": case "star12": case "star16": case "star24": case "star32": Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]); var Za = L.substr(4); switch (Za) { case "4": Qa = 30; break; case "5": Qa = 40; break; case "6": Qa = 60; break; case "7": Qa = 70; break; case "8": Qa = 77; break; case "10": Qa = 86; break; case "12": case "16": case "24": case "32": Qa = 75 }void 0 !== Ua && (void 0 === ($a = J(Ua, ["attrs", "fmla"])) && ($a = Ua[0].attrs.fmla), void 0 !== $a && (Qa = 2 * parseInt($a.substr(4)) / 1e3)), f += " <polygon points='" + function (a, r) { for (var t = a, e = r, s = Math.max(t, 100), i = Math.PI / e, o = [], l = 0; l < 2 * e; l++) { var n = 1 & l ? t : 100; o.push(s + n * Math.sin(l * i)), o.push(s - n * Math.cos(l * i)) } return o }(Qa, Za) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "pie": case "pieWedge": case "arc": var Ja, $a, Ka, ar; "pie" == L ? (Wa = 0, gr = 270, Ja = _, ar = !0) : "pieWedge" == L ? (Wa = 180, gr = 270, Ja = 2 * _, ar = !0) : "arc" == L && (Wa = 270, gr = 0, Ja = _, ar = !1), void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"])) && (Ka = $a = J(Ua, ["attrs", "fmla"]), void 0 === $a && ($a = Ua[0].attrs.fmla, Ka = Ua[1].attrs.fmla), void 0 !== $a && (Wa = parseInt($a.substr(4)) / 6e4), void 0 !== Ka && (gr = parseInt(Ka.substr(4)) / 6e4)); var rr = function (a, r, t, e, s) { var i = parseInt(e), o = parseInt(t), l = parseInt(a) / 2, n = i - o; n < 0 && (n = 360 + n); n = Math.min(Math.max(n, 0), 360); var d, h, p = Math.cos(2 * Math.PI / (360 / n)), c = Math.sin(2 * Math.PI / (360 / n)); if (s) d = "M" + l + "," + l + " L" + l + ",0 A" + l + "," + l + " 0 " + (n <= 180 ? 0 : 1) + ",1 " + (l + c * l) + "," + (l - p * l) + " z", h = "rotate(" + (o - 270) + ", " + l + ", " + l + ")"; else { var f = l, u = r / 2; d = "M" + f + ",0 A" + u + "," + f + " 0 " + (n <= 180 ? 0 : 1) + ",1 " + (u + c * u) + "," + (f - p * f), h = "rotate(" + (o + 90) + ", " + l + ", " + l + ")" } return [d, h] }(Ja, x, Wa, gr, ar); f += "<path   d='" + rr[0] + "' transform='" + rr[1] + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "chord": Na = 45, Oa = 270; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Na = parseInt(tl.substr(4)) / 6e4) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 6e4) } f += "<path d='" + (Xd = P(oh = x / 2, ih = _ / 2, oh, ih, Na, Oa, !0)) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "frame": Wa = 12e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = 96 * parseInt(Ua.substr(4)) / 914400), dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa, f += "<path   d='" + (ka = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " zM" + (So = Math.min(x, _) * dl / Va) + "," + So + " L" + So + "," + (ut = _ - So) + " L" + (ft = x - So) + "," + ut + " L" + ft + "," + So + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "donut": Qa = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400), Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa, Pr = x / 2 - (Ir = Math.min(x, _) * Ao / Va), Cr = _ / 2 - Ir, f += "<path   d='" + (ka = "M0," + _ / 2 + P(x / 2, _ / 2, x / 2, _ / 2, 180, 270, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 270, 360, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 0, 90, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 90, 180, !1).replace("M", "L") + " zM" + Ir + "," + _ / 2 + P(x / 2, _ / 2, Pr, Cr, 180, 90, !1).replace("M", "L") + P(x / 2, _ / 2, Pr, Cr, 90, 0, !1).replace("M", "L") + P(x / 2, _ / 2, Pr, Cr, 0, -90, !1).replace("M", "L") + P(x / 2, _ / 2, Pr, Cr, 270, 180, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "noSmoking": var tr, er, sr, ir, or, lr, nr, dr, hr; Qa = 18e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400), Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa, Pr = x / 2 - (Ir = Math.min(x, _) * Ao / Va), Cr = _ / 2 - Ir, tr = Math.atan(_ / x), er = Cr * Math.cos(tr), sr = Pr * Math.sin(tr), ir = Pr * Cr / Math.sqrt(er * er + sr * sr), or = Ir / 2, Oi = 2 * (lr = Math.atan(or / ir)), nh = -Math.PI + Oi, Hi = (Ke = tr - lr) - Math.PI, nr = Cr * Math.cos(Ke), dr = Pr * Math.sin(Ke), So = x / 2 + (Uo = (hr = Pr * Cr / Math.sqrt(nr * nr + dr * dr)) * Math.cos(Ke)), Ro = _ / 2 + (Go = hr * Math.sin(Ke)), zo = x / 2 - Uo, Bo = _ / 2 - Go; var pr = 180 * Ke / Math.PI, cr = 180 * Hi / Math.PI, fr = 180 * nh / Math.PI; f += "<path   d='" + (ka = "M0," + _ / 2 + P(x / 2, _ / 2, x / 2, _ / 2, 180, 270, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 270, 360, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 0, 90, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 90, 180, !1).replace("M", "L") + " zM" + So + "," + Ro + P(x / 2, _ / 2, Pr, Cr, pr, pr + fr, !1).replace("M", "L") + " zM" + zo + "," + Bo + P(x / 2, _ / 2, Pr, Cr, cr, cr + fr, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "halfFrame": Na = 3.5, Oa = 3.5; var ur = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Na = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), Oa = 96 * parseInt(el.substr(4)) / 914400) } var Lr = ur * x / (bi = Math.min(x, _)), vr = ur * (ge = _ - (Hr = _ * (So = bi * (ql = Oa < 0 ? 0 : Oa > Lr ? Lr : Oa) / ur) / x)) / bi; f += "<path   d='" + (ka = "M0,0 L" + x + ",0 L" + (zo = x - (jo = (Ro = bi * (dl = Na < 0 ? 0 : Na > vr ? vr : Na) / ur) * x / _)) + "," + Ro + " L" + So + "," + Ro + " L" + So + "," + (Bo = _ - (Qo = So * _ / x)) + " L0," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "blockArc": Wa = 180; var mr, gr = 0, br = 24e5 / 914400; Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) / 6e4) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) / 6e4) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } Lo = br < 0 ? 0 : br > Xa ? Xa : br; var Mr, kr, yr, wr, Ir, Pr, Cr, xr, _r, Dr = (lh = Wa < 0 ? 0 : Wa > 360 ? 360 : Wa) + (nh = (mr = (Sd = gr < 0 ? 0 : gr > 360 ? 360 : gr) - lh) > 0 ? mr : mr + 360), Gr = Sd + (zd = -nh); yr = lh * Math.PI / 180, wr = Sd * Math.PI / 180, wa = x / 2, ja = _ / 2, U = x / 2, H = _ / 2, lh > 90 && lh < 270 ? (Mr = wa * Math.sin(Math.PI / 2 - yr), kr = ja * Math.cos(Math.PI / 2 - yr), So = U - (Uo = wa * Math.cos(Math.atan(kr / Mr))), Ro = H - (Go = ja * Math.sin(Math.atan(kr / Mr)))) : (Mr = wa * Math.sin(yr), kr = ja * Math.cos(yr), So = U + (Uo = wa * Math.cos(Math.atan(Mr / kr))), Ro = H + (Go = ja * Math.sin(Math.atan(Mr / kr)))), Pr = wa - (Ir = Math.min(x, _) * Lo / Va), Cr = ja - Ir, Dr <= 450 && Dr > 270 || Dr >= 630 && Dr < 720 ? (xr = Pr * Math.sin(wr), _r = Cr * Math.cos(wr), zo = U + (jo = Pr * Math.cos(Math.atan(xr / _r))), Bo = H + (Qo = Cr * Math.sin(Math.atan(xr / _r)))) : (xr = Pr * Math.sin(Math.PI / 2 - wr), _r = Cr * Math.cos(Math.PI / 2 - wr), zo = U - (jo = Pr * Math.cos(Math.atan(_r / xr))), Bo = H - (Qo = Cr * Math.sin(Math.atan(_r / xr)))), f += "<path   d='" + (ka = "M" + So + "," + Ro + P(wa, ja, wa, ja, lh, Dr, !1).replace("M", "L") + " L" + zo + "," + Bo + P(wa, ja, Pr, Cr, Sd, Gr, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bracePair": Qa = 799968 / 914400, Xa = 24e5 / 914400, Va = 48e5 / 914400; var jr = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); H = _ / 2; var Sr = 360; Ia = 180, ya = 90, Pa = 270; Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa, To = x - (zo = (bi = Math.min(x, _)) * Ao / Va), ft = x - (So = bi * Ao / jr), Bo = H - So, No = H + So, f += "<path   d='" + (ka = "M" + zo + "," + _ + P(zo, ut = _ - So, So, So, ya, Ia, !1).replace("M", "L") + " L" + So + "," + No + P(0, No, So, So, 0, -ya, !1).replace("M", "L") + P(0, Bo, So, So, ya, 0, !1).replace("M", "L") + " L" + So + "," + So + P(zo, So, So, So, Ia, Pa, !1).replace("M", "L") + " M" + To + ",0" + P(To, So, So, So, Pa, Sr, !1).replace("M", "L") + " L" + ft + "," + Bo + P(x, Bo, So, So, Ia, ya, !1).replace("M", "L") + P(x, No, So, So, Pa, Ia, !1).replace("M", "L") + " L" + ft + "," + ut + P(To, ut, So, So, 0, ya, !1).replace("M", "L")) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftBrace": Wa = 799968 / 914400, gr = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } H = _ / 2, Ia = 180, ya = 90, Pa = 270, vr = (kn = (Mn = Va - (ql = gr < 0 ? 0 : gr > Va ? Va : gr)) < ql ? Mn : ql) / 2 * _ / (bi = Math.min(x, _)); Bo = (No = _ * ql / Va) - (Ro = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / Va), ut = No + Ro, f += "<path   d='" + (ka = "M" + x + "," + _ + P(x, _ - Ro, x / 2, Ro, ya, Ia, !1).replace("M", "L") + " L" + x / 2 + "," + ut + P(0, ut, x / 2, Ro, 0, -ya, !1).replace("M", "L") + P(0, Bo, x / 2, Ro, ya, 0, !1).replace("M", "L") + " L" + x / 2 + "," + Ro + P(x, Ro, x / 2, Ro, Ia, Pa, !1).replace("M", "L")) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "rightBrace": Wa = 799968 / 914400, gr = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } H = _ / 2, Sr = 360, Ia = 180, ya = 90, Pa = 270, vr = (kn = (Mn = Va - (ql = gr < 0 ? 0 : gr > Va ? Va : gr)) < ql ? Mn : ql) / 2 * _ / (bi = Math.min(x, _)); Bo = (No = _ * ql / Va) - (Ro = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / Va), ut = _ - Ro, f += "<path   d='" + (ka = "M0,0" + P(0, Ro, x / 2, Ro, Pa, Sr, !1).replace("M", "L") + " L" + x / 2 + "," + Bo + P(x, Bo, x / 2, Ro, Ia, ya, !1).replace("M", "L") + P(x, No + Ro, x / 2, Ro, Pa, Ia, !1).replace("M", "L") + " L" + x / 2 + "," + ut + P(0, ut, x / 2, Ro, 0, ya, !1).replace("M", "L")) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bracketPair": Qa = 1600032 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); var zr = _; Ia = 180, ya = 90, Pa = 270; Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa, zo = (Ar = x) - (So = Math.min(x, _) * Ao / Va), Bo = zr - So, f += "<path   d='" + (ka = P(So, So, So, So, Pa, Ia, !1) + P(So, Bo, So, So, Ia, ya, !1).replace("M", "L") + P(zo, So, So, So, Pa, Pa + ya, !1) + P(zo, Bo, So, So, 0, ya, !1).replace("M", "L")) + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftBracket": Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Qa = 799968 / 914400, Va = 96e5 / 914400; var Tr = (Xa = 48e5 / 914400) * _ / Math.min(x, _); void 0 !== Ua && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); var Ar = x; zr = _, Ia = 180, ya = 90, Pa = 270; Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa, (Ro = Math.min(x, _) * Ao / Va) > x && (Ro = x), f += "<path   d='" + (ka = "M" + Ar + "," + zr + P(Ro, Bo = zr - Ro, Ro, Ro, ya, Ia, !1).replace("M", "L") + " L0," + Ro + P(Ro, Ro, Ro, Ro, Ia, Pa, !1).replace("M", "L") + " L" + Ar + ",0") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "rightBracket": Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"]), Qa = 799968 / 914400, Va = 96e5 / 914400, Tr = (Xa = 48e5 / 914400) * _ / Math.min(x, _); void 0 !== Ua && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); Sr = 360, Ia = 180, ya = 90, Pa = 270; Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa, f += "<path   d='" + (ka = "M0," + _ + P(No = x - (Ro = Math.min(x, _) * Ao / Va), Bo = _ - Ro, Ro, Ro, ya, 0, !1).replace("M", "L") + " L" + x + "," + _ / 2 + P(No, Ro, Ro, Ro, Sr, Pa, !1).replace("M", "L") + " L0,0") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "moon": Qa = .5; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) / 1e5); gr = (1 - Qa) * x; f += "<path   d='" + (ka = "M" + x + "," + _ + P(x, ja = _ / 2, x, ja, ya = 90, ya + (Ia = 180), !1).replace("M", "L") + P(x, ja, gr, ja, ya + Ia, ya, !1).replace("M", "L") + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "corner": Na = 48e5 / 914400, Oa = 48e5 / 914400, ur = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Na = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), Oa = 96 * parseInt(el.substr(4)) / 914400) } vr = ur * _ / (bi = Math.min(x, _)), Lr = ur * x / bi; f += "<path   d='" + (ka = "M0,0 L" + (So = bi * (ql = Oa < 0 ? 0 : Oa > Lr ? Lr : Oa) / ur) + ",0 L" + So + "," + (Ro = _ - (Go = bi * (dl = Na < 0 ? 0 : Na > vr ? vr : Na) / ur)) + " L" + x + "," + Ro + " L" + x + "," + _ + " L0," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "diagStripe": Na = 48e5 / 914400, ur = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Na = 96 * parseInt(Ua.substr(4)) / 914400), f += "<path   d='" + (ka = "M0," + (Bo = _ * (dl = Na < 0 ? 0 : Na > ur ? ur : Na) / ur) + " L" + (zo = x * dl / ur) + ",0 L" + x + ",0 L0," + _ + " z") + "'  fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "gear6": case "gear9": g = 0; var Rr = L.substr(4); f += "<path   d='" + (ka = I(x, _ / 3.5, parseInt(Rr))) + "' transform='rotate(20," + 3 / 7 * _ + "," + 3 / 7 * _ + ")' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bentConnector3": Fa = .5; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Fa = parseInt(Ua.substr(4)) / 1e5, f += m ? " <polyline points='" + x + " 0," + (1 - Fa) * x + " 0," + (1 - Fa) * x + " " + _ + ",0 " + _ + "' fill='transparent'' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' " : " <polyline points='0 0," + Fa * x + " 0," + Fa * x + " " + _ + "," + x + " " + _ + "' fill='transparent'' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' ", void 0 === O || "triangle" !== O.type && "arrow" !== O.type || (f += "marker-start='url(#markerTriangle_" + u + ")' "), void 0 === F || "triangle" !== F.type && "arrow" !== F.type || (f += "marker-end='url(#markerTriangle_" + u + ")' "), f += "/>"); break; case "plus": Wa = .25; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = parseInt(Ua.substr(4)) / 1e5), f += " <polygon points='" + Wa * x + " 0," + Wa * x + " " + Wa * _ + ",0 " + Wa * _ + ",0 " + (gr = 1 - Wa) * _ + "," + Wa * x + " " + gr * _ + "," + Wa * x + " " + _ + "," + gr * x + " " + _ + "," + gr * x + " " + gr * _ + "," + x + " " + gr * _ + "," + +x + " " + Wa * _ + "," + gr * x + " " + Wa * _ + "," + gr * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "teardrop": var Br, Nr, Or, Fr, Er = Wa = 96e5 / 914400, qr = 192e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = 96 * parseInt(Ua.substr(4)) / 914400), dl = Wa < 0 ? 0 : Wa > qr ? qr : Wa, Nr = (Br = Math.sqrt(2)) * (x / 2) * dl / Er, Or = (pl = Br * (_ / 2)) * dl / Er, Fr = 45 * Math.PI / 180, zo = (x / 2 + (So = x / 2 + (Uo = Nr * Math.cos(Fr)))) / 2, Bo = (_ / 2 + (Ro = _ / 2 - (Go = Or * Math.cos(Fr)))) / 2, f += "<path   d='" + (Xd = P(x / 2, _ / 2, x / 2, _ / 2, 180, 270, !1) + "Q " + zo + ",0 " + So + "," + Ro + "Q " + x + "," + Bo + " " + x + "," + _ / 2 + P(x / 2, _ / 2, x / 2, _ / 2, 0, 90, !1).replace("M", "L") + P(x / 2, _ / 2, x / 2, _ / 2, 90, 180, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "plaque": Wa = 1600032 / 914400, Er = 48e5 / 914400, qr = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = 96 * parseInt(Ua.substr(4)) / 914400), zo = x - (So = (dl = Wa < 0 ? 0 : Wa > Er ? Er : Wa) * Math.min(x, _) / qr), Bo = _ - So, f += "<path   d='" + (Xd = "M0," + So + P(0, 0, So, So, 90, 0, !1).replace("M", "L") + " L" + zo + ",0" + P(x, 0, So, So, 180, 90, !1).replace("M", "L") + " L" + x + "," + Bo + P(x, _, So, So, 270, 180, !1).replace("M", "L") + " L" + So + "," + _ + P(0, _, So, So, 0, -90, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "sun": Wa = 25e3 * (Oo = 96 / 914400), Xa = 12500 * Oo, Va = 46875 * Oo; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Wa = parseInt(Ua.substr(4)) * Oo); var Hr, Ur = 5e4 * Oo, Qr = 1e5 * Oo, Xr = Ur - (dl = Wa < Xa ? Xa : Wa > Va ? Va : Wa), Vr = (Hr = Xr * (30274 * Oo) / (32768 * Oo)) + Ur, Yr = (ge = Xr * (12540 * Oo) / (32768 * Oo)) + Ur, Wr = x * (18436 * Oo) / (21600 * Oo), Zr = _ * (3163 * Oo) / (21600 * Oo), Jr = x * (3163 * Oo) / (21600 * Oo), $r = _ * (18436 * Oo) / (21600 * Oo), Kr = x * (ye = Ur + (ke = Xr * (23170 * Oo) / (32768 * Oo))) / Qr, at = x * (we = Ur - ke) / Qr, rt = x * (Ge = Qr - (Ce = (Ie = 3 * (be = Ur - Hr) / 4) + 3662 * Oo)) / Qr, tt = x * (je = Qr - (xe = (Pe = 3 * (Me = Ur - ge) / 4) + 36620 * Oo)) / Qr, et = x * (Se = Qr - (_e = Pe + 12500 * Oo)) / Qr, st = _ * ye / Qr, it = _ * we / Qr, ot = _ * Ce / Qr, lt = _ * xe / Qr, nt = _ * _e / Qr, dt = _ * (De = Qr - Ie) / Qr, ht = _ * Ge / Qr, pt = _ * je / Qr, ct = _ * Se / Qr; f += "<path   d='" + (Xd = "M" + x + "," + _ / 2 + " L" + (Hs = x * De / Qr) + "," + ct + " L" + Hs + "," + nt + "z M" + Wr + "," + Zr + " L" + rt + "," + pt + " L" + (Qs = x * xe / Qr) + "," + ot + "z M" + x / 2 + ",0 L" + et + "," + (Lt = _ * Ie / Qr) + " L" + (Xs = x * _e / Qr) + "," + Lt + "z M" + Jr + "," + Zr + " L" + tt + "," + ot + " L" + (Us = x * Ce / Qr) + "," + pt + "z M0," + _ / 2 + " L" + (Ys = x * Ie / Qr) + "," + nt + " L" + Ys + "," + ct + "z M" + Jr + "," + $r + " L" + Us + "," + lt + " L" + tt + "," + ht + "z M" + x / 2 + "," + _ + " L" + Xs + "," + dt + " L" + et + "," + dt + "z M" + Wr + "," + $r + " L" + Qs + "," + ht + " L" + rt + "," + lt + " z M" + x * dl / Qr + "," + _ / 2 + P(x / 2, _ / 2, oh = x * Xr / Qr, ih = _ * Xr / Qr, 180, 540, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "heart": f += "<path   d='" + (Xd = "M" + x / 2 + "," + _ / 4 + "C" + (To = x / 2 + (jo = 10 * x / 48)) + "," + (Ro = -_ / 3) + " " + (ft = x / 2 + (Uo = 49 * x / 48)) + "," + _ / 4 + " " + x / 2 + "," + _ + "C" + (So = x / 2 - Uo) + "," + _ / 4 + " " + (zo = x / 2 - jo) + "," + Ro + " " + x / 2 + "," + _ / 4 + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "lightningBolt": var ft = 8757 * x / 21600, ut = (at = 13917 * x / 21600, 7437 * _ / 21600), Lt = 14277 * _ / 21600; f += "<path d='" + (Xd = "M" + (To = 8472 * x / 21600) + ",0 L" + (Kr = 12860 * x / 21600) + "," + (Bo = 6080 * _ / 21600) + " L" + (zo = 11050 * x / 21600) + "," + (No = 6797 * _ / 21600) + " L" + 16577 * x / 21600 + "," + (_i = 12007 * _ / 21600) + " L" + ($i = 14767 * x / 21600) + "," + (mo = 12877 * _ / 21600) + " L" + x + "," + _ + " L" + (Ki = 10012 * x / 21600) + "," + 14915 * _ / 21600 + " L" + (Ji = 12222 * x / 21600) + "," + (st = 13987 * _ / 21600) + " L" + (So = 5022 * x / 21600) + "," + (Di = 9705 * _ / 21600) + " L" + (Ys = 7602 * x / 21600) + "," + (it = 8382 * _ / 21600) + " L0," + (Ro = 3890 * _ / 21600) + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "cube": Qa = 25e3 * (Oo = 96 / 914400); void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) * Oo); Va = 1e5 * Oo; f += "<path d='" + (Xd = "M0," + (Ro = (qe = Math.min(x, _)) * (Ao = Qa < 0 ? 0 : Qa > Va ? Va : Qa) / Va) + " L" + Ro + ",0 L" + x + ",0 L" + x + "," + (ut = _ - Ro) + " L" + (ft = x - Ro) + "," + _ + " L0," + _ + " zM0," + Ro + " L" + ft + "," + Ro + " M" + ft + "," + Ro + " L" + x + ",0M" + ft + "," + Ro + " L" + ft + "," + _) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bevel": Qa = 12500 * (Oo = 96 / 914400); void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) * Oo); Xa = 5e4 * Oo, Va = 1e5 * Oo; f += "<path d='" + (Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = (qe = Math.min(x, _)) * (Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa) / Va) + "," + So + " L" + (zo = x - So) + "," + So + " L" + zo + "," + (Bo = _ - So) + " L" + So + "," + Bo + " z M0,0 L" + So + "," + So + " M0," + _ + " L" + So + "," + Bo + " M" + x + ",0 L" + zo + "," + So + " M" + x + "," + _ + " L" + zo + "," + Bo) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "foldedCorner": Qa = 16667 * (Oo = 96 / 914400); void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) * Oo); Xa = 5e4 * Oo, Va = 1e5 * Oo; f += "<path d='" + (Xd = "M" + (So = x - (Qo = (qe = Math.min(x, _)) * (Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa) / Va)) + "," + _ + " L" + (zo = So + (Go = Qo / 5)) + "," + (Ro = (Bo = _ - Qo) + Go) + " L" + x + "," + Bo + " L" + So + "," + _ + " L0," + _ + " L0,0 L" + x + ",0 L" + x + "," + Bo) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "cloud": case "cloudCallout": var vt, mt; So = 4693 * x / 43200, zo = 6928 * x / 43200, To = 16478 * x / 43200, ft = 28827 * x / 43200, Ki = 34129 * x / 43200, $i = 41798 * x / 43200, Ji = 38324 * x / 43200, Kr = 29078 * x / 43200, at = 22141 * x / 43200, Ys = 14e3 * x / 43200, 4127 * x / 43200, mt = 14370 * _ / 43200, Ro = 26177 * _ / 43200, Bo = 34899 * _ / 43200, No = 39090 * _ / 43200, ut = 34751 * _ / 43200, mo = 22954 * _ / 43200, Di = 15354 * _ / 43200, _i = 5426 * _ / 43200, st = 3952 * _ / 43200, it = 4720 * _ / 43200, Lt = 5192 * _ / 43200, 15789 * _ / 43200; var gt, bt, Mt, kt, yt, wt, It, Pt, Ct, xt, _t, Dt, Gt, jt, St, zt, Tt, At, Rt, Bt, Nt = 6753 * x / 43200, Ot = 9190 * _ / 43200, Ft = 5333 * x / 43200, Et = 7267 * _ / 43200, qt = 4365 * x / 43200, Ht = 5945 * _ / 43200, Ut = 4857 * x / 43200, Qt = 6595 * _ / 43200, Xt = 7273 * _ / 43200, Vt = 6775 * x / 43200, Yt = 9220 * _ / 43200, Wt = 5785 * x / 43200, Zt = 7867 * _ / 43200, Jt = 6752 * x / 43200, $t = 9215 * _ / 43200, Kt = 7720 * x / 43200, ae = 10543 * _ / 43200, re = 4360 * x / 43200, te = 5918 * _ / 43200, ee = 4345 * x / 43200, se = -11429249 / 6e4, ie = -8646143 / 6e4, oe = -8748475 / 6e4, le = -7859164 / 6e4, ne = -4722533 / 6e4, de = -46.26725, he = 37501 / 6e4, pe = 22.4516, ce = 3974558 / 6e4, fe = -16496525 / 6e4, ue = -246.8285; _t = (gt = P((vt = 3900 * x / 43200) - Nt * Math.cos(se * Math.PI / 180), mt - Ot * Math.sin(se * Math.PI / 180), Nt, Ot, se, se + 7426832 / 6e4, !1).replace("M", "L")).substr(gt.lastIndexOf("L") + 1).split(" "), Dt = (bt = P(He = parseInt(_t[0]) - Ft * Math.cos(ie * Math.PI / 180), Ue = parseInt(_t[1]) - Et * Math.sin(ie * Math.PI / 180), Ft, Et, ie, -54.15715, !1).replace("M", "L")).substr(bt.lastIndexOf("L") + 1).split(" "), Gt = (Mt = P(Q = parseInt(Dt[0]) - qt * Math.cos(oe * Math.PI / 180), Y = parseInt(Dt[1]) - Ht * Math.sin(oe * Math.PI / 180), qt, Ht, oe, oe + 5983381 / 6e4, !1).replace("M", "L")).substr(Mt.lastIndexOf("L") + 1).split(" "), jt = (kt = P(ba = parseInt(Gt[0]) - Ut * Math.cos(le * Math.PI / 180), X = parseInt(Gt[1]) - Qt * Math.sin(le * Math.PI / 180), Ut, Qt, le, le + 7034504 / 6e4, !1).replace("M", "L")).substr(kt.lastIndexOf("L") + 1).split(" "), St = (yt = P(V = parseInt(jt[0]) - Ft * Math.cos(ne * Math.PI / 180), Ma = parseInt(jt[1]) - Xt * Math.sin(ne * Math.PI / 180), Ft, Xt, ne, ne + 6541615 / 6e4, !1).replace("M", "L")).substr(yt.lastIndexOf("L") + 1).split(" "), zt = (wt = P(parseInt(St[0]) - Vt * Math.cos(de * Math.PI / 180), parseInt(St[1]) - Yt * Math.sin(de * Math.PI / 180), Vt, Yt, de, de + 130.269, !1).replace("M", "L")).substr(wt.lastIndexOf("L") + 1).split(" "), Tt = (It = P(parseInt(zt[0]) - Wt * Math.cos(he * Math.PI / 180), parseInt(zt[1]) - Zt * Math.sin(he * Math.PI / 180), Wt, Zt, he, 114.65835, !1).replace("M", "L")).substr(It.lastIndexOf("L") + 1).split(" "), At = (Pt = P(parseInt(Tt[0]) - Jt * Math.cos(pe * Math.PI / 180), parseInt(Tt[1]) - $t * Math.sin(pe * Math.PI / 180), Jt, $t, pe, 137.62415, !1).replace("M", "L")).substr(Pt.lastIndexOf("L") + 1).split(" "), Rt = (Ct = P(parseInt(At[0]) - Kt * Math.cos(ce * Math.PI / 180), parseInt(At[1]) - ae * Math.sin(ce * Math.PI / 180), Kt, ae, ce, ce + 4542661 / 6e4, !1).replace("M", "L")).substr(Ct.lastIndexOf("L") + 1).split(" "), Bt = (xt = P(parseInt(Rt[0]) - re * Math.cos(fe * Math.PI / 180), parseInt(Rt[1]) - te * Math.sin(fe * Math.PI / 180), re, te, fe, fe + 8804134 / 6e4, !1).replace("M", "L")).substr(xt.lastIndexOf("L") + 1).split(" "); var Le = "M" + vt + "," + mt + gt + bt + Mt + kt + yt + wt + It + Pt + Ct + xt + P(parseInt(Bt[0]) - ee * Math.cos(ue * Math.PI / 180), parseInt(Bt[1]) - Ht * Math.sin(ue * Math.PI / 180), ee, Ht, ue, -94.30965, !1).replace("M", "L") + " z"; if ("cloudCallout" == L) { Wa = -20833 * (Oo = 96 / 914400), gr = 62500 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) } Va = 1e5 * Oo; var ve, me, ge, be, Me, ke, ye, we, Ie, Pe, Ce, xe, _e, De, Ge, je, Se, ze, Te, Ae, Re, Be, Ne, Oe, Fe, Ee, qe = Math.min(x, _); ts = (wa = x / 2) + (as = x * Wa / Va), es = (ja = _ / 2) + (rs = _ * gr / Va), ve = ja * Math.cos(Math.atan(rs / as)), me = wa * Math.sin(Math.atan(rs / as)), ge = wa * Math.cos(Math.atan(me / ve)), Vr = ja * Math.sin(Math.atan(me / ve)), Wa >= 0 ? (Yr = wa + ge, be = ja + Vr) : (Yr = wa - ge, be = ja - Vr), Me = Yr - ts, ke = be - es, je = (De = (xe = (Pe = (Ie = (ye = Math.sqrt(Me * Me + ke * ke)) - (we = 6600 * qe / 21600)) / 3) + (Ce = 1800 * qe / 21600)) * ke / ye) + es, Be = (Re = (Te = (Se = 4800 * qe / 21600) + (ze = 2 * Pe)) * ke / ye) + es, Fe = (Ge = (_e = xe * Me / ye) + ts) + (Ne = 1200 * qe / 21600), Ee = (Ae = Te * Me / ye) + ts + Ce, Le += Xd = P(ts + (Oe = 600 * qe / 21600) - Oe, es, Oe, Oe, 0, 360, !1) + " z M" + Fe + "," + je + P(Fe - Ne, je, Ne, Ne, 0, 360, !1).replace("M", "L") + " z M" + Ee + "," + Be + P(Ee - Ce, Be, Ce, Ce, 0, 360, !1).replace("M", "L") + " z" } f += "<path d='" + Le + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "smileyFace": Qa = 4653 * (Oo = 96 / 914400); void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) * Oo); Xa = 5e4 * Oo, Va = 1e5 * Oo, jr = 4653 * Oo, qe = Math.min(x, _); wa = x / 2, ja = _ / 2, So = 4969 * x / 21699, To = 13135 * x / 21600, ft = 16640 * x / 21600, Ro = 7570 * _ / 21600, Bo = (No = 16515 * _ / 21600) - (Qo = _ * (Ao = Qa < -jr ? -jr : Qa > jr ? jr : Qa) / Va), mo = (ut = No + Qo) + (Xo = _ * Ao / Xa), ih = 1125 * _ / 21600; var He = (zo = 6215 * x / 21600) - (oh = 1125 * x / 21600) * Math.cos(Math.PI), Ue = Ro - ih * Math.sin(Math.PI); Q = To - oh * Math.cos(Math.PI); f += "<path d='" + (Xd = P(He, Ue, oh, ih, 180, 540, !1) + P(Q, Ue, oh, ih, 180, 540, !1) + " M" + So + "," + Bo + " Q" + wa + "," + mo + " " + ft + "," + Bo + " Q" + wa + "," + mo + " " + So + "," + Bo + " M0," + ja + P(wa, ja, wa, ja, 180, 540, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "verticalScroll": case "horizontalScroll": Qa = 12500 * (Oo = 96 / 914400); void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = parseInt(Ua.substr(4)) * Oo); Xa = 25e3 * Oo, Va = 1e5 * Oo; var Qe, Xe, Ve, Ye = 0, We = 0; zr = _, Ar = x; if (Xe = (Qe = (qe = Math.min(x, _)) * (Ao = Qa < 0 ? 0 : Qa > Xa ? Xa : Qa) / Va) / 2, Ve = Qe / 4, "verticalScroll" == L) ft = Qe + Qe, Ji = Ar - Xe, Ki = ($i = Ar - Qe) - Xe, ut = zr - Xe, Xd = "M" + Qe + "," + (No = zr - Qe) + " L" + Qe + "," + Xe + P(To = Qe + Xe, Xe, Xe, Xe, 180, 270, !1).replace("M", "L") + " L" + Ji + "," + Ye + P(Ji, Xe, Xe, Xe, 270, 450, !1).replace("M", "L") + " L" + $i + "," + Qe + " L" + $i + "," + ut + P(Ki, ut, Xe, Xe, 0, 90, !1).replace("M", "L") + " L" + Xe + "," + zr + P(Xe, ut, Xe, Xe, 90, 270, !1).replace("M", "L") + " z M" + To + "," + Ye + P(To, Xe, Xe, Xe, 270, 450, !1).replace("M", "L") + P(To, To / 2, Ve, Ve, 90, 270, !1).replace("M", "L") + " L" + ft + "," + Xe + " M" + $i + "," + Qe + " L" + To + "," + Qe + " M" + Qe + "," + ut + P(Xe, ut, Xe, Xe, 0, 270, !1).replace("M", "L") + P(Xe, (ut + No) / 2, Ve, Ve, 270, 450, !1).replace("M", "L") + " z M" + Qe + "," + ut + " L" + Qe + "," + No; else if ("horizontalScroll" == L) { ut = Qe + Qe, _i = zr - Xe, mo = (Di = zr - Qe) - Xe, To = Ar - Qe, ft = Ar - Xe, Xd = "M" + We + "," + (No = Qe + Xe) + P(Xe, No, Xe, Xe, 180, 270, !1).replace("M", "L") + " L" + To + "," + Qe + " L" + To + "," + Xe + P(ft, Xe, Xe, Xe, 180, 360, !1).replace("M", "L") + " L" + Ar + "," + mo + P(ft, mo, Xe, Xe, 0, 90, !1).replace("M", "L") + " L" + Qe + "," + Di + " L" + Qe + "," + _i + P(Xe, _i, Xe, Xe, 0, 180, !1).replace("M", "L") + " zM" + ft + "," + Qe + P(ft, Xe, Xe, Xe, 90, -180, !1).replace("M", "L") + P((To + ft) / 2, Xe, Ve, Ve, 180, 0, !1).replace("M", "L") + " z M" + ft + "," + Qe + " L" + To + "," + Qe + " M" + Xe + "," + ut + " L" + Xe + "," + No + P(No / 2, No, Ve, Ve, 180, 360, !1).replace("M", "L") + P(Xe, No, Xe, Xe, 0, 180, !1).replace("M", "L") + " M" + Qe + "," + No + " L" + Qe + "," + Di } f += "<path d='" + Xd + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "wedgeEllipseCallout": Wa = -20833 * (Oo = 96 / 914400), gr = 62500 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) } var Ze, Je, $e, Ke; Xa = 96e5 / 914400, Ya = 11 * Math.PI / 180, qe = Math.min(x, _); ts = (U = x / 2) + (as = x * Wa / Xa), es = (H = _ / 2) + (rs = _ * gr / Xa), Ze = as * _, Je = rs * x, lh = ($e = Math.atan(Je / Ze)) + Ya, hl = $e - Ya, console.log("dxPos: ", as, "dyPos: ", rs), Uo = U * Math.cos(lh), Go = H * Math.sin(lh), jo = U * Math.cos(hl), Qo = H * Math.sin(hl), as >= 0 ? (So = U + Uo, Ro = H + Go, zo = U + jo, Bo = H + Qo) : (So = U - Uo, Ro = H - Go, zo = U - jo, Bo = H - Qo), f += "<path d='" + (Xd = "M" + So + "," + Ro + " L" + ts + "," + es + " L" + zo + "," + Bo + P(U, H, U, H, 0, 360, !0)) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "wedgeRectCallout": Wa = -20833 * (Oo = 96 / 914400), gr = 62500 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) } Ai = (ts = (U = x / 2) + (as = x * Wa / (Xa = 96e5 / 914400))) - U, xi = (es = (H = _ / 2) + (rs = _ * gr / Xa)) - H, ss = as * _ / x, So = x * (as > 0 ? 7 : 2) / 12, Ro = _ * (rs > 0 ? 7 : 2) / 12, os = as > 0 ? 0 : ts, ns = rs > 0 ? So : ts, hs = as > 0 ? ts : x, cs = rs > 0 ? ts : So, us = as > 0 ? Ro : es, vs = rs > 0 ? 0 : es, gs = as > 0 ? es : Ro, Ms = rs > 0 ? es : _, f += "<path d='" + (Xd = "M0,0 L" + So + ",0 L" + (ds = (is = Math.abs(rs) - Math.abs(ss)) > 0 ? ns : So) + "," + (ms = is > 0 ? vs : 0) + " L" + (zo = x * (as > 0 ? 10 : 5) / 12) + ",0 L" + x + ",0 L" + x + "," + Ro + " L" + (ps = is > 0 ? x : hs) + "," + (bs = is > 0 ? Ro : gs) + " L" + x + "," + (Bo = _ * (rs > 0 ? 10 : 5) / 12) + " L" + x + "," + _ + " L" + zo + "," + _ + " L" + (fs = is > 0 ? cs : So) + "," + (ks = is > 0 ? Ms : _) + " L" + So + "," + _ + " L0," + _ + " L0," + Bo + " L" + (ls = is > 0 ? 0 : os) + "," + (Ls = is > 0 ? Ro : us) + " L0," + Ro + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "wedgeRoundRectCallout": Wa = -20833 * (Oo = 96 / 914400), gr = 62500 * Oo, br = 16667 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4)) * Oo) } var as, rs, ts, es, ss, is, os, ls, ns, ds, hs, ps, cs, fs, us, Ls, vs, ms, gs, bs, Ms, ks; Xa = 96e5 / 914400, qe = Math.min(x, _); ts = (U = x / 2) + (as = x * Wa / Xa), es = (H = _ / 2) + (rs = _ * gr / Xa), ss = as * _ / x, So = x * (as > 0 ? 7 : 2) / 12, zo = x * (as > 0 ? 10 : 5) / 12, Ro = _ * (rs > 0 ? 7 : 2) / 12, Bo = _ * (rs > 0 ? 10 : 5) / 12, os = as > 0 ? 0 : ts, ls = (is = Math.abs(rs) - Math.abs(ss)) > 0 ? 0 : os, ns = rs > 0 ? So : ts, ds = is > 0 ? ns : So, hs = as > 0 ? ts : x, ps = is > 0 ? x : hs, cs = rs > 0 ? ts : So, fs = is > 0 ? cs : So, us = as > 0 ? Ro : es, Ls = is > 0 ? Ro : us, vs = rs > 0 ? 0 : es, ms = is > 0 ? vs : 0, gs = as > 0 ? es : Ro, bs = is > 0 ? Ro : gs, Ms = rs > 0 ? es : _, ks = is > 0 ? Ms : _, _l = x - (xl = qe * br / Xa), td = _ - xl, f += "<path d='" + (Xd = "M0," + xl + P(xl, xl, xl, xl, 180, 270, !1).replace("M", "L") + " L" + So + ",0 L" + ds + "," + ms + " L" + zo + ",0 L" + _l + ",0" + P(_l, xl, xl, xl, 270, 360, !1).replace("M", "L") + " L" + x + "," + Ro + " L" + ps + "," + bs + " L" + x + "," + Bo + " L" + x + "," + td + P(_l, td, xl, xl, 0, 90, !1).replace("M", "L") + " L" + zo + "," + _ + " L" + fs + "," + ks + " L" + So + "," + _ + " L" + xl + "," + _ + P(xl, td, xl, xl, 90, 180, !1).replace("M", "L") + " L0," + Bo + " L" + ls + "," + Ls + " L0," + Ro + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "accentBorderCallout1": case "accentBorderCallout2": case "accentBorderCallout3": case "borderCallout1": case "borderCallout2": case "borderCallout3": case "accentCallout1": case "accentCallout2": case "accentCallout3": case "callout1": case "callout2": case "callout3": Wa = 18750 * (Oo = 96 / 914400), gr = -8333 * Oo, br = 18750 * Oo; var ys, ws, Is, Ps = -16667 * Oo, Cs = 1e5 * Oo, xs = -16667 * Oo, _s = 112963 * Oo, Ds = -8333 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4)) * Oo) : "adj4" == ll ? (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = parseInt(il.substr(4)) * Oo) : "adj5" == ll ? (ol = J(Zo[Aa], ["attrs", "fmla"]), Cs = parseInt(ol.substr(4)) * Oo) : "adj6" == ll ? (ys = J(Zo[Aa], ["attrs", "fmla"]), xs = parseInt(ys.substr(4)) * Oo) : "adj7" == ll ? (ws = J(Zo[Aa], ["attrs", "fmla"]), _s = parseInt(ws.substr(4)) * Oo) : "adj8" == ll && (Is = J(Zo[Aa], ["attrs", "fmla"]), Ds = parseInt(Is.substr(4)) * Oo) } Xa = 1e5 * Oo; var Gs = !0; switch (L) { case "borderCallout1": case "callout1": Gs = "borderCallout1" == L, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 112500 * Oo, Ps = -38333 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa); break; case "borderCallout2": case "callout2": Gs = "borderCallout2" == L, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 18750 * Oo, Ps = -16667 * Oo, Cs = 112500 * Oo, xs = -46667 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa) + " L" + (To = x * xs / Xa) + "," + (No = _ * Cs / Xa) + " L" + zo + "," + Bo; break; case "borderCallout3": case "callout3": Gs = "borderCallout3" == L, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 18750 * Oo, Ps = -16667 * Oo, Cs = 1e5 * Oo, xs = -16667 * Oo, _s = 112963 * Oo, Ds = -8333 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa) + " L" + (To = x * xs / Xa) + "," + (No = _ * Cs / Xa) + " L" + (ft = x * Ds / Xa) + "," + (ut = _ * _s / Xa) + " L" + To + "," + No + " L" + zo + "," + Bo; break; case "accentBorderCallout1": case "accentCallout1": Gs = "accentBorderCallout1" == L, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 112500 * Oo, Ps = -38333 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa) + " M" + So + ",0 L" + So + "," + _; break; case "accentBorderCallout2": case "accentCallout2": Gs = "accentBorderCallout2" == L, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 18750 * Oo, Ps = -16667 * Oo, Cs = 112500 * Oo, xs = -46667 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa) + " L" + (To = x * xs / Xa) + "," + (No = _ * Cs / Xa) + " L" + zo + "," + Bo + " M" + So + ",0 L" + So + "," + _; break; case "accentBorderCallout3": case "accentCallout3": Gs = "accentBorderCallout3" == L, Gs = !0, void 0 === Zo && (Wa = 18750 * Oo, gr = -8333 * Oo, br = 18750 * Oo, Ps = -16667 * Oo, Cs = 1e5 * Oo, xs = -16667 * Oo, _s = 112963 * Oo, Ds = -8333 * Oo), Xd = "M0,0 L" + x + ",0 L" + x + "," + _ + " L0," + _ + " z M" + (So = x * gr / Xa) + "," + (Ro = _ * Wa / Xa) + " L" + (zo = x * Ps / Xa) + "," + (Bo = _ * br / Xa) + " L" + (To = x * xs / Xa) + "," + (No = _ * Cs / Xa) + " L" + (ft = x * Ds / Xa) + "," + (ut = _ * _s / Xa) + " L" + To + "," + No + " L" + zo + "," + Bo + " M" + So + ",0 L" + So + "," + _ }console.log("shapType: ", L, ",isBorder:", Gs), f += "<path d='" + Xd + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftRightRibbon": Wa = 5e4 * (Oo = 96 / 914400), gr = 5e4 * Oo, br = 16667 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4)) * Oo) } Xa = 33333 * Oo, jr = 2e5 * Oo; var js, Ss, zs, Ts, As, Rs, Bs, Ns, Os = 4e5 * Oo; vr = (Va = 1e5 * Oo) - (Lo = br < 0 ? 0 : br > Xa ? Xa : br), Lr = Va * ((U = x / 2) - (Es = x / 32)) / (qe = Math.min(x, _)), ft = x - (So = qe * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Va), Ss = (H = _ / 2) + (Go = _ * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr) - (Qo = _ * Lo / -jr), Ts = _ - (zs = (js = H + Qo - Go) + Go), Rs = _ - (As = 2 * zs), zo = U - Es, To = U + Es, Bo = (Ns = _ - (Bs = As - js)) - (ih = Lo * qe / Os), f += "<path d='" + (Xd = "M0," + zs + "L" + So + ",0L" + So + "," + js + "L" + U + "," + js + P(U, Ro = js + ih, Es, ih, 270, 450, !1).replace("M", "L") + P(U, Bo, Es, ih, 270, 90, !1).replace("M", "L") + "L" + ft + "," + Ns + "L" + ft + "," + Rs + "L" + x + "," + Ts + "L" + ft + "," + _ + "L" + ft + "," + Ss + "L" + U + "," + Ss + P(U, Ss - ih, Es, ih, 90, 180, !1).replace("M", "L") + "L" + zo + "," + Bs + "L" + So + "," + Bs + "L" + So + "," + As + " zM" + To + "," + Ro + "L" + To + "," + Ns + "M" + zo + "," + Bo + "L" + zo + "," + Bs) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "ribbon": case "ribbon2": Wa = 1600032 / 914400, gr = 48e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } Va = 3199968 / 914400, jr = 72e5 / 914400, Os = 96e5 / 914400, Ye = 0, We = 0, zr = _; if (Ys = (Ar = x) - (Fs = x / 8), To = (zo = (U = x / 2) - (jo = x * (ql = gr < (Xa = 24e5 / 914400) ? Xa : gr > jr ? jr : gr) / (Ws = 192e5 / 914400))) + (Es = x / 32), Kr = (at = U + jo) - Es, ft = (Ki = zo + Fs) - Es, Ji = ($i = at - Fs) + Es, ih = _ * (dl = Wa < 0 ? 0 : Wa > Va ? Va : Wa) / (Do = 384e5 / 914400), "ribbon2" == L) Bo = zr - (Qo = _ * dl / Os), Di = zr - ih, _i = (Ro = zr - (Go = _ * dl / Ws)) - ih, Xd = "M" + We + "," + zr + " L" + Fs + "," + (No = ((ut = Ye + Qo) + zr) / 2) + " L" + We + "," + ut + " L" + zo + "," + ut + " L" + zo + "," + ih + P(To, ih, Es, ih, 180, 270, !1).replace("M", "L") + " L" + Kr + "," + Ye + P(Kr, ih, Es, ih, 270, 360, !1).replace("M", "L") + " L" + at + "," + ut + " L" + at + "," + ut + " L" + Ar + "," + ut + " L" + Ys + "," + No + " L" + Ar + "," + zr + " L" + Ji + "," + zr + P(Ji, Di, Es, ih, 90, 270, !1).replace("M", "L") + " L" + Kr + "," + Ro + P(Kr, _i, Es, ih, 90, -90, !1).replace("M", "L") + " L" + To + "," + Bo + P(To, _i, Es, ih, 270, 90, !1).replace("M", "L") + " L" + ft + "," + Ro + P(ft, Di, Es, ih, 270, 450, !1).replace("M", "L") + " z M" + Ki + "," + Bo + " L" + Ki + "," + Di + "M" + $i + "," + Di + " L" + $i + "," + Bo + "M" + zo + "," + _i + " L" + zo + "," + ut + "M" + at + "," + ut + " L" + at + "," + _i; else if ("ribbon" == L) { Ro = _ * dl / Ws, No = (ut = zr - (Bo = _ * dl / Os)) / 2, mo = zr - ih, Di = Bo - ih, Xd = "M" + We + "," + Ye + " L" + ft + "," + Ye + P(ft, ih, Es, ih, 270, 450, !1).replace("M", "L") + " L" + To + "," + Ro + P(To, Di, Es, ih, 270, 90, !1).replace("M", "L") + " L" + Kr + "," + Bo + P(Kr, Di, Es, ih, 90, -90, !1).replace("M", "L") + " L" + Ji + "," + Ro + P(Ji, ih, Es, ih, 90, 270, !1).replace("M", "L") + " L" + Ar + "," + Ye + " L" + Ys + "," + No + " L" + Ar + "," + ut + " L" + at + "," + ut + " L" + at + "," + mo + P(Kr, mo, Es, ih, 0, 90, !1).replace("M", "L") + " L" + To + "," + zr + P(To, mo, Es, ih, 90, 180, !1).replace("M", "L") + " L" + zo + "," + ut + " L" + We + "," + ut + " L" + Fs + "," + No + " z M" + Ki + "," + ih + " L" + Ki + "," + Bo + "M" + $i + "," + Bo + " L" + $i + "," + ih + "M" + zo + "," + ut + " L" + zo + "," + Di + "M" + at + "," + Di + " L" + at + "," + ut } f += "<path d='" + Xd + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "doubleWave": case "wave": Wa = "doubleWave" == L ? 6e5 / 914400 : 12e5 / 914400, gr = 0; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } Va = -96e4 / 914400, jr = 48e5 / 914400, Os = 96e5 / 914400, U = x / 2, Ye = 0, We = 0, zr = _, Ar = x; var Fs = x / 8, Es = x / 32; if ("doubleWave" == L) { var qs, Hs, Us, Qs, Xs; Xa = 12e5 / 914400; Xd = "M" + (zo = We - (jo = (Vs = x * (ql = gr < Va ? Va : gr > Os ? Os : gr) / jr) > 0 ? 0 : Vs)) + "," + (Ro = _ * (dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa) / Os) + " C" + (To = zo + (yi = (jo + (Kr = Ar - (qs = Vs > 0 ? Vs : 0))) / 6)) + "," + (Bo = Ro - (Qo = 10 * Ro / 3)) + " " + (ft = zo + (ni = (jo + Kr) / 3)) + "," + (No = Ro + Qo) + " " + (Ki = (zo + Kr) / 2) + "," + Ro + " C" + ($i = Ki + yi) + "," + Bo + " " + (Ji = ($i + Kr) / 2) + "," + No + " " + Kr + "," + Ro + " L" + (Hs = Ar + jo) + "," + (ut = zr - Ro) + " C" + (Xs = ((Qs = (Us = ((at = We + qs) + Hs) / 2) + yi) + Hs) / 2) + "," + (Di = ut + Qo) + " " + Qs + "," + (mo = ut - Qo) + " " + Us + "," + ut + " C" + (at + ni) + "," + Di + " " + (Ys = at + yi) + "," + mo + " " + at + "," + ut + " z" } else if ("wave" == L) { var Vs, Ys, Ws = 192e4 / 914400; Xd = "M" + (zo = We - (jo = (Vs = x * (ql = gr < Va ? Va : gr > Os ? Os : gr) / jr) > 0 ? 0 : Vs)) + "," + (Ro = _ * (dl = Wa < 0 ? 0 : Wa > Ws ? Ws : Wa) / Os) + " C" + (To = zo + (yi = (jo + (Ki = Ar - (vi = Vs > 0 ? Vs : 0))) / 3)) + "," + (Bo = Ro - (Qo = 10 * Ro / 3)) + " " + (ft = (To + Ki) / 2) + "," + (No = Ro + Qo) + " " + Ki + "," + Ro + " L" + (Ys = Ar + jo) + "," + (ut = zr - Ro) + " C" + (Kr = ((Ji = ($i = We + vi) + yi) + Ys) / 2) + "," + (Di = ut + Qo) + " " + Ji + "," + (mo = ut - Qo) + " " + $i + "," + ut + " z" } f += "<path d='" + Xd + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "ellipseRibbon": case "ellipseRibbon2": Wa = 24e5 / 914400, gr = 48e5 / 914400, br = 12e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } var Zs, Js, $s, Ks, ai, ri; jr = 72e5 / 914400, Os = 96e5 / 914400, Ye = 0, We = 0; if (ft = (Ar = x) - (To = (zo = (U = x / 2) - (jo = x * (ql = gr < (Xa = 24e5 / 914400) ? Xa : gr > jr ? jr : gr) / (Ws = 192e5 / 914400))) + (Fs = x / 8)), Ki = Ar - zo, $i = Ar - Fs, kn = To - (Mn = To * To / x), Ks = Ar - ($s = To / 2), Xo = (Mn = _ * (dl = Wa < 0 ? 0 : Wa > Os ? Os : Wa) / Os) - (Go = _ * (Lo = br < (Zs = 0 > (zn = dl - (jn = (Gn = Os - dl) / 2)) ? 0 : zn) ? Zs : br > dl ? dl : br) / Os), In = (Js = 4 * Go / x) * (wn = zo - zo * zo / x), ii = (zr = _) - Mn, Cn = 14 * Go / 16, Dn = Js * (ai = zo / 2), ri = Ar - ai, "ellipseRibbon" == L) Mn + Mn - (_i = (Ro = Js * kn) + Xo), st = zr - Go, Xd = "M" + We + "," + Ye + " Q" + $s + "," + (ti = Js * $s) + " " + To + "," + Ro + " L" + zo + "," + (No = In + Xo) + " Q" + U + "," + (si = (Pn = Go + Xo - No + Go) + Xo) + " " + Ki + "," + No + " L" + ft + "," + Ro + " Q" + Ks + "," + ti + " " + Ar + "," + Ye + " L" + $i + "," + (Bo = (Cn + ii) / 2) + " L" + Ar + "," + ii + " Q" + ri + "," + (oi = Dn + ii) + " " + Ki + "," + (mo = In + ii) + " L" + Ki + "," + (Di = No + ii) + " Q" + U + "," + (si + ii) + " " + zo + "," + Di + " L" + zo + "," + mo + " Q" + ai + "," + oi + " " + We + "," + ii + " L" + Fs + "," + Bo + " zM" + zo + "," + mo + " L" + zo + "," + No + "M" + Ki + "," + No + " L" + Ki + "," + mo + "M" + To + "," + Ro + " L" + To + "," + _i + "M" + ft + "," + _i + " L" + ft + "," + Ro; else if ("ellipseRibbon2" == L) { var ti, ei, si, ii, oi; zr - (Mn + Mn - (jl = (xl = Js * kn) + Xo)), Xd = "M" + We + "," + zr + " L" + Fs + "," + (Bo = zr - (_l = (Cn + ii) / 2)) + " L" + We + "," + Mn + " Q" + ai + "," + (oi = zr - (Dn + ii)) + " " + zo + "," + (mo = zr - (In + ii)) + " L" + zo + "," + (Di = zr - ((Dl = In + Xo) + ii)) + " Q" + U + "," + (zr - ((ei = (Pn = Go + Xo - Dl + Go) + Xo) + ii)) + " " + Ki + "," + Di + " L" + Ki + "," + mo + " Q" + ri + "," + oi + " " + Ar + "," + Mn + " L" + $i + "," + Bo + " L" + Ar + "," + zr + " Q" + Ks + "," + (ti = zr - Js * $s) + " " + ft + "," + (Ro = zr - xl) + " L" + Ki + "," + (No = zr - Dl) + " Q" + U + "," + (si = zr - ei) + " " + zo + "," + No + " L" + To + "," + Ro + " Q" + $s + "," + ti + " " + We + "," + zr + " zM" + zo + "," + No + " L" + zo + "," + mo + "M" + Ki + "," + mo + " L" + Ki + "," + No + "M" + To + "," + (_i = zr - jl) + " L" + To + "," + Ro + "M" + ft + "," + Ro + " L" + ft + "," + _i } f += "<path d='" + Xd + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "line": case "straightConnector1": case "bentConnector4": case "bentConnector5": case "curvedConnector2": case "curvedConnector3": case "curvedConnector4": case "curvedConnector5": f += m ? "<line x1='" + x + "' y1='0' x2='0' y2='" + _ + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' " : "<line x1='0' y1='0' x2='" + x + "' y2='" + _ + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' ", void 0 === O || "triangle" !== O.type && "arrow" !== O.type || (f += "marker-start='url(#markerTriangle_" + u + ")' "), void 0 === F || "triangle" !== F.type && "arrow" !== F.type || (f += "marker-end='url(#markerTriangle_" + u + ")' "), f += "/>"; break; case "rightArrow": Na = .25, Oa = .5; var li = x / _; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = .5 - parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = 1 - parseInt(el.substr(4)) / 1e5 / li } } f += " <polygon points='" + x + " " + _ / 2 + "," + Oa * x + " 0," + Oa * x + " " + Na * _ + ",0 " + Na * _ + ",0 " + (1 - Na) * _ + "," + Oa * x + " " + (1 - Na) * _ + ", " + Oa * x + " " + _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftArrow": Na = .25, Oa = .5, li = x / _; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = .5 - parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 1e5 / li } } f += " <polygon points='0 " + _ / 2 + "," + Oa * x + " " + _ + "," + Oa * x + " " + (1 - Na) * _ + "," + x + " " + (1 - Na) * _ + "," + x + " " + Na * _ + "," + Oa * x + " " + Na * _ + ", " + Oa * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "downArrow": case "flowChartOffpageConnector": Na = .25, Oa = .5, li = _ / x; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 1e5 / li } } "flowChartOffpageConnector" == L && (Na = .5, Oa = .212), f += " <polygon points='" + (.5 - Na) * x + " 0," + (.5 - Na) * x + " " + (1 - Oa) * _ + ",0 " + (1 - Oa) * _ + "," + x / 2 + " " + _ + "," + x + " " + (1 - Oa) * _ + "," + (.5 + Na) * x + " " + (1 - Oa) * _ + ", " + (.5 + Na) * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "upArrow": Na = .25, Oa = .5, li = _ / x; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 1e5 / li } } f += " <polygon points='" + x / 2 + " 0,0 " + Oa * _ + "," + (.5 - Na) * x + " " + Oa * _ + "," + (.5 - Na) * x + " " + _ + "," + (.5 + Na) * x + " " + _ + "," + (.5 + Na) * x + " " + Oa * _ + ", " + x + " " + Oa * _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftRightArrow": Na = .25, Oa = .25, li = x / _; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = .5 - parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 1e5 / li } } f += " <polygon points='0 " + _ / 2 + "," + Oa * x + " " + _ + "," + Oa * x + " " + (1 - Na) * _ + "," + (1 - Oa) * x + " " + (1 - Na) * _ + "," + (1 - Oa) * x + " " + _ + "," + x + " " + _ / 2 + ", " + (1 - Oa) * x + " 0," + (1 - Oa) * x + " " + Na * _ + "," + Oa * x + " " + Na * _ + "," + Oa * x + " 0' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "upDownArrow": Na = .25, Oa = .25, li = _ / x; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { if ("adj1" == (ll = J(Zo[Aa], ["attrs", "name"]))) tl = J(Zo[Aa], ["attrs", "fmla"]), Na = .5 - parseInt(tl.substr(4)) / 2e5; else if ("adj2" == ll) { el = J(Zo[Aa], ["attrs", "fmla"]), Oa = parseInt(el.substr(4)) / 1e5 / li } } f += " <polygon points='" + x / 2 + " 0,0 " + Oa * _ + "," + Na * x + " " + Oa * _ + "," + Na * x + " " + (1 - Oa) * _ + ",0 " + (1 - Oa) * _ + "," + x / 2 + " " + _ + ", " + x + " " + (1 - Oa) * _ + "," + (1 - Na) * x + " " + (1 - Oa) * _ + "," + (1 - Na) * x + " " + Oa * _ + "," + x + " " + Oa * _ + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "quadArrow": Wa = 216e4 / 914400, gr = 216e4 / 914400, br = 216e4 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } U = x / 2; uo = (Mn = Va - (vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr))) / 2, f += "<path d='" + (Xd = "M0," + (H = _ / 2) + " L" + (So = (bi = Math.min(x, _)) * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) + "," + (Bo = H - (jo = bi * ql / Va)) + " L" + So + "," + (No = H - (yi = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (To = U - yi) + "," + No + " L" + To + "," + So + " L" + (zo = U - jo) + "," + So + " L" + U + ",0 L" + (Ki = U + jo) + "," + So + " L" + (ft = U + yi) + "," + So + " L" + ft + "," + No + " L" + ($i = x - So) + "," + No + " L" + $i + "," + Bo + " L" + x + "," + H + " L" + $i + "," + (mo = H + jo) + " L" + $i + "," + (ut = H + yi) + " L" + ft + "," + ut + " L" + ft + "," + (Di = _ - So) + " L" + Ki + "," + Di + " L" + U + "," + _ + " L" + zo + "," + Di + " L" + To + "," + Di + " L" + To + "," + ut + " L" + So + "," + ut + " L" + So + "," + mo + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftRightUpArrow": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } H = _ / 2, U = x / 2; uo = (Mn = Va - (vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr))) / 2, f += "<path d='" + (Xd = "M0," + (ut = _ - (jo = (bi = Math.min(x, _)) * ql / Va)) + " L" + (So = bi * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) + "," + (Bo = _ - (Qo = bi * ql / Xa)) + " L" + So + "," + (No = ut - (yi = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (To = U - yi) + "," + No + " L" + To + "," + So + " L" + (zo = U - jo) + "," + So + " L" + U + ",0 L" + (Ki = U + jo) + "," + So + " L" + (ft = U + yi) + "," + So + " L" + ft + "," + No + " L" + ($i = x - So) + "," + No + " L" + $i + "," + Bo + " L" + x + "," + ut + " L" + $i + "," + _ + " L" + $i + "," + (mo = ut + yi) + " L" + So + "," + mo + " L" + So + "," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftUpArrow": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } var ni; H = _ / 2, U = x / 2; uo = Va - (vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr)), f += "<path d='" + (Xd = "M0," + (ut = _ - (ni = (bi = Math.min(x, _)) * ql / Va)) + " L" + (So = bi * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) + "," + (Bo = _ - (jo = bi * ql / Xa)) + " L" + So + "," + (No = ut - (yi = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (To = (ft = x - ni) - yi) + "," + No + " L" + To + "," + So + " L" + (zo = x - jo) + "," + So + " L" + ft + ",0 L" + x + "," + So + " L" + (Ki = ft + yi) + "," + So + " L" + Ki + "," + (mo = ut + yi) + " L" + So + "," + mo + " L" + So + "," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bentUpArrow": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } H = _ / 2, U = x / 2; dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa, f += "<path d='" + (Xd = "M0," + (Bo = _ - (Qo = (bi = Math.min(x, _)) * dl / Va)) + " L" + (zo = (To = x - (yi = bi * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr) / Va)) - (jo = bi * dl / jr)) + "," + Bo + " L" + zo + "," + (Ro = bi * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) + " L" + (So = x - (Uo = bi * ql / Xa)) + "," + Ro + " L" + To + ",0 L" + x + "," + Ro + " L" + (ft = To + jo) + "," + Ro + " L" + ft + "," + _ + " L0," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "bentArrow": var di; Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 42e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr), Mi = Va * ((ci = x - (Ti = (bi = Math.min(x, _)) * (Lo = br < 0 ? 0 : br > Xa ? Xa : br) / Va)) < (di = _ - (pi = (Bi = bi * ql / Va) - (fl = (pl = bi * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / Va) / 2))) ? ci : di) / bi, To = pl + (Li = (ui = (fi = bi * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va) - pl) > 0 ? ui : 0), ft = x - Ti, ut = (No = pi + pl) + pi, Di = No + Li, f += "<path d='" + (Xd = "M0," + _ + " L0," + (mo = pi + fi) + P(fi, mo, fi, fi, 180, 270, !1).replace("M", "L") + " L" + ft + "," + pi + " L" + ft + ",0 L" + x + "," + Bi + " L" + ft + "," + ut + " L" + ft + "," + No + " L" + To + "," + No + P(To, Di, Li, Li, 270, 180, !1).replace("M", "L") + " L" + pl + "," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "uturnArrow": var hi, pi, ci, fi, ui, Li; Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 42e5 / 914400, Cs = 72e5 / 914400, Xa = 24e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll ? (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) : "adj5" == ll && (ol = J(Zo[Aa], ["attrs", "fmla"]), Cs = 96 * parseInt(ol.substr(4)) / 914400) } vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr), uo = (Va - (kn = (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) * (bi = Math.min(x, _)) / _)) * _ / bi, Mi = Va * ((ci = (at = x - (pi = (Bi = bi * ql / Va) - (fl = (pl = bi * dl / Va) / 2))) / 2) < (ut = (mo = _ * (nl = Cs < (hi = (Mn = (Lo = br < 0 ? 0 : br > uo ? uo : br) + dl) * bi / _) ? hi : Cs > Va ? Va : Cs) / Va) - (Ti = bi * Lo / Va)) ? ci : ut) / bi, To = pl + (Li = (ui = (fi = bi * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va) - pl) > 0 ? ui : 0), ft = at - fi, Ki = (Ji = ($i = (Kr = x - Bi) - Bi) + pi) - Li, cx = (pl + Ji) / 2; f += "<path d='" + (Xd = "M0," + _ + " L0," + fi + P(fi, fi, fi, fi, 180, 270, !1).replace("M", "L") + " L" + ft + ",0" + P(ft, fi, fi, fi, 270, 360, !1).replace("M", "L") + " L" + at + "," + ut + " L" + x + "," + ut + " L" + Kr + "," + mo + " L" + $i + "," + ut + " L" + Ji + "," + ut + " L" + Ji + "," + To + P(Ki, To, Li, Li, 0, -90, !1).replace("M", "L") + " L" + To + "," + pl + P(To, To, Li, Li, 270, 180, !1).replace("M", "L") + " L" + pl + "," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "stripedRightArrow": Wa = 48e5 / 914400, gr = 48e5 / 914400, Xa = 96e5 / 914400, Va = 192e5 / 914400, jr = 81e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } var vi; H = _ / 2; Lr = jr * x / (bi = Math.min(x, _)); var mi = bi / 16, gi = bi / 32; f += "<path d='" + (Xd = "M0," + (Ro = H - (Go = _ * (dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa) / Va)) + " L" + gi + "," + Ro + " L" + gi + "," + (Bo = H + Go) + " L0," + Bo + " z M" + mi + "," + Ro + " L" + (Fo = bi / 8) + "," + Ro + " L" + Fo + "," + Bo + " L" + mi + "," + Bo + " z M" + (ft = 5 * bi / 32) + "," + Ro + " L" + (Ki = x - (vi = bi * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Xa)) + "," + Ro + " L" + Ki + ",0 L" + x + "," + H + " L" + Ki + "," + _ + " L" + Ki + "," + Bo + " L" + ft + "," + Bo + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "notchedRightArrow": Wa = 48e5 / 914400, gr = 48e5 / 914400, Xa = 96e5 / 914400, Va = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) } ja = H = _ / 2; Lr = Xa * x / (bi = Math.min(x, _)), f += "<path d='" + (Xd = "M0," + (Ro = H - (Go = _ * (dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa) / Va)) + " L" + (zo = x - (jo = bi * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Xa)) + "," + Ro + " L" + zo + ",0 L" + x + "," + H + " L" + zo + "," + _ + " L" + zo + "," + (Bo = H + Go) + " L0," + Bo + " L" + (So = Go * jo / ja) + "," + H + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "homePlate": Qa = 48e5 / 914400, Xa = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); H = _ / 2; Tr = Xa * x / (bi = Math.min(x, _)), f += "<path  d='" + (Xd = "M0,0 L" + (So = x - (Uo = bi * (Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa) / Xa)) + ",0 L" + x + "," + H + " L" + So + "," + _ + " L0," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "chevron": Qa = 48e5 / 914400, Xa = 96e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400); var bi; H = _ / 2; Tr = Xa * x / (bi = Math.min(x, _)), f += "<path d='" + (Xd = "M0,0 L" + (zo = x - (So = bi * (Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa) / Xa)) + ",0 L" + x + "," + H + " L" + zo + "," + _ + " L0," + _ + " L" + So + "," + H + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "rightArrowCallout": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 6237792 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } H = _ / 2, Ar = x, zr = _, We = 0, Ye = 0; Lr = Xa * _ / (qe = Math.min(x, _)), vr = 2 * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr), uo = Va * x / qe, Mi = Ha - (kn = (Lo = br < 0 ? 0 : br > uo ? uo : br) * qe / x), So = (zo = x * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va) / 2, f += "<path d='" + (Xd = "M" + We + "," + Ye + " L" + zo + "," + Ye + " L" + zo + "," + (Bo = H - (Qo = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (To = Ar - (yi = qe * Lo / Va)) + "," + Bo + " L" + To + "," + (Ro = H - (Go = qe * ql / Va)) + " L" + Ar + "," + H + " L" + To + "," + (ut = H + Go) + " L" + To + "," + (No = H + Qo) + " L" + zo + "," + No + " L" + zo + "," + zr + " L" + We + "," + zr + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "downArrowCallout": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 6237792 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } U = x / 2, Ar = x, zr = _, We = 0, Ye = 0; Lr = Xa * x / (qe = Math.min(x, _)), vr = 2 * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr), uo = Va * _ / qe, Mi = Va - (kn = (Lo = br < 0 ? 0 : br > uo ? uo : br) * qe / _), Ro = (Bo = _ * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va) / 2, f += "<path d='" + (Xd = "M" + We + "," + Ye + " L" + Ar + "," + Ye + " L" + Ar + "," + Bo + " L" + (To = U + (jo = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + "," + Bo + " L" + To + "," + (No = zr - (Xo = qe * Lo / Va)) + " L" + (ft = U + (Uo = qe * ql / Va)) + "," + No + " L" + U + "," + zr + " L" + (So = U - Uo) + "," + No + " L" + (zo = U - jo) + "," + No + " L" + zo + "," + Bo + " L" + We + "," + Bo + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftArrowCallout": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 6237792 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } H = _ / 2, Ar = x, zr = _, We = 0, Ye = 0; Lr = Xa * _ / (qe = Math.min(x, _)), vr = 2 * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr), uo = Va * x / qe, Mi = Va - (kn = (Lo = br < 0 ? 0 : br > uo ? uo : br) * qe / x), To = ((zo = Ar - (jo = x * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va)) + Ar) / 2, f += "<path d='" + (Xd = "M" + We + "," + H + " L" + (So = qe * Lo / Va) + "," + (Ro = H - (Go = qe * ql / Va)) + " L" + So + "," + (Bo = H - (Qo = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + zo + "," + Bo + " L" + zo + "," + Ye + " L" + Ar + "," + Ye + " L" + Ar + "," + zr + " L" + zo + "," + zr + " L" + zo + "," + (No = H + Qo) + " L" + So + "," + No + " L" + So + "," + (ut = H + Go) + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "upArrowCallout": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 6237792 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } U = x / 2, Ar = x, zr = _, We = 0, Ye = 0; Lr = Xa * x / (qe = Math.min(x, _)), vr = 2 * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr), uo = Va * _ / qe, Mi = Va - (kn = (Lo = br < 0 ? 0 : br > uo ? uo : br) * qe / _), No = ((Bo = zr - (Qo = _ * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / Va)) + zr) / 2, f += "<path d='" + (Xd = "M" + We + "," + Bo + " L" + (zo = U - (jo = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + "," + Bo + " L" + zo + "," + (Ro = qe * Lo / Va) + " L" + (So = U - (Uo = qe * ql / Va)) + "," + Ro + " L" + U + "," + Ye + " L" + (ft = U + Uo) + "," + Ro + " L" + (To = U + jo) + "," + Ro + " L" + To + "," + Bo + " L" + Ar + "," + Bo + " L" + Ar + "," + zr + " L" + We + "," + zr + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftRightArrowCallout": Wa = 24e5 / 914400, gr = 24e5 / 914400, br = 24e5 / 914400, Ps = 4619808 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } H = _ / 2, U = x / 2, Ar = x, zr = _, We = 0, Ye = 0; Lr = Xa * _ / (qe = Math.min(x, _)), vr = 2 * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr), uo = Xa * x / qe, Mi = Va - (kn = (Lo = br < 0 ? 0 : br > uo ? uo : br) * qe / wa), f += "<path d='" + (Xd = "M" + We + "," + H + " L" + (So = qe * Lo / Va) + "," + (Ro = H - (Go = qe * ql / Va)) + " L" + So + "," + (Bo = H - (Qo = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (zo = U - (jo = x * (ki = Ps < 0 ? 0 : Ps > Mi ? Mi : Ps) / jr)) + "," + Bo + " L" + zo + "," + Ye + " L" + (To = U + jo) + "," + Ye + " L" + To + "," + Bo + " L" + (ft = Ar - So) + "," + Bo + " L" + ft + "," + Ro + " L" + Ar + "," + H + " L" + ft + "," + (ut = H + Go) + " L" + ft + "," + (No = H + Qo) + " L" + To + "," + No + " L" + To + "," + zr + " L" + zo + "," + zr + " L" + zo + "," + No + " L" + So + "," + No + " L" + So + "," + ut + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "quadArrowCallout": Wa = 1777440 / 914400, gr = 1777440 / 914400, br = 1777440 / 914400, Ps = 4619808 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) : "adj4" == ll && (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = 96 * parseInt(il.substr(4)) / 914400) } var Mi, ki, yi; U = x / 2, Ar = x, zr = _, Ye = 0; vr = 2 * (ql = gr < 0 ? 0 : gr > Xa ? Xa : gr), uo = Xa - ql, Mi = Va - (kn = 2 * (Lo = br < 0 ? 0 : br > uo ? uo : br)), f += "<path d='" + (Xd = "M" + (We = 0) + "," + (H = _ / 2) + " L" + (Ti = (qe = Math.min(x, _)) * Lo / Va) + "," + (No = H - (jo = qe * ql / Va)) + " L" + Ti + "," + (ut = H - (yi = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / jr)) + " L" + (zo = U - (Uo = x * (ki = Ps < dl ? dl : Ps > Mi ? Mi : Ps) / jr)) + "," + ut + " L" + zo + "," + (Bo = H - (Go = _ * ki / jr)) + " L" + (ft = U - yi) + "," + Bo + " L" + ft + "," + Ti + " L" + (To = U - jo) + "," + Ti + " L" + U + "," + Ye + " L" + ($i = U + jo) + "," + Ti + " L" + (Ki = U + yi) + "," + Ti + " L" + Ki + "," + Bo + " L" + (Ji = U + Uo) + "," + Bo + " L" + Ji + "," + ut + " L" + (Kr = Ar - Ti) + "," + ut + " L" + Kr + "," + No + " L" + Ar + "," + H + " L" + Kr + "," + (Di = H + jo) + " L" + Kr + "," + (mo = H + yi) + " L" + Ji + "," + mo + " L" + Ji + "," + (_i = H + Go) + " L" + Ki + "," + _i + " L" + Ki + "," + (st = zr - Ti) + " L" + $i + "," + st + " L" + U + "," + zr + " L" + To + "," + st + " L" + ft + "," + st + " L" + ft + "," + _i + " L" + zo + "," + _i + " L" + zo + "," + mo + " L" + Ti + "," + mo + " L" + Ti + "," + Di + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "curvedDownArrow": Wa = 24e5 / 914400, gr = 48e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } H = _ / 2, U = x / 2, wa = x / 2, Ar = x, zr = _, We = 0, Ye = 0, Pa = 270, Ia = 180, ya = 90; Lr = Xa * x / (qe = Math.min(x, _)), Gn = (Cn = (Pn = 2 * (oh = wa - (Mn = ((pl = qe * (dl = Wa < 0 ? 0 : Wa > Va ? Va : Wa) / Va) + (Si = qe * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Va)) / 4))) * Pn) - (Dn = pl * pl), uo = Va * (zi = (jn = Math.sqrt(Gn)) * _ / Pn) / qe, Lo = br < 0 ? 0 : br > uo ? uo : br, To = oh + pl, wn = (kn = _ * _) - (Ti = qe * br / Va) * Ti, ft = (Ki = oh + (Ai = (In = Math.sqrt(wn)) * oh / _)) - (Ri = (Si - pl) / 2), Kr = (Ji = To + Ai) + Ri, $i = Ar - (Bi = Si / 2), Ro = zr - Ti; var wi = 180 * (nh = Math.atan(Ai / Ti)) / Math.PI; Ni = -wi, zr - zi, (oh + To) / 2, zn = pl / 2; var Ii = 180 * (Oi = Math.atan(zn / zi)) / Math.PI; Hi = Pa - Ii, Fi = Ii - ya, qi = ya + Ii, f += "<path d='" + (Xd = "M" + $i + "," + zr + " L" + ft + "," + Ro + " L" + Ki + "," + Ro + P(oh, _, oh, _, lh = Pa + wi, lh + Ni, !1).replace("M", "L") + " L" + To + "," + Ye + P(To, _, oh, _, Pa, Pa + wi, !1).replace("M", "L") + " L" + (Ki + pl) + "," + Ro + " L" + Kr + "," + Ro + " zM" + To + "," + Ye + P(To, _, oh, _, Hi, Hi + Fi, !1).replace("M", "L") + P(oh, _, oh, _, Ia, Ia + qi, !1).replace("M", "L")) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "curvedLeftArrow": Wa = 24e5 / 914400, gr = 48e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } var Pi, Ci; H = _ / 2, U = x / 2, ja = _ / 2, Ar = x, zr = _, We = 0, Ye = 0, Pa = 270, Ia = 180, ya = 90; Lr = Xa * _ / (qe = Math.min(x, _)), ql = gr < 0 ? 0 : gr > Lr ? Lr : gr, Gn = (Cn = (Pn = 2 * (ih = ja - (Mn = ((pl = qe * (dl = Wa < 0 ? 0 : Wa > ql ? ql : Wa) / Va) + (Si = qe * ql / Va)) / 4))) * Pn) - (Dn = pl * pl), uo = Va * (i = (jn = Math.sqrt(Gn)) * x / Pn) / qe, No = ih + pl, wn = (kn = x * x) - (Ti = qe * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) * Ti, ut = (mo = ih + (xi = (In = Math.sqrt(wn)) * ih / x)) - (Ri = (Si - pl) / 2), st = (_i = No + xi) + Ri, Di = zr - (Bi = Si / 2), So = We + Ti, Ni = -(nh = Math.atan(xi / Ti)), We + i, (ih + No) / 2, zn = pl / 2, Fi = (Oi = Math.atan(zn / i)) - nh, qi = nh + Oi, Ei = -Oi, Qi = 180 * nh / Math.PI, Pi = 180 * Fi / Math.PI, 180 * qi / Math.PI, Ci = 180 * Ei / Math.PI, f += "<path d='" + (Xd = "M" + Ar + "," + No + P(We, ih, x, ih, 0, -ya, !1).replace("M", "L") + " L" + We + "," + Ye + P(We, No, x, ih, Pa, Pa + ya, !1).replace("M", "L") + " L" + Ar + "," + No + P(We, No, x, ih, 0, Qi, !1).replace("M", "L") + " L" + So + "," + _i + " L" + So + "," + st + " L" + We + "," + Di + " L" + So + "," + ut + " L" + So + "," + mo + P(We, ih, x, ih, Qi, Qi + Pi, !1).replace("M", "L") + P(We, ih, x, ih, 0, -ya, !1).replace("M", "L") + P(We, No, x, ih, Pa, Pa + ya, !1).replace("M", "L")) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "curvedRightArrow": Wa = 24e5 / 914400, gr = 48e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } var xi, _i, Di, Gi, ji; H = _ / 2, U = x / 2, ja = _ / 2, Ar = x, zr = _, We = 0, Ye = 0, Pa = 270, Ia = 180, ya = 90; Lr = Xa * _ / (qe = Math.min(x, _)), ql = gr < 0 ? 0 : gr > Lr ? Lr : gr, Gn = (Cn = (Pn = 2 * (ih = ja - (Mn = ((pl = qe * (dl = Wa < 0 ? 0 : Wa > ql ? ql : Wa) / Va) + (Si = qe * ql / Va)) / 4))) * Pn) - (Dn = pl * pl), uo = Va * (i = (jn = Math.sqrt(Gn)) * x / Pn) / qe, No = ih + pl, wn = (kn = x * x) - (Ti = qe * (Lo = br < 0 ? 0 : br > uo ? uo : br) / Va) * Ti, ut = (mo = ih + (xi = (In = Math.sqrt(wn)) * ih / x)) - (Ri = (Si - pl) / 2), st = (_i = No + xi) + Ri, Di = zr - (Bi = Si / 2), So = Ar - Ti, nh = Math.atan(xi / Ti), lh = Math.PI + 0 - nh, Ni = -nh, Ar - i, (ih + No) / 2, zn = pl / 2, Fi = (Oi = Math.atan(zn / i)) - Math.PI / 2, qi = Math.PI / 2 + Oi, Ei = Math.PI - Oi, Gi = 180 * lh / Math.PI, ji = 180 * Ni / Math.PI, Qi = 180 * nh / Math.PI, Xi = 180 * Fi / Math.PI, f += "<path d='" + (Xd = "M" + We + "," + ih + P(x, ih, x, ih, Ia, Ia + ji, !1).replace("M", "L") + " L" + So + "," + mo + " L" + So + "," + ut + " L" + Ar + "," + Di + " L" + So + "," + st + " L" + So + "," + _i + P(x, No, x, ih, Gi, Gi + Qi, !1).replace("M", "L") + " L" + We + "," + ih + P(x, ih, x, ih, Ia, Ia + ya, !1).replace("M", "L") + " L" + Ar + "," + pl + P(x, No, x, ih, Pa, Pa + Xi, !1).replace("M", "L")) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "curvedUpArrow": Wa = 24e5 / 914400, gr = 48e5 / 914400, br = 24e5 / 914400, Xa = 48e5 / 914400, Va = 96e5 / 914400; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = 96 * parseInt(el.substr(4)) / 914400) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = 96 * parseInt(sl.substr(4)) / 914400) } var Si, zi, Ti, Ai, Ri, Bi, Ni, Oi, Fi, Ei, qi, Hi, Ui, Qi, Xi; H = _ / 2, U = x / 2, wa = x / 2, Ar = x, zr = _, We = 0, Ye = 0, Pa = 270, Ia = 180, ya = 90; Lr = Xa * x / (qe = Math.min(x, _)), Gn = (Cn = (Pn = 2 * (oh = wa - (Mn = ((pl = qe * (dl = Wa < 0 ? 0 : Wa > Va ? Va : Wa) / Va) + (Si = qe * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Va)) / 4))) * Pn) - (Dn = pl * pl), uo = Va * (zi = (jn = Math.sqrt(Gn)) * _ / Pn) / qe, Lo = br < 0 ? 0 : br > uo ? uo : br, To = oh + pl, wn = (kn = _ * _) - (Ti = qe * br / Va) * Ti, ft = (Ki = oh + (Ai = (In = Math.sqrt(wn)) * oh / _)) - (Ri = (Si - pl) / 2), Kr = (Ji = To + Ai) + Ri, $i = Ar - (Bi = Si / 2), Ro = Ye + Ti, Ni = -(nh = Math.atan(Ai / Ti)), Ye + zi, (oh + To) / 2, zn = pl / 2, -(Fi = (Oi = Math.atan(zn / zi)) - nh), Ei = Math.PI / 2 - nh, qi = nh + Oi, Ui = 180 * (Hi = Math.PI / 2 - Oi) / Math.PI, Xi = 180 * Fi / Math.PI, Ci = 180 * Ei / Math.PI, Qi = 180 * nh / Math.PI, f += "<path d='" + (Xd = P(oh, 0, oh, _, Ui, Ui + Xi, !1) + " L" + Ki + "," + Ro + " L" + ft + "," + Ro + " L" + $i + "," + Ye + " L" + Kr + "," + Ro + " L" + Ji + "," + Ro + P(To, 0, oh, _, Ci, Ci + Qi, !1).replace("M", "L") + " L" + oh + "," + zr + P(oh, 0, oh, _, ya, Ia, !1).replace("M", "L") + " L" + pl + "," + Ye + P(To, 0, oh, _, Ia, ya, !1).replace("M", "L")) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "mathDivide": case "mathEqual": case "mathMinus": case "mathMultiply": case "mathNotEqual": case "mathPlus": if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) if (Zo.constructor === Array) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4))) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4))) : "adj3" == ll && (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4))) } else tl = J(Zo, ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)); Xa = 48e5 / 914400, Va = 96e5 / 914400, jr = 192e5 / 914400, U = x / 2, H = _ / 2, ja = _ / 2; if ("mathNotEqual" == L) { var Vi, Yi, Wi, Zi, Ji, $i, Ki, ao, ro, to, eo, so, io, oo, lo, no, ho; void 0 === Zo ? (Wa = 2257920 / 914400, gr = 110 * Math.PI / 180, br = 1128960 / 914400) : (Wa = 96 * Wa / 914400, gr = gr / 6e4 * Math.PI / 180, br = 96 * br / 914400); Ya = 70 * Math.PI / 180; var po = 110 * Math.PI / 180; uo = Va - 2 * (dl = Wa < 0 ? 0 : Wa > Xa ? Xa : Wa), So = U - (Uo = x * (Os = 7055040 / 914400) / jr), Kr = U + Uo, Ro = (Bo = H - (Qo = _ * (Lo = br < 0 ? 0 : br > uo ? uo : br) / jr)) - (Go = _ * dl / Va), ut = (No = H + Qo) + Go, Vi = (gr < Ya ? Ya : gr > po ? po : gr) - Math.PI / 2, ao = (Ji = U + (Yi = ja * Math.tan(Vi)) - (Zi = (Wi = Math.sqrt(Yi * Yi + ja * ja)) * Go / ja) / 2) + Zi, (zo = Ji - 2 * Yi) + Zi, ro = Go * ja / Wi, Vo = -(Xo = Go * Yi / Wi), ((to = Vi > 0 ? Ji + ro : ao) + (eo = Vi > 0 ? Ji : ao - ro)) / 2, ((lo = x - eo) + (oo = x - to)) / 2, ((so = Vi > 0 ? Xo : 0) + (io = Vi > 0 ? 0 : Vo)) / 2, (Ro + Bo) / 2, (No + ut) / 2, ((ho = _ - io) + (no = _ - so)) / 2, Yo = "M" + So + "," + Ro + " L" + ($i = Ji - Yi * Ro / ja) + "," + Ro + " L" + eo + "," + io + " L" + to + "," + so + " L" + ($i + Zi) + "," + Ro + " L" + Kr + "," + Ro + " L" + Kr + "," + Bo + " L" + ((Ki = Ji - Yi * Bo / ja) + Zi) + "," + Bo + " L" + ((ft = Ji - Yi * No / ja) + Zi) + "," + No + " L" + Kr + "," + No + " L" + Kr + "," + ut + " L" + ((To = Ji - Yi * ut / ja) + Zi) + "," + ut + " L" + lo + "," + ho + " L" + oo + "," + no + " L" + To + "," + ut + " L" + So + "," + ut + " L" + So + "," + No + " L" + ft + "," + No + " L" + Ki + "," + Bo + " L" + So + "," + Bo + " z" } else if ("mathDivide" == L) { var co, fo, uo, Lo, vo, mo; void 0 === Zo ? (Wa = 2257920 / 914400, gr = 564480 / 914400, br = 1128960 / 914400) : (Wa = 96 * Wa / 914400, gr = 96 * gr / 914400, br = 96 * br / 914400); Ws = 3527520 / 914400; uo = (co = ((Do = 7055040 / 914400) + -(dl = Wa < (Os = 96e3 / 914400) ? Os : Wa > Ws ? Ws : Wa)) / 4) < (fo = Ws * x / _) ? co : fo, Lr = Do + -4 * (Lo = br < Os ? Os : br > uo ? uo : br) - dl, ut = H + (Go = _ * dl / jr), mo = _ - (Ro = (Bo = (No = H - Go) - (Ao = _ * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Va + (vo = _ * Lo / Va))) - vo), So = U - (Uo = x * Do / jr), To = U + Uo, zo = U - vo; ya = 90, Pa = 270, He = U - Math.cos(Pa * Math.PI / 180) * vo, Ue = Ro - Math.sin(Pa * Math.PI / 180) * vo, Q = U - Math.cos(Math.PI / 2) * vo, Y = mo - Math.sin(Math.PI / 2) * vo; Yo = "M" + U + "," + Ro + P(He, Ue, vo, vo, Pa, Pa + 360, !1).replace("M", "L") + " z M" + U + "," + mo + P(Q, Y, vo, vo, ya, ya + 360, !1).replace("M", "L") + " z M" + So + "," + No + " L" + To + "," + No + " L" + To + "," + ut + " L" + So + "," + ut + " z" } else if ("mathEqual" == L) { void 0 === Zo ? (Wa = 2257920 / 914400, gr = 1128960 / 914400) : (Wa = 96 * Wa / 914400, gr = 96 * gr / 914400); var go; Ws = 3527520 / 914400; go = Va - 2 * (dl = Wa < 0 ? 0 : Wa > Ws ? Ws : Wa), ((Ro = (Bo = H - (Qo = _ * (ql = gr < 0 ? 0 : gr > go ? go : gr) / jr)) - (Go = _ * dl / Va)) + Bo) / 2, ((No = H + Qo) + (ut = No + Go)) / 2, Yo = "M" + (So = U - (Uo = x * (Do = 7055040 / 914400) / jr)) + "," + Ro + " L" + (zo = U + Uo) + "," + Ro + " L" + zo + "," + Bo + " L" + So + "," + Bo + " zM" + So + "," + No + " L" + zo + "," + No + " L" + zo + "," + ut + " L" + So + "," + ut + " z" } else if ("mathMinus" == L) { Yo = "M" + (So = U - (Uo = x * (Do = 7055040 / 914400) / jr)) + "," + (Ro = H - (Go = _ * (dl = (Wa = void 0 === Zo ? 2257920 / 914400 : 96 * Wa / 914400) < 0 ? 0 : Wa > Va ? Va : Wa) / jr)) + " L" + (zo = U + Uo) + "," + Ro + " L" + zo + "," + (Bo = H + Go) + " L" + So + "," + Bo + " z" } else if ("mathMultiply" == L) { Wa = void 0 === Zo ? 2257920 / 914400 : 96 * Wa / 914400; var bo, Mo, ko, yo, wo, Io, Po, Co, xo, _o, Do = 4988640 / 914400; pl = (qe = Math.min(x, _)) * (dl = Wa < 0 ? 0 : Wa > Do ? Do : Wa) / Va, Ao = Math.atan(_ / x), bo = 1 * Math.sin(Ao), Mo = 1 * Math.cos(Ao), ko = 1 * Math.tan(Ao), x - (Io = Mo * (wo = (yo = Math.sqrt(x * x + _ * _)) - yo * Do / Va) / 2), _ - (Po = bo * wo / 2), Yo = "M" + (Xl = Io - (Co = bo * pl / 2)) + "," + (Vl = Po + (xo = Mo * pl / 2)) + " L" + (on = Io + Co) + "," + (ln = Po - xo) + " L" + U + "," + (Pd = (U - on) * ko + ln) + " L" + (an = x - on) + "," + ln + " L" + (Zl = x - Xl) + "," + Vl + " L" + (Vn = Zl - (_o = (H - Vl) / ko)) + "," + H + " L" + Zl + "," + (sn = _ - Vl) + " L" + an + "," + (Pl = _ - ln) + " L" + U + "," + (_ - Pd) + " L" + on + "," + Pl + " L" + Xl + "," + sn + " L" + (Xl + _o) + "," + H + " z" } else if ("mathPlus" == L) { var Go, jo, So, zo, To; Wa = void 0 === Zo ? 2257920 / 914400 : 96 * Wa / 914400, Yo = "M" + (So = U - (Uo = x * (Do = 7055040 / 914400) / jr)) + "," + (Bo = H - (jo = (qe = Math.min(x, _)) * (dl = Wa < 0 ? 0 : Wa > Do ? Do : Wa) / jr)) + " L" + (zo = U - jo) + "," + Bo + " L" + zo + "," + (Ro = H - (Go = _ * Do / jr)) + " L" + (To = U + jo) + "," + Ro + " L" + To + "," + Bo + " L" + (ft = U + Uo) + "," + Bo + " L" + ft + "," + (No = H + jo) + " L" + To + "," + No + " L" + To + "," + (ut = H + Go) + " L" + zo + "," + ut + " L" + zo + "," + No + " L" + So + "," + No + " z" } f += "<path d='" + Yo + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "can": case "flowChartMagneticDisk": case "flowChartMagneticDrum": var Ao, Ro, Bo, No; Qa = 24e5 / 914400, Xa = 48e5 / 914400, Va = 192e5 / 914400; void 0 !== (Ua = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd", "attrs", "fmla"])) && (Qa = 96 * parseInt(Ua.substr(4)) / 914400), "flowChartMagneticDisk" != L && "flowChartMagneticDrum" != L || (Qa = 48e5 / 914400), Tr = Xa * _ / (qe = Math.min(x, _)), Bo = (Ro = qe * (Ao = Qa < 0 ? 0 : Qa > Tr ? Tr : Qa) / Va) + Ro, No = _ - Ro; Ba = ""; "flowChartMagneticDrum" == L && (Ba = "transform='rotate(90 " + x / 2 + "," + _ / 2 + ")'"), f += "<path " + Ba + " d='" + (Yo = P(wa = x / 2, Ro, wa, Ro, 0, Ia = 180, !1) + P(wa, Ro, wa, Ro, Ia, Ia + Ia, !1).replace("M", "L") + " L" + x + "," + No + P(wa, No, wa, Ro, 0, Ia, !1).replace("M", "L") + " L0," + Ro) + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "swooshArrow": var Oo; Wa = 25e3 * (Oo = 96 / 914400), gr = 16667 * Oo; if (void 0 !== (Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]))) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = parseInt(tl.substr(4)) * Oo) : "adj2" == ll && (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) * Oo) } Xa = 1 * Oo, jr = 75e3 * Oo, Os = 1e5 * Oo; var Fo, Eo, qo, Ho, Uo, Qo, Xo, Vo, Yo, Wo = _ / 6; Lr = (Va = 7e4 * Oo) * x / (qe = Math.min(x, _)), Eo = _ * (dl = Wa < Xa ? Xa : Wa > jr ? jr : Wa) / Os, on = x - qe * (ql = gr < 0 ? 0 : gr > Lr ? Lr : gr) / Os, ln = Fo = qe / 8, qo = Math.PI / 2 / 14, f += "<path d='" + (Yo = "M0," + _ + " Q" + x / 6 + "," + (Wo + (Vo = Wo)) + " " + on + "," + ln + " L" + (Id = on - (Ho = Fo * Math.tan(qo))) + ",0 L" + x + "," + (rn = (Qo = (Jl = (Yn = ln + Eo) + Fo) - 0) / 2 - (Xo = _ / 20)) + " L" + (Zl = (Vn = on + (Uo = Eo * Math.tan(qo))) + Ho) + "," + Jl + " L" + Vn + "," + Yn + " Q" + x / 4 + "," + (Yn + Wo / 2) + " 0," + _ + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "circularArrow": var Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]); Wa = 12e5 / 914400, gr = 19.03865 * Math.PI / 180, br = 340.96135 * Math.PI / 180, Ps = 180 * Math.PI / 180, Cs = 12e5 / 914400; if (void 0 !== Zo) for (Aa = 0; Aa < Zo.length; Aa++) { "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) / 6e4 * Math.PI / 180) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4)) / 6e4 * Math.PI / 180) : "adj4" == ll ? (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = parseInt(il.substr(4)) / 6e4 * Math.PI / 180) : "adj5" == ll && (ol = J(Zo[Aa], ["attrs", "fmla"]), Cs = 96 * parseInt(ol.substr(4)) / 914400) } H = _ / 2, U = x / 2, Ar = x, zr = _, We = 0, Ye = 0, wa = x / 2, ja = _ / 2, qe = Math.min(x, _), Xa = 24e5 / 914400, Va = 96e5 / 914400; var Jo, $o = 1 / 6e4 * Math.PI / 180, Ko = 21599999 / 6e4 * Math.PI / 180, al = 2 * Math.PI; vr = 2 * (nl = Cs < 0 ? 0 : Cs > Xa ? Xa : Cs), hl = br < $o ? $o : br > Ko ? Ko : br, lh = Ps < 0 ? 0 : Ps > Ko ? Ko : Ps, bl = (ml = (Ll = ja + (fl = (pl = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / Va) / 2) - (cl = qe * nl / Va)) - pl) + fl, Ml = (gl = (vl = (ul = wa + fl - cl) - pl) + fl) * Math.sin(hl), kl = bl * Math.cos(hl), Il = U + (yl = gl * Math.cos(Math.atan2(Ml, kl))), Pl = H + (wl = bl * Math.sin(Math.atan2(Ml, kl))), Sl = 1 - (jl = (Gl = (xl = yl * yl) - (Dl = (Cl = vl < ml ? vl : ml) * Cl)) * ((_l = wl * wl) - Dl) / xl / _l), zl = (1 + Math.sqrt(Sl)) / (Gl / yl / wl), Al = (Tl = Math.atan2(zl, 1)) + al, Bl = (Rl = (Tl > 0 ? Tl : Al) - hl) + al, Ol = (Nl = Rl > 0 ? Rl : Bl) - al, Fl = Nl - Ia > 0 ? Ol : Nl, Jo = Math.abs(Fl), Hl = hl + (gr < 0 ? 0 : gr > Jo ? Jo : gr), Ul = gl * Math.sin(Hl), Ql = bl * Math.cos(Hl), Xl = U + gl * Math.cos(Math.atan2(Ul, Ql)), Vl = H + bl * Math.sin(Math.atan2(Ul, Ql)), Yl = ul * Math.sin(lh), Wl = Ll * Math.cos(lh), Zl = U + ul * Math.cos(Math.atan2(Yl, Wl)), Jl = H + Ll * Math.sin(Math.atan2(Yl, Wl)), tn = cl * Math.cos(Hl), sn = Pl + cl * Math.sin(Hl), mn = (Ln = (hn = (en = Il + tn) - U) * (cn = ul < Ll ? ul : Ll) / ul) - (fn = (nn = (on = Il - cl * Math.cos(Hl)) - U) * cn / ul), gn = (vn = (pn = sn - H) * cn / Ll) - (un = (dn = (ln = Pl - cl * Math.sin(Hl)) - H) * cn / Ll), Cn = (Pn = (In = cn * cn * (wn = (bn = Math.sqrt(mn * mn + gn * gn)) * bn)) - (yn = (Mn = fn * vn) - (kn = Ln * un)) * yn) > 0 ? Pn : 0, On = Ln - (Sn = ((jn = yn * gn) + (Gn = (Dn = (_n = -1 * gn > 0 ? -1 : 1) * mn) * (xn = Math.sqrt(Cn)))) / wn), Fn = Ln - (Tn = (zn = jn - Gn) / wn), En = vn - (Bn = ((Rn = yn * mn / -1) + (An = Math.abs(gn) * xn)) / wn), qn = vn - (Nn = (Rn - An) / wn), Hn = Math.sqrt(On * On + En * En), Vn = U + (Qn = ((Un = Math.sqrt(Fn * Fn + qn * qn) - Hn) > 0 ? Sn : Tn) * ul / cn), Yn = H + (Xn = (Un > 0 ? Bn : Nn) * Ll / cn), Kn = (Jn = hn * Cl / vl) - (Wn = nn * Cl / vl), ad = ($n = pn * Cl / ml) - (Zn = dn * Cl / ml), od = (id = Cl * Cl * (sd = (rd = Math.sqrt(Kn * Kn + ad * ad)) * rd) - (ed = Wn * $n - (td = Jn * Zn)) * ed) > 0 ? id : 0, vd = Wn - (hd = ((dd = ed * ad) + (nd = _n * Kn * (ld = Math.sqrt(od)))) / sd), md = Wn - (pd = (dd - nd) / sd), gd = Zn - (ud = ((fd = ed * Kn / -1) + (cd = Math.abs(ad) * ld)) / sd), bd = Zn - (Ld = (fd - cd) / sd), Md = Math.sqrt(vd * vd + gd * gd), Id = U + (yd = ((kd = Math.sqrt(md * md + bd * bd) - Md) > 0 ? hd : pd) * vl / Cl), Pd = H + (wd = (kd > 0 ? ud : Ld) * ml / Cl), xd = (Cd = Math.atan2(wd, yd)) + al, Gd = (Dd = lh - (Sd = Cd > 0 ? Cd : xd)) - al, zd = Dd > 0 ? Gd : Dd, Td = Vn - Id, Ad = Yn - Pd, Bd = (Rd = Math.sqrt(Td * Td + Ad * Ad) / 2 - cl) > 0 ? Vn : en, Nd = Rd > 0 ? Yn : sn, Od = Rd > 0 ? Id : on, Fd = Rd > 0 ? Pd : ln, qd = (Ed = Math.atan2(Xn, Qn)) + al, Ud = (Hd = (Ed > 0 ? Ed : qd) - lh) + al, nh = Hd > 0 ? Hd : Ud; Dr = (Vd = 180 * lh / Math.PI) + 180 * nh / Math.PI; var rl = (Qd = 180 * Sd / Math.PI) + 180 * zd / Math.PI; f += "<path d='" + (Xd = P(x / 2, _ / 2, ul, Ll, Vd, Dr, !1) + " L" + Bd + "," + Nd + " L" + Xl + "," + Vl + " L" + Od + "," + Fd + " L" + Id + "," + Pd + P(x / 2, _ / 2, vl, ml, Qd, rl, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftCircularArrow": var tl, el, sl, il, ol; Zo = J(a, ["p:spPr", "a:prstGeom", "a:avLst", "a:gd"]), Wa = 12e5 / 914400, gr = -19.03865 * Math.PI / 180, br = 19.03865 * Math.PI / 180, Ps = 180 * Math.PI / 180, Cs = 12e5 / 914400; if (void 0 !== Zo) for (Aa = 0; Aa < Zo.length; Aa++) { var ll; "adj1" == (ll = J(Zo[Aa], ["attrs", "name"])) ? (tl = J(Zo[Aa], ["attrs", "fmla"]), Wa = 96 * parseInt(tl.substr(4)) / 914400) : "adj2" == ll ? (el = J(Zo[Aa], ["attrs", "fmla"]), gr = parseInt(el.substr(4)) / 6e4 * Math.PI / 180) : "adj3" == ll ? (sl = J(Zo[Aa], ["attrs", "fmla"]), br = parseInt(sl.substr(4)) / 6e4 * Math.PI / 180) : "adj4" == ll ? (il = J(Zo[Aa], ["attrs", "fmla"]), Ps = parseInt(il.substr(4)) / 6e4 * Math.PI / 180) : "adj5" == ll && (ol = J(Zo[Aa], ["attrs", "fmla"]), Cs = 96 * parseInt(ol.substr(4)) / 914400) } var nl, dl, hl, pl, cl, fl, ul, Ll, vl, ml, gl, bl, Ml, kl, yl, wl, Il, Pl, Cl, xl, _l, Dl, Gl, jl, Sl, zl, Tl, Al, Rl, Bl, Nl, Ol, Fl, El, ql, Hl, Ul, Ql, Xl, Vl, Yl, Wl, Zl, Jl, $l, Kl, an, rn, tn, en, sn, on, ln, nn, dn, hn, pn, cn, fn, un, Ln, vn, mn, gn, bn, Mn, kn, yn, wn, In, Pn, Cn, xn, _n, Dn, Gn, jn, Sn, zn, Tn, An, Rn, Bn, Nn, On, Fn, En, qn, Hn, Un, Qn, Xn, Vn, Yn, Wn, Zn, Jn, $n, Kn, ad, rd, td, ed, sd, id, od, ld, nd, dd, hd, pd, cd, fd, ud, Ld, vd, md, gd, bd, Md, kd, yd, wd, Id, Pd, Cd, xd, _d, Dd, Gd, jd, Sd, zd, Td, Ad, Rd, Bd, Nd, Od, Fd, Ed, qd, Hd, Ud; H = _ / 2, U = x / 2, Ar = x, zr = _, We = 0, Ye = 0, wa = x / 2, ja = _ / 2, qe = Math.min(x, _), Xa = 24e5 / 914400, Va = 96e5 / 914400, $o = 1 / 6e4 * Math.PI / 180, Ko = 21599999 / 6e4 * Math.PI / 180, al = 2 * Math.PI; vr = 2 * (nl = Cs < 0 ? 0 : Cs > Xa ? Xa : Cs), hl = br < $o ? $o : br > Ko ? Ko : br, lh = Ps < 0 ? 0 : Ps > Ko ? Ko : Ps, bl = (ml = (Ll = ja + (fl = (pl = qe * (dl = Wa < 0 ? 0 : Wa > vr ? vr : Wa) / Va) / 2) - (cl = qe * nl / Va)) - pl) + fl, Ml = (gl = (vl = (ul = wa + fl - cl) - pl) + fl) * Math.sin(hl), kl = bl * Math.cos(hl), Il = U + (yl = gl * Math.cos(Math.atan2(Ml, kl))), Pl = H + (wl = bl * Math.sin(Math.atan2(Ml, kl))), Sl = 1 - (jl = (Gl = (xl = yl * yl) - (Dl = (Cl = vl < ml ? vl : ml) * Cl)) * ((_l = wl * wl) - Dl) / xl / _l), zl = (1 + Math.sqrt(Sl)) / (Gl / yl / wl), Al = (Tl = Math.atan2(zl, 1)) + al, Bl = (Rl = (Tl > 0 ? Tl : Al) - hl) + al, Ol = (Nl = Rl > 0 ? Rl : Bl) - al, Fl = Nl - Ia > 0 ? Ol : Nl, El = -1 * Math.abs(Fl), Hl = hl + ((ql = -1 * Math.abs(gr)) < El ? El : ql > 0 ? 0 : ql), Ul = gl * Math.sin(Hl), Ql = bl * Math.cos(Hl), Xl = U + gl * Math.cos(Math.atan2(Ul, Ql)), Vl = H + bl * Math.sin(Math.atan2(Ul, Ql)), Yl = ul * Math.sin(lh), Wl = Ll * Math.cos(lh), Zl = U + ul * Math.cos(Math.atan2(Yl, Wl)), Jl = H + Ll * Math.sin(Math.atan2(Yl, Wl)), $l = vl * Math.sin(lh), Kl = ml * Math.cos(lh), an = U + vl * Math.cos(Math.atan2($l, Kl)), rn = H + ml * Math.sin(Math.atan2($l, Kl)), tn = cl * Math.cos(Hl), sn = Pl + cl * Math.sin(Hl), mn = (Ln = (hn = (en = Il + tn) - U) * (cn = ul < Ll ? ul : Ll) / ul) - (fn = (nn = (on = Il - cl * Math.cos(Hl)) - U) * cn / ul), gn = (vn = (pn = sn - H) * cn / Ll) - (un = (dn = (ln = Pl - cl * Math.sin(Hl)) - H) * cn / Ll), Cn = (Pn = (In = cn * cn * (wn = (bn = Math.sqrt(mn * mn + gn * gn)) * bn)) - (yn = (Mn = fn * vn) - (kn = Ln * un)) * yn) > 0 ? Pn : 0, On = Ln - (Sn = ((jn = yn * gn) + (Gn = (Dn = (_n = -1 * gn > 0 ? -1 : 1) * mn) * (xn = Math.sqrt(Cn)))) / wn), Fn = Ln - (Tn = (zn = jn - Gn) / wn), En = vn - (Bn = ((Rn = yn * mn / -1) + (An = Math.abs(gn) * xn)) / wn), qn = vn - (Nn = (Rn - An) / wn), Hn = Math.sqrt(On * On + En * En), Vn = U + (Qn = ((Un = Math.sqrt(Fn * Fn + qn * qn) - Hn) > 0 ? Sn : Tn) * ul / cn), Yn = H + (Xn = (Un > 0 ? Bn : Nn) * Ll / cn), Kn = (Jn = hn * Cl / vl) - (Wn = nn * Cl / vl), ad = ($n = pn * Cl / ml) - (Zn = dn * Cl / ml), od = (id = Cl * Cl * (sd = (rd = Math.sqrt(Kn * Kn + ad * ad)) * rd) - (ed = Wn * $n - (td = Jn * Zn)) * ed) > 0 ? id : 0, vd = Wn - (hd = ((dd = ed * ad) + (nd = _n * Kn * (ld = Math.sqrt(od)))) / sd), md = Wn - (pd = (dd - nd) / sd), gd = Zn - (ud = ((fd = ed * Kn / -1) + (cd = Math.abs(ad) * ld)) / sd), bd = Zn - (Ld = (fd - cd) / sd), Md = Math.sqrt(vd * vd + gd * gd), Id = U + (yd = ((kd = Math.sqrt(md * md + bd * bd) - Md) > 0 ? hd : pd) * vl / Cl), Pd = H + (wd = (kd > 0 ? ud : Ld) * ml / Cl), xd = (Cd = Math.atan2(wd, yd)) + al, Gd = (Dd = lh - (_d = Cd > 0 ? Cd : xd)) + al, Sd = _d + (jd = Dd > 0 ? Dd : Gd), zd = -jd, Td = Vn - Id, Ad = Yn - Pd, Bd = (Rd = Math.sqrt(Td * Td + Ad * Ad) / 2 - cl) > 0 ? Vn : en, Nd = Rd > 0 ? Yn : sn, Od = Rd > 0 ? Id : on, Fd = Rd > 0 ? Pd : ln, qd = (Ed = Math.atan2(Xn, Qn)) + al, Ud = (Hd = (Ed > 0 ? Ed : qd) - lh) - al; var Qd, Xd, Vd = 180 * (lh + (nh = Hd > 0 ? Ud : Hd)) / Math.PI; Dr = 180 * lh / Math.PI; f += "<path d='" + (Xd = "M" + Zl + "," + Jl + " L" + an + "," + rn + P(x / 2, _ / 2, vl, ml, Qd = 180 * Sd / Math.PI, rl = Qd + 180 * zd / Math.PI, !1).replace("M", "L") + " L" + Od + "," + Fd + " L" + Xl + "," + Vl + " L" + Bd + "," + Nd + " L" + Vn + "," + Yn + P(x / 2, _ / 2, ul, Ll, Vd, Dr, !1).replace("M", "L") + " z") + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' />"; break; case "leftRightCircularArrow": case "chartPlus": case "chartStar": case "chartX": case "cornerTabs": case "flowChartOfflineStorage": case "folderCorner": case "funnel": case "lineInv": case "nonIsoscelesTrapezoid": case "plaqueTabs": case "squareTabs": case "upDownArrowCallout": console.log(L, " -unsupported shape type."); break; case void 0: default: console.warn("Undefine shape type.(" + L + ")") }f += "</svg>", f += "<div class='block content " + z(a, r, t, o) + "' _id='" + e + "' _idx='" + i + "' _type='" + o + "' _name='" + s + "' style='" + G(h, p, c) + j(h, p, c) + " z-index: " + l + ";transform: rotate(" + g + "deg);'>", void 0 !== a["p:txBody"] && (f += C(a["p:txBody"], a, r, t, o, n)), f += "</div>" } else if (void 0 !== v) { var Yd = J(v, ["a:pathLst"]), Wd = J(Yd, ["a:path", "attrs"]), Zd = (parseInt(Wd.w), parseInt(Wd.h), J(Yd, ["a:path", "a:close"])), Jd = J(Yd, ["a:path", "a:moveTo", "a:pt", "attrs"]), $d = (ka = "M" + 96 * parseInt(Jd.x) / 914400 + "," + 96 * parseInt(Jd.y) / 914400, J(Yd, ["a:path"])), Kd = $d["a:lnTo"], ah = $d["a:cubicBezTo"], rh = $d["a:arcTo"], th = []; if (void 0 !== Kd && Object.keys(Kd).forEach(function (a) { var r = Kd[a]["a:pt"]; void 0 !== r && Object.keys(r).forEach(function (a) { var t = {}, e = r[a], s = e.x, i = e.y, o = e.order; t.type = "lnto", t.order = o, t.x = s, t.y = i, th.push(t) }) }), void 0 !== ah && Object.keys(ah).forEach(function (a) { var r = ah[a]["a:pt"]; void 0 !== r && Object.keys(r).forEach(function (a) { var t = r[a]; Object.keys(t).forEach(function (a) { var r = {}, e = t[a], s = e.x, i = e.y, o = e.order; r.type = "cubicBezTo", r.order = o, r.x = s, r.y = i, th.push(r) }) }) }), void 0 !== rh) { var eh = rh.attrs, sh = eh.order, ih = eh.hR, oh = eh.wR, lh = eh.stAng, nh = eh.swAng, dh = 0, hh = 0, ph = J(rh, ["a:pt", "attrs"]); void 0 !== ph && (dh = ph.x, hh = ph.y); var ch = { type: "arcTo" }; ch.order = sh, ch.hR = ih, ch.wR = oh, ch.stAng = lh, ch.swAng = nh, ch.shftX = dh, ch.shftY = hh, th.push(ch) } var fh = th.slice(0); fh.sort(function (a, r) { return a.order - r.order }); for (var uh = 0; uh < fh.length;) { if ("lnto" == fh[uh].type) ka += "L" + 96 * parseInt(fh[uh].x) / 914400 + "," + 96 * parseInt(fh[uh].y) / 914400, uh++; else if ("cubicBezTo" == fh[uh].type) { ka += "C" + 96 * parseInt(fh[uh].x) / 914400 + "," + 96 * parseInt(fh[uh].y) / 914400 + " " + 96 * parseInt(fh[uh + 1].x) / 914400 + "," + 96 * parseInt(fh[uh + 1].y) / 914400 + " " + 96 * parseInt(fh[uh + 2].x) / 914400 + "," + 96 * parseInt(fh[uh + 2].y) / 914400, uh += 3 } else if ("arcTo" == fh[uh].type) { ih = 96 * parseInt(fh[uh].hR) / 914400; ka += P(oh = 96 * parseInt(fh[uh].wR) / 914400, ih, oh, ih, lh = parseInt(fh[uh].stAng) / 6e4, Dr = lh + (nh = parseInt(fh[uh].swAng) / 6e4), !1), uh++ } } f += "<path d='" + ka + "' fill='" + (T ? "url(#imgPtrn_" + u + ")" : S ? "url(#linGrd_" + u + ")" : D) + "' stroke='" + N.color + "' stroke-width='" + N.width + "' stroke-dasharray='" + N.strokeDasharray + "' ", void 0 !== Zd ? f += "/>" : (void 0 === O || "triangle" !== O.type && "arrow" !== O.type || (f += "marker-start='url(#markerTriangle_" + u + ")' "), void 0 === F || "triangle" !== F.type && "arrow" !== F.type || (f += "marker-end='url(#markerTriangle_" + u + ")' "), f += "/>"), f += "</svg>", f += "<div class='block content " + z(a, r, t, o) + "' _id='" + e + "' _idx='" + i + "' _type='" + o + "' _name='" + s + "' style='" + G(h, p, c) + j(h, p, c) + " z-index: " + l + ";transform: rotate(" + g + "deg);'>", void 0 !== a["p:txBody"] && (f += C(a["p:txBody"], a, r, t, o, n)), f += "</div>" } else f += "<div class='block content " + z(a, r, t, o) + "' _id='" + e + "' _idx='" + i + "' _type='" + o + "' _name='" + s + "' style='" + G(h, p, c) + j(h, p, c) + B(a, !1, "shape") + E(a, !1, n) + " z-index: " + l + ";transform: rotate(" + g + "deg);'>", void 0 !== a["p:txBody"] && (f += C(a["p:txBody"], a, r, t, o, n)), f += "</div>"; return f } function I(r, t, e) { var s = 1.5 * t, i = s; cy = s, notches = e, radiusO = s, radiusI = t, taperO = 50, taperI = 35, pi2 = 2 * Math.PI, angle = pi2 / (2 * notches), taperAI = angle * taperI * .005, taperAO = angle * taperO * .005, a = angle, toggle = !1; for (var o = " M" + (i + radiusO * Math.cos(taperAO)) + " " + (cy + radiusO * Math.sin(taperAO)); a <= pi2 + angle; a += angle)toggle ? (o += " L" + (i + radiusI * Math.cos(a - taperAI)) + "," + (cy + radiusI * Math.sin(a - taperAI)), o += " L" + (i + radiusO * Math.cos(a + taperAO)) + "," + (cy + radiusO * Math.sin(a + taperAO))) : (o += " L" + (i + radiusO * Math.cos(a - taperAO)) + "," + (cy + radiusO * Math.sin(a - taperAO)), o += " L" + (i + radiusI * Math.cos(a + taperAI)) + "," + (cy + radiusI * Math.sin(a + taperAI))), toggle = !toggle; return o += " " } function P(a, r, t, e, s, i, o) { var l, n = s; if (i >= s) for (; n <= i;) { var d = n * (Math.PI / 180), h = a + Math.cos(d) * t, p = r + Math.sin(d) * e; n == s && (l = " M" + h + " " + p), l += " L" + h + " " + p, n++ } else for (; n > i;) { d = n * (Math.PI / 180), h = a + Math.cos(d) * t, p = r + Math.sin(d) * e; n == s && (l = " M " + h + " " + p), l += " L " + h + " " + p, n-- } return l += o ? " z" : "" } function C(a, r, t, e, s, i) { var o = "", l = i.slideMasterTextStyles; if (void 0 === a) return o; if (a["a:p"].constructor === Array) for (var n = 0; n < a["a:p"].length; n++) { var d = (p = a["a:p"][n])["a:r"]; if (o += "<div  class='" + S(p, t, e, s, l) + "'>", o += x(p, r, t, e, s, i), void 0 === d) o += _(p, r, t, e, s, i); else if (d.constructor === Array) for (var h = 0; h < d.length; h++)o += _(d[h], r, t, e, s, i), void 0 !== p["a:br"] && (o += "<br>"); else o += _(d, r, t, e, s, i); o += "</div>" } else { var p; d = (p = a["a:p"])["a:r"]; if (o += "<div class='slide-prgrph " + S(p, t, e, s, l) + "'>", o += x(p, r, t, e, s, i), void 0 === d) o += _(p, r, t, e, s, i); else if (d.constructor === Array) for (h = 0; h < d.length; h++)o += _(d[h], r, t, e, s, i), void 0 !== p["a:br"] && (o += "<br>"); else o += _(d, r, t, e, s, i); o += "</div>" } return o } function x(a, r, t, e, s, i) { var o, l, n, d, h = i.slideMasterTextStyles, p = J(a, ["a:r"]); void 0 !== p && p.constructor === Array && (p = p[0]), void 0 !== p ? (o = T(p, r, s, h)[0], l = A(p, t, e, s, h)) : (o = T(a, r, s, h)[0], l = A(a, t, e, s, h)); var c = "", f = a["a:pPr"], u = J(f, ["attrs", "rtl"]), L = !1; void 0 !== u && "1" == u && (L = !0); var v = parseInt(J(f, ["attrs", "lvl"])); isNaN(v) && (v = 0); var m = J(f, ["a:buChar", "attrs", "char"]), g = "TYPE_NONE", b = J(f, ["a:buAutoNum", "attrs", "type"]), M = J(f, ["a:buBlip"]); if (void 0 !== m && (g = "TYPE_BULLET"), void 0 !== b && (g = "TYPE_NUMERIC"), void 0 !== M && (g = "TYPE_BULPIC"), "TYPE_NONE" != g) var k = J(f, ["a:buFont", "attrs"]); var y, w = J(f, ["a:buClr"]), I = "NoNe"; if (void 0 !== w && (I = Q(w)), n = "NoNe" == I ? o : "#" + I, void 0 !== (y = J(f, ["a:buSzPts", "attrs", "val"]))) d = parseInt(y) / 100 + "pt"; else if (void 0 !== (y = J(f, ["a:buSzPct", "attrs", "val"]))) { var P = parseInt(y) / 1e5, C = l.substr(0, l.length - 2); d = P * parseInt(C) + "pt" } else d = l; if ("TYPE_BULLET" == g) if (void 0 !== k) { var x = 96 * parseInt(J(f, ["attrs", "marL"])) / 914400, _ = parseInt(k.pitchFamily); isNaN(x) && (x = 31545600 / 914400), isNaN(_) && (_ = 0), c = "<span style='font-family: " + k.typeface + "; margin-left: " + x * v + "px; margin-right: " + _ + "px;color:" + n + ";font-size:" + d + ";", L && (c += " float: right;  direction:rtl"), c += "'>" + m + "</span>" } else c = "<span style='margin-left: " + (x = 31545600 / 914400 * v) + "px;'>" + m + "</span>"; else if ("TYPE_NUMERIC" == g) if (void 0 !== k) { x = 96 * parseInt(J(f, ["attrs", "marL"])) / 914400, _ = parseInt(k.pitchFamily); isNaN(x) && (x = 31545600 / 914400), isNaN(_) && (_ = 0), c = "<span style='margin-left: " + x * v + "px; margin-right: " + _ + "px;color:" + n + ";font-size:" + d + ";", c += L ? " float: right; direction:rtl;" : " float: left; direction:ltr;", c += "' data-bulltname = '" + b + "' data-bulltlvl = '" + v + "' class='numeric-bullet-style'></span>" } else c = "<span style='margin-left: " + (x = 31545600 / 914400 * v) + "px;", c += L ? " float: right; direction:rtl;" : " float: left; direction:ltr;", c += "' data-bulltname = '" + b + "' data-bulltlvl = '" + v + "' class='numeric-bullet-style'></span>"; else if ("TYPE_BULPIC" == g) { x = 96 * parseInt(J(f, ["attrs", "marL"])) / 914400, _ = 96 * parseInt(J(f, ["attrs", "marR"])) / 914400; isNaN(_) && (_ = 0), x = isNaN(x) ? 31545600 / 914400 : 0; var D, G = J(M, ["a:blip", "attrs", "r:embed"]); if (void 0 !== G) { var j = i.slideResObj[G].target, S = i.zip.file(j).asArrayBuffer(); D = "<img src='data:" + ra(j.split(".").pop()) + ";base64," + da(S) + "' style='width: 100%; height: 100%'/>" } void 0 === G && (D = "&#8227;"), c = "<span style='margin-left: " + x * v + "px; margin-right: " + _ + "px;width:" + d + ";display: inline-block; ", L && (c += " float: right;direction:rtl"), c += "'>" + D + "  </span>" } else c = "<span style='margin-left: " + 31545600 / 914400 * v + "px; margin-right: 0px;'></span>"; return c } function _(a, r, t, e, s, i) { var o = i.slideMasterTextStyles, n = a["a:t"]; "string" != typeof n && "string" != typeof (n = J(a, ["a:fld", "a:t"])) && (n = "&nbsp;"); var d = T(a, r, s, o), h = "color:" + d[0] + ";text-shadow:" + d[1] + ";font-size:" + A(a, t, e, s, o) + ";font-family:" + function (a, r, t) { var e = J(a, ["a:rPr", "a:latin", "attrs", "typeface"]); if (void 0 === e) { var s = J(l, ["a:theme", "a:themeElements", "a:fontScheme"]); e = J(s, "title" == r || "subTitle" == r || "ctrTitle" == r ? ["a:majorFont", "a:latin", "attrs", "typeface"] : ["a:minorFont", "a:latin", "attrs", "typeface"]) } return void 0 === e ? "inherit" : e }(a, s) + ";font-weight:" + function (a, r, t) { return void 0 !== a["a:rPr"] && "1" === a["a:rPr"].attrs.b ? "bold" : "initial" }(a) + ";font-style:" + function (a, r, t) { return void 0 !== a["a:rPr"] && "1" === a["a:rPr"].attrs.i ? "italic" : "normal" }(a) + ";text-decoration:" + function (a, r, t) { if (void 0 !== a["a:rPr"]) { var e = void 0 !== a["a:rPr"].attrs.u ? a["a:rPr"].attrs.u : "none", s = void 0 !== a["a:rPr"].attrs.strike ? a["a:rPr"].attrs.strike : "noStrike"; return "none" != e && "noStrike" == s ? "underline" : "none" == e && "noStrike" != s ? "line-through" : "none" != e && "noStrike" != s ? "underline line-through" : "initial" } return "initial" }(a) + ";text-align:" + function (a, r, t) { var e = J(a, ["a:pPr", "attrs", "algn"]), s = "initial"; if (void 0 !== e) switch (e) { case "l": s = "left"; break; case "r": s = "right"; break; case "ctr": s = "center"; break; case "just": case "dist": s = "justify"; break; default: s = "initial" }return s }(a) + ";vertical-align:" + function (a, r, t) { var e = J(a, ["a:rPr", "attrs", "baseline"]); return void 0 === e ? "baseline" : parseInt(e) / 1e3 + "%" }(a) + ";", p = J(a, ["a:rPr", "a:highlight"]); void 0 !== p && (h += "background-color:#" + Q(p) + ";", h += "Opacity:" + Y(p) + ";"); var c = ""; h in u ? c = u[h].name : (c = "_css_" + (Object.keys(u).length + 1), u[h] = { name: c, text: h }); var f = J(a, ["a:rPr", "a:hlinkClick", "attrs", "r:id"]); if (void 0 !== f) { var L = i.slideResObj[f].target; return "<span class='text-block " + c + "'><a href='" + (L = pa(L)) + "' target='_blank'>" + n.replace(/\s/i, "&nbsp;") + "</a></span>" } return "<span class='text-block " + c + "'>" + n.replace(/\s/i, "&nbsp;") + "</span>" } function D() { var a = ""; for (var r in u) { a += (L.slideMode && "revealjs" == L.slideType ? "section" : "div") + " ." + u[r].name + "{" + u[r].text + "}\n" } return L.slideMode && "divs2slidesjs" == L.slideType && (a += "#all_slides_warpper{margin-right: auto;margin-left: auto;padding-top:10px;width: " + p + "px;}\n"), a } function G(a, r, t) { var e = void 0, s = -1, i = -1; return void 0 !== a ? e = a["a:off"].attrs : void 0 !== r ? e = r["a:off"].attrs : void 0 !== t && (e = t["a:off"].attrs), void 0 === e ? "" : (s = 96 * parseInt(e.x) / 914400, i = 96 * parseInt(e.y) / 914400, isNaN(s) || isNaN(i) ? "" : "top:" + i + "px; left:" + s + "px;") } function j(a, r, t) { var e = void 0, s = -1, i = -1; return void 0 !== a ? e = a["a:ext"].attrs : void 0 !== r ? e = r["a:ext"].attrs : void 0 !== t && (e = t["a:ext"].attrs), void 0 === e ? "" : (s = 96 * parseInt(e.cx) / 914400, i = 96 * parseInt(e.cy) / 914400, isNaN(s) || isNaN(i) ? "" : "width:" + s + "px; height:" + i + "px;") } function S(a, r, t, e, s) { var i = J(a, ["a:pPr", "attrs", "algn"]); if (void 0 === i && void 0 === (i = J(r, ["p:txBody", "a:p", "a:pPr", "attrs", "algn"])) && void 0 === (i = J(t, ["p:txBody", "a:p", "a:pPr", "attrs", "algn"]))) switch (e) { case "title": case "subTitle": case "ctrTitle": i = J(s, ["p:titleStyle", "a:lvl1pPr", "attrs", "alng"]); break; default: i = J(s, ["p:otherStyle", "a:lvl1pPr", "attrs", "alng"]) }if (void 0 === i) { if ("title" == e || "subTitle" == e || "ctrTitle" == e) return "h-mid"; if ("sldNum" == e) return "h-right" } return "ctr" === i ? "h-mid" : "r" === i ? "h-right" : "h-left" } function z(a, r, t, e, s) { var i = J(a, ["p:txBody", "a:bodyPr", "attrs", "anchor"]); return void 0 === i && void 0 === (i = J(r, ["p:txBody", "a:bodyPr", "attrs", "anchor"])) && (i = J(t, ["p:txBody", "a:bodyPr", "attrs", "anchor"])), "ctr" === i ? "v-mid" : "b" === i ? "v-down" : "v-up" } function T(a, r, t, e) { var s, i, o, l = J(a, ["a:rPr"]); if (void 0 !== l) if ("SOLID_FILL" == (s = q(l))) i = Q(J(a, ["a:rPr", "a:solidFill"])); else if ("PATTERN_FILL" == s) { i = U(J(a, ["a:rPr", "a:pattFill"])) } else { var n = J(r, ["p:style", "a:fontRef"]); void 0 !== n && (i = Q(n)) } if (i = void 0 === i || "FFF" === i ? "#000" : "#" + i, void 0 !== J(a, ["a:rPr", "a:ln"])) { var d = B(a, !1, "text").split(" "), h = parseInt(d[0].substring(0, d[0].indexOf("pt"))) * (4 / 3) + "px", p = d[2]; o = "-" + h + " 0 " + p + ", 0 " + h + " " + p + ", " + h + " 0 " + p + ", 0 -" + h + " " + p + ";" } else o = "none"; return [i, o] } function A(a, r, t, e, s) { var i = void 0; if (void 0 !== a["a:rPr"] && (i = parseInt(a["a:rPr"].attrs.sz) / 100), isNaN(i) || void 0 === i) { var o = J(r, ["p:txBody", "a:lstStyle", "a:lvl1pPr", "a:defRPr", "attrs", "sz"]); i = parseInt(o) / 100 } if (isNaN(i) || void 0 === i) { if ("title" == e || "subTitle" == e || "ctrTitle" == e) o = J(s, ["p:titleStyle", "a:lvl1pPr", "a:defRPr", "attrs", "sz"]); else if ("body" == e) o = J(s, ["p:bodyStyle", "a:lvl1pPr", "a:defRPr", "attrs", "sz"]); else if ("dt" == e || "sldNum" == e) o = "1200"; else if (void 0 === e) o = J(s, ["p:otherStyle", "a:lvl1pPr", "a:defRPr", "attrs", "sz"]); i = parseInt(o) / 100 } return void 0 === J(a, ["a:rPr", "attrs", "baseline"]) || isNaN(i) || (i -= 10), isNaN(i) ? "inherit" : i + "pt" } function R(a) { var r = ""; void 0 !== a["a:bottom"] && (r += B({ "p:spPr": { "a:ln": a["a:bottom"]["a:ln"] } }, !1, "shape").replace("border", "border-bottom")); void 0 !== a["a:top"] && (r += B({ "p:spPr": { "a:ln": a["a:top"]["a:ln"] } }, !1, "shape").replace("border", "border-top")); void 0 !== a["a:right"] && (r += B({ "p:spPr": { "a:ln": a["a:right"]["a:ln"] } }, !1, "shape").replace("border", "border-right")); void 0 !== a["a:left"] && (r += B({ "p:spPr": { "a:ln": a["a:left"]["a:ln"] } }, !1, "shape").replace("border", "border-left")); return r } function B(a, r, t) { var e, s; "shape" == t ? (e = "border: ", s = a["p:spPr"]["a:ln"]) : "text" == t && (e = "", s = a["a:rPr"]["a:ln"]); var i = parseInt(J(s, ["attrs", "w"])) / 12700; isNaN(i) || i < 1 ? e += "1pt " : e += i + "pt "; var o = J(s, ["a:prstDash", "attrs", "val"]), l = "0"; switch (o) { case "solid": e += "solid", l = "0"; break; case "dash": e += "dashed", l = "5"; break; case "dashDot": e += "dashed", l = "5, 5, 1, 5"; break; case "dot": e += "dotted", l = "1, 5"; break; case "lgDash": e += "dashed", l = "10, 5"; break; case "lgDashDotDot": e += "dashed", l = "10, 5, 1, 5, 1, 5"; break; case "sysDash": e += "dashed", l = "5, 2"; break; case "sysDashDot": e += "dashed", l = "5, 2, 1, 5"; break; case "sysDashDotDot": e += "dashed", l = "5, 2, 1, 5, 1, 5"; break; case "sysDot": e += "dotted", l = "2, 5"; break; case void 0: default: e += "solid", l = "0" }if (void 0 === (n = J(s, ["a:solidFill", "a:srgbClr", "attrs", "val"])) && void 0 !== (d = J(s, ["a:solidFill", "a:schemeClr"]))) var n = W("a:" + J(d, ["attrs", "val"]), void 0); if (void 0 === n) { var d; if (void 0 !== (d = J(a, ["p:style", "a:lnRef", "a:schemeClr"]))) n = W("a:" + J(d, ["attrs", "val"]), void 0); if (void 0 !== n) { var h = J(d, ["a:shade", "attrs", "val"]); if (void 0 !== h) { h = parseInt(h) / 1e5; var p = new colz.Color("#" + n); p.setLum(p.hsl.l * h), n = p.hex.replace("#", "") } } } return e += " " + (n = void 0 === n ? r ? "none" : "#000" : "#" + n) + " ", r ? { color: n, width: i, type: o, strokeDasharray: l } : e + ";" } function N(a, r, t) { var e = ""; if (void 0 !== a) { for (var s = a["a:gradFill"], i = s["a:gsLst"]["a:gs"], o = [], l = [], n = 0; n < i.length; n++) { var d, h = ""; if (void 0 !== i[n]["a:srgbClr"]) void 0 === r && (h = J(i[n], ["a:srgbClr", "attrs", "val"])), d = J(i[n], ["a:srgbClr", "a:tint", "attrs", "val"]); else if (void 0 !== i[n]["a:schemeClr"]) { if (void 0 === r) h = W("a:" + J(i[n], ["a:schemeClr", "attrs", "val"]), t); d = J(i[n], ["a:schemeClr", "a:tint", "attrs", "val"]) } o[n] = h, l[n] = void 0 !== d ? parseInt(d) / 1e5 : 1 } var p = s["a:lin"], c = 90; void 0 !== p && (c = aa(p.attrs.ang) + 90), e = "background: linear-gradient(" + c + "deg,"; for (n = 0; n < i.length; n++)n == i.length - 1 ? e += void 0 === r ? "rgba(" + F(o[n]) + "," + l[n] + "));" : "rgba(" + F(r) + "," + l[n] + "));" : e += void 0 === r ? "rgba(" + F(o[n]) + "," + l[n] + "), " : "rgba(" + F(r) + "," + l[n] + "), " } else void 0 === r && (e = "rgba(" + F(r) + ",0);"); return e } function O(a, r, t) { return "background-image: url(" + H(r, a["a:blipFill"], t) + ");  z-index: " + a.attrs.order + ";" } function F(a) { var r = new ArrayBuffer(4); new DataView(r).setUint32(0, parseInt(a, 16), !1); var t = new Uint8Array(r); return t[1] + "," + t[2] + "," + t[3] } function E(a, r, t) { var e, s = q(J(a, ["p:spPr"])); if ("NO_FILL" == s) return r ? "none" : "background-color: initial;"; if ("SOLID_FILL" == s) e = Q(a["p:spPr"]["a:solidFill"]); else if ("GRADIENT_FILL" == s) { e = function (a) { for (var r = a["a:gsLst"]["a:gs"], t = [], e = 0; e < r.length; e++) { var s = Q(r[e]); if (void 0 !== r[e]["a:srgbClr"]) { var i = parseInt(J(a, ["a:srgbClr", "a:lumMod", "attrs", "val"])) / 1e5, o = parseInt(J(a, ["a:srgbClr", "a:lumOff", "attrs", "val"])) / 1e5; isNaN(i) && (i = 1), isNaN(o) && (o = 0), s = K(s, i, o) } else if (void 0 !== r[e]["a:schemeClr"]) { var i = parseInt(J(r[e], ["a:schemeClr", "a:lumMod", "attrs", "val"])) / 1e5, o = parseInt(J(r[e], ["a:schemeClr", "a:lumOff", "attrs", "val"])) / 1e5; isNaN(i) && (i = 1), isNaN(o) && (o = 0), s = K(s, i, o) } t[e] = s } var l = a["a:lin"], n = 0; void 0 !== l && (n = aa(l.attrs.ang) + 90); return { color: t, rot: n } }(a["p:spPr"]["a:gradFill"]) } else if ("PATTERN_FILL" == s) { e = U(a["p:spPr"]["a:pattFill"]) } else if ("PIC_FILL" == s) { e = H("slideBg", a["p:spPr"]["a:blipFill"], t) } void 0 === e && (e = Q(J(a, ["p:style", "a:fillRef"]))); if (void 0 !== e) { if ("GRADIENT_FILL" == s) { if (r) return e; for (var i = e.color, o = "background: linear-gradient(" + e.rot + "deg,", l = 0; l < i.length; l++)l == i.length - 1 ? o += i[l] + ");" : o += i[l] + ", "; return o } return "PIC_FILL" == s ? r ? e : "background-image:url(" + e + ");" : r ? e = new colz.Color(e).rgb.toString() : "background-color: #" + e + ";" } return r ? "none" : "background-color: initial;" } function q(a) { var r = ""; return void 0 !== a["a:noFill"] && (r = "NO_FILL"), void 0 !== a["a:solidFill"] && (r = "SOLID_FILL"), void 0 !== a["a:gradFill"] && (r = "GRADIENT_FILL"), void 0 !== a["a:pattFill"] && (r = "PATTERN_FILL"), void 0 !== a["a:blipFill"] && (r = "PIC_FILL"), r } function H(a, r, t) { var e, s = r["a:blip"].attrs["r:embed"]; if ("slideBg" == a ? e = J(t, ["slideResObj", s, "target"]) : "layoutBg" == a ? e = J(t, ["layoutResObj", s, "target"]) : "masterBg" == a && (e = J(t, ["masterResObj", s, "target"])), void 0 !== (e = pa(e))) { var i = e.split(".").pop(); if ("xml" != i) { var o = t.zip.file(e).asArrayBuffer(); return "data:" + ra(i) + ";base64," + da(o) } } } function U(a) { return Q(a["a:fgClr"]) } function Q(a) { if (void 0 !== a) { var r = "FFF"; if (void 0 !== a["a:srgbClr"]) r = J(a, ["a:srgbClr", "attrs", "val"]); else if (void 0 !== a["a:schemeClr"]) { r = W("a:" + J(a, ["a:schemeClr", "attrs", "val"]), void 0) } else if (void 0 !== a["a:scrgbClr"]) { var t = -1 != (i = a["a:scrgbClr"].attrs).r.indexOf("%") ? i.r.split("%").shift() : i.r, e = -1 != i.g.indexOf("%") ? i.g.split("%").shift() : i.g, s = -1 != i.b.indexOf("%") ? i.b.split("%").shift() : i.b; r = X(Number(t) / 100 * 255) + X(Number(e) / 100 * 255) + X(Number(s) / 100 * 255) } else if (void 0 !== a["a:prstClr"]) { r = function (a) { var r, t = ["AliceBlue", "AntiqueWhite", "Aqua", "Aquamarine", "Azure", "Beige", "Bisque", "Black", "BlanchedAlmond", "Blue", "BlueViolet", "Brown", "BurlyWood", "CadetBlue", "Chartreuse", "Chocolate", "Coral", "CornflowerBlue", "Cornsilk", "Crimson", "Cyan", "DarkBlue", "DarkCyan", "DarkGoldenRod", "DarkGray", "DarkGrey", "DarkGreen", "DarkKhaki", "DarkMagenta", "DarkOliveGreen", "DarkOrange", "DarkOrchid", "DarkRed", "DarkSalmon", "DarkSeaGreen", "DarkSlateBlue", "DarkSlateGray", "DarkSlateGrey", "DarkTurquoise", "DarkViolet", "DeepPink", "DeepSkyBlue", "DimGray", "DimGrey", "DodgerBlue", "FireBrick", "FloralWhite", "ForestGreen", "Fuchsia", "Gainsboro", "GhostWhite", "Gold", "GoldenRod", "Gray", "Grey", "Green", "GreenYellow", "HoneyDew", "HotPink", "IndianRed", "Indigo", "Ivory", "Khaki", "Lavender", "LavenderBlush", "LawnGreen", "LemonChiffon", "LightBlue", "LightCoral", "LightCyan", "LightGoldenRodYellow", "LightGray", "LightGrey", "LightGreen", "LightPink", "LightSalmon", "LightSeaGreen", "LightSkyBlue", "LightSlateGray", "LightSlateGrey", "LightSteelBlue", "LightYellow", "Lime", "LimeGreen", "Linen", "Magenta", "Maroon", "MediumAquaMarine", "MediumBlue", "MediumOrchid", "MediumPurple", "MediumSeaGreen", "MediumSlateBlue", "MediumSpringGreen", "MediumTurquoise", "MediumVioletRed", "MidnightBlue", "MintCream", "MistyRose", "Moccasin", "NavajoWhite", "Navy", "OldLace", "Olive", "OliveDrab", "Orange", "OrangeRed", "Orchid", "PaleGoldenRod", "PaleGreen", "PaleTurquoise", "PaleVioletRed", "PapayaWhip", "PeachPuff", "Peru", "Pink", "Plum", "PowderBlue", "Purple", "RebeccaPurple", "Red", "RosyBrown", "RoyalBlue", "SaddleBrown", "Salmon", "SandyBrown", "SeaGreen", "SeaShell", "Sienna", "Silver", "SkyBlue", "SlateBlue", "SlateGray", "SlateGrey", "Snow", "SpringGreen", "SteelBlue", "Tan", "Teal", "Thistle", "Tomato", "Turquoise", "Violet", "Wheat", "White", "WhiteSmoke", "Yellow", "YellowGreen"].indexOf(a); -1 != t && (r = ["f0f8ff", "faebd7", "00ffff", "7fffd4", "f0ffff", "f5f5dc", "ffe4c4", "000000", "ffebcd", "0000ff", "8a2be2", "a52a2a", "deb887", "5f9ea0", "7fff00", "d2691e", "ff7f50", "6495ed", "fff8dc", "dc143c", "00ffff", "00008b", "008b8b", "b8860b", "a9a9a9", "a9a9a9", "006400", "bdb76b", "8b008b", "556b2f", "ff8c00", "9932cc", "8b0000", "e9967a", "8fbc8f", "483d8b", "2f4f4f", "2f4f4f", "00ced1", "9400d3", "ff1493", "00bfff", "696969", "696969", "1e90ff", "b22222", "fffaf0", "228b22", "ff00ff", "dcdcdc", "f8f8ff", "ffd700", "daa520", "808080", "808080", "008000", "adff2f", "f0fff0", "ff69b4", "cd5c5c", "4b0082", "fffff0", "f0e68c", "e6e6fa", "fff0f5", "7cfc00", "fffacd", "add8e6", "f08080", "e0ffff", "fafad2", "d3d3d3", "d3d3d3", "90ee90", "ffb6c1", "ffa07a", "20b2aa", "87cefa", "778899", "778899", "b0c4de", "ffffe0", "00ff00", "32cd32", "faf0e6", "ff00ff", "800000", "66cdaa", "0000cd", "ba55d3", "9370db", "3cb371", "7b68ee", "00fa9a", "48d1cc", "c71585", "191970", "f5fffa", "ffe4e1", "ffe4b5", "ffdead", "000080", "fdf5e6", "808000", "6b8e23", "ffa500", "ff4500", "da70d6", "eee8aa", "98fb98", "afeeee", "db7093", "ffefd5", "ffdab9", "cd853f", "ffc0cb", "dda0dd", "b0e0e6", "800080", "663399", "ff0000", "bc8f8f", "4169e1", "8b4513", "fa8072", "f4a460", "2e8b57", "fff5ee", "a0522d", "c0c0c0", "87ceeb", "6a5acd", "708090", "708090", "fffafa", "00ff7f", "4682b4", "d2b48c", "008080", "d8bfd8", "ff6347", "40e0d0", "ee82ee", "f5deb3", "ffffff", "f5f5f5", "ffff00", "9acd32"][t]); return r }(a["a:prstClr"].attrs.val) } else if (void 0 !== a["a:hslClr"]) { var i = a["a:hslClr"].attrs, o = Number(i.hue) / 1e5, l = Number(-1 != i.sat.indexOf("%") ? i.sat.split("%").shift() : i.sat) / 100, n = Number(-1 != i.lum.indexOf("%") ? i.lum.split("%").shift() : i.lum) / 100, d = (i.hue, i.sat, i.lum, function (a, r, t) { var e, s, i, o, l; s = t <= .5 ? t * (r + 1) : t + r - t * r; return i = 255 * V(e = 2 * t - s, s, 2 + (a /= 60)), o = 255 * V(e, s, a), l = 255 * V(e, s, a - 2), { r: i, g: o, b: l } }(o, l, n)); r = X(d.r) + X(d.g) + X(d.b) } else if (void 0 !== a["a:sysClr"]) { var h = J(a, ["a:sysClr", "attrs", "lastClr"]); void 0 !== h && (r = h) } return r } } function X(a) { for (var r = a.toString(16); r.length < 2;)r = "0" + r; return r } function V(a, r, t) { return t < 0 && (t += 6), t >= 6 && (t -= 6), t < 1 ? (r - a) * t + a : t < 3 ? r : t < 4 ? (r - a) * (4 - t) + a : a } function Y(a) { if (void 0 !== a) { var r = 1; if (void 0 !== a["a:srgbClr"]) void 0 !== (t = J(a, ["a:srgbClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5); else if (void 0 !== a["a:schemeClr"]) { void 0 !== (t = J(a, ["a:schemeClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5) } else if (void 0 !== a["a:scrgbClr"]) { void 0 !== (t = J(a, ["a:scrgbClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5) } else if (void 0 !== a["a:prstClr"]) { void 0 !== (t = J(a, ["a:prstClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5) } else if (void 0 !== a["a:hslClr"]) { void 0 !== (t = J(a, ["a:hslClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5) } else if (void 0 !== a["a:sysClr"]) { var t; void 0 !== (t = J(a, ["a:sysClr", "a:tint", "attrs", "val"])) && (r = parseInt(t) / 1e5) } return r } } function W(a, r) { "" != n && void 0 !== n || (n = J(r, ["p:sldMaster", "p:clrMap", "attrs"])); var t = a.substr(2); if (void 0 !== n) switch (t) { case "tx1": case "tx2": case "bg1": case "bg2": a = "a:" + n[t] } else switch (t) { case "tx1": a = "a:dk1"; break; case "tx2": a = "a:dk2"; break; case "bg1": a = "a:lt1"; break; case "bg2": a = "a:lt2" }var e = J(l, ["a:theme", "a:themeElements", "a:clrScheme", a]), s = J(e, ["a:srgbClr", "attrs", "val"]); return void 0 === s && void 0 !== e && (s = J(e, ["a:sysClr", "attrs", "lastClr"])), s } function Z(a) { var r = new Array; if (void 0 === a) return r; if (void 0 !== a["c:xVal"]) { var t = new Array; $(a["c:xVal"]["c:numRef"]["c:numCache"]["c:pt"], function (a, r) { return t.push(parseFloat(a["c:v"])), "" }), r.push(t), t = new Array, $(a["c:yVal"]["c:numRef"]["c:numCache"]["c:pt"], function (a, r) { return t.push(parseFloat(a["c:v"])), "" }), r.push(t) } else $(a, function (a, t) { var e = new Array, s = J(a, ["c:tx", "c:strRef", "c:strCache", "c:pt", "c:v"]) || t, i = {}; return void 0 !== J(a, ["c:cat", "c:strRef", "c:strCache", "c:pt"]) ? $(a["c:cat"]["c:strRef"]["c:strCache"]["c:pt"], function (a, r) { return i[a.attrs.idx] = a["c:v"], "" }) : void 0 !== J(a, ["c:cat", "c:numRef", "c:numCache", "c:pt"]) && $(a["c:cat"]["c:numRef"]["c:numCache"]["c:pt"], function (a, r) { return i[a.attrs.idx] = a["c:v"], "" }), void 0 !== J(a, ["c:val", "c:numRef", "c:numCache", "c:pt"]) && $(a["c:val"]["c:numRef"]["c:numCache"]["c:pt"], function (a, r) { return e.push({ x: a.attrs.idx, y: parseFloat(a["c:v"]) }), "" }), r.push({ key: s, values: e, xlabels: i }), "" }); return r } function J(a, r) { if (r.constructor !== Array) throw Error("Error of path type! path is not array."); if (void 0 !== a) { for (var t = r.length, e = 0; e < t; e++)if (void 0 === (a = a[r[e]])) return; return a } } function $(a, r) { if (void 0 !== a) { var t = ""; if (a.constructor === Array) for (var e = a.length, s = 0; s < e; s++)t += r(a[s], s); else t += r(a, 0); return t } } function K(a, r, t) { var e = new colz.Color(a); return e.setLum(e.hsl.l * (1 + t)), e.rgb.toString() } function aa(a) { return "" == a || null == a ? 0 : Math.round(a / 6e4) } function ra(a) { var r = ""; switch (a.toLowerCase()) { case "jpg": case "jpeg": r = "image/jpeg"; break; case "png": r = "image/png"; break; case "gif": r = "image/gif"; break; case "emf": r = "image/x-emf"; break; case "wmf": r = "image/x-wmf"; break; case "svg": r = "image/svg+xml"; break; case "mp4": r = "video/mp4"; break; case "webm": r = "video/webm"; break; case "ogg": r = "video/ogg"; break; case "avi": r = "video/avi"; break; case "mpg": r = "video/mpg"; break; case "wmv": r = "video/wmv"; break; case "mp3": r = "audio/mpeg"; break; case "wav": r = "audio/wav"; break; case "emf": r = "image/emf"; break; case "wmf": r = "image/wmf" }return r } function ta(a) { for (var r = 0; r < a.length; r++)ea(a[r].data) } function ea(a) { var r = a.chartID, t = a.chartType, e = a.chartData, s = [], o = null; switch (t) { case "lineChart": s = e, (o = nv.models.lineChart().useInteractiveGuideline(!0)).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "barChart": s = e, (o = nv.models.multiBarChart()).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "pieChart": case "pie3DChart": s = e[0].values, o = nv.models.pieChart(); break; case "areaChart": s = e, (o = nv.models.stackedAreaChart().clipEdge(!0).useInteractiveGuideline(!0)).xAxis.tickFormat(function (a) { return e[0].xlabels[a] || a }); break; case "scatterChart": for (var l = 0; l < e.length; l++) { for (var n = [], d = 0; d < e[l].length; d++)n.push({ x: d, y: e[l][d] }); s.push({ key: "data" + (l + 1), values: n }) } (o = nv.models.scatterChart().showDistX(!0).showDistY(!0).color(d3.scale.category10().range())).xAxis.axisLabel("X").tickFormat(d3.format(".02f")), o.yAxis.axisLabel("Y").tickFormat(d3.format(".02f")) }null !== o && (d3.select("#" + r).append("svg").datum(s).transition().duration(500).call(o), nv.utils.windowResize(o.update), i = !0) } function sa(a) { for (var t = a, e = 0; e < t.length; e++) { var s = r(t[e]).find(".numeric-bullet-style"); if (s.length > 0) for (var i = "", o = "", l = 0, n = new Array, d = 0, h = new Array, p = 0; p < s.length; p++) { var c = r(s[p]).data("bulltname"), f = r(s[p]).data("bulltlvl"); 0 == l ? (i = c, o = f, n[d] = l, h[d] = c, l++) : c == i && f == o ? (i = c, o = f, l++, n[d] = l, h[d] = c) : c != i && f == o ? (i = c, o = f, n[++d] = l, h[d] = c, l = 1) : c != i && Number(f) > Number(o) ? (i = c, o = f, n[++d] = l, h[d] = c, l = 1) : c != i && Number(f) < Number(o) && (i = c, o = f, l = n[--d] + 1); var u = ia(h[d], l); r(s[p]).html(u) } } } function ia(a, r) { var t = ""; switch (a) { case "arabicPeriod": t = r + ". "; break; case "arabicParenR": t = r + ") "; break; case "alphaLcParenR": t = na(r, "lowerCase") + ") "; break; case "alphaLcPeriod": t = na(r, "lowerCase") + ". "; break; case "alphaUcParenR": t = na(r, "upperCase") + ") "; break; case "alphaUcPeriod": t = na(r, "upperCase") + ". "; break; case "romanUcPeriod": t = oa(r) + ". "; break; case "romanLcParenR": t = oa(r) + ") "; break; case "hebrew2Minus": t = la.format(r) + "-"; break; default: t = r }return t } function oa(a) { if (!+a) return !1; for (var r = String(+a).split(""), t = ["", "C", "CC", "CCC", "CD", "D", "DC", "DCC", "DCCC", "CM", "", "X", "XX", "XXX", "XL", "L", "LX", "LXX", "LXXX", "XC", "", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"], e = "", s = 3; s--;)e = (t[+r.pop() + 10 * s] || "") + e; return Array(+r.join("") + 1).join("M") + e } r("#" + s).prepend(r("<div></div>").attr({ class: "slides-loadnig-msg", style: "display:block; width:100%; color:white; background-color: #ddd;" }).html(r("<div></div>").attr({ class: "slides-loading-progress-bar", style: "width: 1%; background-color: #4775d1;" }).html("<span style='text-align: center;'>Loading... (1%)</span>"))), L.slideMode && (jQuery().divs2slides || jQuery.getScript("./js/divs2slides.js")), !1 !== L.jsZipV2 && (jQuery.getScript(L.jsZipV2), "yes" !== localStorage.getItem("isPPTXjsReLoaded") && (localStorage.setItem("isPPTXjsReLoaded", "yes"), location.reload())), L.keyBoardShortCut && r(document).bind("keydown", function (a) { a.preventDefault(); var r = a.keyCode; console.log(r, i), 116 != r || f || (f = !0, m(s, L)) }), FileReaderJS.setSync(!1), "" != L.pptxFileUrl ? JSZipUtils.getBinaryContent(L.pptxFileUrl, function (a, r) { var t = new Blob([r]), e = L.pptxFileUrl.split("."); e.pop(), t.name = e[0], FileReaderJS.setupBlob(t, { readAsDefault: "ArrayBuffer", on: { load: function (a, r) { v(a.target.result) } } }) }) : r(".slides-loadnig-msg").remove(), "" != L.fileInputId && r("#" + L.fileInputId).on("change", function (a) { e.html(""); var r = a.target.files[0]; "application/vnd.openxmlformats-officedocument.presentationml.presentation" == r.type ? FileReaderJS.setupBlob(r, { readAsDefault: "ArrayBuffer", on: { load: function (a, r) { v(a.target.result) } } }) : alert("This is not pptx file") }); var la = function (a) { a.slice().sort(function (a, r) { return r[1].length - a[1].length }); return { format: function (r) { var t = ""; return jQuery.each(a, function () { var a = this[0]; if (parseInt(a) > 0) for (; r >= a; r -= a)t += this[1]; else t = t.replace(a, this[1]) }), t } } }([[1e3, ""], [400, "ת"], [300, "ש"], [200, "ר"], [100, "ק"], [90, "צ"], [80, "פ"], [70, "ע"], [60, "ס"], [50, "נ"], [40, "מ"], [30, "ל"], [20, "כ"], [10, "י"], [9, "ט"], [8, "ח"], [7, "ז"], [6, "ו"], [5, "ה"], [4, "ד"], [3, "ג"], [2, "ב"], [1, "א"], [/יה/, "ט״ו"], [/יו/, "ט״ז"], [/([א-ת])([א-ת])$/, "$1״$2"], [/^([א-ת])$/, "$1׳"]]); function na(a, r) { a = Number(a) - 1; var t = ""; return "upperCase" == r ? t = ((a / 26 >= 1 ? String.fromCharCode(a / 26 + 64) : "") + String.fromCharCode(a % 26 + 65)).toUpperCase() : "lowerCase" == r && (t = ((a / 26 >= 1 ? String.fromCharCode(a / 26 + 64) : "") + String.fromCharCode(a % 26 + 65)).toLowerCase()), t } function da(a) { for (var r, t = "", e = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", s = new Uint8Array(a), i = s.byteLength, o = i % 3, l = i - o, n = 0; n < l; n += 3)t += e[(16515072 & (r = s[n] << 16 | s[n + 1] << 8 | s[n + 2])) >> 18] + e[(258048 & r) >> 12] + e[(4032 & r) >> 6] + e[63 & r]; return 1 == o ? t += e[(252 & (r = s[l])) >> 2] + e[(3 & r) << 4] + "==" : 2 == o && (t += e[(64512 & (r = s[l] << 8 | s[l + 1])) >> 10] + e[(1008 & r) >> 4] + e[(15 & r) << 2] + "="), t } function ha(a) { return a.substr(2 + (~-a.lastIndexOf(".") >>> 0)) } function pa(a) { var r = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#039;" }; return a.replace(/[&<>"']/g, function (a) { return r[a] }) } function ca(a, r) { "use strict"; function t() { for (var r = []; a[n];)if (a.charCodeAt(n) == h) { if (a.charCodeAt(n + 1) === u) return (n = a.indexOf(p, n)) + 1 && (n += 1), r; if (a.charCodeAt(n + 1) === L) { if (a.charCodeAt(n + 2) == f) { for (; -1 !== n && (a.charCodeAt(n) !== c || a.charCodeAt(n - 1) != f || a.charCodeAt(n - 2) != f || -1 == n);)n = a.indexOf(p, n + 1); -1 === n && (n = a.length) } else for (n += 2; a.charCodeAt(n) !== c && a[n];)n++; n++; continue } var t = i(); r.push(t) } else { var s = e(); s.trim().length > 0 && r.push(s), n++ } return r } function e() { var r = n; return -2 === (n = a.indexOf(d, n) - 1) && (n = a.length), a.slice(r, n + 1) } function s() { for (var r = n; -1 === g.indexOf(a[n]) && a[n];)n++; return a.slice(r, n) } function i() { var r = {}; n++, r.tagName = s(); for (var e = !1; a.charCodeAt(n) !== c && a[n];) { var i = a.charCodeAt(n); if (i > 64 && 91 > i || i > 96 && 123 > i) { for (var l = s(), d = a.charCodeAt(n); d && d !== v && d !== m && !(d > 64 && 91 > d || d > 96 && 123 > d) && d !== c;)n++, d = a.charCodeAt(n); if (e || (r.attributes = {}, e = !0), d === v || d === m) { var h = o(); if (-1 === n) return r } else h = null, n--; r.attributes[l] = h } n++ } if (a.charCodeAt(n - 1) !== u) if ("script" == r.tagName) { var p = n + 1; n = a.indexOf("<\/script>", n), r.children = [a.slice(p, n - 1)], n += 8 } else if ("style" == r.tagName) { p = n + 1; n = a.indexOf("</style>", n), r.children = [a.slice(p, n - 1)], n += 7 } else -1 == b.indexOf(r.tagName) && (n++, r.children = t()); else n++; return r } function o() { var r = a[n], t = ++n; return n = a.indexOf(r, t), a.slice(t, n) } function l() { var t = new RegExp("\\s" + r.attrName + "\\s*=['\"]" + r.attrValue + "['\"]").exec(a); return t ? t.index : -1 } var n = (r = r || {}).pos || 0, d = "<", h = "<".charCodeAt(0), p = ">", c = ">".charCodeAt(0), f = "-".charCodeAt(0), u = "/".charCodeAt(0), L = "!".charCodeAt(0), v = "'".charCodeAt(0), m = '"'.charCodeAt(0), g = "\n\t>/= ", b = ["img", "br", "input", "meta", "link"], M = null; if (void 0 !== r.attrValue) { r.attrName = r.attrName || "id"; for (M = []; -1 !== (n = l());)-1 !== (n = a.lastIndexOf("<", n)) && M.push(i()), a = a.substr(n), n = 0 } else M = r.parseNode ? i() : t(); return r.filter && (M = ca.filter(M, r.filter)), r.simplify && (M = ca.simplify(M)), M.pos = n, M } h = 1; ca.simplify = function (a) { var r = {}; if (void 0 === a) return {}; if (1 === a.length && "string" == typeof a[0]) return a[0]; for (var t in a.forEach(function (a) { if ("object" == typeof a) { r[a.tagName] || (r[a.tagName] = []); var t = ca.simplify(a.children || []); r[a.tagName].push(t), a.attributes && (t.attrs = a.attributes), void 0 === t.attrs ? t.attrs = { order: h } : t.attrs.order = h, h++ } }), r) 1 == r[t].length && (r[t] = r[t][0]); return r }, ca.filter = function (a, r) { var t = []; return a.forEach(function (a) { if ("object" == typeof a && r(a) && t.push(a), a.children) { var e = ca.filter(a.children, r); t = t.concat(e) } }), t }, ca.stringify = function (a) { function r(a) { if (a) for (var r = 0; r < a.length; r++)"string" == typeof a[r] ? e += a[r].trim() : t(a[r]) } function t(a) { for (var t in e += "<" + a.tagName, a.attributes) e += null === a.attributes[t] ? " " + t : -1 === a.attributes[t].indexOf('"') ? " " + t + '="' + a.attributes[t].trim() + '"' : " " + t + "='" + a.attributes[t].trim() + "'"; e += ">", r(a.children), e += "</" + a.tagName + ">" } var e = ""; return r(a), e }, ca.toContentString = function (a) { if (Array.isArray(a)) { var r = ""; return a.forEach(function (a) { r = (r += " " + ca.toContentString(a)).trim() }), r } return "object" == typeof a ? ca.toContentString(a.children) : " " + a }, ca.getElementById = function (a, r, t) { var e = ca(a, { attrValue: r, simplify: t }); return t ? e : e[0] }, ca.getElementsByClassName = function (a, r, t) { return ca(a, { attrName: "class", attrValue: "[a-zA-Z0-9-s ]*" + r + "[a-zA-Z0-9-s ]*", simplify: t }) }, ca.parseStream = function (a, r) { if ("function" == typeof r && (cb = r, r = 0), "string" == typeof r && (r = r.length + 2), "string" == typeof a) { var t = require("fs"); a = t.createReadStream(a, { start: r }), r = 0 } var e = r, s = ""; return a.on("data", function (r) { 0, s += r; for (var t = 0; ;) { e = s.indexOf("<", e) + 1; var i = ca(s, { pos: e, parseNode: !0 }); if ((e = i.pos) > s.length - 1 || t > e) return void (t && (s = s.slice(t), e = 0, t = 0)); a.emit("xml", i), t = e } s = s.slice(e), e = 0 }), a.on("end", function () { console.log("end") }), a }, "object" == typeof module && (module.exports = ca) }, function (a) { "object" == typeof exports ? module.exports = a() : "function" == typeof define && define.amd ? define(a) : "undefined" != typeof window ? window.JSZipUtils = a() : "undefined" != typeof global ? global.JSZipUtils = a() : "undefined" != typeof self && (self.JSZipUtils = a()) }(function () { return function a(r, t, e) { function s(o, l) { if (!t[o]) { if (!r[o]) { var n = "function" == typeof require && require; if (!l && n) return n(o, !0); if (i) return i(o, !0); throw new Error("Cannot find module '" + o + "'") } var d = t[o] = { exports: {} }; r[o][0].call(d.exports, function (a) { var t = r[o][1][a]; return s(t || a) }, d, d.exports, a, r, t, e) } return t[o].exports } for (var i = "function" == typeof require && require, o = 0; o < e.length; o++)s(e[o]); return s }({ 1: [function (a, r) { "use strict"; function t() { try { return new window.XMLHttpRequest } catch (a) { } } var e = { _getBinaryFromXHR: function (a) { return a.response || a.responseText } }, s = window.ActiveXObject ? function () { return t() || function () { try { return new window.ActiveXObject("Microsoft.XMLHTTP") } catch (a) { } }() } : t; e.getBinaryContent = function (a, r) { try { var t = s(); t.open("GET", a, !0), "responseType" in t && (t.responseType = "arraybuffer"), t.overrideMimeType && t.overrideMimeType("text/plain; charset=x-user-defined"), t.onreadystatechange = function () { var s, i; if (4 === t.readyState) if (200 === t.status || 0 === t.status) { s = null, i = null; try { s = e._getBinaryFromXHR(t) } catch (a) { i = new Error(a) } r(i, s) } else r(new Error("Ajax error for " + a + " : " + this.status + " " + this.statusText), null) }, t.send() } catch (a) { r(new Error(a), null) } }, r.exports = e }, {}] }, {}, [1])(1) }), function (a, r) { if ("function" == typeof define) define(r); else if ("undefined" != typeof module && module.exports) module.exports = r(); else { var t = r(), e = this, s = e.colz; t.noConflict = function () { return e.colz = s, t }, e.colz = t } }(0, function () { var a, r, t, e, s, i, o, l, n, d = Math.round, h = h || {}; (a = h.Rgb = function (a) { this.r = a[0], this.g = a[1], this.b = a[2] }).prototype.toString = function () { return "rgb(" + this.r + "," + this.g + "," + this.b + ")" }, (r = h.Rgba = function (a) { this.r = a[0], this.g = a[1], this.b = a[2], this.a = a[3] }).prototype.toString = function () { return "rgba(" + this.r + "," + this.g + "," + this.b + "," + this.a + ")" }, (t = h.Hsl = function (a) { this.h = a[0], this.s = a[1], this.l = a[2] }).prototype.toString = function () { return "hsl(" + this.h + "," + this.s + "%," + this.l + "%)" }, (e = h.Hsla = function (a) { this.h = a[0], this.s = a[1], this.l = a[2], this.a = a[3] }).prototype.toString = function () { return "hsla(" + this.h + "," + this.s + "%," + this.l + "%," + this.a + ")" }; var p = (s = h.Color = function () { this.hex = this.r = this.g = this.b = this.h = this.s = this.l = this.a = this.hsl = this.hsla = this.rgb = this.rgba = null, this.init(arguments) }).prototype; p.init = function (s) { "string" == typeof s[0] && ("#" !== s[0].charAt(0) && (s[0] = "#" + s[0]), s[0].length < 7 && (s[0] = "#" + s[0][1] + s[0][1] + s[0][2] + s[0][2] + s[0][3] + s[0][3]), this.hex = s[0].toLowerCase(), this.rgb = new a(o(this.hex)), this.r = this.rgb.r, this.g = this.rgb.g, this.b = this.rgb.b, this.a = 1, this.rgba = new r([this.r, this.g, this.b, this.a])), "number" == typeof s[0] && (this.r = s[0], this.g = s[1], this.b = s[2], void 0 === s[3] ? this.a = 1 : this.a = s[3], this.rgb = new a([this.r, this.g, this.b]), this.rgba = new r([this.r, this.g, this.b, this.a]), this.hex = n([this.r, this.g, this.b])), s[0] instanceof Array && (this.r = s[0][0], this.g = s[0][1], this.b = s[0][2], void 0 === s[0][3] ? this.a = 1 : this.a = s[0][3], this.rgb = new a([this.r, this.g, this.b]), this.rgba = new r([this.r, this.g, this.b, this.a]), this.hex = n([this.r, this.g, this.b])), this.hsl = new t(h.rgbToHsl([this.r, this.g, this.b])), this.h = this.hsl.h, this.s = this.hsl.s, this.l = this.hsl.l, this.hsla = new e([this.h, this.s, this.l, this.a]) }, p.setHue = function (a) { this.h = a, this.hsl.h = a, this.hsla.h = a, this.updateFromHsl() }, p.setSat = function (a) { this.s = a, this.hsl.s = a, this.hsla.s = a, this.updateFromHsl() }, p.setLum = function (a) { this.l = a, this.hsl.l = a, this.hsla.l = a, this.updateFromHsl() }, p.setAlpha = function (a) { this.a = a, this.hsla.a = a, this.rgba.a = a }, p.updateFromHsl = function () { this.rgb = null, this.rgb = new a(h.hslToRgb([this.h, this.s, this.l])), this.r = this.rgb.r, this.g = this.rgb.g, this.b = this.rgb.b, this.rgba.r = this.rgb.r, this.rgba.g = this.rgb.g, this.rgba.b = this.rgb.b, this.hex = null, this.hex = n([this.r, this.g, this.b]) }, h.randomColor = function () { var a = "#" + Math.random().toString(16).slice(2, 8); return new s(a) }, o = h.hexToRgb = function (a) { var r = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a); return r ? [parseInt(r[1], 16), parseInt(r[2], 16), parseInt(r[3], 16)] : null }, l = h.componentToHex = function (a) { var r = a.toString(16); return 1 === r.length ? "0" + r : r }, n = h.rgbToHex = function () { var a, r, t, e; return (a = arguments).length > 1 ? (r = a[0], t = a[1], e = a[2]) : (r = a[0][0], t = a[0][1], e = a[0][2]), "#" + l(r) + l(t) + l(e) }, h.rgbToHsl = function () { var a, r, t, e, s, i, o, l, n, h; if ("number" == typeof (a = arguments)[0] ? (r = a[0], t = a[1], e = a[2]) : (r = a[0][0], t = a[0][1], e = a[0][2]), r /= 255, t /= 255, e /= 255, o = ((n = Math.max(r, t, e)) + (h = Math.min(r, t, e))) / 2, n === h) s = i = 0; else { switch (l = n - h, i = o > .5 ? l / (2 - n - h) : l / (n + h), n) { case r: s = (t - e) / l + (t < e ? 6 : 0); break; case t: s = (e - r) / l + 2; break; case e: s = (r - t) / l + 4 }s /= 6 } return [s = d(360 * s), i = d(100 * i), o = d(100 * o)] }, h.hue2rgb = function (a, r, t) { return t < 0 && (t += 1), t > 1 && (t -= 1), t < 1 / 6 ? a + 6 * (r - a) * t : t < .5 ? r : t < 2 / 3 ? a + (r - a) * (2 / 3 - t) * 6 : a }, h.hslToRgb = function () { var a, r, t, e, s, i, o, l, n; return "number" == typeof (a = arguments)[0] ? (s = a[0] / 360, i = a[1] / 100, o = a[2] / 100) : (s = a[0][0] / 360, i = a[0][1] / 100, o = a[0][2] / 100), 0 === i ? r = t = e = o : (n = 2 * o - (l = o < .5 ? o * (1 + i) : o + i - o * i), r = h.hue2rgb(n, l, s + 1 / 3), t = h.hue2rgb(n, l, s), e = h.hue2rgb(n, l, s - 1 / 3)), [d(255 * r), d(255 * t), d(255 * e)] }, h.rgbToHsb = function (a, r, t) { var e, s, i, o, l, n; if (a /= 255, r /= 255, t /= 255, l = e = Math.max(a, r, t), n = e - (s = Math.min(a, r, t)), o = 0 === e ? 0 : n / e, e === s) i = 0; else { switch (e) { case a: i = (r - t) / n + (r < t ? 6 : 0); break; case r: i = (t - a) / n + 2; break; case t: i = (a - r) / n + 4 }i /= 6 } return [i = d(360 * i), o = d(100 * o), l = d(100 * l)] }, h.hsbToRgb = function (a, r, t) { var e, s, i, o, l, n, d, h; return 0 === t ? [0, 0, 0] : (n = (t /= 100) * (1 - (r /= 100)), d = t * (1 - r * (l = (a /= 60) - (o = Math.floor(a)))), h = t * (1 - r * (1 - l)), 0 === o ? (e = t, s = h, i = n) : 1 === o ? (e = d, s = t, i = n) : 2 === o ? (e = n, s = t, i = h) : 3 === o ? (e = n, s = d, i = t) : 4 === o ? (e = h, s = n, i = t) : 5 === o && (e = t, s = n, i = d), [e = Math.floor(255 * e), s = Math.floor(255 * s), i = Math.floor(255 * i)]) }, h.hsbToHsl = function (a, r, t) { return h.rgbToHsl(h.hsbToRgb(a, r, t)) }, h.hsvToHsl = h.hsbToHsl, h.hsvToRgb = h.hsbToRgb; var c = (i = h.ColorScheme = function (a, r) { this.palette = [], void 0 === r && a instanceof Array ? this.createFromColors(a) : this.createFromAngles(a, r) }).prototype; return c.createFromColors = function (a) { for (var r in a) a.hasOwnProperty(r) && this.palette.push(new s(a[r])); return this.palette }, c.createFromAngles = function (a, r) { for (var t in this.palette.push(new s(a)), r) if (r.hasOwnProperty(t)) { var e = (this.palette[0].h + r[t]) % 360; this.palette.push(new s(h.hslToRgb([e, this.palette[0].s, this.palette[0].l]))) } return this.palette }, i.Compl = function (a) { return new i(a, [180]) }, i.Triad = function (a) { return new i(a, [120, 240]) }, i.Tetrad = function (a) { return new i(a, [60, 180, 240]) }, i.Analog = function (a) { return new i(a, [-45, 45]) }, i.Split = function (a) { return new i(a, [150, 210]) }, i.Accent = function (a) { return new i(a, [-45, 45, 180]) }, h }) }(jQuery);