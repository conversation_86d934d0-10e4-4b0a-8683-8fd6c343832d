import request from '@/utils/request'

// 查询地下工程评估列表
export function listData(query) {
  return request({
    url: '/project/gcUndergroundAssessData/list',
    method: 'get',
    params: query
  })
}

// 查询地下工程评估详细
export function getData(id) {
  return request({
    url: '/project/gcUndergroundAssessData/' + id,
    method: 'get'
  })
}

// 新增地下工程评估
export function addData(data) {
  return request({
    url: '/project/gcUndergroundAssessData',
    method: 'post',
    data: data
  })
}

// 修改地下工程评估
export function updateData(data) {
  return request({
    url: '/project/gcUndergroundAssessData',
    method: 'put',
    data: data
  })
}

// 删除地下工程评估
export function delData(id) {
  return request({
    url: '/project/gcUndergroundAssessData/' + id,
    method: 'delete'
  })
}


// 地下工程评估数据导入
export function importFile(data) {
  return request({
    url: '/project/gcUndergroundAssessData/import',
    method: 'post',
    data: data
  })
}

// 地下工程评估导入数据提交
export function importDataSubmit(data) {
  return request({
    url: '/project/gcUndergroundAssessData/importDataSubmit',
    method: 'post',
    data: data
  })
}
