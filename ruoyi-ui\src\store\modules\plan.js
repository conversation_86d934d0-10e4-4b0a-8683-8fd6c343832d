import {allTemplate} from '@/api/plan/template'
import {cityCompanyList, deptRoletreeselect, cityCompanyArr} from '@/api/system/dept' //  城市公司
import {latestVersionList} from '@/api/plan/feedback' //  最新版本计划编制列表
import {listProject} from '@/api/plan/project' //  项目列表
const state = {
  allTemplateList: [], // 计划模板列表
  deptList: [], // 城市公司列表
  latestVersionList: [], // 最新版本计划编制列表
  departList: [],// 部门树状数据
  projectList: [], // 项目列表
  deptReportList: [],  // 销售日报部门列表
}
const mutations = {
  SET_ALL_TEMPLATE_LIST (state, payload) {
    state.allTemplateList = payload
  },
  SET_DEPT_LIST (state, payload) {
    state.deptList = payload
  },
  SET_LATEST_VERSION_LIST (state, payload) {
    state.latestVersionList = payload
  },
  SET_DEPART_LIST (state, payload) {
    state.departList = payload
  },
  SET_PROJECT_LIST (state, payload) {
    state.projectList = payload
  },
  SET_DEPT_REPORT_LIST (state, payload) {
    state.deptReportList = payload

  }
}
const actions = {
  fetchAllTemplateData ({ commit }, payload = {}) {
    return allTemplate(payload).then(response => {
      commit('SET_ALL_TEMPLATE_LIST', response.data || [])
    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  },
  fetchDeptList ({ commit }) {
    return cityCompanyList().then(response => {
      commit('SET_DEPT_LIST', response.data || [])

    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  },
  fetchDeptListOfReport ({ commit }) {
    return cityCompanyArr().then(response => {
      // 返回数据由对象数组修改为名称数组，需要做数据过滤
      let newData = (response.data || []).map(item => {
        return {
          deptName: item,
          children: []
        }
      })

      commit('SET_DEPT_REPORT_LIST', newData)
    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  },
  fetchProjectList ({ commit }, payload = {}) {
    return listProject(payload).then(response => {
      commit('SET_PROJECT_LIST', response.rows || [])
    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  },
  clearProjectList ({ commit }) {
    commit('SET_PROJECT_LIST', [])
  },
  fetchLatestVersionList ({ commit }) {
    latestVersionList().then(response => {
      commit('SET_LATEST_VERSION_LIST', response.rows || [])
    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  },
  fetchDepartList ({ commit }) {
    deptRoletreeselect().then(response => {
      commit('SET_DEPART_LIST', response.data || [])
    }).catch(error => {
      console.error('Error fetching data:', error)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
