var scale = 1;
var rich = {
  yellow: {
    color: "#ffc72b",
    fontSize: 30 * scale,
    padding: [5, 4],
    align: 'center'
  },
  total: {
    color: "#ffc72b",
    fontSize: 40 * scale,
    align: 'center'
  },
  white: {
    color: "#fff",
    align: 'center',
    fontSize: 14 * scale,
    padding: [21, 0]
  },
  blue: {
    color: '#49dff0',
    fontSize: 16 * scale,
    align: 'center'
  },
  hr: {
    borderColor: '#0b5263',
    width: '100%',
    borderWidth: 1,
    height: 0,
  }
}

class PieOption {
    constructor(xAxisData = [], seriesData = []) {
        this.option = {
          grid: {
            top: 0,
            left: 0,
            right: 0,
            bottom: 0
          },
            tooltip: {
              show: true,
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: []
            },
            series: [
              {
                name: '',
                type: 'pie',
                center: ['50%', '50%'], // 设置饼图的中心位置
                radius:  ['40%', '55%'],
                avoidLabelOverlap: true,

                emphasis: {
                  number: 1.1 * scale,
                  disabled: true,
                  scale: false,
                  scaleSize: 10 * scale,
                  label: {
                    show: true,
                    fontSize: `${20 * scale}`,
                    fontWeight: 'bold'
                  }
                },
                /*labelLine: {
                  show: true
                },
                label: {
                  show: true,
                  position: 'outside'
                },*/
                label: {
                  show: false
                },
                labelLine: {
                  normal: {
                    // length: 55 * scale,
                    // length2: 0,
                    lineStyle: {
                      // color: '#0b5263'
                      width: 1 * scale
                    }
                  }
                },
                data: [
                  // { value: 100, name: '住宅' },
                  // { value: 2000, name: '车位' },
                  // { value: 200, name: '办公' },
                  // { value: 20, name: '商业' }
                ]
              }
            ]
      }
    }

    getOption(){
        return this.option
    }
    setColor(color){
        this.option.color = color
    }
}

export default PieOption
