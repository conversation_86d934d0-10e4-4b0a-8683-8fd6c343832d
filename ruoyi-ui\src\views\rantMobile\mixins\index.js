/**
 * rantMobile共用mixin
 */
// import {getDicts} from '@/api/rantMobile/common'
import {getFile, getMobileConfigKey, getDicts} from '@/api/rantMobile/common'
export default {
  data() {
    return {
      classifyOptions: [],
      mattersTypeOptions: [],
      mattersTypeOptionsLabel: [],
      classifyOptionsLabel: [],
      file: {},
      isEnable: null,
      limitDay: null,
      minDate: null
    }
  },
  computed: {
    pickerOptions() {
      return this.isEnable === 1 ? {
        disabledDate: (time) => {
          const oneWeekAgo = new Date();
          oneWeekAgo.setTime(oneWeekAgo.getTime() - this.limitDay * 24 * 3600 * 1000);
          return time.getTime() < oneWeekAgo.getTime();
        }
      } : {};
    }
  },
  async created() {
    await this.getLimitDayIsEnable();
  },
  mounted() {
  },
  methods: {
    async getLimitDayIsEnable() {
      let isEnable = await this.getMobileConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
      let limitDay = await this.getMobileConfigKey("rant.finish.date.limit.day"); // 设置限制天数
      this.isEnable = parseInt(isEnable.msg);
      this.limitDay = parseInt(limitDay.msg);
      this.minDate = new Date(new Date().getTime() - this.limitDay * 24 * 60 * 60 * 1000)
    },
    handleDownload(file) {
      this.file = file
      // 显示下载加载状态
      const loading = this.$loading({
        text: '正在下载文件，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 通过 API 获取文件流
      getFile(this.file.url).then(response => {
        console.log('File response received for download')

        // 创建 Blob 对象
        const blob = new Blob([response], {
          type: 'application/octet-stream'
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = this.file.name

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        // 触发下载事件
        this.$emit('download', {
          fileUrl: this.file.url,
          fileName: this.file.name
        })

        loading.close()
        this.$message.success('文件下载成功')
      }).catch(error => {
        console.error('下载失败:', error)
        loading.close()
        this.$message.error('下载失败: ' + (error.message || '网络错误'))
      })
    },
    async fetchMattersType(){
      this.mattersTypeOptions = await this.fetchDicts('rant_matters_type')
      this.mattersTypeOptionsLabel = this.mattersTypeOptions.map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        raw: {
          listClass: item.listClass || 'default',
          cssClass: item.cssClass || ''
        }
      }))
    },
    async fetchRantClassify(){
      this.classifyOptions = await this.fetchDicts('rant_classify')
      this.classifyOptionsLabel = this.classifyOptions.map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        raw: {
          listClass: item.listClass || 'default',
          cssClass: item.cssClass || ''
        }
      }))
    },
    async filterRantClassify(value){
      const data = await this.fetchDicts('rant_classify')
      this.classifyOptions = data.map(item => ({
        text: item.dictLabel || item.label,
        value: item.dictValue || item.value || item.dictLabel || item.label
      }))
    },
    /**
     * 获取字典数据
     * @param {string} type 字典类型
     * @returns {Promise<Array>} 字典数据
     */
    async fetchDicts(type){
      const res = await getDicts(type)
      if (res.code === 200) {
        return res.data
      }
    },

  }
}
