
8965c6a76d928bdadfa25fdd291c44566d4d7e36	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.fa5c0b843087fb3f16e9.hot-update.js\",\"contentHash\":\"d3db6e7c998ed97b26736f267b9d72c3\"}","integrity":"sha512-SLzIb5F00czPByxUYaPEi97IzitKY/L3IR7uzsOGYVNSPiWpwhFzwaay6XzMbQ4hzspcLh50VgT0ksqrtthCEg==","time":1754311551750,"size":77984}