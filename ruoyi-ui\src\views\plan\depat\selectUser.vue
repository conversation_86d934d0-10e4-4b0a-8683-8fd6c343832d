<template>
  <!-- 授权用户 -->
  <el-dialog
    v-if="visible"
    title="选择用户"
    :visible.sync="visible"
    width="900px"
    top="5vh"
    append-to-body
  >
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
        >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        v-loading="loading"
        ref="table"
        :data="userList"
        @select="selectRow"
        :row-key="getRowKey"
        @select-all="onSelectAll"
        height="260px"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          label="用户名称"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户昵称"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="邮箱"
          prop="email"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="手机"
          prop="phonenumber"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_normal_disable"
              :value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { pagesUser } from "@/api/system/user"

  export default {
    dicts: ['sys_normal_disable'],
    props: {
      // 角色编号
      roleId: {
        type: [Number, String]
      },
      selectUsers: {
        type: Array,
        default: []
      },
    },
    mounted () {
    },
    data () {
      return {
        loading: false,
        // 遮罩层
        visible: false,
        // 选中数组值
        users: [],
        // 总条数
        total: 0,
        // 未授权用户数据
        userList: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 200,
          roleId: undefined,
          userName: undefined,
          phonenumber: undefined
        },
        userIds: []
      }
    },
    methods: {
      getUserIds () {
        this.userIds = this.users.map(item => Number(item.userId))
      },
      getRowKey (row) {
        return row.userId
      },
      onSelectAll (selection) {
        //全选
        if (selection.length > 0) {
          selection.forEach(item => {
            if (!this.userIds.includes(item.userId)) {
              this.users.push(item)
            }
          })
        } else {
          //取消
          this.userList.forEach(item => {
              this.users.forEach((it, index) => {
                if (item.usrId == it.userId) {
                  this.users.splice(index, 1)
                }
              })
            }
          )
        }
        this.users = [...this.users]
        this.getUserIds()
      },

      selectRow (section, row) {
        let { userId } = row
        //已存在则表示取消
        let index = this.users.findIndex(i => i.userId === userId)
        if (index > -1) {
          this.users.splice(index, 1)
        } else {
          this.users.push(row)
        }
      },
      // 显示弹框
      show () {
        this.users = [...this.selectUsers]
        //获取ID数组 用于勾选
        this.getUserIds()
        this.queryParams.roleId = this.roleId
        this.visible = true
        this.$nextTick(() => {
          this.getList()
        });
        /*this.getList()
        this.visible = true*/
      },
      // 查询表数据
      getList () {
        this.loading = true
        pagesUser(this.queryParams).then(res => {
          this.loading = false
          this.userList = res.rows
          this.total = res.total
          this.$nextTick(() => {
            this.userList.filter(item => {
              if (this.userIds.includes(item.userId)) {
                this.$refs.table.toggleRowSelection(item, true)
              }
            })
          })
        }).catch(err => {
          this.loading = false

        })
      },
      /** 搜索按钮操作 */
      handleQuery () {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery () {
        this.resetForm("queryForm")
        this.handleQuery()
      },
      /** 选择授权用户操作 */
      handleSelectUser () {
        if (!this.users.length) {
          this.$modal.msgError("请选择用户")
          return
        }
        this.$emit('selectUser', this.users)
        this.resetQuery()
        this.visible = false
      }
    }
  };
</script>
