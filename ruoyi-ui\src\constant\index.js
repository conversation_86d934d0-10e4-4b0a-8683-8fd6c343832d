
export const statusOption = [ //  节点成果状态
  { label: '禁用', value: 1, type: 'danger'},
  { label: '启用', value: 0, type: 'success'},
];

export const fillFlagOption = [ //  节点必填状态
  { label: '否', value: 1, type: 'danger'},
  { label: '是', value: 0, type: 'success'},
];

export const nodeStatusOption = [
  // 0-未到期 type=null 1-即将到期 type=warning  2-按期完成 type=success  3-延期完成 type=warning  4-延期未完成 type=danger
  {label: '未到期', value: 0, type: null},
  {label: '即将到期', value: 1, type: 'info', color: 'yellow'},
  {label: '按期完成', value: 2, type: 'success'},
  {label: '延期完成', value: 3, type: 'danger'},
  {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
]

export const feedbackFlowStatusOption = [
  // 反流程态 (0-未开始 1-审批中 2-审批通过 3-审批拒绝 4-作废)
  {label: '未开始', value: 0, type: 'info'},
  {label: '审批中', value: 1, type: 'primary'},
  {label: '审批通过', value: 2, type: 'success'},
  {label: '审批拒绝', value: 3, type: 'danger'},
  {label: '作废', value: 4, type: 'warning'},
]

export const planStatusOption = [
  // 0-草稿 type=null 1-审批中 type=warning  2-生效 type=success  3-作废失效 type=warning  4-审批驳回 type=danger 5-待办 type=primary
  {label: '草稿', value: 0, type: 'info'},
  {label: '审批中', value: 1, type: null},
  {label: '生效', value: 2, type: 'success'},
  {label: '作废失效', value: 3, type: 'warning'},
  {label: '审批驳回', value: 4, type: 'danger'},
  {label: '待发', value: 5, type: 'danger'},
]

export const isPrivateOption = [
  // true-是  false-否
  {label: '是', value: 1, type: 'success'},
  {label: '否', value: 0, type: 'danger'},
]

export const adminFlagOption = [
  // true-是  false-否
  {label: '是', value: 1, type: 'success'},
  {label: '否', value: 0, type: 'danger'},
]

export const rantStatusOption = [
  // 0草稿 -蓝色 ，1进行中 -黄色，2按时完成 -绿色，3延期完成-浅红，4延期未完成-深红，5终止-灰色, 6审批中，7驳回
  {label: '草稿', value: 0, type: null},
  {label: '进行中', value: 1, type: 'info', color: 'yellow'},
  {label: '按时完成', value: 2, type: 'success'},
  {label: '延期完成', value: 3, type: 'danger'},
  {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
  {label: '终止', value: 5, type: 'info'},
  {label: '审批中', value: 6, type: 'warning'},
  {label: '驳回', value: 7,  type: 'success', color: 'red', fontColor: 'white!important'},
]

export const feedbackStatusOption = [
  // 0草稿 -蓝色 ，1进行中 -黄色，2按时完成 -绿色，3延期完成-浅红，4延期未完成-深红，5终止-灰色, 6审批中，7驳回
  {label: '草稿', value: 0, type: null},
  {label: '审批中', value: 1, type: 'info', color: 'yellow'},
  {label: '审批通过', value: 2, type: 'success'},
  {label: '驳回', value: 3,  type: 'success', color: 'red', fontColor: 'white!important'},
]
