
642c4bf40ed4f592ad14cadec44180a7c9cedaaa	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.902df5d82f77665dab92.hot-update.js\",\"contentHash\":\"e72a74b9caa66c48f03018183ffddd99\"}","integrity":"sha512-0n02yQU6aXtxcfSjUkiHcaEEI8EL3cuEkeZtmzQ14ExLDFHy8z5yV/ziIA0ROwauC6TzUiBja7eDAerZW9XGBA==","time":1754311559637,"size":78314}