<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="类型" prop="mattersType">
        <el-select v-model="queryParams.mattersType" multiple placeholder="请选择分类" clearable
                   @keyup.enter.native="handleQuery" style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
                     :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="rantClassify">
        <el-select v-model="queryParams.rantClassify" placeholder="请选择分类" clearable
                   @keyup.enter.native="handleQuery"
                   style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                     :value="dict.label"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="责任人" prop="responsiblePerson">
        <el-input suffix-icon="el-icon-search" v-model="responsiblePersonNameQuery" placeholder="请选择责任人" clearable
                  @keyup.enter.native="handleQuery" @focus="selectUserFun('responsibilityerQuery')"
                  @change="changeresponsibilityer(responsiblePersonNameQuery)" style="width: 200px;"/>

      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" multiple clearable size="small" style="width: 200px">
          <el-option v-for="dict in rantStatusOption" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="责任部门" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :normalizer="normalizer"
                    @input="handleSelectDept"
                    placeholder="选择责任部门" style="width: 200px;"/>
      </el-form-item>
      <el-form-item label="内容" prop="rantContent">
        <el-input v-model="queryParams.rantContent" placeholder="请输入内容" clearable size="small"
                  @keyup.enter.native="handleQuery" style="width: 200px;" maxlength="100"/>
      </el-form-item>
      <el-form-item label="计划完成时间" prop="planTimeRange">
        <el-date-picker
          v-model="planTimeRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd" style="width: 400px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实际完成时间" prop="closingTimeRange">
        <el-date-picker
          v-model="closingTimeRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd" style="width: 400px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
        </el-button>
      </el-form-item>
    </el-form>


    <div>
      <el-table v-loading="loading" :data="mattersList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column align="center" label="序号" prop="id" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center" prop="status">
          <template slot-scope="scope">
            <status-tag class="no-transition" :status="scope.row.status" :options="rantStatusOption"/>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="200" align="center" prop="mattersType">
          <template slot-scope="scope">
            <section class="matters-type-box">
              <dict-tag v-for="(type, index) in scope.row.mattersType.split(',')" :key="index"
                        :options="dict.type.rant_matters_type" :value="type"/>
            </section>

          </template>
        </el-table-column>

        <el-table-column label="分类" align="center" prop="rantClassify" width="80"/>
        <el-table-column label="内容" align="center" prop="rantContent">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.rantContent" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.rantContent }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="措施" align="center" prop="solution">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.solution" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.solution }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="计划完成时间" align="center" prop="planTime" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="责任部门" align="center" prop="deptName">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.deptName" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.deptName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="责任人" align="center" prop="responsiblePersonName"/>
        <el-table-column label="实际进展" align="center" prop="thisProgress">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.thisProgress" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.thisProgress }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="实际完成时间" align="center" prop="closingTime" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.closingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" align="center" class-name="hidden-column" min-width="100"
                         label-class-name="hidden-column">

          <template slot-scope="scope">
            <el-button v-if="scope.row.status == 2 || scope.row.status == 3" size="mini" type="text" icon="el-icon-view"
                       @click="handleEvaluation(scope.row)">评价
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  :page-sizes="[10, 20, 50, 100, 200, 500, 1000]" @pagination="getList"/>
    </div>

    <!-- 添加评价对话框 -->
    <el-dialog title="事项评价" :visible.sync="evaluationVisible" width="500px" append-to-body
               v-loading="evaluationLoading">
      <el-form ref="evaluationForm" :model="evaluationForm" :rules="evaluationRules" v-if="evaluationVisible">
        <el-form-item label="评分" prop="evaluationScore">
          <div class="rate-display-container" @mouseleave="hoverRating = null">
            <el-rate v-model="evaluationForm.evaluationScore" :max="5" allow-half
                     :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                     @hover-change="handleHoverChange" @change="handleRateChange">
            </el-rate>
            <span class="score-text">{{ displayRating }}</span>
          </div>
        </el-form-item>
        <el-form-item label="评价内容" prop="evaluationContent">
          <el-input v-model="evaluationForm.evaluationContent" type="textarea" :rows="4" placeholder="请输入评价内容"
                    maxlength="200" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEvaluation">提 交</el-button>
      </div>
    </el-dialog>


    <select-user ref="userRef" :roleId="roleName" @feedbackEmit="selectUserData"/>
  </div>
</template>

<script>
import {getMatters, lookList, addMatters, updateMatters, delMatters} from '@/api/rant/matters'
import {addEvaluative} from '@/api/rant/evaluative'
import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import {rantStatusOption} from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {listRespdet} from "@/api/rant/respdet";

export default {
  name: 'Look',
  components: {Treeselect, SelectUser, StatusTag},
  dicts: ['rant_classify', 'rant_matters_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督办事项表格数据
      mattersList: [],
      // 责任部门列表
      deptOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      planTimeRange: [],
      closingTimeRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranter: null,
        mattersType: [],
        rantClassify: null,
        beginPlanTime: null,
        endPlanTime: null,
        deptId: null,
        responsiblePerson: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        beginClosingTime: null,
        endClosingTime: null,
        achievement: null,
        status: []
      },
      // 表单参数
      form: {},
      evaluationVisible: false,
      evaluationLoading: false,
      evaluationForm: {
        id: null,
        score: 0,
        evaluationContent: '',
        evaluationScore: 0
      },
      hoverRating: null,
      evaluationScore: 0,
      evaluationRules: {
        evaluationScore: [
          {
            required: true,
            message: '请选择评分',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === 0) {
                callback(new Error('请选择评分'))
              } else {
                callback()
              }
            }
          }
        ],
        evaluationContent: [
          {required: true, message: '请输入评价内容', trigger: 'blur'}
        ]
      },
      // 表单校验
      rules: {
        ranter: [
          {required: true, message: '督办来源不能为空', trigger: 'blur'}
        ],
        mattersType: [
          {required: true, message: '吐槽对象不能为空', trigger: 'blur'}
        ],
        rantClassify: [
          {required: true, message: '吐槽分类不能为空', trigger: 'blur'}
        ],
        planTime: [
          {required: true, message: '计划完成时间不能为空', trigger: 'blur'}
        ],
        responsiblePerson: [
          {required: true, message: '责任人不能为空', trigger: 'blur'}
        ],
        rantContent: [
          {required: true, message: '吐槽内容不能为空', trigger: 'blur'}
        ],
        solution: [
          {required: true, message: '解决方案不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  created() {
    this.getList()
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, "id");
    });
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    },
    displayRating() {
      const value = this.hoverRating !== null ? this.hoverRating : this.evaluationForm.evaluationScore
      return value !== null ? `${(value * 2).toFixed(1)}分` : '0.0分'
    }
  },
  methods: {
    // 处理评分改变事件
    handleRateChange(value) {
      // 手动触发表单验证以清除错误提示
      this.$nextTick(() => {
        if (this.$refs.evaluationForm) {
          this.$refs.evaluationForm.validateField('evaluationScore')
        }
      })
    },
    /** 查询督办事项列表 */
    getList() {
      this.loading = true
      lookList(this.queryParams).then((response) => {
        this.mattersList = response.rows
        this.total = response.total
        this.loading = false
      })

    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.ranter = null
      this.ranterName = null
      this.responsiblePerson = null
      this.responsiblePersonName = null
      this.deptId = null
      this.deptName = null
      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // 拆分时间范围
      if (this.planTimeRange && this.planTimeRange.length === 2) {
        this.queryParams.beginPlanTime = this.planTimeRange[0];
        this.queryParams.endPlanTime = this.planTimeRange[1];
      } else {
        this.queryParams.beginPlanTime = null;
        this.queryParams.endPlanTime = null;
      }
      if (this.closingTimeRange && this.closingTimeRange.length === 2) {
        this.queryParams.beginClosingTime = this.closingTimeRange[0];
        this.queryParams.endClosingTime = this.closingTimeRange[1];
      } else {
        this.queryParams.beginClosingTime = null;
        this.queryParams.endClosingTime = null;
      }
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.planTimeRange = [];
      this.closingTimeRange = [];
      this.responsiblePersonNameQuery = '';
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加督办事项'
    },
    handleView(row) {
      this.$router.push({
        path: '/rant/rantDetail',
        query: {
          id: row.id,
          type: "look",
        }
      })
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        this.form.ranter = this.ranter
        this.form.ranterName = this.ranterName
        this.form.responsiblePerson = this.responsiblePerson
        this.form.responsiblePersonName = this.responsiblePersonName
        this.form.deptId = this.deptId
        this.form.deptName = this.deptName

        if (valid) {
          if (this.form.id != null) {
            updateMatters(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMatters(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除督办事项编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMatters(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'rant/matters/export',
        {
          ...this.queryParams
        },
        `matters_${new Date().getTime()}.xlsx`
      )
    },
    selectUserFun(type) {
      this.selectUserType = type
      this.$refs.userRef.show()
    },
    selectUserData(data) {
      if (this.selectUserType == 'ranter') {
        this.ranter = data.userId
        this.ranterName = data.nickName
        this.form.ranter = data.userId
        this.form.ranterName = data.nickName
      } else if (this.selectUserType == 'responsibilityer') {
        this.responsiblePerson = data.userId
        this.responsiblePersonName = data.nickName
        this.deptId = data.deptId
        this.deptName = data.dept.deptName
      } else if (this.selectUserType == 'responsibilityerQuery') {
        this.queryParams.responsiblePerson = data.userId
        this.responsiblePersonNameQuery = data.nickName

      }
    },
    changeresponsibilityer() {
      if (this.responsiblePersonNameQuery == '' || this.responsiblePersonNameQuery == null) {
        this.queryParams.responsiblePerson = null
      }

    },
    /** 评价按钮操作 */
    handleEvaluation(row) {
      this.evaluationForm = {
        rantMattersId: row.id,
        score: 0,
        evaluationContent: ''
      }
      this.evaluationScore = 0
      this.evaluationVisible = true
    },
    // 新增悬停事件处理
    handleHoverChange(value) {

      this.hoverRating = value
    },
    /** 提交评价 */
    submitEvaluation() {
      this.$refs.evaluationForm.validate(valid => {
        if (valid) {
          // 转换评分值（前端保存0-5分，允许0.5分）
          this.evaluationForm.score = this.evaluationForm.evaluationScore * 2 // 转换为10分制
          const payload = {
            ...this.evaluationForm,
          };
          this.evaluationLoading = true
          addEvaluative(payload).then(response => {
            this.$modal.msgSuccess("评价成功")
            this.evaluationVisible = false
            this.getList()
          })
            .finally(() => {
              this.evaluationLoading = false
            })
        }
      })
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    handleSelectDept(value) {
      this.form.deptId = null
    },
    /** 转换部门数据结构 */
    normalizerVisible(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
  }
}
</script>

<style scoped lang="scss">
:deep(.matters-type-box .el-tag) {
  display: inline-block;
  width: 70px;
  text-align: center;
}

.matters-type-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
}

:deep(.el-table__cell .cell) {
  display: flex;
  justify-content: center;
}

.rate-display-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-text {
  color: #606266;
  font-size: 14px;
  min-width: 60px;
}
</style>
