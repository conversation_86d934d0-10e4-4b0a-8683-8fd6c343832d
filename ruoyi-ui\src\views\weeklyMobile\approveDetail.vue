<script>

import { Popup, Picker, Icon, Toast, Field, Tag } from 'vant';
import {
  approveDaiBanInfo
} from "@/api/weekly/mobile-reportInfo";
export default {
  name: "approve",
  components: {
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Tag.name]: Tag,
  },
  data() {
    return {
      loading: true,
      labelPosition: "right",
      weeklyForm: {
        id: null,
        year: null, // 年份不能为空
        week: null, // 第几周不能为空
        userId: "", // 员工编码不能为空
        nickName: "", // 员工名称不能为空
        deptId: null, // 部门ID不能为空
        deptName: "", // 部门名称不能为空
        postName: "", // 岗位名称
        startDate: "", // 周开始日期不能为空
        endDate: "", // 周结束日期不能为空
        approveUserCode: "", // 审批人编码不能为空
        approveUserName: "", // 审批人名称不能为空
        circulateUserCodes: "", // 传阅人（多个用逗号隔开）
        userCode: "", // 员工编码不能为空
        // 本周工作总结
        workSummaryList: [
          {
            workMatter: "", // 工作板块
            progressScheme: "", // 工作事项/完成进度
            completeStatus: "", // 完成及预警情况
            type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
            serialNumber: 1, // 序号
          },
        ],
        // 下周工作计划
        workPlanList: [
          {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 2,
            serialNumber: 1,
          },
        ],
        // 需部门支持事项
        workSupportList: [
          {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 3,
            serialNumber: 1,
          },
        ],
        reflectInfo: {type: 4, progressScheme: "" },
      },
      userName: "", // 用户名称
      year: "", // 年份
      week: "", // 周数
      daiBanZbInfoList: [], // 待办周报集合
      showPicker: false,
      currentUserIndex: 0,
      zbTodoNotice: null
    }
  },
  computed: {
    currentYear() {
      return this.weeklyForm.year;
    },
    weekNumber() {
      // 这里需要实现获取当前周数的逻辑
      return this.weeklyForm.week;
    },
    dateRange() {
      // 这里需要实现获取日期范围的逻辑
      return this.formatDateRange(
        this.weeklyForm.startDate,
        this.weeklyForm.endDate
      );
    },
    department() {
      return this.weeklyForm.deptName;
    },
    userColumns() {
      return this.daiBanZbInfoList.map(item => item.nickName);
    },
    isEmpty() {
      return this.daiBanZbInfoList.length === 0;
    }
  },
  created() {
    const id = this.$route.params.id;
    this.getDaiBanZbInfo(id);
  },
  methods: {
    onUserChange(value, index) {
      this.currentUserIndex = index;
      this.weeklyForm = this.daiBanZbInfoList[index];
      this.showPicker = false;
    },
    onUserCancel() {
      this.showPicker = false;
    },
    getDaiBanZbInfo(id) {
      this.loading = true;
      approveDaiBanInfo({
        todoNoticeId: id,
        type: 1
      })
        .then((response) => {
          this.daiBanZbInfoList = response.data;
          if (!this.daiBanZbInfoList || this.daiBanZbInfoList.length === 0) {
            return;
          }
          this.weeklyForm = this.daiBanZbInfoList[0];
          this.weeklyForm.reflectInfo = this.weeklyForm.reflectInfo || {type: 4, progressScheme: "" };
        })
        .finally(() => {
          this.loading = false;
        });
    },
    formatDateRange(startDate, endDate) {
      return `${new Date(startDate).getMonth() + 1}月${new Date(
        startDate
      ).getDate()}日-${new Date(endDate).getMonth() + 1}月${new Date(
        endDate
      ).getDate()}日`;
    },
    getStatusType(status) {
      switch (status) {
        case 1:
          return "primary";
        case 2:
          return "success";
        case 4:
          return "danger";
        default:
          return "success";
      }
    },
    getStatusText(status) {
      // 审阅状态（0-待阅 1-已阅 4-已驳回）
      const statusMap = {
        1: "待阅",
        2: "已阅",
        4: "已驳回"
      };
      return statusMap[status] || "未知";
    },
  }
}
</script>

<template>
  <div class="weekly-approve">
    <!-- 顶部导航 -->
    <div class="nav-header">
      <div class="title">审批详情</div>
      <div class="more" @click="showPicker = true">{{ weeklyForm.nickName }} <van-icon name="arrow-down" /></div>
    </div>
    <!-- 周报信息 -->
    <div class="info-card">
      <div class="weekly-range">
        {{ currentYear }}年第{{ weekNumber }}周（{{ dateRange }}）
        <van-tag :type="getStatusType(weeklyForm.approveStatus)" class="status-tag">
          {{ getStatusText(weeklyForm.approveStatus) }}
        </van-tag>
      </div>
      <div class="employee-info">
        <span>员工姓名：{{ weeklyForm.nickName }}</span>
        <span>员工所属部门：{{ weeklyForm.deptName }}</span>
        <span>员工职位：{{ weeklyForm.postName }}</span>
      </div>
    </div>
    <!-- 本周工作总结 -->
    <div class="work-card">
      <div class="card-title">本周工作总结</div>
      <div class="work-content" v-for="item in weeklyForm.workSummaryList" :key="item.serialNumber">
        <div>{{item.serialNumber}}</div>
        <div class="section">
          <div class="label">工作事项</div>
          <div class="content">{{ item.workMatter }}</div>
        </div>
        <div class="section">
          <div class="label">完成进度/解决方案</div>
          <div class="content">{{ item.progressScheme }}</div>
        </div>
        <div class="section">
          <div class="label">完成及预期情况</div>
          <div class="content">{{ item.completeStatus }}</div>
        </div>
      </div>
    </div>


    <!-- 下周工作计划 -->
    <div class="work-card">
      <div class="card-title">下周工作计划</div>
      <div class="work-content" v-for="item in weeklyForm.workPlanList" :key="item.serialNumber">
        <div>{{item.serialNumber}}</div>
        <div class="section">
          <div class="label">工作事项</div>
          <div class="content">{{ item.workMatter }}</div>
        </div>
        <div class="section">
          <div class="label">完成进度/解决方案</div>
          <div class="content">{{ item.progressScheme }}</div>
        </div>
        <div class="section">
          <div class="label">完成及预期情况</div>
          <div class="content">{{ item.completeStatus }}</div>
        </div>
      </div>
    </div>
    <section class="work-card">
      <div>
        <div class="card-title">每周反思</div>
      </div>
      <div class="work-content">
       <!-- {{ weeklyForm.reflectInfo.progressScheme }} -->
       <van-field
          v-model="weeklyForm.reflectInfo.progressScheme"
          type="textarea"
          placeholder=""
          :rules="[{ required: true, message: '请输入每周反思' }]"
          class="card-field"
          autosize
          readonly
        />
      </div>
    </section>
    <!-- 需部门支持项目 -->
    <div class="work-card">
      <div class="card-title">需部门支持项目</div>
      <div class="work-content" v-for="item in weeklyForm.workSupportList" :key="item.serialNumber">
        <div>{{item.serialNumber}}</div>
        <div class="section">
          <div class="label">工作事项</div>
          <div class="content">{{ item.workMatter }}</div>
        </div>
        <div class="section">
          <div class="label">完成进度/解决方案</div>
          <div class="content">{{ item.progressScheme }}</div>
        </div>
        <div class="section">
          <div class="label">完成及预期情况</div>
          <div class="content">{{ item.completeStatus }}</div>
        </div>
      </div>
    </div>

    <!-- 审阅意见 -->
    <div class="review-card">
      <div class="card-title">审阅意见</div>
      <textarea v-model="weeklyForm.approveDesc" readonly placeholder="请填写意见" class="review-input"></textarea>
    </div>

    <!-- 添加 Picker 组件 -->
    <van-popup v-show="!isEmpty" v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="userColumns"
        @confirm="onUserChange"
        @cancel="onUserCancel"
        :default-index="currentUserIndex"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
.weekly-approve {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80px;
  background: #D6E1F1;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
}

.nav-header {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 10px;
  //background-color: #fff;
  background: #D6E1F1;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .back,
  .more {
    font-size: 14px;
    color: #666;
  }
}

.info-card,
.work-card,
.review-card {
  margin: 12px;
  padding: 16px 10px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}

.weekly-range {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}
.status-tag {
  margin-left: auto;
}
.employee-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.card-title {
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: bold;
  font-size: 15px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 20px;

}

.work-content {
  .section {
    margin-bottom: 16px;

    .label {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }

    .content {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 24px;
      white-space: pre-wrap
    }
  }
}

.support-list {
  .support-item {
    margin-bottom: 20px;

    .item-id {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
    }
  }
}

.review-input {
  width: 100%;
  height: 100px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: none;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

.submit-btn-group{
  display: flex;
  justify-content: space-between;
  gap: 20px;
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}
.submit-btn {
  flex: 1;
  width: calc(50vw - 30px);
  height: 44px;
  background-color: #4080ff;
  color: #fff;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.empty-block{
  font-size: 20px;
  text-align: center;
  padding-top: 150px;
}
</style>
