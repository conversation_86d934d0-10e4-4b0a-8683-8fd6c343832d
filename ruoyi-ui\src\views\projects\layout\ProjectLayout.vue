<template>
  <div class="project-wrapper" id="watermark-02">
    <!-- <Watermark :options="options">  -->
      <div class="project-container">
        <div class="project-inner">
          <!--          <Watermark-->
          <!--            style="width: 100vw; height: 100vh;"-->
          <!--            text="机密文件"-->
          <!--            :font-size="20"-->
          <!--            font-family="Arial"-->
          <!--            color="red"-->
          <!--            :opacity="0.1"-->
          <!--            :rotate="-45"-->
          <!--            :width="220"-->
          <!--            :height="150"-->
          <!--          >-->
          <slot :user="user"></slot>
          <!--          </Watermark>-->
        </div>
      </div>
    <!-- </Watermark> -->
  </div>
</template>

<script>
import {getUserProfile} from "@/api/system/user";
import {formatDate} from '@/utils/index'
import { mapState,mapMutations } from 'vuex'
// import { Watermark } from '@pansy/vue-watermark';
import { Watermark } from '@pansy/watermark';
export default {
  name: 'ProjectLayout',
  components:{
    Watermark
  },
  data() {
    return {
      baseSize: 16,
      designWidth: 1680,
      // user: {},
      userInfo: {},
      options: {
       text: '测试水印',
     }

    }
  },
  computed: {
    ...mapState({
      user: state => state.projects.project
    }),
    waterMarkText(){
      return `${this.userInfo.nickName}（${this.userInfo.userName}）${formatDate(new Date())}`
    }
  },
  created() {
    this.getUser();
    this.debouncedSetRem = this.debounce(this.setRem, 100)
  },
  mounted() {
    this.setRem()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    ...mapMutations({
      setUser: 'projects/SET_USER'
    }),
    createWatermark(text){
      // const scale = document.documentElement.clientWidth / this.designWidth
      // const fontSize = (this.baseSize * scale)
      new Watermark({
        text,
        container: 'watermark-02',
        opacity: 0.15,
        width: 400,
        height: 260,
        offsetLeft: 0,
        offsetTop: 0,
        gapX: 150,
        gapY: 100,
        zIndex: 1,
        rotate: -22,
        fontSize: 18,
        textAlign: 'center',
        fontStyle: 'normal',
        fontColor: '#000',
        fontFamily: 'sans-serif',
        fontWeight: '300',
      });
    },
    getUser() {
      if (!this.user || Object.keys(this.user).length === 0) {
        getUserProfile().then(response => {
          this.userInfo = response.data;
          this.setUser(response.data);
          this.createWatermark(this.waterMarkText);
        });
      }
    },
    setRem() {
      const scale = document.documentElement.clientWidth / this.designWidth
      document.documentElement.style.fontSize = (this.baseSize * scale) + 'px'
      // this.createWatermark(this.waterMarkText);
    },
    debounce(fn, delay) {
      let timer = null
      return (...args) => {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    },
    handleResize() {
      this.debouncedSetRem()
    }
  },
}
</script>

<style lang="scss" scoped>
.project-wrapper {
  position: relative;
  overflow: visible;
  background-color: #f5f5f5;
  /* 此时wrapper本身的transform由:style控制 */
}

.project-container {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  /* 不再在这里设置transform相关，因为transform移到了上层wrapper */
}

.project-inner {
  width: 100%;
  height: 100%;
}

::v-deep ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::v-deep ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::v-deep ::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;

  &:hover {
    background: #555;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
