import request from '@/utils/request'

// 查询客关项目满意度数据列表
export function listKgSatisfiedData(query) {
  return request({
    url: '/project/kgSatisfiedData/list',
    method: 'get',
    params: query
  })
}

// 查询客关项目满意度数据详细
export function getKgSatisfiedData(id) {
  return request({
    url: '/project/kgSatisfiedData/' + id,
    method: 'get'
  })
}

// 新增客关项目满意度数据
export function addKgSatisfiedData(data) {
  return request({
    url: '/project/kgSatisfiedData',
    method: 'post',
    data: data
  })
}

// 修改客关项目满意度数据
export function updateKgSatisfiedData(data) {
  return request({
    url: '/project/kgSatisfiedData',
    method: 'put',
    data: data
  })
}


// 删除客关项目满意度数据
export function delKgSatisfiedData(id) {
  return request({
    url: '/project/kgSatisfiedData/' + id,
    method: 'delete'
  })
}

// 客关项目满意度数据导入
export function importFile(data) {
  return request({
    url: '/project/kgSatisfiedData/import',
    method: 'post',
    data: data
  })
}
