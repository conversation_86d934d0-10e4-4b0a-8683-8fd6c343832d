
1fefcfcf3d6eb1aa09999087407e52c5bcb270db	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.e7aa551239cf6309864b.hot-update.js\",\"contentHash\":\"77473e8d8e67e3acf7cfe106d0049491\"}","integrity":"sha512-Ik6UZ2YvFI7SmpmG0jjB/03A3DNNrqUO1cGkgaUamlu+6abyjO6lLNJF1Vg//PJiVZnjYZ/WNqBygppOMK7eTQ==","time":1754311466469,"size":78182}