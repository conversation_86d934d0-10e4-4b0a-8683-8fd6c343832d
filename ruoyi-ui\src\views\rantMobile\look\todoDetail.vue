<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container" v-loading="loading">
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="rant-form-title">
            <img src="@/assets/rant/rant-info.png" class="icon">基本信息
          </div>
          <van-form class="rant-detail">
            <div class="form-group">
              <div class="form-item">
                <div class="form-label">来源</div>
                <div class="form-value">{{ rantDetail.ranterName }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">类型</div>
                <div class="form-value" style="display: flex;flex-wrap: wrap;gap: 6px;">
                  <dict-tag v-for="(type, index) in rantDetail.mattersType?.split(',')" :key="index"
                    :options="mattersTypeOptionsLabel" :value="type" style="height: 29px" /></div>
              </div>

              <div class="form-item">
                <div class="form-label">分类</div>
                <div class="form-value">
                  <dict-tag :options="classifyOptionsLabel" :value="rantDetail.rantClassify" />
                </div>
              </div>
              <div class="form-item">
                <div class="form-label">责任人</div>
                <div class="form-value">{{ rantDetail.responsiblePersonName }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">责任部门</div>
                <div class="form-value">{{ rantDetail.deptName }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">责任部门负责人</div>
                <div class="form-value">{{ rantDetail.respDeptResponsiblerName }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">分管领导</div>
                <div class="form-value">{{ rantDetail.respDeptLeaderName }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">计划完成时间</div>
                <div class="form-value">{{ rantDetail.planTime }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">是否结项</div>
                <div class="form-value">{{ rantDetail.isCompletion ? "是" : "否" }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">内容</div>
                <div class="form-value">{{ rantDetail.rantContent }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">措施</div>
                <div class="form-value">{{ rantDetail.solution || "--"}}</div>
              </div>

              <div class="form-item" v-if="rantDetail.isCompletion">
                <div class="form-label">结项时间</div>
                <div class="form-value">{{ rantDetail.closingTime || "--" }}</div>
              </div>

              <div class="form-item">
                <div class="form-label">成果</div>
                <div class="form-value">
                  <section v-if="rantDetail.fileList && rantDetail.fileList.length > 0" class="file-list">
                    <div v-for="(file, index) in rantDetail.fileList" :key="index" class="file-item" @click="openFile(file.url)">
                      <div class="file-link">
                        <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
                        <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
                      </div>
                     <!--  <van-button type="info" size="small" icon="description" @click="openFile(file.url)">
                        {{ file.name || "--" }}
                      </van-button> -->
                    </div>
                  </section>
                  <span v-else>--</span>
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">最新进展</div>
                <div class="form-value" v-html="rantDetail.thisProgress || '--'"></div>
              </div>
            </div>
          </van-form>

          <div class="rant-form-title">
            <img src="@/assets/rant/rant-progress.png" class="icon">进度情况
          </div>

          <div class="progress-list">
            <div v-for="(record, index) in recordDtoList" :key="index" class="progress-item">
              <div class="progress-header">
                <div class="progress-number">{{ index + 1 }}</div>
                <div class="progress-date">{{ parseTime(record.feedbackTime, "{y}-{m}-{d}") }}</div>
              </div>
              <div class="progress-content" v-html="record.actualProgress"></div>
              <div v-if="record.fileList && record.fileList.length > 0" class="progress-files">
                <div class="files-title">成果文件：</div>
                <div v-for="(file, fileIdx) in record.fileList" :key="fileIdx" class="file-link"  @click="openFile(file.url)">
                  <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
                  <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
                  <!-- <van-button type="info" size="small" icon="description" @click="openFile(file.url)">
                    {{ file.name || "--" }}
                  </van-button> -->
                </div>
              </div>
            </div>
            <div v-if="!recordDtoList || recordDtoList.length === 0" class="no-data">
              暂无进度记录
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// http://localhost/wechatE/mobile/rant/todoDetail?readTodoNoticeId=10
import { daiBanLookRankMatter, readDaiBanChange } from "@/api/rantMobile/matters";
import { Button, Form, Field, NavBar } from 'vant';
import mixin from '../mixins'
export default {
  name: "Detail",
  mixins: [mixin],
  // dicts: ["rant_classify"],
  components: {
    [Button.name]: Button,
    [Form.name]: Form,
    [Field.name]: Field,
    [NavBar.name]: NavBar
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
    };
  },
  mounted() {
    const readTodoNoticeId = this.$route.query.readTodoNoticeId;
    this.handleGetRantDetail(readTodoNoticeId);
    this.fetchRantClassify();
    this.fetchMattersType();
  },
  methods: {
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    handleGetRantDetail(readTodoNoticeId) {
      daiBanLookRankMatter(readTodoNoticeId).then((response) => {
        this.rantDetail = response.data;
        this.recordDtoList = response.data.recordDtoList;
        this.loading = false;
        //调用已阅接口
        readDaiBanChange(readTodoNoticeId).then((response) => {
        });
      });
    },
    handleClose() {
      this.$modal.confirm("确认是否关闭当前页面").then(() => {
        this.$store.dispatch("tagsView/delView", this.$route);
      });
    },
    openFile(url) {
      console.log('openFile', url);
      if (!url) return;
      window.open(url);
    },
    parseTime(time, pattern) {
      if (!time) return '';
      let date = new Date(time);
      let formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      };
      const timeStr = pattern.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        if (key === 'a') {
          return ['日', '一', '二', '三', '四', '五', '六'][value];
        }
        if (result.length > 0 && value < 10) {
          value = '0' + value;
        }
        return value || 0;
      });
      return timeStr;
    }
  },
};
</script>

<style lang="scss" scoped>
@import '../css/common.scss';
.form-value{
  min-width: 0;
  flex-grow: 1;
}
.file-link {
    width: 100%;
    display: flex;
    align-items: center;
    color: #376DF7!important;
    font-size: 12px!important;
    font-weight: 400!important;
    line-height: 1.8;
    min-width: 0;
    gap: 0; /* 精确控制间距 */
    
    .filename-part{
      min-width: 0;
      flex: 1; /* 可伸缩部分 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #4080FF;
    }
    
    .extension-part{
      flex-shrink: 0; /* 不收缩 */
      white-space: nowrap;
      color: #4080FF;
    }
    
    .download-icon {
      flex-shrink: 0; /* 不收缩 */
      margin-left: 8px; /* 与文件名保持间距 */
      font-size: 14px;
      color: #909399;
    }
  }
/* .app-container-feedback {
  padding-top: 56px; // 为固定顶部导航栏腾出空间
}

// 补充特定的样式
.van-nav-bar {
  background-color: #DAEEF2;

  :deep(.van-nav-bar__title) {
    color: #333333;
    font-weight: 600;
  }

  :deep(.van-icon) {
    color: #3673FF;
  }
}

.no-data {
  text-align: center;
  color: #999999;
  padding: 20px 0;
} */
</style>
