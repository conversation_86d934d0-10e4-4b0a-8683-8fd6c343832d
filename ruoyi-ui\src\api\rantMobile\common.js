import request from '@/utils/request'
// import request from '@/utils/request-rant-mobile'
// 文件上传
export function uploadFile(file) { // 单文件上传
  return request({
    url: '/file/upload',
    method: 'post',
    data: file
  })
}

// 多文件上传
export function uploadFileMultiple(files) {

  return request({
    url: '/file/rantUploads',
    method: 'post',
    data: files
  })
}

// 查询成果设置列表
export function listConfig(query) {
  return request({
    url: '/plan/config/list',
    method: 'get',
    params: query
  })
}

// 企业微信登录
export function wechatLoginRant(data) {
  return request({
    url: '/rant/h5Login',
    method: 'post',
    data: data
  })
}

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 获取用户详细信息
export function getUserInfoRant() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

export function getDicts(dictType) {
  return request({
    url: '/system/dict/data/type/' + dictType,
    method: 'get'
  })
}

// 获取文件
export function getFile(fileUrl) {
  return request({
    url: 'rant/common/file/stream',
    params: {
      url: fileUrl
    },
    method: 'get',
    responseType: 'blob'
  })
}

// 根据参数键名查询参数值
export function getMobileConfigKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}
