<template>
  <div class="projects-content-container">
    <div v-if="!hasPermi(['project:cost'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限" />
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else class="w-100">
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中" />
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据" />
        <div class="desc">暂无数据</div>
      </div>
      <div v-else class="w-100">
        <div class="cost-content">
          <div class="card-content cost mb-12">
            <div class="card-title mb-16">成本指标</div>
            <div class="cost-card-item">
              <div
                class="cost-card-item-content cost"
                v-loading="costAndOutputLoading"
              >
                <div class="title mb-12">成本</div>
                <Empty :no-authority="hasPermi(['cost:data'])">
                  <section class="card-with-title">
                    <Chart
                      class="chart-size mb-20"
                      :option="costChartOption"
                      ref="costChart"
                    />
                    <section class="chart-title-block">
                      <div
                        class="title-1"
                        :style="{
                          color: costChartOption.title.textStyle.color,
                        }"
                      >
                        {{ costChartOption.title.text }}
                      </div>
                      <div class="title-2">
                        {{ costChartOption.title.subtext }}
                      </div>
                    </section>
                  </section>

                  <div class="data-content">
                    <div class="value-item">
                      <div class="value mb-8">
                        {{ $toFixed2(costAndOutput.targetAmount) }} 亿
                      </div>
                      <div class="label">目标成本</div>
                    </div>
                    <div class="value-item">
                      <div class="value mb-8">
                        {{ $toFixed2(costAndOutput.dynamicAmount) }} 亿
                      </div>
                      <div class="label">动态成本</div>
                    </div>
                  </div>
                </Empty>
              </div>
              <div class="cost-card-item-content change">
                <div class="title mb-12">变签</div>
                <Empty :no-authority="hasPermi(['cost:changeVisa'])">
                  <section class="card-with-title">
                    <Chart
                      v-if="changeChartShow"
                      :key="changeChartKey"
                      class="chart-size mb-20"
                      ref="changeChart"
                    />
                    <section class="chart-title-block">
                      <div
                        class="title-1"
                        :style="{
                          color: changeChartOption.title.textStyle.color,
                        }"
                      >
                        {{ changeChartOption.title.text }}
                      </div>
                      <div class="title-2">
                        {{ changeChartOption.title.subtext }}
                      </div>
                    </section>
                  </section>

                  <div class="data-content">
                    <div class="value-item">
                      <div class="value">
                        {{ changeVisa.changeVisaLimit || "--" }}%
                      </div>
                      <div class="label">变签率限额</div>
                    </div>
                    <!--                  <div class="value-item">-->
                    <!--                    <div class="value">{{ changeVisa.countChangeVisaRate }}%</div>-->
                    <!--                    <div class="label">累计变签率</div>-->
                    <!--                  </div>-->
                    <div class="value-item">
                      <div class="value">
                        {{ $toFixed2(changeVisa.changeVisaAmount) }} 万
                      </div>
                      <div class="label">变签金额</div>
                    </div>
                  </div>
                </Empty>
              </div>
              <div class="cost-card-item-content value">
                <div class="title mb-12">产值</div>
                <Empty :no-authority="hasPermi(['cost:output'])">
                  <section class="card-with-title">
                    <Chart
                      v-if="valueChartShow"
                      :key="valueChartKey"
                      class="chart-size mb-20"
                      ref="valueChart"
                    />
                    <section class="chart-title-block">
                      <div
                        class="title-1"
                        :style="{
                          color: valueChartOption.title.textStyle.color,
                        }"
                      >
                        {{ valueChartOption.title.text }}
                      </div>
                      <div class="title-2">
                        {{ valueChartOption.title.subtext }}
                      </div>
                    </section>
                  </section>

                  <div class="data-content">
                    <div class="value-item">
                      <div class="value">
                        {{ $toFixed2(costAndOutput.totalTargetAmount) }} 亿
                      </div>
                      <div class="label">目标成本（含营销费）</div>
                    </div>
                    <div class="value-item">
                      <div class="value">
                        {{ $toFixed2(costAndOutput.outputAmount) }} 亿
                      </div>
                      <div class="label">累计产值</div>
                    </div>
                  </div>
                </Empty>
              </div>
            </div>
          </div>
          <div
            class="card-content dynamic-cost mb-12"
            v-loading="dynamicCostLoading"
          >
            <div class="card-title mb-16">动态成本按月分布</div>
            <div class="cost-card-item">
              <Empty :no-authority="hasPermi(['cost:dynamic:trend'])">
                <Chart
                  v-if="dynamicCostChartShow"
                  :key="dynamicCostChartKey"
                  class="line-chart"
                  ref="dynamicCostChart"
                />
              </Empty>
            </div>
          </div>
          <div
            class="card-content dynamic-cost mb-12"
            v-loading="detailDataLoading"
          >
            <div class="card-title mb-16 flex-space">
              <div class="title">成本数据明细</div>
              <div class="info">单位：万元</div>
            </div>
            <div class="cost-card-item">
              <Empty :no-authority="hasPermi(['cost:data:detail'])">
                <el-table
                  class="project-table"
                  :data="tableData"
                  style="width: 100%"
                  :border="true"
                  :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.4rem 0',
                    fontSize: '1rem',
                  }"
                  :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontSize: '1rem',
                    padding: '0.4rem 0',
                    lineHeight: '1rem',
                  }"
                  row-key="id"
                >
                  <el-table-column
                    prop="subjectName"
                    label="科目名称"
                    min-width="25%"
                  >
                    <template slot-scope="scope">
                      <section
                        :style="{
                          paddingLeft: (scope.row.level || 0) * 1.25 + 'rem',
                          cursor: 'pointer',
                          fontSize: '1rem',
                        }"
                        @click.stop="toggleExpand(scope.row, scope.$index)"
                      >
                        <span
                          v-if="hasChildren(scope.row)"
                          class="expand-icon"
                          :style="{ fontSize: '1rem' }"
                        >
                          {{ scope.row.expanded ? "▼" : "▶" }}
                        </span>
                        <span :style="{ fontSize: '1rem' }">
                          {{ scope.row.subjectName }}
                        </span>
                      </section>
                    </template>
                  </el-table-column>
                  <el-table-column
                    min-width="25%"
                    prop="targetAmount"
                    align="center"
                    label="目标版"
                  >
                    <template slot-scope="scope">
                      <span :style="{ fontSize: '1rem' }">
                        {{ $toThousands($toFixed2(scope.row.targetAmount))}}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    min-width="25%"
                    prop="dynamicAmount"
                    align="center"
                    label="动态版"
                  >
                    <template slot-scope="scope">
                      <span :style="{ fontSize: '1rem' }">
                        {{ $toThousands($toFixed2(scope.row.dynamicAmount))}}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    min-width="25%"
                    prop="diffAmount"
                    align="center"
                    label="差额"
                  >
                    <template slot-scope="scope">
                      <span :style="{ fontSize: '1rem' }">
                         {{ $toThousands($toFixed2(scope.row.diffAmount))}}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </Empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Chart from "@/views/projects/components/Chart.vue";
import ProgressCircleOption from "@/views/projects/constants/ProgressCircle";
import LineTrend from "@/views/projects/constants/LineTrend";
import API from "@/views/projects/api";
import RecursiveTable from "@/views/projects/components/RecursiveTable.vue";
import Empty from "@/views/projects/components/empty.vue";

export default {
  name: "cost",
  components: {
    Chart,
    // ExapndTable,
    RecursiveTable,
    Empty,
  },
  data() {
    return {
      projectCode: this.$route.query.projectCode,
      costAndOutput: {},
      dynamicCost: {},
      xAxisData: [],
      seriesData: [],
      detailData: [],
      changeVisa: {},
      tableData: [],
      originalData: [],
      loading: false,
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      costAndOutputLoading: false,
      dynamicCostLoading: false,
      detailDataLoading: false,
      changeVisaLoading: false,
      chartsLoading: false,
      costChartShow: true,
      changeChartShow: true,
      valueChartShow: true,
      dynamicCostChartShow: true,
      costChartKey: 0,
      changeChartKey: 0,
      valueChartKey: 0,
      dynamicCostChartKey: 0,
      valueData: {}, // 货值
      valueLoading: false,
    };
  },
  mounted() {
    this.fetchCostAndOutput(this.projectCode);
    this.fetchDyanmicCost(this.projectCode);
    this.fetchDetailData(this.projectCode);
    this.fetchChangeVisa(this.projectCode);
    this.fetchValueData(this.projectCode);
    // window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // window.removeEventListener('resize', this.handleResize)
  },
  computed: {
    valueOption() {
      return this.getValueOption();
    },
    costChartOption() {
      return this.getCostChartOption();
    },
    changeChartOption() {
      return this.getChangeChartOption();
    },
    valueChartOption() {
      return this.getValueChartOption();
    },
  },
  methods: {
    fetchValueData(projectCode) {
      // 货值
      this.valueLoading = true;
      return API.Value.goodsValue(projectCode)
        .then((res) => {
          this.valueData = res.data;
        })
        .finally(() => {
          this.valueLoading = false;
        });
    },
    getValueOption() {
      const option = this.getProgressCircleOption(
        this.valueData.rate,
        100,
        this.$toFixed2(this.valueData.deviationRate) + "%",
        "偏差率",
        "#CCE4FF",
        "#376DF7",
        "40%",
        "#1433CC"
      );
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        formatter: (params) => {
          return `<div  style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">目标货值: ${this.$formatNull(
            this.valueData.targetAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">动态货值: ${this.$formatNull(
            this.valueData.dynamicAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">偏差率: ${this.$formatNull(
            this.valueData.deviationRate
          )}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(this.valueData.rate)}%</div>
          </div>`;
        },
      };
      return option;
    },
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions;
      // Check for all permissions wildcard first
      if (permissions.includes("*:*:*")) {
        return true;
      }
      // Check specific permissions
      return permissions.some((p) => permission.includes(p));
    },
    hasChildren(row) {
      return row.childList && row.childList.length > 0;
    },

    toggleExpand(row, index) {
      row.expanded = !row.expanded;
      if (row.expanded) {
        // 展开时插入子行数据
        const children = row.childList.map((child) => ({
          ...child,
          level: (row.level || 0) + 1,
          expanded: false,
        }));
        this.tableData.splice(index + 1, 0, ...children);
      } else {
        // 收起时移除所有子行数据（包括孙子行）
        let removeCount = 0;
        const rowLevel = row.level || 0;

        for (let i = index + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].level > rowLevel) {
            removeCount++;
          } else {
            break;
          }
        }

        if (removeCount > 0) {
          this.tableData.splice(index + 1, removeCount);
        }
      }
    },

    formatAmount(value) {
      return value ? Number(value).toLocaleString() : "0";
    },
    fetchChangeVisa(projectCode) {
      this.changeVisaLoading = true;
      API.Cost.changeVisa(projectCode)
        .then((res) => {
          this.changeVisa = res.data;
          this.$nextTick(() => {
            this.$refs.changeChart?.clearChart();
            this.$refs.changeChart?.updateChart(this.getChangeChartOption());
          });
        })
        .finally(() => {
          this.changeVisaLoading = false;
        });
    },
    fetchDetailData(projectCode) {
      this.detailDataLoading = true;
      API.Cost.detail(projectCode)
        .then((res) => {
          this.originalData = res.data;
          this.tableData = this.originalData.map((item) => ({
            ...item,
            level: 0,
            expanded: false,
          }));
        })
        .finally(() => {
          this.detailDataLoading = false;
        });
    },
    getDynamicCostChartOption(xAxisData, seriesData) {
      return this.getLineTrendChartOption(xAxisData, seriesData);
    },
    getLineTrendChartOption(xAxisData, seriesData) {
      const instance = new LineTrend();
      instance.setAxisData(xAxisData, seriesData);
      const option = JSON.parse(JSON.stringify(instance.getOption()));
      option.grid.left = "5%";
      option.grid.right = "5%";
      option.grid.bottom = "15%";
      option.grid.top = "20%";
      option.tooltip.formatter = (params) => {
        return `<div style="font-size: 0.875rem; line-height: 1.2;padding: 0.3rem 0.75rem;border-radius: 0.1rem;">
          <div>动态成本: ${params[0].value}亿</div>
        </div>`;
      };
      option.tooltip.padding = [0, 0];
      return option;
    },
    fetchDyanmicCost(projectCode) {
      this.dynamicCostLoading = true;
      API.Cost.dynamicCost(projectCode)
        .then((res) => {
          this.dynamicCost = res.data;
          this.xAxisData = this.dynamicCost.map((item) => item.yearMonth);
          this.seriesData = this.dynamicCost.map((item) => item.amount);
          this.$nextTick(() => {
            this.$refs.dynamicCostChart?.clearChart();
            this.$refs.dynamicCostChart?.updateChart(
              this.getDynamicCostChartOption(this.xAxisData, this.seriesData)
            );
          });
        })
        .finally(() => {
          this.dynamicCostLoading = false;
        });
    },
    fetchCostAndOutput(projectCode) {
      this.costAndOutputLoading = true;
      API.Cost.costAndOutput(projectCode)
        .then((res) => {
          this.costAndOutput = res.data;
          this.$nextTick(() => {
            // this.$refs.costChart.clearChart()
            // this.$refs.costChart.updateChart(this.costChartOption)
            this.$refs.valueChart?.clearChart();
            this.$refs.valueChart?.updateChart(this.getValueChartOption());
          });
        })
        .finally(() => {
          this.costAndOutputLoading = false;
        });
    },
    getCostChartOption() {
      const option = this.getProgressCircleOption(
        this.costAndOutput.rate,
        100,
        this.$toFixed2(this.costAndOutput.deviationRate) + "%",
        "偏差率",
        "#BDE8D1", // 背景色
        "#3DCC85", // 进度条颜色
        "#0F9954" // 进度条颜色2
      );
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">目标成本: ${this.$formatNull(
            this.costAndOutput.targetAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">动态成本: ${this.$formatNull(
            this.costAndOutput.dynamicAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">偏差率: ${this.$formatNull(
            this.costAndOutput.deviationRate
          )}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(this.costAndOutput.rate)}%</div>
          </div>`;
        },
      };
      return option;
    },
    getChangeChartOption() {
      const option = this.getProgressCircleOption(
        this.changeVisa.countChangeVisaRate,
        100,
        this.$toFixed2(this.changeVisa.countChangeVisaRate) + "%",
        "变签率",
        "#CCE4FF",
        "#376DF7",
        "#1433CC"
      );

      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          return `<div style="font-size: ${fontSize}px;>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">变更率: ${this.$formatNull(this.changeVisa.changeRate)}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">变更金额: ${this.$formatNull(
            this.changeVisa.changeAmount
          )}万</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">签证率: ${this.$formatNull(this.changeVisa.visaRate)}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">签证金额: ${this.$formatNull(
            this.changeVisa.visaAmount
          )}万</div>
          </div>`;
        },
      };
      return option;
    },
    getValueChartOption() {
      const option = this.getProgressCircleOption(
        this.costAndOutput.outputRate,
        100,
        this.$toFixed2(this.costAndOutput.outputRate) + "%",
        "产值率",
        "#F9DCC2",
        "#FF974C",
        "#e56d16"
      );
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          return `<div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">目标成本: ${this.$formatNull(
            this.costAndOutput.totalTargetAmount
          )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计金额: ${this.$formatNull(
            this.costAndOutput.outputAmount
          )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">产值率: ${this.$formatNull(
            this.costAndOutput.outputRate
          )}%</div>
            </div>`;
        },
      };
      return option;
    },
    getProgressCircleOption(
      finishNum = 0,
      total = 100,
      text = "",
      subText = "",
      bgColor = "#3DCC85",
      color = "#376DF7",
      color2 = "#4D7EF7"
    ) {
      let actualBgColor = bgColor;
      let actualColor = color;
      let displayNum = finishNum;
      const scale = window.innerWidth / 1680;
      if (finishNum > total) {
        actualBgColor = color;
        actualColor = color2;
        displayNum = (finishNum % total).toFixed(2);
      }

      const instance = new ProgressCircleOption();
      instance.setBackgroundStyle(actualBgColor);
      instance.setBarItemStyle(actualColor);
      const option = JSON.parse(JSON.stringify(instance.getOption()));

      option.series[0].data = [displayNum];
      option.angleAxis.max = total;
      option.series[0].roundCap = option.series[0].data[0] > 3;
      option.title = {
        show: false,
        text: text,
        subtext: subText,
        textStyle: {
          color: actualColor,
        },
      };
      option.polar.radius = ["80%", "100%"];
      return option;
    },
    // handleResize() {
    //   this.chartsLoading = true
    //   this.$nextTick(() => {
    //     setTimeout(() => {
    //       this.chartsLoading = false
    //     }, 0)
    //   })
    // },
  },
};
</script>

<style scoped lang="scss">
@import "~@/views/projects/styles/projects.scss";
.card-content {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0.25rem;
}

.cost-content {
  .card-content {
    padding: 1rem;
    box-sizing: border-box;
  }

  .card-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem;
    color: #222222;
    line-height: 1.375rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .flex-space{
    display: flex;
    justify-content: space-between;
    margin-right: 10px;
  }

  .chart-size {
    width: 10rem;
    height: 10rem;
    // margin-left: 50%;
    // transform: translateX(-50%);
  }

  .cost-card-item {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }

  .cost-card-item-content {
    .title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 0.875rem;
      color: #222222;
      line-height: 1.375rem;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    flex: 1;
    padding: 0.75rem;
    border-radius: 0.25rem;

    &:first-child {
      background: rgba(61, 204, 133, 0.05);
    }

    &:nth-child(2) {
      background: rgba(0, 106, 255, 0.05);
    }

    &:nth-child(3) {
      background: rgba(255, 151, 77, 0.05);
    }

    .data-content {
      font-family: PingFang SC, PingFang SC;
      line-height: 1.375rem;
      text-align: center;
      font-style: normal;
      text-transform: none;

      .value-item {
        .value {
          font-weight: 500;
          font-size: 1.25rem;
          color: #3dcc85;
        }

        .label {
          font-weight: 400;
          font-size: 0.75rem;
          color: #666666;
        }
      }
    }

    &.cost {
      .value-item {
        .value {
          font-weight: 500;
          font-size: 1.25rem;
          color: #3dcc85;
        }
      }
    }

    &.change {
      .value-item {
        .value {
          font-weight: 500;
          font-size: 1.25rem;
          color: #376df7;
        }
      }
    }

    &.value {
      .value-item {
        .value {
          font-weight: 500;
          font-size: 1.25rem;
          color: #ff974c;
        }
      }
    }
  }

  .data-content {
    padding: 0 0.75rem 0.75rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}

.el-table {
  :deep(.el-table__expanded-cell) {
    padding: 0.625rem 3.125rem;
  }

  :deep(.el-table__expand-icon) {
    transform: rotate(0deg);

    &.el-table__expand-icon--expanded {
      transform: rotate(90deg);
    }
  }
  :deep(.el-table__row) {
    .cell {
      padding: 0.4rem 0;
      line-height: 1rem;
    }
  }
}

.expand-icon {
  cursor: pointer;
}
.line-chart {
  width: 100%;
  height: 15rem;
}
.card-with-title {
  display: flex;
  justify-content: center;
}
</style>
