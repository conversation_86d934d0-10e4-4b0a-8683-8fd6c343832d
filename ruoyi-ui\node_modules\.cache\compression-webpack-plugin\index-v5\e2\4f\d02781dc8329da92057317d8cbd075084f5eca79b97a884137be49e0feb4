
ce7993dd855f3d9ff5ab4ed70ecfb7a5d27e7604	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"b4897158b44b28d42abcbcfe2c2e9848\"}","integrity":"sha512-6VtGj8aO0x9KaZ6Tg811BwM55DwFtp2e/kEff9Aoc9p3tbr16wYJLllVDuwHMFyo3rFEtTOqBtBouVXeGH6nhQ==","time":1754311729461,"size":12045173}