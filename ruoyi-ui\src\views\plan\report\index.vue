<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="auto"
      abel-position="right"
    >
      <el-row justify="start">
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <el-form-item label="城市公司" prop="deptNum">
            <el-select
              v-model="queryParams.deptNum"
              placeholder="请选择城市公司"
              clearable
              @change="handleQueryProject"
            >
              <el-option
                v-for="dict in cityCompanys"
                :key="dict.deptNum"
                :label="dict.deptName"
                :value="dict.deptNum"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <el-form-item label="项目名称" prop="projectName">
            <el-select
              v-model="queryParams.projectName"
              placeholder="请选择项目"
              clearable
              filterable
            >
              <el-option
                v-for="dict in storeProjectList"
                :key="dict.name"
                :label="dict.name"
                :value="dict.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-if="showAllSearch">
          <el-form-item label="到期预警时间(天)" prop="deptNum">
            <el-select
              v-model="queryParams.expireNum"
              placeholder="请选择到期预警时间"
            >
              <el-option
                v-for="item in dict.type.warning_day"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col  :xs="24" :sm="24" :md="12" :lg="8" :xl="8"  v-if="showAllSearch">
          <el-form-item label="节点状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择节点状态"
              clearable
            >
              <el-option
                v-for="dict in nodeStatusOption"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-if="showAllSearch">
          <el-form-item label="分期" prop="stageId">
            <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                       clearable
                       filterable>
              <el-option
                v-for="dict in stages"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-if="showAllSearch">
          <el-form-item label="标段" prop="lot" v-show="showAllSearch">
            <el-select v-model="queryParams.lot" placeholder="请选择标段"
                       clearable
                       filterable>
              <el-option
                v-for="dict in projectLot"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col  :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-if="showAllSearch">
          <el-form-item label="责任部门" prop="department">
            <el-select clearable v-model="queryParams.department" placeholder="请选择责任部门">
              <el-option v-for="dict in dict.type.resp_department"
                         :key="dict.value"
                         :label="dict.label"
                         :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16"  v-if="showAllSearch">
          <el-form-item label="计划完成时间段">
            <div class="date-box">
              <el-date-picker
                v-model="queryParams.startTime"
                type="date"
                format="yyyy-MM-dd"
                @change="getStartDate"
                placeholder="开始日期">
              </el-date-picker>
             <span style="margin: 0 10px;color:#999">-</span>
              <el-date-picker
                v-model="queryParams.endTime"
                type="date"
                format="yyyy-MM-dd"
                @change="getEndDate"
                placeholder="结束日期">
              </el-date-picker>
            </div>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-if="showAllSearch">
          <el-form-item label="反馈人" prop="feedbackUser">
            <div class="flex-row">
              <div style="margin-right: 15px">
                {{ feedBackUserInfo ? feedBackUserInfo.nickName : "" }}
              </div>
              <el-button type="primary" @click="selectFeedbackFun" size="mini"
              >选择</el-button
              >
            </div>
          </el-form-item>
        </el-col>
<!--      </el-row>
      <el-row>-->
        <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="primary"
              :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
              size="mini"
              @click="moreFilter"
              >更多筛选</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport(1)"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain size="mini" @click="handleRation"
          >达成率</el-button
        >
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="infoList">
      <el-table-column
        label="序号"
        align="center"
        prop="id"
        width="55"
        fixed="left"
      >
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption" />
        </template>
      </el-table-column>
      <el-table-column
        label="节点名称"
        align="center"
        prop="nodeName"
      >
      </el-table-column>
      <el-table-column label="城市公司" align="center" prop="cityCompany" />
      <el-table-column
        label="项目"
        align="center"
        prop="projectName"
      >
      </el-table-column>
      <el-table-column
        label="分期"
        align="center"
        prop="stageName"
      >
      </el-table-column>
      <el-table-column
        label="标段"
        align="center"
        prop="lot"
      >
      </el-table-column>
      <el-table-column
        label="计划开始时间"
        align="center"
        prop="startTime"
      >
      </el-table-column>
      <el-table-column
        label="计划完成时间"
        align="center"
        prop="endTime"
      >
      </el-table-column>
      <el-table-column
        label="实际完成时间"
        align="center"
        prop="actualCompletionDate"
        width="120"
      >
        <template slot-scope="scope">
          <div class="time">
            {{ scope.row.actualCompletionDate || "-" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="实际偏差"
        align="center"
        prop="deviationNum"
        width="120"
      >
        <template slot-scope="scope">
          <div class="text">
            {{ scope.row.deviationNum || "-" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="责任部门"
        align="center"
        prop="department"
        width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.department)">{{ scope.row.department || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="反馈人" align="center" prop="feedbackUserName">
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 完成率表单 -->
    <el-dialog
      :title="title"
      :visible.sync="showRation"
      width="900px"
      append-to-body
      v-if="showRation"
    >
      <el-button
        type="primary"
        @click="handleExport(2)"
        style="margin-bottom: 10px"
        >导出</el-button
      >
      <el-table v-loading="loading" :data="rateList">
        <el-table-column
          label="序号"
          align="center"
          prop="id"
          width="55"
          fixed="left"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.index }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="queryParams.deptNum"
          label="项目"
          align="center"
          prop="projectName"
          width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-else
          label="城市公司"
          align="center"
          prop="cityCompany"
          width="150"
          fixed="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="已延期(个)" align="center" prop="delay">
        </el-table-column>
        <el-table-column label="即将延期(个)" align="center" prop="soonDelay">
        </el-table-column>
        <el-table-column label="按期完成" align="center" prop="success">
        </el-table-column>
        <el-table-column label="节点总数" align="center" prop="nodeTotal">
        </el-table-column>
        <el-table-column label="达成率" align="center" prop="rate" />
      </el-table>
    </el-dialog>
    <select-depart
      ref="departRef"
      :selectDepartIds="departInfo"
      @departEmit="selectDepartData"
      :single="true"
    />
    <select-user
      ref="userRef"
      :roleId="feedBackUserInfo.userName"
      @feedbackEmit="selectFeedbackData"
    />
  </div>
</template>

<script>
import { reportList, getProjectRate, getCityRate, cityRateExport, projectRateExport } from "@/api/plan/report"
import { listProject } from "@/api/plan/project"
import { nodeStatusOption } from '@/constant'
import StatusTag from "@/views/plan/components/StatusTag/index.vue"
import SelectUser from '@/views/plan/components/SelectUser'
import { mapState } from 'vuex';
import mixin from "@/mixins";
export default {
  mixins: [mixin],
  name: "Report",
  dicts: ['warning_day','resp_department' /*责任部门*/],
  components: { StatusTag, SelectUser},
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 总条数
      total: 0,
      // 计划编制表格数据
      infoList: [],
      // 弹出层标题
      title: "节点达成率",
      // 是否显示弹出层
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        deptNum: null,
        projectName: null,
        nodeCode: null,
        startTime: null,
        endTime: null,
        expireNum: null,//到期预警时间
        status: null,
        feedbackUser: null,
        department: null
      },
      projectList: [],
      planId: null,
      showHistoryDialog: false,
      rowData: null,
      dialogVisible: false,
      approveUserInfo: null,
      showPlanAndNodeDialog: false,
      showRation: false,
      showAllSearch: false,
      selectDepartIds: [],
      roleName: null,
      rateList: [],
      feedBackUserInfo: { userName: '' },
      departInfo: { department: "", deptName: "" },
      stages:[],
      projectLot:[],
    }
  },
  mounted () {
    this.getdictsHandle()
    this.$store.dispatch('plan/fetchDepartList')
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList')
    this.getConfigKey("node.compute.expire.day").then(response => {
      this.queryParams.expireNum = response.msg
      this.getList()
      //获取部门列表
    })
  },
  watch: {
    // dateRange: function (newval, oldval) {
    //   this.queryParams.startTime = !newval ? null : this.parseTime(newval[0], "{y}-{m}-{d}")
    //   this.queryParams.endTime = !newval ? null : this.parseTime(newval[1], "{y}-{m}-{d}")
    // },
    'queryParams.deptNum': {
      handler (newVal, oldVal,) {
        if (!this.showRation) return null
        if (newVal && newVal != oldVal) {
          this.fetchCityRate()
        } else {
          this.fetchProjectRation()
        }
      }
    },
    // 'form.deptNum': {
    //   handler (newVal, oldVal,) {
    //     if (newVal && newVal != oldVal) {
    //       this.fetchProjectByCity(newVal)
    //     }
    //   }
    // },
    // 'queryParams.deptNum': {
    //   handler (newVal, oldVal,) {
    //     if (newVal && newVal != oldVal) {
    //       this.fetchProjectByCity(newVal)
    //     }
    //   }
    // }
  },
  computed: {
    cityCompanys () {
      return [{ deptName: '全部', deptNum: null }, ...this.$store.state.plan.deptList]
    },
    nodeStatusOption () {
      return nodeStatusOption
    },
    ...mapState('plan', {storeProjectList: 'projectList'})
  },
  methods: {
    handleQueryProject(value){
      this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
    },
    getStartDate(date){
      this.queryParams.startTime=this.parseTime(date, "{y}-{m}-{d}")
    },
    getEndDate(date){
      this.queryParams.endTime=this.parseTime(date, "{y}-{m}-{d}")
    },
    async fetchCityRate () {
      this.title = '城市节点完成率'
      this.loading = true
      return getCityRate(this.queryParams).then(res => {
        let data = res.data
        data.cityRateDtoList.map((item, index) => {
          item.index = index + 1
        })
        let totalRote = {
          index: '综合',
          cityCompany: '',
          delay: data.delayTotal,
          rate: data.avgRate,
          soonDelay: data.soonDelayTotal,
          success: data.successTotal,
          nodeTotal: data.total,

        }
        this.rateList = [...data.cityRateDtoList, totalRote]
        this.loading = false
      }).catch(err => {
        this.loading = false
      })
    },
    async fetchProjectRation () {
      let index = this.cityCompanys.findIndex(item => item.deptNum == this.queryParams.deptNum)
      this.title = this.cityCompanys[index].deptName + '节点完成率'
      this.loading = true
      await getProjectRate(this.queryParams).then(res => {
        let data = res.data
        data.projectRateDtoList.map((item, index) => {
          item.index = index + 1
        })
        let totalRote = {
          index: '综合',
          cityCompany: '',
          delay: data.delayTotal,
          rate: data.avgRate,
          soonDelay: data.soonDelayTotal,
          success: data.successTotal,
          nodeTotal: data.total,
        }
        this.rateList = [...data.projectRateDtoList, totalRote]
        this.loading = false
      }).catch(err => {
        this.loading = false
      })
    },
    selectFeedbackData (data) {
      this.feedBackUserInfo = data
      this.queryParams.feedbackUser = data.userName
    },
    selectDepartData (data) {
      this.departInfo = data
      this.queryParams.department = data.department
    },
    selectFeedbackFun () {
      this.$refs.userRef.show()
    },
    selectDepartFun () {
      this.$refs.departRef.show()
    },
    moreFilter () {
      this.showAllSearch = !this.showAllSearch
      if (!this.showAllSearch) {
        this.queryParams.feedbackUser = null
        this.queryParams.department = null
        this.resetOther()
        this.handleQuery()
      }
    },
    async handleRation () {
      //达成率弹窗
      this.showRation = true
      if (this.queryParams.deptNum) {
        await this.fetchProjectRation()
      } else {
        await this.fetchCityRate()
      }
    },
    // 获取城市公司下面的项目
    fetchProjectByCity (params) {
      listProject({
        name: '',
        company: params
      }).then(res => {
        this.projectList = res.rows
        this.queryParams.projectId = null
        this.loading = false
      }).catch(err => {
        this.loading = false

      })
    },
    /** 查询计划编制列表 */
    getList () {
      this.loading = true
      reportList(this.queryParams).then(response => {
        console.log(JSON.parse(JSON.stringify(response)))
        this.infoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      console.log(this.queryParams)
      this.getList()
    },
    resetOther () {
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.feedBackUserInfo = { userName: '' }
      this.departInfo = { department: "", deptName: "" }
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetOther()
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport (type) {
      let title = this.title
      let url = 'plan/report/cityRateExport'
      if (type == 1) {
        url = 'plan/report/export'
        title = '计划报表'
      } else if (this.queryParams.deptNum) {
        url = 'plan/report/projectRateExport'
      }
      this.download(url, {
        ...this.queryParams
      }, `${title}_${new Date().getTime()}.xlsx`)
    },
    getdictsHandle(){
      this.getDicts('stages').then(res=>{
        let data=res.data
        this.stages=data.map(item=>{
          return {
            label:item.dictLabel,
            value:item.dictValue,
            raw:item
          }}
        )
      }).catch(err=>{
        console.log(err)
      })


      this.getDicts('project_lot').then(res=>{
        let data=res.data
        this.projectLot=data.map(item=>{
          return {
            label:item.dictLabel,
            value:item.dictValue,
            raw:item
          }}
        )
      }).catch(err=>{
        console.log(err)

      })

    },
  }
};
</script>
<style scoped>
.infoStyle {
  height: 34px;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
