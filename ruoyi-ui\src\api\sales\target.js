import request from '@/utils/request'

// 查询销售目标列表
export function listTarget(query) {
  return request({
    url: '/sales/targetData/list',
    method: 'get',
    params: query
  })
}

// 查询销售目标详细
export function getTarget(id) {
  return request({
    url: '/sales/targetData/' + id,
    method: 'get'
  })
}

// 新增销售目标
export function addTarget(data) {
  return request({
    url: '/sales/targetData',
    method: 'post',
    data: data
  })
}

// 修改销售目标
export function updateTarget(data) {
  return request({
    url: '/sales/targetData',
    method: 'put',
    data: data
  })
}

// 删除销售目标
export function delTarget(id) {
  return request({
    url: '/sales/targetData/' + id,
    method: 'delete'
  })
}

// 获取城市公司
export function getCity(data) {
  return request({
    url: '/sales/getCity',
    method: 'post',
    data: data
  })
}

// 获取城市公司下所有项目
export function getProject(cityName) {
  return request({
    url: '/sales/getProject/' + cityName,
    method: 'get'
  })
}
