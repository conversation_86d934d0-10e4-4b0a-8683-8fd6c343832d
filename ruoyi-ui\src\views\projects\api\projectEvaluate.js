import request from '@/utils/request'

// 获取过程评估历史数据
function processAssess(projectCode) {
  return request({
    url: `/project/gc/processAssess/${projectCode}`,
    method: 'get',
  })
}

// 获取交互评估历史数据
function deliverAssess(projectCode) {
  return request({
    url: `/project/gc/deliverAssess/${projectCode}`,
    method: 'get',
  })
}

// 获取地下工程评估历史数据
function undergroundAssess(projectCode) {
  return request({
    url: `/project/gc/undergroundAssess/${projectCode}`,
    method: 'get',
  })
}

// 获取材料飞检历史数据
function materialCheck(projectCode) {
  return request({
    url: `/project/gc/materialCheck/${projectCode}`,
    method: 'get',
  })
}

// 获取大型机械检查历史数据
function largeMachineryCheck(projectCode) {
  return request({
    url: `/project/gc/largeMachineryCheck/${projectCode}`,
    method: 'get',
  })
}

// 获取过程评估详情
function processAssessDetail(gcAssessDetailReq) {
  return request({
    url: `/project/gc/processAssess/detail`,
    method: 'get',
    params: gcAssessDetailReq,
    data: gcAssessDetailReq
  })
}

// 获取交互评估详情
function deliverAssessDetail(gcAssessDetailReq) {
  return request({
    url: `/project/gc/deliverAssess/detail`,
    method: 'get',
    params: gcAssessDetailReq
  })
}

// 获取地下工程评估详情
function undergroundAssessDetail(gcAssessDetailReq) {
  return request({
    url: `/project/gc/undergroundAssess/detail`,
    method: 'get',
    params: gcAssessDetailReq
  })
}

// 获取材料飞检详情
function materialCheckDetail(gcAssessDetailReq) {
  return request({
    url: `/project/gc/materialCheck/detail`,
    method: 'get',
    params: gcAssessDetailReq
  })
}

// 获取大型机械检查详情
function largeMachineryCheckDetail(gcAssessDetailReq) {
  return request({
    url: `/project/gc/largeMachineryCheck/detail`,
    method: 'get',
    params: gcAssessDetailReq
  })
}

export default {
  processAssess,
  deliverAssess,
  undergroundAssess,
  materialCheck,
  largeMachineryCheck,
  processAssessDetail,
  deliverAssessDetail,
  undergroundAssessDetail,
  materialCheckDetail,
  largeMachineryCheckDetail
}
