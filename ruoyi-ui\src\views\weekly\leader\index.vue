<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            class="tree-container"
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            @node-click="handleNodeClick"
            :highlight-current="true"
            :default-expanded-keys="defaultExpandedKeys"
            node-key="id"
          />
        </div>
      </el-col>

      <el-col :span="20" :xs="24" class="right-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="正职领导" prop="leaderNickName">
            <el-input
              v-model="queryParams.leaderNickName"
              placeholder="请输入正职领导名称"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="副职领导" prop="deputyLeaderNickName">
            <el-input
              v-model="queryParams.deputyLeaderNickName"
              placeholder="请输入副职领导名称"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['weekly:leader:export']"
            >导出</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="leaderList" @selection-change="handleSelectionChange" class="dept-list">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="单位名称" align="center" prop="unitName" />
          <el-table-column label="部门上级" align="center" prop="deptParentName" />
          <el-table-column label="部门名称" align="center" prop="deptName" />
          <el-table-column label="正职领导用户名" align="center" prop="leaderUserName" />
          <el-table-column label="正职领导" align="center" prop="leaderNickName" />
          <el-table-column label="副职领导用户名" align="center" prop="deputyLeaderUserName" />
          <el-table-column label="副职领导" align="center" prop="deputyLeaderNickName" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['weekly:leader:edit']"
              >修改</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改周报部门领导信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body @close="handleDialogClose">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="单位名称" prop="unitName" >
          <el-input v-model="form.unitName" readonly/>
        </el-form-item>
        <el-form-item label="部门上级" prop="deptParentName">
          <el-input v-model="form.deptParentName" readonly/>
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" readonly/>
        </el-form-item>
        <el-form-item label="正职领导用户名" prop="leaderUserName">
          <el-input v-model="form.leaderUserName" type="textarea"  readonly/>
        </el-form-item>
        <el-form-item label="正职领导" prop="leaderNickName">
          <el-input v-model="form.leaderNickName" type="textarea" readonly/>
        </el-form-item>
        <el-form-item label="副职领导用户名" prop="deputyLeaderUserName">
          <el-input v-model="form.deputyLeaderUserName" type="textarea" disabled="" />
        </el-form-item>
        <el-form-item label="副职领导" prop="deputyLeaderNickName">
          <el-input v-model="form.deputyLeaderNickName" class="display-none" type="textarea" placeholder="请输入内容" />
          <el-select
            class="width-100"
            @focus="handleOpenUserDialog"
            v-model="userCode"
            multiple
            clearable
            placeholder="请选择"
            :popper-append-to-body="false"
            :popper-class="'select-hidden-dropdown'"
            @change="handleChange"
          >
            <el-option v-for="item in reportReviewDetails" :key="item.nickName" :label="item.nickName"
                       :value="item.nickName"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <select-user
      v-if="selectUserShow"
      ref="userRef"
      :selectMultiple="true"
      @feedbackEmit="handleSelectUser"
    />
  </div>
</template>

<script>
  import {getLeader, listLeader, addLeader, updateLeader} from "@/api/weekly/leader";
  import { deptTree } from '@/api/weekly/reportInfo'
  import SelectUser from '../components/selectUser.vue'
  import Treeselect from '@riophae/vue-treeselect'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  export default {
  name: "Leader",
  components: {
    SelectUser,
    Treeselect
  },
  data() {
    return {
      defaultExpandedKeys: [],
      deptOptions: undefined,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deptName: '',
      selectUserShow: false,
      userCode: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 周报部门领导信息表格数据
      leaderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptId: null,
        leaderUserName: null,
        leaderNickName: null,
        deputyLeaderUserName: null,
        deputyLeaderNickName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      reportReviewDetails: []
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList();
    this.getTreeselect();
  },
  methods: {
    setDefaultExpandedKeys() {
      // 如果有数据，默认展开第一层的子节点（即第二层级）

      if (this.deptOptions && this.deptOptions.length > 0) {
        // 获取第一层节点的ID
        const firstLevelNode = this.deptOptions[0];
        if (firstLevelNode.children && firstLevelNode.children.length > 0) {
          // 将第一层节点的ID添加到默认展开的keys中
          this.defaultExpandedKeys = [firstLevelNode.id];
          console.log("defaultExpandedKeys", this.defaultExpandedKeys);
        }
      }
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      deptTree().then(response => {
        this.deptOptions = response.data
        this.setDefaultExpandedKeys()
      })
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.getList()
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleDialogClose() {
      this.selectUserShow = false
    },
    handleChange(value) {
      this.reportReviewDetails = this.reportReviewDetails.filter(
        (item) => this.userCode.includes(item.nickName)
      )
      console.log('reportReviewDetails---', this.reportReviewDetails)
      this.form.deputyLeaderUserName = this.reportReviewDetails.map(
        (item) => item.userName
      ).join(',')
      this.form.deputyLeaderNickName = this.userCode.join(',')
    },
    handleSelectUser(data) {
      console.log('data---------', data)
      // 过滤掉已经存在的用户
      /* const newUsers = data.filter(
        (newUser) =>
          !this.reportReviewDetails.some(
            (existingUser) => existingUser.userName === newUser.userName
          )
      )

      // 只添加新的用户
      this.reportReviewDetails.push(...newUsers) */
      this.reportReviewDetails = data

      // 更新选中的用户ID数组
      this.userCode = this.reportReviewDetails.map(
        (item) => item.nickName
      )
      this.form.deputyLeaderNickName = this.userCode.join(',')
      this.form.deputyLeaderUserName = this.reportReviewDetails.map(
        (item) => item.userName
      ).join(',')
    },
    handleOpenUserDialog() {
      // this.$refs.userRef.queryParams.userName = this.approveUserName
      this.$refs.userRef.show()
    },
    /** 查询周报部门领导信息列表 */
    getList() {
      this.loading = true;
      listLeader(this.queryParams).then(response => {
        this.leaderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.selectUserShow = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        unitName: null,
        deptParentName: null,
        deptId: null,
        leaderUserName: null,
        leaderNickName: null,
        deputyLeaderUserName: null,
        deputyLeaderNickName: null
      };
      this.reportReviewDetails = []
      this.userCode = []
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加周报部门领导信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.selectUserShow = true;
      this.reset();
      const id = row.id || this.ids
      getLeader(id).then(response => {
        this.form = response.data;
        this.reportReviewDetails = response.data.deputyLeaderNickName?.split(',')
        this.userCode = response.data.deputyLeaderNickName?.split(',')
        this.open = true;
        this.title = "修改周报部门领导信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLeader(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.selectUserShow = false;
            });
          } else {
            addLeader(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.selectUserShow = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除周报部门领导信息编号为"' + ids + '"的数据项？').then(function() {
        return delLeader(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('weekly/leader/export', {
        ...this.queryParams
      }, `leader_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped lang="scss">
.app-container {
  height: calc(100vh - 90px);
}
.head-container{
  .tree-container{
    max-height: calc(100vh - 90px - 90px);
    overflow-y: auto;
  }
}
.right-container{
  max-height: calc(100vh - 90px - 90px);
}
.dept-list{
  max-height: calc(100vh - 260px);
  overflow-y: auto;
}

/* 添加树节点文本换行样式 */
.tree-container {
  :deep(.el-tree-node__label) {
    white-space: normal;
    word-break: break-all;
  }

  :deep(.el-tree-node__content) {
    height: auto;
    min-height: 26px;
  }
}
.width-100{
  width: 100%;
}
.display-none{
  display: none;
}
/* Hide dropdown options */
:deep(.select-hidden-dropdown) {
  display: none !important;
}
</style>
