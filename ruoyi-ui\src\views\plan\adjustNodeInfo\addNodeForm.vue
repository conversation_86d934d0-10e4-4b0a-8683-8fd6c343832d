<template>
  <div>
    <!-- 添加或修改计划-节点对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      v-if="open"
      width="900px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="节点名称" prop="nodeName">
              <el-input v-model="form.nodeName" :disabled="adjustType == 2" placeholder="请输入节点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点描述" prop="nodeDesc">
              <el-input v-model="form.nodeDesc" :disabled="adjustType == 2" placeholder="请输入节点描述" />
            </el-form-item>
          </el-col>
          </el-row>
            <el-row>
          <el-col :span="12">
            <el-form-item label="责任部门" prop="departmentArr">
              <el-select :disabled="adjustType == 2" v-model="form.departmentArr" multiple placeholder="请选择" size="small" @change="selectDepartFun" >
                <el-option
                  v-for="item in departmentsData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="反馈人" prop="feedbackUserName">
              <div style="width: 220px" class="flex-row">
                <div class="feedback-people-container" v-if="!!form.feedbackUserName">
                  <div style="margin-right: 15px">
                    {{ form.feedbackUserName }}
                  </div>
                  <i class="el-icon-circle-close" @click="clearFeedbackUserInfo"></i>
                </div>

                <el-button type="primary" @click="selectFeedbackFun" size="mini"
                  >选择</el-button
                >
              </div>
            </el-form-item>
          </el-col>
            </el-row>
        <el-row>
          <el-col :span="12"  v-if="adjustType != 2">
            <el-form-item label="计划开始时间" prop="startTime">
              <div style="width: 207px">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="form.startTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择计划开始时间"
                  @change="changeData($event,1)"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="adjustType != 2">
            <el-form-item label="计划完成时间" prop="endTime">
              <div style="width: 208px">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="form.endTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择计划完成时间"
                  @change="changeData($event,2)"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="实际完成时间" prop="actualCompletionDate">
              <div style="width: 208px">
                <el-date-picker
                  disabled
                  clearable
                  size="small"
                  v-model="form.actualCompletionDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item prop="isValid" label="是否可用" width="120">
              <el-select
                v-model="form.isValid"
                :disabled="(form.isGrey === 0 && form.isValid == 1) || adjustType == 2"
                clearable
              >
                <el-option
                  v-for="option in isValidOption"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标准工期" prop="durationNum">
              <el-input
                type="number"
                v-model="form.durationNum"
                :disabled="adjustType == 2"
                placeholder="请输入标准工期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业成果设置" prop="resultConfigId">
              <div class="flex-row">
                <div>{{ form.resultConfigName }}</div>
                <el-button
                  size="mini"
                  type="primary"
                  @click="settingFun"
                  :disabled="adjustType == 2"
                  style="margin-left: 10px"
                  >设置</el-button
                >
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="完成标准" prop="completeCriteria">
              <el-input
                v-model="form.completeCriteria"
                placeholder="完成标准"
                type="textarea"
                autosize
                :disabled="adjustType == 2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
      <result-config
        ref="resultRef"
        :defaultResult="defaultResult"
        @settingEmit="settingEmit"
      ></result-config>
    </el-dialog>
  </div>
</template>

<script>
import { isValidOption } from '@/views/plan/constant'
import variables from '@/assets/styles/element-variables.scss'
import ResultConfig from "../components/ResultConfig/index.vue"

export default {
  name: "Node",
  components: { ResultConfig },
  props:{
    departmentsData: {
      type: Array,
      default: []
    },
    adjustType: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        nodeLevel: null,
        nodeName: null,
        planId: null,
        isAdjust: 1
      },
      // 表单参数
      form: {},
      // 表单校验`
     /* rules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" }
        ],
        durationNum: [
          { required: false, message: "标准工期不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        endTime: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        isEdit: [
          { required: true, message: "是否可以编辑不能为空", trigger: "change" }
        ],
        departmentNames: [
          { required: true, message: "责任部门不能为空", trigger: "change" }
        ],
        feedbackUserName: [
          { required: true, message: "反馈人不能为空", trigger: "change" }
        ]
      },*/
      /*专业成果*/
      resultList: [],
      showResultDialog: false,
      ids2: [],
      //责任人
      theme: variables.theme,
    }
  },
  computed: {
    rules: function () {
      if(this.adjustType == 2){
        return {
          nodeName: [
            { required: false, message: "节点名称不能为空", trigger: "blur" }
          ],
          departmentArr: [
            { required: true, message: "责任部门不能为空", trigger: "change" }
          ],
          durationNum: [
            { required: false, message: "标准工期不能为空", trigger: "blur" }
          ],
          startTime: [
            { required: false, message: "开始时间不能为空", trigger: "change" }
          ],
          endTime: [
            { required: false, message: "开始时间不能为空", trigger: "change" }
          ],
          isEdit: [
            { required: false, message: "是否可以编辑不能为空", trigger: "change" }
          ],
          departmentNames: [
            { required: false, message: "责任部门不能为空", trigger: "change" }
          ],
          feedbackUserName: [
            { required: true, message: "反馈人不能为空", trigger: "change" }
          ]
        }
      }
      else{
        return {
          nodeName: [
            { required: true, message: "节点名称不能为空", trigger: "blur" }
          ],
          departmentArr: [
            { required: true, message: "责任部门不能为空", trigger: "change" }
          ],
          durationNum: [
            { required: false, message: "标准工期不能为空", trigger: "blur" }
          ],
          startTime: [
            { required: true, message: "开始时间不能为空", trigger: "change" }
          ],
          endTime: [
            { required: true, message: "开始时间不能为空", trigger: "change" }
          ],
          isEdit: [
            { required: true, message: "是否可以编辑不能为空", trigger: "change" }
          ],
          departmentNames: [
            { required: true, message: "责任部门不能为空", trigger: "change" }
          ],
          feedbackUserName: [
            { required: true, message: "反馈人不能为空", trigger: "change" }
          ]
        }
      }

    },
    isValidOption: () => isValidOption,
    defaultResult: function () {
      let listConfig = this.form.resultConfigId ? this.form.resultConfigId.split(',') : []
      let listConfigName = this.form.resultConfigName ? this.form.resultConfigName.split(',') : []
      return listConfig.map((item, index) => {
        return {
          id: item,
          name: listConfigName[index]
        }
      })
    }
  },
  watch: {},
  methods: {
    clearFeedbackUserInfo(){
      this.$emit('clearFeedbackUserInfo');
    },
    changeData(val,type){
      let item=this.form
      if(!item.startTime||!item.endTime)return null;
      let date1 = new Date(item.startTime);
      let date2 = new Date(item.endTime);
      if (type==1&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('开始时间不能大于结束时间')
        item.startTime=null
      } else if (type==2&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('结束时间不能小于开始时间')
        item.endTime=null
      }
    },
    settingEmit (data) {
      this.form.resultConfigId = data.map(item => item.id).join(',')
      this.form.resultConfigName = data.map(item => item.name).join(',')
    },
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))
          // console.log(data)
          this.$emit('handleFormFun', data)
          this.open = false

        }
      })
    },
    show (type, data) {
      // console.log(data, type)
      this.open = true
      this.reset()
      if (type == 0) {
        this.title = "添加计划-节点"
      } else {
        this.form = JSON.parse(JSON.stringify(data))      // const id = row.id || this.ids
        this.title = "修改计划-节点"
      }
    },
    selectDepartFun (val) {
      this.$emit('clickDepart', val)
    },
    selectFeedbackFun () {
      this.$emit('clickFeedback', null)

    },
    settingFun () {
      this.$refs.resultRef.show()
    },
    // 取消按钮
    cancel () {
      this.$emit('cancel')
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        nodeStage: null,//节点状态
        nodeCode: null, //节点顺序
        // nodeLevel: null, //节点等级
        nodeName: null, //节点名字
        startTime: null, //开始时间
        endTime: null, //结束时间
        department: '', //部门ID
        responsibilityPeople: null, //责任人ID
        respPeopleName: null,//责任人名字
        durationNum: null,// 标准工期
        nodeDesc: null,//节点描述
        isValid: 1,
        isGrey: null,//控制是否可用
        departmentNames: null, //部门ID
        feedbackUser: '',
        feedbackUserName: '',
        resultConfigId: '',
        resultConfigName: '',
        adjustType: 2, //调整类型（0-未调整 1-修改 2-新增 3-删除）
        departmentNameArr:[],// 名字数组
        departmentArr:[], // id数组
        status: null
      }
      this.resetForm("form")
    },
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";
.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 150px;
}
.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
