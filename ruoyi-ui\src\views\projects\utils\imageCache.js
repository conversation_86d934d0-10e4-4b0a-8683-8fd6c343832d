// indexedDB.js
const DB_NAME = 'myImageDB';
const STORE_NAME = 'images';
const DB_VERSION = 1;

let dbInstance = null;

/**
 * 初始化 IndexedDB
 * @returns {Promise<IDBDatabase>}
 */
function initDB() {
  return new Promise((resolve, reject) => {
    if (dbInstance) {
      return resolve(dbInstance);
    }

    const request = window.indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('IndexedDB 打开失败', event);
      reject(event);
    };

    // 数据库版本升级时会触发
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        // keyPath 若为 'id' 则存储时需要 { id: key, data: xxxx } 这种写法
        db.createObjectStore(STORE_NAME, { keyPath: 'id' });
      }
    };

    request.onsuccess = (event) => {
      dbInstance = event.target.result;
      resolve(dbInstance);
    };
  });
}

/**
 * 保存图片数据到 IndexedDB
 * @param {string} id  - 存储 key
 * @param {Blob | string} data - 图片 Blob 或 Base64
 * @returns {Promise<void>}
 */
export async function saveImageToDB(id, data) {
  const db = await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    const request = store.put({ id, data });

    request.onsuccess = () => resolve();
    request.onerror = (e) => reject(e);
  });
}

/**
 * 从 IndexedDB 获取图片数据
 * @param {string} id
 * @returns {Promise<Blob|string|null>} - 如果存的是 Blob，则返回 Blob；如果存的是 Base64，则返回 string；没有则返回 null
 */
export async function getImageFromDB(id) {
  const db = await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    const request = store.get(id);

    request.onsuccess = () => {
      const result = request.result;
      resolve(result ? result.data : null);
    };
    request.onerror = (e) => reject(e);
  });
}
