<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="过程评估" name="gcProcessAssess">
        <gc-process-assess></gc-process-assess>
      </el-tab-pane>
      <el-tab-pane label="交付评估数据" name="gcDeliverAssess">
        <gc-deliver-assess></gc-deliver-assess>
      </el-tab-pane>
      <el-tab-pane label="地下工程评估" name="gcUndergroundAssess">
        <gc-underground-assess></gc-underground-assess>
      </el-tab-pane>
      <el-tab-pane label="材料飞检" name="gcMaterialCheck">
        <gc-material-check></gc-material-check>
      </el-tab-pane>
      <el-tab-pane label="大型机械检查" name="gcLargeMachineryCheck">
        <gc-large-machinery-check></gc-large-machinery-check>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import gcProcessAssess from './gcProcessAssessData.vue';
import GcUndergroundAssess from './gcUndergroundAssessData.vue';
import GcMaterialCheck from './gcMaterialCheckData.vue';
import GcLargeMachineryCheck from './gcLargeMachineryCheckData.vue';
import GcDeliverAssess from './gcDeliverAssessData.vue';

export default {
  name: 'GcAssess',
  components: {
    gcProcessAssess,
    GcUndergroundAssess,
    GcMaterialCheck,
    GcLargeMachineryCheck,
    GcDeliverAssess,
  },
  data() {
    return {
      activeTab: 'gcProcessAssess', // 默认激活的标签页
    };
  },
};
</script>
