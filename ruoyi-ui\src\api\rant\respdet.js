import request from '@/utils/request'

// 查询责任人责任部门分管领导关系列表
export function listRespdet(query) {
  return request({
    url: '/rant/respdet/list',
    method: 'get',
    params: query
  })
}

// 查询责任人责任部门分管领导关系详细
export function getRespdet(id) {
  return request({
    url: '/rant/respdet/' + id,
    method: 'get'
  })
}

// 新增责任人责任部门分管领导关系
export function addRespdet(data) {
  return request({
    url: '/rant/respdet',
    method: 'post',
    data: data
  })
}

// 修改责任人责任部门分管领导关系
export function updateRespdet(data) {
  return request({
    url: '/rant/respdet',
    method: 'put',
    data: data
  })
}

// 删除责任人责任部门分管领导关系
export function delRespdet(id) {
  return request({
    url: '/rant/respdet/' + id,
    method: 'delete'
  })
}
