<template>
  <div>
    <template v-if="status !== null && status !== undefined">
      <el-tag
        v-for="option in options"
        :key="option.value"
        v-if="status === option.value"
        :color="option.color"
        :style="{'color': option.fontColor}"
        :type="option.type"
        class="no-transition">
        {{ option.label }}
      </el-tag>
    </template>
    <el-tag v-else type="info" class="no-transition">--</el-tag>
  </div>
</template>

<script>
export default {
  props: {
    status: {
      type: Number,
      default: null
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    setTagFontColor(fontColor) {
      if (fontColor) {
        return { color: fontColor};
      }
    }
  }
};
</script>

<style scoped>
.no-transition {
  transition: none !important;
  animation: none !important;
}
</style>
