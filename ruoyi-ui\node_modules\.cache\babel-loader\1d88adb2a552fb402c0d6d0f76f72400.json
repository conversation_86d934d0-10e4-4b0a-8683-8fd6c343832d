{"remainingRequest": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue", "mtime": 1754312350764}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\babel.config.js", "mtime": 1743918438036}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743920556726}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743920555710}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ProgressBar", "_interopRequireDefault", "require", "_CumulativeIndicatorsCard", "_ProgressCircle", "_Chart", "_bar", "_Pie", "_api", "_CardTabSales", "_empty", "_default", "exports", "default", "name", "components", "CumulativeIndicatorsCard", "ProgressBar", "Chart", "CardTabSales", "Empty", "data", "yearQyData", "yearHkData", "signDataByTime", "loadingAllTargetQYProgress", "loadingAllTargetHKProgress", "hkDataByTime", "queryTypeSignList", "code", "queryTypeSign", "qyData", "rgData", "queryTypeTrendList", "queryTypeTrend", "hkTrendData", "qyTrendData", "rgTrendData", "dfTrendData", "hkTrendRawData", "qyTrendRawData", "rgTrendRawData", "dfTrendRawData", "ProgressCircleOption", "ProgressCircle", "getOption", "cardData", "label", "value", "color", "projectCode", "projectTypeList", "hkProjectType", "rgProjectType", "qyProjectType", "queryType", "allQyData", "allHkData", "kqData", "qyYTData", "tabs", "activeTabRGTrend", "activeTabQYTrend", "tooltipStyles", "blue", "left", "yellow", "dfOption", "rgOption", "qyOption", "hkOption", "analysisData", "hasData", "isDeveloping", "loadingSales", "loadingTimeProgress", "loadingYearProgress", "loadingPayments", "loadingTables", "loadingTrends", "hkOptionName", "dfOptionName", "rgOptionName", "qyOptionName", "created", "$route", "query", "mounted", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getProjectType", "initTrend", "getAllQyData", "getAllHkData", "getkqData", "getQyDfData", "getRgData", "getQYYTData", "fetchAnalyse", "getYearQyData", "getYearHkData", "getAllTargetQyData", "getAllTargetHkData", "stop", "methods", "<PERSON><PERSON><PERSON><PERSON>", "permission", "permissions", "$store", "getters", "includes", "some", "p", "filterRatioMax", "ratio", "getArrowStyle", "concat", "getTooltipStyle", "tooltipClass", "_document$querySelect", "_document$querySelect2", "scale", "window", "innerWidth", "tooltipWidth", "document", "querySelector", "clientWidth", "containerWidth", "tooltipRatio", "textAlign", "Math", "ceil", "right", "floor", "transform", "formatValue", "absValue", "abs", "formattedValue", "unit", "toFixed", "selectTabQYTrend", "index", "updateQYTrendData", "getQyOption", "getBarOption", "yAxis", "axisLabel", "formatter", "series", "params", "tooltip", "show", "filteredQYTrendData", "map", "item", "num", "area", "selectTabRGTrend", "updateRGTrendData", "getRgOption", "filteredRGTrendData", "_this2", "_callee2", "res", "_callee2$", "_context2", "API", "Sales", "bussDist", "sent", "getQYYTOption", "_this3", "option", "getPieOption", "rich", "fontSize", "padding", "align", "total", "labelColor", "hr", "borderColor", "width", "borderWidth", "height", "margin", "_objectSpread2", "$toFixed2", "rate", "bussName", "windowWidth", "confine", "textStyle", "lineHeight", "formatAmount", "amount", "qyAmount", "$formatNull", "labelLine", "minTurnAngle", "normal", "length", "length2", "lineStyle", "legend", "itemGap", "itemWidth", "itemHeight", "bottom", "orient", "icon", "position", "distance", "_this4", "_callee3", "_callee3$", "_context3", "finish", "_this5", "_callee4", "_callee4$", "_context4", "handleTrendTab", "updateCSSVariable", "documentElement", "style", "setProperty", "_this6", "_callee5", "_callee5$", "_context5", "allTargetQyData", "Object", "fromEntries", "entries", "_ref", "_ref2", "_slicedToArray2", "key", "_this7", "_callee6", "_callee6$", "_context6", "allTargetHkData", "_ref3", "_ref4", "_this8", "_callee7", "_callee7$", "_context7", "yearTargetQyData", "_ref5", "_ref6", "_this9", "_callee8", "_callee8$", "_context8", "yearTargetHkData", "_ref7", "_ref8", "_this10", "_callee9", "_callee9$", "_context9", "sanitizeData", "_this11", "_callee10", "_callee10$", "_context10", "qyDfData", "_ref9", "_ref10", "handleTabSign", "_this12", "Value", "analyse", "then", "projectType", "hxName", "totalNum", "ysNum", "wsNum", "ygwsNum", "wgNum", "allTopNum", "allBottomNum", "ysTopNum", "ysBottomNum", "wsTopNum", "wsBottomNum", "allHzAmount", "ysHzAmount", "wsHzAmount", "_this13", "_callee11", "_callee11$", "_context11", "kqTargetAmount", "yearKqAmount", "kjlKqAmount", "handleHKTab", "getHkTrend", "handleRgTab", "getRgTrend", "handleQyTab", "getQyTrend", "_this14", "_callee12", "_callee12$", "_context12", "Common", "_this15", "_callee13", "_callee13$", "_context13", "Promise", "all", "getDfTrend", "getDfOption", "_this16", "_callee14", "_callee14$", "_context14", "dfTrend", "xAxis", "replace", "_this17", "_callee15", "_callee15$", "_context15", "rgTrend", "_this18", "_callee16", "_callee16$", "_context16", "qyTrend", "getHkOption", "_this19", "_callee17", "_callee17$", "_context17", "hkTrend", "arguments", "undefined", "pieOption", "PieOption", "setColor", "type", "barOption", "BarOption", "updateData", "Array", "isArray", "handleSpanMethod", "_ref11", "row", "column", "rowIndex", "columnIndex", "rowspan", "colspan", "startIndex", "spanCount", "i", "computed", "qyProgressOption", "_this20", "progressCircleOption", "setBarItemStyle", "setBackgroundStyle", "setBarData", "title", "subtext", "text", "subtextStyle", "roundCap", "polar", "radius", "dynamicAmount", "hkProgressOption", "_this21", "hkAmount", "qyRate", "kqProgressOption", "_this22", "kqRatio", "qyYTOption"], "sources": ["src/views/projects/views/sales.vue"], "sourcesContent": ["<template>\r\n  <section class=\"projects-content-container w-100\">\r\n    <!-- 无权限状态 -->\r\n    <div v-if=\"!hasPermi(['project:sales:manage'])\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-auth.png\" alt=\"无权限\">\r\n      <div class=\"desc\">暂无权限</div>\r\n      <div class=\"desc\">请联系管理员</div>\r\n    </div>\r\n    <!-- 无数据状态 -->\r\n    <div v-else-if=\"!hasData\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-data.png\" alt=\"无数据\">\r\n      <div class=\"desc\">暂无数据</div>\r\n    </div>\r\n    <section v-else class=\"w-100\">\r\n      <div class=\"sales-content\">\r\n        <!-- 全周期销售进度模块 -->\r\n        <div class=\"sales card mb-16 mt-16\" v-loading=\"loadingSales\">\r\n          <div class=\"card-title\">\r\n            全周期销售进度\r\n              <span class=\"total-target\" v-if=\"hasPermi(['sales:all:price'])\">全周期住宅均价 ¥ {{ allQyData.zzUnitPrice || '--' }}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">签约</div>\r\n                <Empty :no-authority=\"hasPermi(['sales:all:qy'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"qyProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': qyProgressOption.title.textStyle.color}\">\r\n                            {{ qyProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ qyProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.qyAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计签约</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计签约</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzQyAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDyAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"flex-item hk-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">回款</div>\r\n<!--                <div class=\"progress-item-title\">回款（权益）</div>-->\r\n                <Empty :no-authority=\"hasPermi(['sales:all:hk'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"hkProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': hkProgressOption.title.textStyle.color}\">\r\n                            {{ hkProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ hkProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">动态货值（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.hkAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计回款</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">累计回款（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">{{ allHkData.qyRate }}%</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">权益比例</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n<!--                          <td class=\"progress-label\">动态货值（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计回款</td>\r\n<!--                          <td class=\"progress-label\">累计回款（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzHkAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzHkAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 全周期进度偏差模块 -->\r\n        <div class=\"time-progress card mb-16 mt-16\">\r\n          <div class=\"card-title\">全周期进度偏差</div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetQYProgress\">\r\n              <div class=\"target-title mb-4rem\">签约</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:all:qyDeviation'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': signDataByTime.openDate ? `'${signDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': signDataByTime.liquidateDate ? `'${signDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue sign-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdRate), 'sign-tooltip-1')\">\r\n                          <div>时点完成{{$toFixed2(signDataByTime.qyAmount)}}亿（{{$toFixed2(signDataByTime.allSdRate)}}%）</div>\r\n                          <div>全盘动态货值{{$toFixed2(signDataByTime.dynamicAmount)}}亿</div>\r\n                          <div>偏差率{{$toFixed2(signDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow  sign-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdTargetRate),  'sign-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(signDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(signDataByTime.targetAmount)}}亿</div>\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(signDataByTime.zzQyAmount)}}亿（{{$toFixed2(signDataByTime.zzSdRate)}}%)</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(signDataByTime.zzDynamicAmount)}}亿</td>\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(signDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(signDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{ signDataByTime.zzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{ $formatNull(signDataByTime.zzLiquidateDate) }}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td  class=\"progress-label\">非住: 时点完成{{$toFixed2(signDataByTime.noZzQyAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdRate)}}%)</td>\r\n                      <td>| 动态货值{{$toFixed2(signDataByTime.noZzDynamicAmount)}}亿</td>\r\n                      <td>| 时点目标{{$toFixed2(signDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdTargetRate)}}%)</td>\r\n                      <td>| 目标货值{{$toFixed2(signDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td> | 清盘日期{{ signDataByTime.noZzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"pl-date\">清盘日期 {{ $formatNull(signDataByTime.noZzLiquidateDate) }}</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div></Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetHKProgress\">\r\n              <div class=\"target-title mb-4rem\">回款</div>\r\n<!--              <div class=\"target-title mb-4rem\">回款（权益）</div>-->\r\n              <Empty :no-authority=\"hasPermi(['sales:all:hkDeviation'])\"><div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': hkDataByTime.openDate ? `'${hkDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': hkDataByTime.liquidateDate ? `'${hkDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue  hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdRate), 'hk-tooltip-1')\">\r\n                      <div>时点完成{{$toFixed2(hkDataByTime.hkAmount)}}亿（{{$toFixed2(hkDataByTime.allSdRate)}}%）</div>\r\n                      <div>全盘动态货值{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>\r\n<!--                      <div>全盘动态货值（权益）{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>-->\r\n                      <div>偏差率{{$toFixed2(hkDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdTargetRate), 'hk-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(hkDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>\r\n<!--                      <div>全盘目标货值（权益）{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>-->\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(hkDataByTime.zzHkAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.zzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\">清盘日期 {{$formatNull(hkDataByTime.zzLiquidateDate)}}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 时点完成{{$toFixed2(hkDataByTime.noZzHkAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.noZzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{$formatNull(hkDataByTime.noZzLiquidateDate)}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <!-- <div>住宅: 时点完成{{hkDataByTime.zzHkAmount}}亿（{{hkDataByTime.allSdRate}}%） | 住宅动态货值{{hkDataByTime.zzDynamicAmount}}亿 | 时点目标{{hkDataByTime.zzSdTargetAmount}}亿（{{hkDataByTime.zzSdRate}}%） | 住宅目标货值{{hkDataByTime.targetAmount}}亿 | 清盘日期{{hkDataByTime.zzQpDate}}</div>\r\n                  <div>非住: 时点完成{{hkDataByTime.noZzHkAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住动态货值{{hkDataByTime.noZzDynamicAmount}}亿 | 时点目标{{hkDataByTime.noZzSdTargetAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住目标货值{{hkDataByTime.noZzTargetAmount}}亿 | 清盘日期{{hkDataByTime.noZzQpDate}}</div> -->\r\n                </div>\r\n              </div>\r\n            </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年度销售进度模块 -->\r\n        <div class=\"year-progress card mb-16 mt-16\" v-loading=\"loadingYearProgress\">\r\n          <div class=\"card-title\">\r\n            年度销售进度\r\n            <span class=\"total-target\" v-if=\"hasPermi(['sales:year:price'])\">年度住宅均价 ¥ {{ $toFixed2(yearQyData.zzUnitPrice)}}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度签约目标 ¥ {{ $toFixed2(yearQyData.targetAmount)}} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:qy'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <!-- 刻度线 -->\r\n                    <div\r\n                      class=\"marker blue\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.ratio)}%`}\"\r\n                    ></div>\r\n                    <!-- 提示框 -->\r\n                    <div class=\"tooltip-progress blue year-tooltip-1\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.ratio), 'year-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearQyData.totalAmount) }} 亿 ({{ $toFixed2(yearQyData.ratio) }}%)</span>\r\n                    </div>\r\n                    <!-- 三角形 -->\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearQyData.ratio))\"></div>\r\n                    <div\r\n                      class=\"marker yellow\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-tooltip-2\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.dayRatio), 'year-tooltip-2')\">\r\n                      <span>时间进度：{{ yearQyData.month }}月 ({{ $toFixed2(yearQyData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearQyData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度签约目标{{ $toFixed2(yearQyData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计签约金额{{ $toFixed2(yearQyData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearQyData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度签约目标{{ $toFixed2(yearQyData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计签约金额{{ $toFixed2(yearQyData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearQyData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度回款目标 ¥ {{ $toFixed2(yearHkData.targetAmount) || '--' }} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:hk'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <div class=\"marker blue\" :style=\"{ left: `${filterRatioMax(yearHkData.ratio)}%` }\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress blue year-hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.ratio), 'year-hk-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearHkData.totalAmount) }} 亿 ({{ $toFixed2(yearHkData.ratio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearHkData.ratio))\"></div>\r\n\r\n                    <div class=\"marker yellow\" :style=\"{ left: `${filterRatioMax(yearHkData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.dayRatio), 'year-hk-tooltip-2')\">\r\n                      <span>时间进度：{{ yearHkData.month }}月 ({{ $toFixed2(yearHkData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearHkData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度回款目标{{ $toFixed2(yearHkData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计回款金额{{ $toFixed2(yearHkData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearHkData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度回款目标 {{ $toFixed2(yearHkData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计回款金额 {{ $toFixed2(yearHkData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearHkData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 款齐模块 -->\r\n        <div class=\"receiving-payments mb-16\" v-loading=\"loadingPayments\">\r\n          <div class=\"card content-block\">\r\n            <div class=\"card-title-2\">款齐</div>\r\n            <Empty :no-authority=\"hasPermi(['sales:kq'])\">\r\n              <div class=\"basic-info-item flex-container\">\r\n              <div class=\"circle-chart chart-size\">\r\n                <section class=\"card-with-title\">\r\n                  <Chart :option=\"kqProgressOption\" class=\"chart-size\"></Chart>\r\n                  <section class=\"chart-title-block\">\r\n                    <div class=\"title-1\" :style=\"{'color': kqProgressOption.title.textStyle.color}\">\r\n                      {{ kqProgressOption.title.text }}\r\n                    </div>\r\n                    <div class=\"title-2\">{{ kqProgressOption.title.subtext }}</div>\r\n                  </section>\r\n                </section>\r\n              </div>\r\n              <div class=\"card-wrapper\">\r\n                <div class=\"data-card\" v-for=\"item in cardData\"\r\n                     :style=\"{ backgroundColor: item.color, boxShadow: `0px 8px 20px 0px ${item.color}` }\">\r\n                  <div class=\"card-label\">{{ item.label }}</div>\r\n                  <br/>\r\n                  <div class=\"card-value\">\r\n                    <span class=\"currency\">¥ </span>\r\n                    <span>{{ $toFixed2(item.value) }} 亿</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            </Empty>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格数据模块 -->\r\n        <div class=\"table-container mb-16\" v-loading=\"loadingTables\">\r\n          <div class=\"card-title mb-16\">销售分析</div>\r\n          <Empty :no-authority=\"hasPermi(['sales:data'])\">\r\n            <CardTabSales v-model=\"queryTypeSign\" :tabs=\"queryTypeSignList\" @click=\"handleTabSign\" class=\"mb-14\"/>\r\n          <el-table\r\n            :data=\"qyData\"\r\n            style=\"width: 100%\"\r\n            :border=\"true\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.5rem'\r\n                  }\"\r\n            :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                     lineHeight: '1.5rem'\r\n                  }\"\r\n            class=\"project-table mb-16\">\r\n            <!-- 签约table -->\r\n            <el-table-column align=\"center\" label=\"累计签约\">\r\n              <el-table-column label=\"金额\" prop=\"qyAmount\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.qyAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.qyAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"qyArea\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  <span>{{ $toFixed2(scope.row.qyArea)}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"套数\" prop=\"qyNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"12.5%\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"累计到访转化率\">\r\n              <el-table-column label=\"到访人数\" prop=\"dfNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"转化率\" prop=\"ratio\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{ $toFixed2(scope.row.ratio) }}%\r\n                </template>\r\n              </el-table-column>\r\n            </el-table-column>\r\n          </el-table>\r\n          <!-- 认购table -->\r\n          <el-table\r\n            :data=\"rgData\"\r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n                  :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n            class=\"mb-16 project-table\">\r\n            <el-table-column align=\"center\" label=\"累计认购\" :colspan=\"6\">\r\n              <el-table-column label=\"金额\" prop=\"rgAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.rgAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.rgAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"rgArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"tsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"截至当前认未签\" :colspan=\"5\">\r\n              <el-table-column label=\"金额\" prop=\"noQyTotalAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    (scope.row.noQyTotalAmount / 100000000).toFixed(2) + '亿'\r\n\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"noQyTotalArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"noQyTsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"noQyZzNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n          </el-table>\r\n          </Empty>\r\n\r\n\r\n          <div class=\"cost flex-container\" style=\"min-height: 26.5rem;\">\r\n            <div class=\"flex-item chart\">\r\n              <div class=\"card-title mb-10\">签约业态分布</div>\r\n              <!--              <div class=\"sign-chart-block\">-->\r\n              <Empty :no-authority=\"hasPermi(['sales:bussDist'])\">\r\n                <!-- <Chart class=\"\" :option=\"getQYYTOption(qyYTData)\" v-if=\"qyYTData.length > 0\"></Chart> -->\r\n                <Chart class=\"\" :option=\"qyYTOption\"></Chart>\r\n                <!-- <div v-else class=\"no-data\">暂无数据</div> -->\r\n              </Empty>\r\n              <!--              </div>-->\r\n            </div>\r\n            <div class=\"flex-item\" style=\"min-width:0;\">\r\n              <div class=\"card-title mb-10\">货值分布表</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:goodsValue'])\">\r\n                <!-- max-height: 30rem; -->\r\n                <el-table\r\n                class=\"project-table\"\r\n                :data=\"analysisData\"\r\n                style=\"width: 100%;max-height: 30rem; \"\r\n                :border=\"true\"\r\n                :span-method=\"handleSpanMethod\"\r\n                height=\"'30rem'\"\r\n                :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    fontSize: '0.875rem',\r\n                    height: '2rem',\r\n                  }\"\r\n                :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                  }\"\r\n              >\r\n                <el-table-column label=\"业态\" prop=\"projectType\" align=\"center\" min-width=\"8\"/>\r\n                <el-table-column label=\"户型\" prop=\"hxName\" align=\"center\" min-width=\"12\"/>\r\n                <el-table-column label=\"总套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"totalNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"allTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"allBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"总货值(万元)\" prop=\"allHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"已售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"ysNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"ysTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"ysBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                  <el-table-column label=\"已售货值(万元)\" prop=\"ysHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"未售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"wsNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"已供未售\" prop=\"ygwsNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"未供\" prop=\"wgNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"顶层\" prop=\"wsTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"wsBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"未售货值(万元)\" prop=\"wsHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n              </el-table>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n        <!-- 趋势图模块 -->\r\n        <div class=\"trend mb-16\" v-loading=\"loadingTrends\">\r\n          <div class=\"card-title mb-16\">趋势分析</div>\r\n          <CardTabSales v-model=\"queryTypeTrend\" :tabs=\"queryTypeTrendList\" @click=\"handleTrendTab\" class=\"mb-14\"/>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">到访趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:dfTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ dfOptionName }}</div>\r\n                <Chart :option=\"dfOption\" class=\"trend-chart\"></Chart>\r\n              </Empty>\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">认购趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:rgTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ rgOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabRGTrend === index }]\"\r\n                    @click=\"selectTabRGTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"rgOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"rgProjectType\" :tabs=\"projectTypeList\" @click=\"handleRgTab\" class=\"mb-14\"/>\r\n                <!--            <CardTabSales v-model=\"activeTabRGTrend\" :tabs=\"tabs\" @click=\"selectTabRGTrend\" class=\"mb-14\"/>-->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">签约趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:qyTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ qyOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabQYTrend === index }]\"\r\n                    @click=\"selectTabQYTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"qyOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"qyProjectType\" :tabs=\"projectTypeList\" @click=\"handleQyTab\" class=\"mb-14\"/>\r\n                <!-- <CardTabSales v-model=\"activeTabQYTrend\" :tabs=\"tabs\" @click=\"selectTabQYTrend\" class=\"mb-14\"/> -->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title  mb-25\">回款趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:hkTrend'])\">\r\n                <div class=\"trend-chart-container\">\r\n                <div class=\"trend-chart-title\">{{ hkOptionName }}</div>\r\n                <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart>\r\n              </div>\r\n              <!-- <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart> -->\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"hkProjectType\" :tabs=\"projectTypeList\" @click=\"handleHKTab\" class=\"mb-14\"/>\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </section>\r\n\r\n</template>\r\n<script>\r\nimport ProgressBar from '@/views/projects/components/ProgressBar.vue'\r\nimport CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'\r\nimport ProgressCircle from '@/views/projects/constants/ProgressCircle'\r\nimport Chart from '@/views/projects/components/Chart.vue'\r\nimport BarOption from '@/views/projects/constants/bar'\r\nimport PieOption from '@/views/projects/constants/Pie'\r\nimport API from '@/views/projects/api'\r\nimport CardTabSales from \"@/views/projects/components/CardTabSales.vue\";\r\nimport Empty from \"@/views/projects/components/empty.vue\";\r\n\r\nexport default {\r\n  name: 'Sales',\r\n  components: {\r\n    CumulativeIndicatorsCard,\r\n    ProgressBar,\r\n    Chart,\r\n    CardTabSales,\r\n    Empty\r\n  },\r\n  data() {\r\n    return {\r\n      yearQyData: {\r\n        /*ratio: 0,\r\n        dayRatio: 100,\r\n        totalAmount: 0,\r\n        month: 0,\r\n        zzTotalAmount: 0,\r\n        noZzTotalAmount: 0*/\r\n      }, // 年度销售进度-年度签约数据\r\n      yearHkData: {}, // 年度销售进度-年度回款数据\r\n      signDataByTime: { // 序时销售进度-签约\r\n      },\r\n      loadingAllTargetQYProgress: false,\r\n      loadingAllTargetHKProgress: false,\r\n      hkDataByTime: { // 序时销售进度-回款\r\n      },\r\n      queryTypeSignList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 4,\r\n          name: '年'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        },\r\n      ],\r\n      queryTypeSign: 2,\r\n      qyData: [],\r\n      rgData: [],\r\n      ////////////\r\n      queryTypeTrendList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        }\r\n      ],\r\n      queryTypeTrend: 2,\r\n      hkTrendData: {}, // 回款趋势图数据\r\n      qyTrendData: {}, // 签约趋势图数据\r\n      rgTrendData: {}, // 认购趋势图数据\r\n      dfTrendData: {}, // 到访趋势图数据\r\n      hkTrendRawData: {}, // 回款趋势图原始数据\r\n      qyTrendRawData: {}, // 签约趋势图原始数据\r\n      rgTrendRawData: {}, // 认购趋势图原始数据\r\n      dfTrendRawData: {}, // 到访趋势图原始数据\r\n      ProgressCircleOption: new ProgressCircle().getOption(),\r\n      cardData: [ // 款齐\r\n        {\r\n          label: '年度目标',\r\n          value: '--',\r\n          color: '#53BD88'\r\n        },\r\n        {\r\n          label: '年度款齐',\r\n          value: '--',\r\n          color: '#7B6FF2'\r\n        },\r\n        {\r\n          label: '可结利',\r\n          value: '--',\r\n          color: '#3EB6CF'\r\n        }\r\n      ],\r\n      projectCode: '',\r\n      projectTypeList: [], // 业态集合\r\n      hkProjectType: '',\r\n      rgProjectType: '',\r\n      qyProjectType: '',\r\n      queryType: 2, // （0-全盘 1-今日 2-本周 3-本月）\r\n      allQyData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, //全盘销售进度 - 签约\r\n      allHkData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, // 全盘销售进度 - 回款\r\n      kqData: {},\r\n      qyYTData: [], // 签约业态分布\r\n      tabs: [\r\n        '套数',\r\n        '金额',\r\n        '面积'\r\n      ],\r\n      activeTabRGTrend: 0, // 套数\r\n      activeTabQYTrend: 0, // 套数\r\n      tooltipStyles: {\r\n        blue: {left: '0px'},\r\n        yellow: {left: '0px'}\r\n      },\r\n      dfOption: {},\r\n      rgOption: {},\r\n      qyOption: {},\r\n      hkOption: {},\r\n      analysisData: [], // 货值分析数据\r\n      hasData: true,\r\n      isDeveloping: false, // 控制是否显示开发中状态\r\n\r\n      // 添加loading状态变量\r\n      loadingSales: false,\r\n      loadingTimeProgress: false,\r\n      loadingYearProgress: false,\r\n      loadingPayments: false,\r\n      loadingTables: false,\r\n      loadingTrends: false,\r\n      hkOptionName: '',\r\n      dfOptionName: '',\r\n      rgOptionName: '',\r\n      qyOptionName: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.projectCode = this.$route.query.projectCode\r\n  },\r\n  async mounted() {\r\n    await this.getProjectType(this.projectCode);\r\n    this.initTrend(); // 初始化趋势图\r\n    this.getAllQyData({ //全盘销售进度 - 签约\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getAllHkData({ //全盘销售进度 - 回款\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getkqData({ // 款齐\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQyDfData({ // 累���签约\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getRgData({ // 累计认购\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQYYTData({ // 签约业态分布\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.fetchAnalyse(this.projectCode) // 获取货值分析数据\r\n    // 年度销售进度-年度签约数据\r\n    this.getYearQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 年度销售进度-年度回款数据\r\n    this.getYearHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-签约数据\r\n    this.getAllTargetQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-回款数据\r\n    this.getAllTargetHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n\r\n  },\r\n  methods: {\r\n    hasPermi(permission) {\r\n      const permissions = this.$store.getters.permissions\r\n      // Check for all permissions wildcard first\r\n      if (permissions.includes('*:*:*')) {\r\n        return true\r\n      }\r\n      // Check specific permissions\r\n      return permissions.some(p => permission.includes(p))\r\n    },\r\n    filterRatioMax(ratio) {\r\n      if (ratio > 100) {\r\n        ratio = 100;\r\n      } else if (ratio < 0) {\r\n        ratio = 0;\r\n      }\r\n      return ratio;\r\n    },\r\n    getArrowStyle(ratio) {\r\n      return {left: `${ratio}%`};\r\n    },\r\n    getTooltipStyle(ratio, tooltipClass) {\r\n      const scale = window.innerWidth / 1680;\r\n      // const tooltipWidth = 170 * scale; // tooltip的宽度(px)\r\n      const tooltipWidth = document.querySelector(`.${tooltipClass}`)?.clientWidth || 0; // tooltip的宽度(px)\r\n      const containerWidth = document.querySelector('.timeline')?.clientWidth || 0;\r\n      const tooltipRatio = (tooltipWidth / containerWidth) * 100;\r\n      if (ratio == '--') { // raio为--时，tooltip靠边\r\n        return {\r\n          left: `0`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n\r\n      // 超出右边界\r\n      if ((Math.ceil(ratio) + Math.ceil(tooltipRatio) / 2) > 100) {\r\n        return {\r\n          // right: `0`,\r\n          right: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // bordrRadius: `0 0 0 0`\r\n\r\n      }\r\n      // 超出左边界\r\n      else if ((Math.ceil(ratio) - Math.floor(tooltipRatio) / 2) <= 0) {\r\n        return {\r\n          left: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // borderRadius: `0 0  0 0`\r\n\r\n      }\r\n\r\n      // console.log('ratio---', ratio);\r\n      // console.log('tooltipRatio---', tooltipRatio);\r\n      if (ratio >= 50) {\r\n        return {\r\n          // right: `${100 - ratio - 8}%`,\r\n          right: `${100 - ratio}%`,\r\n          transform: `translateX(50%)`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n      return {\r\n        left: `${ratio}%`,\r\n        transform: `translateX(-50%)`,\r\n        textAlign: 'left'\r\n      };\r\n    },\r\n    formatValue(value) {\r\n      const absValue = Math.abs(value);\r\n      let formattedValue;\r\n      let unit = '';\r\n\r\n      if (absValue >= 100000000) {\r\n        formattedValue = (absValue / 100000000).toFixed(2);\r\n        unit = '亿';\r\n      } else if (absValue >= 10000) {\r\n        formattedValue = (absValue / 10000).toFixed(2);\r\n        unit = '万';\r\n      } else {\r\n        formattedValue = absValue.toFixed(2);\r\n      }\r\n\r\n      return {\r\n        formattedValue: value < 0 ? `-${formattedValue}` : formattedValue,\r\n        unit\r\n      };\r\n    },\r\n    selectTabQYTrend(index) {\r\n      this.activeTabQYTrend = index;\r\n      // this.activeTabQYTrend = item.code;\r\n      this.updateQYTrendData();\r\n    },\r\n    getQyOption() {\r\n      this.qyOption = this.getBarOption(['#FF9B47'], this.qyTrendData);\r\n      if (this.activeTabQYTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.qyOption.yAxis[1].name = '单位：万元';\r\n          this.qyOptionName = '单位：万元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          //  this.qyOption.yAxis[1].name = '单位：亿元';\r\n          this.qyOptionName = '单位：亿元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabQYTrend === 0) {\r\n        // this.qyOption.yAxis[1].name = '单位：套';\r\n        this.qyOptionName = '单位：套';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      } else if (this.activeTabQYTrend === 2) {\r\n        //  this.qyOption.yAxis[1].name = '单位：m²';\r\n        this.qyOptionName = '单位：m²';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.qyOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    updateQYTrendData() {\r\n      const data = this.qyTrendRawData;\r\n      switch (this.activeTabQYTrend) {\r\n        case 0: // 套数\r\n          this.filteredQYTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredQYTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredQYTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredQYTrendData = data;\r\n      }\r\n      this.qyTrendData.series = this.filteredQYTrendData;\r\n      this.getQyOption();\r\n    },\r\n    selectTabRGTrend(index) {\r\n      this.activeTabRGTrend = index;\r\n      this.updateRGTrendData();\r\n    },\r\n    getRgOption() {\r\n      this.rgOption = this.getBarOption(['#376DF7'], this.rgTrendData)\r\n    },\r\n    updateRGTrendData() {\r\n      const data = this.rgTrendRawData;\r\n      switch (this.activeTabRGTrend) {\r\n        case 0: // 套数\r\n          this.filteredRGTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredRGTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredRGTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredRGTrendData = data;\r\n      }\r\n      this.rgTrendData.series = this.filteredRGTrendData;\r\n      this.getRgOption();\r\n      if (this.activeTabRGTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.rgOption.yAxis[1].name = '单位：万元';\r\n          this.rgOptionName = '单位：万元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          // this.rgOption.yAxis[1].name = '单位：亿元';\r\n          this.rgOptionName = '单位：亿元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabRGTrend === 0) {\r\n        // this.rgOption.yAxis[1].name = '单位：套';\r\n        this.rgOptionName = '单位：套';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n          ;\r\n        }\r\n      } else if (this.activeTabRGTrend === 2) {\r\n        // this.rgOption.yAxis[1].name = '单位：m²';\r\n        this.rgOptionName = '单位：m²';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.rgOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getQYYTData(data) { // 签约业态分布\r\n      const res = await API.Sales.bussDist(data);\r\n      this.qyYTData = res.data;\r\n    },\r\n    getQYYTOption(data) { // 签约业���分布chart option\r\n      const option = this.getPieOption();\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n      const scale = window.innerWidth / 1680;\r\n      const rich = {\r\n        yellow: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0],\r\n          align: 'center'\r\n        },\r\n        total: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 40 * scale,\r\n          align: 'center'\r\n        },\r\n        labelColor: {\r\n          color: \"#333333\",\r\n          align: 'center',\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0]\r\n        },\r\n        blue: {\r\n          color: '#49dff0',\r\n          fontSize: 16 * scale,\r\n          align: 'center'\r\n        },\r\n        hr: {\r\n          borderColor: '#0b5263',\r\n          width: '100%',\r\n          borderWidth: 1 * scale,\r\n          height: 0,\r\n          margin: [5, 0]\r\n        }\r\n      }\r\n      option.series[0].data = data.map(item => {\r\n        return {\r\n          value: this.$toFixed2(item.rate),\r\n          name: item.bussName,\r\n          ...item\r\n        }\r\n      });\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">名称: ${params.data.bussName}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">套数: ${params.data.num}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">面积: ${params.data.area}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n      // option.series[0].avoidLabelOverlap = true\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示连接线\r\n        minTurnAngle: 90, // 限制转折角度，防止错位\r\n        normal: {\r\n          length: 20 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: data.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle', // 设置图例为小圆点\r\n      };\r\n      option.series[0].label = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        // overflow: 'truncate', // 防止标签超出容器\r\n        distance: 5 * scale,\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n      return option;\r\n    },\r\n    async getAllHkData(data) { // 全盘销售进度 - 回款\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allHkData(data);\r\n        this.allHkData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    async getAllQyData(data) { // 全盘销售进度 - 签约\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allQyData(data);\r\n        this.allQyData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    handleTrendTab(item) { // 趋势图查询类型切换\r\n      this.queryTypeTrend = item.code;\r\n      this.initTrend(); // 初始化趋势图\r\n    },\r\n    updateCSSVariable(value) {\r\n      document.documentElement.style.setProperty('--timeline-before-content', value);\r\n    },\r\n    async getAllTargetQyData(data){ // 全周期进度偏差-签约数据\r\n      this.loadingAllTargetQYProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetQyData(data);\r\n        // 使用$formatNull对res.data中的所有数据字段格式化\r\n        this.signDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetQYProgress = false;\r\n      }\r\n    },\r\n    async getAllTargetHkData(data) { // 全周期进度偏差-回款数据\r\n      this.loadingAllTargetHKProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetHkData(data);\r\n        this.hkDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetHKProgress = false;\r\n      }\r\n    },\r\n    async getYearQyData(data) { // 年度销售进度-年度签约数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetQyData(data);\r\n        this.yearQyData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getYearHkData(data) { // 年度销售进度-年度回款数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetHkData(data);\r\n        this.yearHkData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n        // this.yearHkData = res.data;\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getRgData(data) { // 累计认购\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.rgData(data);\r\n        this.rgData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    async getQyDfData(data) { // 累计签约\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.qyDfData(data);\r\n        this.qyData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    sanitizeData(data) { // 数据为空时，显示--\r\n      if (!data) return {};\r\n      return Object.fromEntries(\r\n        Object.entries(data).map(([key, value]) => [key, value ?? '--'])\r\n      );\r\n    },\r\n    handleTabSign(item) {\r\n      this.queryTypeSign = item.code;\r\n      this.getQyDfData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getRgData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getQYYTData({ // 签约业态分布\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n    },\r\n    fetchAnalyse(projectCode) { // 货值分析-货值分布表\r\n      API.Value.analyse(projectCode).then(res => {\r\n        this.analysisData = res.data.map(item => ({\r\n          projectType: item.projectType || '--',\r\n          hxName: item.hxName || '--',\r\n          totalNum: item.totalNum === null ? '--' : item.totalNum,\r\n          ysNum: item.ysNum === null ? '--' : item.ysNum,\r\n          wsNum: item.wsNum === null ? '--' : item.wsNum,\r\n          ygwsNum: item.ygwsNum === null ? '--' : item.ygwsNum,\r\n          wgNum: item.wgNum === null ? '--' : item.wgNum,\r\n          allTopNum: item.allTopNum === null ? '--' : item.allTopNum,\r\n          allBottomNum: item.allBottomNum === null ? '--' : item.allBottomNum,\r\n          ysTopNum: item.ysTopNum === null ? '--' : item.ysTopNum,\r\n          ysBottomNum: item.ysBottomNum === null ? '--' : item.ysBottomNum,\r\n          wsTopNum: item.wsTopNum === null ? '--' : item.wsTopNum,\r\n          wsBottomNum: item.wsBottomNum === null ? '--' : item.wsBottomNum,\r\n          allHzAmount: item.allHzAmount === null ? '--' : item.allHzAmount,\r\n          ysHzAmount: item.ysHzAmount === null ? '--' : item.ysHzAmount,\r\n          wsHzAmount: item.wsHzAmount === null ? '--' : item.wsHzAmount,\r\n        }));\r\n      })\r\n    },\r\n    async getkqData(data) { // 款齐\r\n      this.loadingPayments = true;\r\n      try {\r\n        const res = await API.Sales.kqData(data);\r\n        this.kqData = res.data;\r\n        this.cardData[0].value = this.kqData.kqTargetAmount;\r\n        this.cardData[1].value = this.kqData.yearKqAmount;\r\n        this.cardData[2].value = this.kqData.kjlKqAmount;\r\n      } finally {\r\n        this.loadingPayments = false;\r\n      }\r\n    },\r\n\r\n    handleHKTab(item) {\r\n      this.hkProjectType = item.code;\r\n      this.getHkTrend();\r\n    },\r\n    handleRgTab(item) {\r\n      this.rgProjectType = item.code;\r\n      this.getRgTrend();\r\n    },\r\n    handleQyTab(item) {\r\n      this.qyProjectType = item.code;\r\n      this.getQyTrend();\r\n    },\r\n    async getProjectType(projectCode) { // 查询业态列表\r\n      const res = await API.Common.getProjectType(projectCode);\r\n      this.projectTypeList = res.data;\r\n      this.hasData = this.projectTypeList.length > 0;\r\n      this.qyProjectType = this.rgProjectType = this.hkProjectType\r\n        = this.projectTypeList[0].code;\r\n    },\r\n    async initTrend() {\r\n      this.loadingTrends = true;\r\n      try {\r\n        await Promise.all([\r\n          this.getQyTrend(),\r\n          this.getRgTrend(),\r\n          this.getDfTrend(),\r\n          this.getHkTrend()\r\n        ]);\r\n      } finally {\r\n        this.loadingTrends = false;\r\n      }\r\n    },\r\n    getDfOption() { // 到访趋势图数据\r\n      this.dfOption = this.getBarOption(['#53B997'], this.dfTrendData, 'df')\r\n      // this.dfOption.yAxis[1].name = '单位：人次';\r\n      this.dfOptionName = '单位：人次';\r\n      this.dfOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getDfTrend() { // 销售页签 - 到访趋势\r\n      const res = await API.Sales.dfTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.dfTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.dfTrendData.series = res.data.map(item => item.value);\r\n      this.getDfOption();\r\n    },\r\n    async getRgTrend() { // 销售页签 - 认购趋势\r\n      const res = await API.Sales.rgTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.rgProjectType\r\n      });\r\n      this.rgTrendRawData = res.data;\r\n      this.rgTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.rgTrendData.series = res.data.map(item => item.value);\r\n      this.updateRGTrendData();\r\n    },\r\n    async getQyTrend() { // 销售页签 - 签约趋势\r\n      const res = await API.Sales.qyTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.qyProjectType\r\n      });\r\n      this.qyTrendRawData = res.data;\r\n      this.qyTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.qyTrendData.series = res.data.map(item => item.value);\r\n      this.updateQYTrendData();\r\n    },\r\n    getHkOption() {\r\n      this.hkOption = this.getBarOption(['#9D7BFF'], this.hkTrendData);\r\n      if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n        // this.hkOption.yAxis[1].name = '单位：万元';\r\n        this.hkOptionName = '单位：万元'\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 10000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 10000).toFixed(2);\r\n        }\r\n      } else {\r\n        // this.hkOption.yAxis[1].name = '单位：亿元';\r\n        this.hkOptionName = '单位：万元';\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 100000000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 100000000).toFixed(2);\r\n        }\r\n\r\n      }\r\n      this.hkOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    async getHkTrend() { //销售页签 - 回款趋势\r\n      const res = await API.Sales.hkTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.hkProjectType\r\n      });\r\n\r\n      this.hkTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.hkTrendData.series = res.data.map(item => item.value);\r\n      this.getHkOption();\r\n    },\r\n    getPieOption(color = ['#376DF7', '#53B997', '#6750AA', '#F8C541']) {\r\n      const pieOption = new PieOption();\r\n      pieOption.setColor(color);\r\n      return pieOption.getOption();\r\n    },\r\n    getBarOption(color = ['#53B997'], data = {xAxis: [], series: []}, type = '') {\r\n      const barOption = new BarOption();\r\n      barOption.updateData(data.xAxis, data.series);\r\n      barOption.setColor(color);\r\n      const option = barOption.getOption();\r\n      if (!Array.isArray(option.yAxis)) {\r\n        option.yAxis = [{}];\r\n      }\r\n      return option;\r\n    },\r\n    handleSpanMethod({row, column, rowIndex, columnIndex}) {\r\n      // 处理最后一行\r\n      if (rowIndex === this.analysisData.length - 1) {\r\n        if (columnIndex === 0) {  // 第一列\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2  // 合并两列\r\n          };\r\n        }\r\n        if (columnIndex === 1) {  // 第二列\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0  // 隐藏第二列\r\n          };\r\n        }\r\n      }\r\n\r\n      // 处理其他行的第一列合并\r\n      if (columnIndex === 0) {\r\n        const projectType = row.projectType;\r\n\r\n        // 向上查找相同业态的起始位置\r\n        let startIndex = rowIndex;\r\n        while (startIndex > 0 && this.analysisData[startIndex - 1].projectType === projectType) {\r\n          startIndex--;\r\n        }\r\n\r\n        // 计算相同业态的行数\r\n        let spanCount = 0;\r\n        for (let i = startIndex; i < this.analysisData.length; i++) {\r\n          if (this.analysisData[i].projectType === projectType) {\r\n            spanCount++;\r\n          } else {\r\n            break;\r\n          }\r\n        }\r\n\r\n        // 只在每组的第一行显示，其他行隐藏\r\n        if (rowIndex === startIndex) {\r\n          return {\r\n            rowspan: spanCount,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    qyProgressOption() {\r\n      const data = this.allQyData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#1433CC');\r\n        progressCircleOption.setBackgroundStyle('#376DF7');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#376DF7');\r\n        progressCircleOption.setBackgroundStyle('#C2CEF8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${this.$toFixed2(data.rate)}%`,\r\n        textStyle: {\r\n          color: '#376DF7',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计签约: ${data.qyAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    hkProgressOption() {\r\n      const data = this.allHkData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#e56d16');\r\n        progressCircleOption.setBackgroundStyle('#FF974C');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#FF974C');\r\n        progressCircleOption.setBackgroundStyle('#F9DEC8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      // option.title[0].subtext = '进度';\r\n      // option.title[0].text = `${data.rate}%`;\r\n      // option.title[0].textStyle.color = '#FF974C';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${data.rate}%`,\r\n        textStyle: {\r\n          color: '#FF974C',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计回款: ${data.hkAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">权益比例: ${data.qyRate}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    kqProgressOption() {\r\n      const data = this.kqData.kqRatio;\r\n      let text = '--';\r\n      if (!(data === null || data === undefined)) {\r\n        text = data;\r\n      }\r\n\r\n      const progressCircleOption = new ProgressCircle();\r\n      progressCircleOption.setBarData([(data % 100).toFixed(2) || '--']);\r\n\r\n      if (data > 100) {\r\n        progressCircleOption.setBarItemStyle('#6A3DC4');\r\n        progressCircleOption.setBackgroundStyle('#7B6FF2');\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#7B6FF2');\r\n        progressCircleOption.setBackgroundStyle('#E2E0FB');\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      // option.title[0].subtext = '';\r\n      // option.title[0].text = `${text}%`;\r\n      // option.title[0].textStyle.color = '#7B6FF2';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '',\r\n        text: `${text}%`,\r\n        textStyle: {\r\n          color: '#7B6FF2',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(16 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: false,\r\n        position: 'right',\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐目标金额: ${this.kqData.kqTargetAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">可结利款齐金额: ${this.kqData.kjlKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">年度款齐: ${this.kqData.yearKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐百分比: ${this.$formatNull(this.kqData.kqRatio)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    qyYTOption() { // 签约业态分布chart option\r\n      return this.getQYYTOption(this.qyYTData);\r\n      /* const option = this.getPieOption();\r\n      const scale = window.innerWidth / 1680;\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        minTurnAngle: 90,\r\n        normal: {\r\n          length: 12 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.series[0].data = this.qyYTData.map(item => ({\r\n        value: item.rate,\r\n        name: item.bussName,\r\n        ...item\r\n      }));\r\n\r\n      option.tooltip = {\r\n        show: true,\r\n        padding: [0, 0],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: 0.875rem; line-height: 1.2;padding: 0.3rem 0.75rem;border-radius: 0.1rem;\">\r\n            <div>名称: ${params.data.bussName}</div>\r\n            <div>套数: ${params.data.num}</div>\r\n            <div>面积: ${params.data.area}</div>\r\n            <div>金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div>比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: this.qyYTData.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle',\r\n      };\r\n\r\n      option.series[0].label = {\r\n        show: true,\r\n        position: 'outside',\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n\r\n      return option; */\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import './sales.scss';\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA8rBA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,IAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,MAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACAC,wBAAA,EAAAA,iCAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,KAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACA;AACA;AACA;AACA;AACA;AACA;MALA,CAMA;MAAA;MACAC,UAAA;MAAA;MACAC,cAAA;MAAA,CACA;MACAC,0BAAA;MACAC,0BAAA;MACAC,YAAA;MAAA,CACA;MACAC,iBAAA,GACA;QACAC,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,EACA;MACAgB,aAAA;MACAC,MAAA;MACAC,MAAA;MACA;MACAC,kBAAA,GACA;QACAJ,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,GACA;QACAe,IAAA;QACAf,IAAA;MACA,EACA;MACAoB,cAAA;MACAC,WAAA;MAAA;MACAC,WAAA;MAAA;MACAC,WAAA;MAAA;MACAC,WAAA;MAAA;MACAC,cAAA;MAAA;MACAC,cAAA;MAAA;MACAC,cAAA;MAAA;MACAC,cAAA;MAAA;MACAC,oBAAA,MAAAC,uBAAA,GAAAC,SAAA;MACAC,QAAA;MAAA;MACA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,KAAA;MACA,EACA;MACAC,WAAA;MACAC,eAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,aAAA;MACAC,SAAA;MAAA;MACAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAAA;MACAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAAA;MACAC,MAAA;MACAC,QAAA;MAAA;MACAC,IAAA,GACA,MACA,MACA,KACA;MACAC,gBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;QACAC,IAAA;UAAAC,IAAA;QAAA;QACAC,MAAA;UAAAD,IAAA;QAAA;MACA;MACAE,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA;MAAA;MACAC,OAAA;MACAC,YAAA;MAAA;;MAEA;MACAC,YAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,eAAA;MACAC,aAAA;MACAC,aAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAlC,WAAA,QAAAmC,MAAA,CAAAC,KAAA,CAAApC,WAAA;EACA;EACAqC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,cAAA,CAAAV,KAAA,CAAAtC,WAAA;UAAA;YACAsC,KAAA,CAAAW,SAAA;YACAX,KAAA,CAAAY,YAAA;cAAA;cACA7C,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAa,YAAA;cAAA;cACA9C,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAc,SAAA;cAAA;cACA/C,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAe,WAAA;cAAA;cACAhD,SAAA,EAAAiC,KAAA,CAAA1D,aAAA;cACAoB,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAgB,SAAA;cAAA;cACAjD,SAAA,EAAAiC,KAAA,CAAA1D,aAAA;cACAoB,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAiB,WAAA;cAAA;cACAlD,SAAA,EAAAiC,KAAA,CAAA1D,aAAA;cACAoB,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACAsC,KAAA,CAAAkB,YAAA,CAAAlB,KAAA,CAAAtC,WAAA;YACA;YACAsC,KAAA,CAAAmB,aAAA;cACApD,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACA;YACAsC,KAAA,CAAAoB,aAAA;cACArD,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACA;YACAsC,KAAA,CAAAqB,kBAAA;cACAtD,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;YACA;YACAsC,KAAA,CAAAsB,kBAAA;cACAvD,SAAA;cACAL,WAAA,EAAAsC,KAAA,CAAAtC;YACA;UAAA;UAAA;YAAA,OAAA6C,QAAA,CAAAgB,IAAA;QAAA;MAAA,GAAAnB,OAAA;IAAA;EAEA;EACAoB,OAAA;IACAC,QAAA,WAAAA,SAAAC,UAAA;MACA,IAAAC,WAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,WAAA;MACA;MACA,IAAAA,WAAA,CAAAG,QAAA;QACA;MACA;MACA;MACA,OAAAH,WAAA,CAAAI,IAAA,WAAAC,CAAA;QAAA,OAAAN,UAAA,CAAAI,QAAA,CAAAE,CAAA;MAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAA,KAAA;QACAA,KAAA;MACA,WAAAA,KAAA;QACAA,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IACAC,aAAA,WAAAA,cAAAD,KAAA;MACA;QAAAzD,IAAA,KAAA2D,MAAA,CAAAF,KAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAAH,KAAA,EAAAI,YAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAC,UAAA;MACA;MACA,IAAAC,YAAA,KAAAL,qBAAA,GAAAM,QAAA,CAAAC,aAAA,KAAAV,MAAA,CAAAE,YAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAQ,WAAA;MACA,IAAAC,cAAA,KAAAR,sBAAA,GAAAK,QAAA,CAAAC,aAAA,2BAAAN,sBAAA,uBAAAA,sBAAA,CAAAO,WAAA;MACA,IAAAE,YAAA,GAAAL,YAAA,GAAAI,cAAA;MACA,IAAAd,KAAA;QAAA;QACA;UACAzD,IAAA;UACAyE,SAAA;QACA;MACA;;MAEA;MACA,IAAAC,IAAA,CAAAC,IAAA,CAAAlB,KAAA,IAAAiB,IAAA,CAAAC,IAAA,CAAAH,YAAA;QACA;UACA;UACAI,KAAA;UACAH,SAAA;QACA;QACA;MAEA;MACA;MAAA,KACA,IAAAC,IAAA,CAAAC,IAAA,CAAAlB,KAAA,IAAAiB,IAAA,CAAAG,KAAA,CAAAL,YAAA;QACA;UACAxE,IAAA;UACAyE,SAAA;QACA;QACA;MAEA;;MAEA;MACA;MACA,IAAAhB,KAAA;QACA;UACA;UACAmB,KAAA,KAAAjB,MAAA,OAAAF,KAAA;UACAqB,SAAA;UACAL,SAAA;QACA;MACA;MACA;QACAzE,IAAA,KAAA2D,MAAA,CAAAF,KAAA;QACAqB,SAAA;QACAL,SAAA;MACA;IACA;IACAM,WAAA,WAAAA,YAAAhG,KAAA;MACA,IAAAiG,QAAA,GAAAN,IAAA,CAAAO,GAAA,CAAAlG,KAAA;MACA,IAAAmG,cAAA;MACA,IAAAC,IAAA;MAEA,IAAAH,QAAA;QACAE,cAAA,IAAAF,QAAA,cAAAI,OAAA;QACAD,IAAA;MACA,WAAAH,QAAA;QACAE,cAAA,IAAAF,QAAA,UAAAI,OAAA;QACAD,IAAA;MACA;QACAD,cAAA,GAAAF,QAAA,CAAAI,OAAA;MACA;MAEA;QACAF,cAAA,EAAAnG,KAAA,WAAA4E,MAAA,CAAAuB,cAAA,IAAAA,cAAA;QACAC,IAAA,EAAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAzF,gBAAA,GAAAyF,KAAA;MACA;MACA,KAAAC,iBAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAApF,QAAA,QAAAqF,YAAA,mBAAAtH,WAAA;MACA,SAAA0B,gBAAA;QACA,SAAA5B,cAAA,eAAAA,cAAA;UACA;UACA,KAAAiD,YAAA;UACA,KAAAd,QAAA,CAAAsF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;YACA,QAAAA,KAAA,UAAAqG,OAAA;UACA;UACA,KAAAhF,QAAA,CAAAyF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;YACA,QAAAA,MAAA,CAAA/G,KAAA,UAAAqG,OAAA;UACA;QACA;UACA;UACA,KAAAlE,YAAA;UACA,KAAAd,QAAA,CAAAsF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;YACA,QAAAA,KAAA,cAAAqG,OAAA;UACA;UACA,KAAAhF,QAAA,CAAAyF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;YACA,QAAAA,MAAA,CAAA/G,KAAA,cAAAqG,OAAA;UACA;QACA;MACA,gBAAAvF,gBAAA;QACA;QACA,KAAAqB,YAAA;QACA,KAAAd,QAAA,CAAAsF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,OAAAA,KAAA;QACA;QACA,KAAAqB,QAAA,CAAAyF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,OAAAA,MAAA,CAAA/G,KAAA;QACA;MACA,gBAAAc,gBAAA;QACA;QACA,KAAAqB,YAAA;QACA,KAAAd,QAAA,CAAAsF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,OAAAA,KAAA;QACA;QACA,KAAAqB,QAAA,CAAAyF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,OAAAA,MAAA,CAAA/G,KAAA;QACA;MACA;MACA,KAAAqB,QAAA,CAAA2F,OAAA;QACAC,IAAA;MACA;IACA;IACAT,iBAAA,WAAAA,kBAAA;MACA,IAAAnI,IAAA,QAAAmB,cAAA;MACA,aAAAsB,gBAAA;QACA;UAAA;UACA,KAAAoG,mBAAA,GAAA7I,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,GAAA;UAAA;UACA;QACA;UAAA;UACA,KAAAH,mBAAA,GAAA7I,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAApH,KAAA;UAAA;UACA;QACA;UAAA;UACA,KAAAkH,mBAAA,GAAA7I,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAE,IAAA;UAAA;UACA;QACA;UACA,KAAAJ,mBAAA,GAAA7I,IAAA;MACA;MACA,KAAAe,WAAA,CAAA0H,MAAA,QAAAI,mBAAA;MACA,KAAAT,WAAA;IACA;IACAc,gBAAA,WAAAA,iBAAAhB,KAAA;MACA,KAAA1F,gBAAA,GAAA0F,KAAA;MACA,KAAAiB,iBAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAArG,QAAA,QAAAsF,YAAA,mBAAArH,WAAA;IACA;IACAmI,iBAAA,WAAAA,kBAAA;MACA,IAAAnJ,IAAA,QAAAoB,cAAA;MACA,aAAAoB,gBAAA;QACA;UAAA;UACA,KAAA6G,mBAAA,GAAArJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,GAAA;UAAA;UACA;QACA;UAAA;UACA,KAAAK,mBAAA,GAAArJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAApH,KAAA;UAAA;UACA;QACA;UAAA;UACA,KAAA0H,mBAAA,GAAArJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAE,IAAA;UAAA;UACA;QACA;UACA,KAAAI,mBAAA,GAAArJ,IAAA;MACA;MACA,KAAAgB,WAAA,CAAAyH,MAAA,QAAAY,mBAAA;MACA,KAAAD,WAAA;MACA,SAAA5G,gBAAA;QACA,SAAA3B,cAAA,eAAAA,cAAA;UACA;UACA,KAAAgD,YAAA;UACA,KAAAd,QAAA,CAAAuF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;YACA,QAAAA,KAAA,UAAAqG,OAAA;UACA;UACA,KAAAjF,QAAA,CAAA0F,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;YACA,QAAAA,MAAA,CAAA/G,KAAA,UAAAqG,OAAA;UACA;QACA;UACA;UACA,KAAAnE,YAAA;UACA,KAAAd,QAAA,CAAAuF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;YACA,QAAAA,KAAA,cAAAqG,OAAA;UACA;UACA,KAAAjF,QAAA,CAAA0F,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;YACA,QAAAA,MAAA,CAAA/G,KAAA,cAAAqG,OAAA;UACA;QACA;MACA,gBAAAxF,gBAAA;QACA;QACA,KAAAqB,YAAA;QACA,KAAAd,QAAA,CAAAuF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,OAAAA,KAAA;QACA;QACA,KAAAoB,QAAA,CAAA0F,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,OAAAA,MAAA,CAAA/G,KAAA;UACA;QACA;MACA,gBAAAa,gBAAA;QACA;QACA,KAAAqB,YAAA;QACA,KAAAd,QAAA,CAAAuF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,OAAAA,KAAA;QACA;QACA,KAAAoB,QAAA,CAAA0F,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,OAAAA,MAAA,CAAA/G,KAAA;QACA;MACA;MACA,KAAAoB,QAAA,CAAA4F,OAAA;QACAC,IAAA;MACA;IACA;IACAxD,WAAA,WAAAA,YAAApF,IAAA;MAAA,IAAAsJ,MAAA;MAAA,WAAAlF,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAiF,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAiF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,IAAA,GAAA+E,SAAA,CAAA9E,IAAA;YAAA;cAAA8E,SAAA,CAAA9E,IAAA;cAAA,OACA+E,YAAA,CAAAC,KAAA,CAAAC,QAAA,CAAA7J,IAAA;YAAA;cAAAwJ,GAAA,GAAAE,SAAA,CAAAI,IAAA;cACAR,MAAA,CAAAhH,QAAA,GAAAkH,GAAA,CAAAxJ,IAAA;YAAA;YAAA;cAAA,OAAA0J,SAAA,CAAAhE,IAAA;UAAA;QAAA,GAAA6D,QAAA;MAAA;IACA;IACAQ,aAAA,WAAAA,cAAA/J,IAAA;MAAA,IAAAgK,MAAA;MAAA;MACA,IAAAC,MAAA,QAAAC,YAAA;MACAD,MAAA,CAAArI,KAAA;MACA,IAAAgF,KAAA,GAAAC,MAAA,CAAAC,UAAA;MACA,IAAAqD,IAAA;QACAtH,MAAA;UACAjB,KAAA;UACAwI,QAAA,OAAAxD,KAAA;UACAyD,OAAA;UACAC,KAAA;QACA;QACAC,KAAA;UACA3I,KAAA;UACAwI,QAAA,OAAAxD,KAAA;UACA0D,KAAA;QACA;QACAE,UAAA;UACA5I,KAAA;UACA0I,KAAA;UACAF,QAAA,OAAAxD,KAAA;UACAyD,OAAA;QACA;QACA1H,IAAA;UACAf,KAAA;UACAwI,QAAA,OAAAxD,KAAA;UACA0D,KAAA;QACA;QACAG,EAAA;UACAC,WAAA;UACAC,KAAA;UACAC,WAAA,MAAAhE,KAAA;UACAiE,MAAA;UACAC,MAAA;QACA;MACA;MACAb,MAAA,CAAAxB,MAAA,IAAAzI,IAAA,GAAAA,IAAA,CAAA8I,GAAA,WAAAC,IAAA;QACA,WAAAgC,cAAA,CAAAvL,OAAA;UACAmC,KAAA,EAAAqI,MAAA,CAAAgB,SAAA,CAAAjC,IAAA,CAAAkC,IAAA;UACAxL,IAAA,EAAAsJ,IAAA,CAAAmC;QAAA,GACAnC,IAAA;MAEA;MACA,IAAAoC,WAAA,GAAAtE,MAAA,CAAAC,UAAA;MACA,IAAAsD,QAAA,GAAA9C,IAAA,CAAAG,KAAA,OAAA0D,WAAA;MACAlB,MAAA,CAAAtB,OAAA;QACAC,IAAA;QACAwC,OAAA;QACAC,SAAA;UACAjB,QAAA,EAAAA,QAAA;UACAkB,UAAA,EAAAhE,IAAA,CAAAG,KAAA,CAAA2C,QAAA;QACA;QACAC,OAAA,QAAAc,WAAA,gBAAAA,WAAA;QACA3C,SAAA,WAAAA,UAAAE,MAAA;UACA,IAAA6C,YAAA,YAAAA,aAAAC,MAAA;YACA,KAAAA,MAAA,IAAAA,MAAA;YACA,UAAAjF,MAAA,EAAAiF,MAAA,cAAAxD,OAAA;UACA;UAEA,kCAAAzB,MAAA,CAAA6D,QAAA,oDAAA7D,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAAmC,MAAA,CAAA1I,IAAA,CAAAkL,QAAA,oDAAA3E,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAAmC,MAAA,CAAA1I,IAAA,CAAAgJ,GAAA,oDAAAzC,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAAmC,MAAA,CAAA1I,IAAA,CAAAiJ,IAAA,oDAAA1C,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAAgF,YAAA,CAAA7C,MAAA,CAAA1I,IAAA,CAAAyL,QAAA,qDAAAlF,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAAyD,MAAA,CAAA0B,WAAA,CAAAhD,MAAA,CAAA1I,IAAA,CAAAiL,IAAA;QAEA;MACA;MACA;MACAhB,MAAA,CAAAxB,MAAA,IAAAkD,SAAA;QACA/C,IAAA;QAAA;QACAgD,YAAA;QAAA;QACAC,MAAA;UACAC,MAAA,OAAAlF,KAAA;UACAmF,OAAA,OAAAnF,KAAA;UACAoF,SAAA;YACArB,KAAA,MAAA/D;UACA;QACA;MACA;MAEAqD,MAAA,CAAAgC,MAAA;QACAjM,IAAA,EAAAA,IAAA,CAAA8I,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAmC,QAAA;QAAA;QACAG,SAAA;UACAzJ,KAAA;UACAwI,QAAA,OAAAxD;QACA;QACAsF,OAAA,OAAAtF,KAAA;QACAuF,SAAA,OAAAvF,KAAA;QACAwF,UAAA,OAAAxF,KAAA;QACAyF,MAAA;QACAzJ,IAAA;QACA0J,MAAA;QACAC,IAAA;MACA;MACAtC,MAAA,CAAAxB,MAAA,IAAA/G,KAAA;QACAkH,IAAA;QAAA;QACA4D,QAAA;QAAA;QACA;QACAC,QAAA,MAAA7F,KAAA;QACAiF,MAAA;UACArD,SAAA,WAAAA,UAAAE,MAAA;YACA,IAAA6C,YAAA,YAAAA,aAAAC,MAAA;cACA,KAAAA,MAAA,IAAAA,MAAA;cACA,UAAAjF,MAAA,EAAAiF,MAAA,cAAAxD,OAAA;YACA;YACA,wBAAAU,MAAA,CAAAjJ,IAAA,uBAAA8L,YAAA,CAAA7C,MAAA,CAAA1I,IAAA,CAAAyL,QAAA,UAAA/C,MAAA,CAAA1I,IAAA,CAAAiL,IAAA;UACA;UACAd,IAAA,EAAAA;QACA;MACA;MACA,OAAAF,MAAA;IACA;IACAjF,YAAA,WAAAA,aAAAhF,IAAA;MAAA,IAAA0M,MAAA;MAAA,WAAAtI,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAqI,SAAA;QAAA,IAAAnD,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAoI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlI,IAAA,GAAAkI,SAAA,CAAAjI,IAAA;YAAA;cAAA;cACA8H,MAAA,CAAArJ,YAAA;cAAAwJ,SAAA,CAAAlI,IAAA;cAAAkI,SAAA,CAAAjI,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAxH,SAAA,CAAApC,IAAA;YAAA;cAAAwJ,GAAA,GAAAqD,SAAA,CAAA/C,IAAA;cACA4C,MAAA,CAAAtK,SAAA,GAAAoH,GAAA,CAAAxJ,IAAA;YAAA;cAAA6M,SAAA,CAAAlI,IAAA;cAEA+H,MAAA,CAAArJ,YAAA;cAAA,OAAAwJ,SAAA,CAAAC,MAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IAEA;IACA5H,YAAA,WAAAA,aAAA/E,IAAA;MAAA,IAAA+M,MAAA;MAAA,WAAA3I,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAA0I,SAAA;QAAA,IAAAxD,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAyI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvI,IAAA,GAAAuI,SAAA,CAAAtI,IAAA;YAAA;cAAA;cACAmI,MAAA,CAAA1J,YAAA;cAAA6J,SAAA,CAAAvI,IAAA;cAAAuI,SAAA,CAAAtI,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAzH,SAAA,CAAAnC,IAAA;YAAA;cAAAwJ,GAAA,GAAA0D,SAAA,CAAApD,IAAA;cACAiD,MAAA,CAAA5K,SAAA,GAAAqH,GAAA,CAAAxJ,IAAA;YAAA;cAAAkN,SAAA,CAAAvI,IAAA;cAEAoI,MAAA,CAAA1J,YAAA;cAAA,OAAA6J,SAAA,CAAAJ,MAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAxH,IAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IAEA;IACAG,cAAA,WAAAA,eAAApE,IAAA;MAAA;MACA,KAAAlI,cAAA,GAAAkI,IAAA,CAAAvI,IAAA;MACA,KAAAsE,SAAA;IACA;IACAsI,iBAAA,WAAAA,kBAAAzL,KAAA;MACAqF,QAAA,CAAAqG,eAAA,CAAAC,KAAA,CAAAC,WAAA,8BAAA5L,KAAA;IACA;IACA6D,kBAAA,WAAAA,mBAAAxF,IAAA;MAAA,IAAAwN,MAAA;MAAA,WAAApJ,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAmJ,SAAA;QAAA,IAAAjE,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAkJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhJ,IAAA,GAAAgJ,SAAA,CAAA/I,IAAA;YAAA;cAAA;cACA4I,MAAA,CAAApN,0BAAA;cAAAuN,SAAA,CAAAhJ,IAAA;cAAAgJ,SAAA,CAAA/I,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAgE,eAAA,CAAA5N,IAAA;YAAA;cAAAwJ,GAAA,GAAAmE,SAAA,CAAA7D,IAAA;cACA;cACA0D,MAAA,CAAArN,cAAA,GAAA0N,MAAA,CAAAC,WAAA,CACAD,MAAA,CAAAE,OAAA,CAAAvE,GAAA,CAAAxJ,IAAA,EAAA8I,GAAA,WAAAkF,IAAA;gBAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA1O,OAAA,EAAAwO,IAAA;kBAAAG,GAAA,GAAAF,KAAA;kBAAAtM,KAAA,GAAAsM,KAAA;gBAAA,QAAAE,GAAA,EAAAX,MAAA,CAAA9B,WAAA,CAAA/J,KAAA;cAAA,EACA;YAAA;cAAAgM,SAAA,CAAAhJ,IAAA;cAEA6I,MAAA,CAAApN,0BAAA;cAAA,OAAAuN,SAAA,CAAAb,MAAA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAjI,IAAA;UAAA;QAAA,GAAA+H,QAAA;MAAA;IAEA;IACAhI,kBAAA,WAAAA,mBAAAzF,IAAA;MAAA,IAAAoO,MAAA;MAAA,WAAAhK,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAA+J,SAAA;QAAA,IAAA7E,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAA8J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5J,IAAA,GAAA4J,SAAA,CAAA3J,IAAA;YAAA;cAAA;cACAwJ,MAAA,CAAA/N,0BAAA;cAAAkO,SAAA,CAAA5J,IAAA;cAAA4J,SAAA,CAAA3J,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAA4E,eAAA,CAAAxO,IAAA;YAAA;cAAAwJ,GAAA,GAAA+E,SAAA,CAAAzE,IAAA;cACAsE,MAAA,CAAA9N,YAAA,GAAAuN,MAAA,CAAAC,WAAA,CACAD,MAAA,CAAAE,OAAA,CAAAvE,GAAA,CAAAxJ,IAAA,EAAA8I,GAAA,WAAA2F,KAAA;gBAAA,IAAAC,KAAA,OAAAR,eAAA,CAAA1O,OAAA,EAAAiP,KAAA;kBAAAN,GAAA,GAAAO,KAAA;kBAAA/M,KAAA,GAAA+M,KAAA;gBAAA,QAAAP,GAAA,EAAAC,MAAA,CAAA1C,WAAA,CAAA/J,KAAA;cAAA,EACA;YAAA;cAAA4M,SAAA,CAAA5J,IAAA;cAEAyJ,MAAA,CAAA/N,0BAAA;cAAA,OAAAkO,SAAA,CAAAzB,MAAA;YAAA;YAAA;cAAA,OAAAyB,SAAA,CAAA7I,IAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IAEA;IACA/I,aAAA,WAAAA,cAAAtF,IAAA;MAAA,IAAA2O,MAAA;MAAA,WAAAvK,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAsK,SAAA;QAAA,IAAApF,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAqK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnK,IAAA,GAAAmK,SAAA,CAAAlK,IAAA;YAAA;cAAA;cACA+J,MAAA,CAAApL,mBAAA;cAAAuL,SAAA,CAAAnK,IAAA;cAAAmK,SAAA,CAAAlK,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAmF,gBAAA,CAAA/O,IAAA;YAAA;cAAAwJ,GAAA,GAAAsF,SAAA,CAAAhF,IAAA;cACA6E,MAAA,CAAA1O,UAAA,GAAA4N,MAAA,CAAAC,WAAA,CACAD,MAAA,CAAAE,OAAA,CAAAvE,GAAA,CAAAxJ,IAAA,EAAA8I,GAAA,WAAAkG,KAAA;gBAAA,IAAAC,KAAA,OAAAf,eAAA,CAAA1O,OAAA,EAAAwP,KAAA;kBAAAb,GAAA,GAAAc,KAAA;kBAAAtN,KAAA,GAAAsN,KAAA;gBAAA,QAAAd,GAAA,EAAAQ,MAAA,CAAAjD,WAAA,CAAA/J,KAAA;cAAA,EACA;YAAA;cAAAmN,SAAA,CAAAnK,IAAA;cAEAgK,MAAA,CAAApL,mBAAA;cAAA,OAAAuL,SAAA,CAAAhC,MAAA;YAAA;YAAA;cAAA,OAAAgC,SAAA,CAAApJ,IAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA;IAEA;IACArJ,aAAA,WAAAA,cAAAvF,IAAA;MAAA,IAAAkP,MAAA;MAAA,WAAA9K,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAA6K,SAAA;QAAA,IAAA3F,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAA4K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1K,IAAA,GAAA0K,SAAA,CAAAzK,IAAA;YAAA;cAAA;cACAsK,MAAA,CAAA3L,mBAAA;cAAA8L,SAAA,CAAA1K,IAAA;cAAA0K,SAAA,CAAAzK,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAA0F,gBAAA,CAAAtP,IAAA;YAAA;cAAAwJ,GAAA,GAAA6F,SAAA,CAAAvF,IAAA;cACAoF,MAAA,CAAAhP,UAAA,GAAA2N,MAAA,CAAAC,WAAA,CACAD,MAAA,CAAAE,OAAA,CAAAvE,GAAA,CAAAxJ,IAAA,EAAA8I,GAAA,WAAAyG,KAAA;gBAAA,IAAAC,KAAA,OAAAtB,eAAA,CAAA1O,OAAA,EAAA+P,KAAA;kBAAApB,GAAA,GAAAqB,KAAA;kBAAA7N,KAAA,GAAA6N,KAAA;gBAAA,QAAArB,GAAA,EAAAe,MAAA,CAAAxD,WAAA,CAAA/J,KAAA;cAAA,EACA;cACA;YAAA;cAAA0N,SAAA,CAAA1K,IAAA;cAEAuK,MAAA,CAAA3L,mBAAA;cAAA,OAAA8L,SAAA,CAAAvC,MAAA;YAAA;YAAA;cAAA,OAAAuC,SAAA,CAAA3J,IAAA;UAAA;QAAA,GAAAyJ,QAAA;MAAA;IAEA;IACAhK,SAAA,WAAAA,UAAAnF,IAAA;MAAA,IAAAyP,OAAA;MAAA,WAAArL,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAoL,SAAA;QAAA,IAAAlG,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAmL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjL,IAAA,GAAAiL,SAAA,CAAAhL,IAAA;YAAA;cAAA;cACA6K,OAAA,CAAAhM,aAAA;cAAAmM,SAAA,CAAAjL,IAAA;cAAAiL,SAAA,CAAAhL,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAjJ,MAAA,CAAAX,IAAA;YAAA;cAAAwJ,GAAA,GAAAoG,SAAA,CAAA9F,IAAA;cACA2F,OAAA,CAAA9O,MAAA,IAAA8O,OAAA,CAAAI,YAAA,CAAArG,GAAA,CAAAxJ,IAAA;YAAA;cAAA4P,SAAA,CAAAjL,IAAA;cAEA8K,OAAA,CAAAhM,aAAA;cAAA,OAAAmM,SAAA,CAAA9C,MAAA;YAAA;YAAA;cAAA,OAAA8C,SAAA,CAAAlK,IAAA;UAAA;QAAA,GAAAgK,QAAA;MAAA;IAEA;IACAxK,WAAA,WAAAA,YAAAlF,IAAA;MAAA,IAAA8P,OAAA;MAAA,WAAA1L,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAyL,UAAA;QAAA,IAAAvG,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAwL,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtL,IAAA,GAAAsL,UAAA,CAAArL,IAAA;YAAA;cAAA;cACAkL,OAAA,CAAArM,aAAA;cAAAwM,UAAA,CAAAtL,IAAA;cAAAsL,UAAA,CAAArL,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAsG,QAAA,CAAAlQ,IAAA;YAAA;cAAAwJ,GAAA,GAAAyG,UAAA,CAAAnG,IAAA;cACAgG,OAAA,CAAApP,MAAA,IAAAoP,OAAA,CAAAD,YAAA,CAAArG,GAAA,CAAAxJ,IAAA;YAAA;cAAAiQ,UAAA,CAAAtL,IAAA;cAEAmL,OAAA,CAAArM,aAAA;cAAA,OAAAwM,UAAA,CAAAnD,MAAA;YAAA;YAAA;cAAA,OAAAmD,UAAA,CAAAvK,IAAA;UAAA;QAAA,GAAAqK,SAAA;MAAA;IAEA;IACAF,YAAA,WAAAA,aAAA7P,IAAA;MAAA;MACA,KAAAA,IAAA;MACA,OAAA6N,MAAA,CAAAC,WAAA,CACAD,MAAA,CAAAE,OAAA,CAAA/N,IAAA,EAAA8I,GAAA,WAAAqH,KAAA;QAAA,IAAAC,MAAA,OAAAlC,eAAA,CAAA1O,OAAA,EAAA2Q,KAAA;UAAAhC,GAAA,GAAAiC,MAAA;UAAAzO,KAAA,GAAAyO,MAAA;QAAA,QAAAjC,GAAA,EAAAxM,KAAA,aAAAA,KAAA,cAAAA,KAAA;MAAA,EACA;IACA;IACA0O,aAAA,WAAAA,cAAAtH,IAAA;MACA,KAAAtI,aAAA,GAAAsI,IAAA,CAAAvI,IAAA;MACA,KAAA0E,WAAA;QACAhD,SAAA,OAAAzB,aAAA;QACAoB,WAAA,OAAAA;MACA;MACA,KAAAsD,SAAA;QACAjD,SAAA,OAAAzB,aAAA;QACAoB,WAAA,OAAAA;MACA;MACA,KAAAuD,WAAA;QAAA;QACAlD,SAAA,OAAAzB,aAAA;QACAoB,WAAA,OAAAA;MACA;IACA;IACAwD,YAAA,WAAAA,aAAAxD,WAAA;MAAA,IAAAyO,OAAA;MAAA;MACA3G,YAAA,CAAA4G,KAAA,CAAAC,OAAA,CAAA3O,WAAA,EAAA4O,IAAA,WAAAjH,GAAA;QACA8G,OAAA,CAAApN,YAAA,GAAAsG,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;UAAA;YACA2H,WAAA,EAAA3H,IAAA,CAAA2H,WAAA;YACAC,MAAA,EAAA5H,IAAA,CAAA4H,MAAA;YACAC,QAAA,EAAA7H,IAAA,CAAA6H,QAAA,mBAAA7H,IAAA,CAAA6H,QAAA;YACAC,KAAA,EAAA9H,IAAA,CAAA8H,KAAA,mBAAA9H,IAAA,CAAA8H,KAAA;YACAC,KAAA,EAAA/H,IAAA,CAAA+H,KAAA,mBAAA/H,IAAA,CAAA+H,KAAA;YACAC,OAAA,EAAAhI,IAAA,CAAAgI,OAAA,mBAAAhI,IAAA,CAAAgI,OAAA;YACAC,KAAA,EAAAjI,IAAA,CAAAiI,KAAA,mBAAAjI,IAAA,CAAAiI,KAAA;YACAC,SAAA,EAAAlI,IAAA,CAAAkI,SAAA,mBAAAlI,IAAA,CAAAkI,SAAA;YACAC,YAAA,EAAAnI,IAAA,CAAAmI,YAAA,mBAAAnI,IAAA,CAAAmI,YAAA;YACAC,QAAA,EAAApI,IAAA,CAAAoI,QAAA,mBAAApI,IAAA,CAAAoI,QAAA;YACAC,WAAA,EAAArI,IAAA,CAAAqI,WAAA,mBAAArI,IAAA,CAAAqI,WAAA;YACAC,QAAA,EAAAtI,IAAA,CAAAsI,QAAA,mBAAAtI,IAAA,CAAAsI,QAAA;YACAC,WAAA,EAAAvI,IAAA,CAAAuI,WAAA,mBAAAvI,IAAA,CAAAuI,WAAA;YACAC,WAAA,EAAAxI,IAAA,CAAAwI,WAAA,mBAAAxI,IAAA,CAAAwI,WAAA;YACAC,UAAA,EAAAzI,IAAA,CAAAyI,UAAA,mBAAAzI,IAAA,CAAAyI,UAAA;YACAC,UAAA,EAAA1I,IAAA,CAAA0I,UAAA,mBAAA1I,IAAA,CAAA0I;UACA;QAAA;MACA;IACA;IACAxM,SAAA,WAAAA,UAAAjF,IAAA;MAAA,IAAA0R,OAAA;MAAA,WAAAtN,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAqN,UAAA;QAAA,IAAAnI,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAoN,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlN,IAAA,GAAAkN,UAAA,CAAAjN,IAAA;YAAA;cAAA;cACA8M,OAAA,CAAAlO,eAAA;cAAAqO,UAAA,CAAAlN,IAAA;cAAAkN,UAAA,CAAAjN,IAAA;cAAA,OAEA+E,YAAA,CAAAC,KAAA,CAAAvH,MAAA,CAAArC,IAAA;YAAA;cAAAwJ,GAAA,GAAAqI,UAAA,CAAA/H,IAAA;cACA4H,OAAA,CAAArP,MAAA,GAAAmH,GAAA,CAAAxJ,IAAA;cACA0R,OAAA,CAAAjQ,QAAA,IAAAE,KAAA,GAAA+P,OAAA,CAAArP,MAAA,CAAAyP,cAAA;cACAJ,OAAA,CAAAjQ,QAAA,IAAAE,KAAA,GAAA+P,OAAA,CAAArP,MAAA,CAAA0P,YAAA;cACAL,OAAA,CAAAjQ,QAAA,IAAAE,KAAA,GAAA+P,OAAA,CAAArP,MAAA,CAAA2P,WAAA;YAAA;cAAAH,UAAA,CAAAlN,IAAA;cAEA+M,OAAA,CAAAlO,eAAA;cAAA,OAAAqO,UAAA,CAAA/E,MAAA;YAAA;YAAA;cAAA,OAAA+E,UAAA,CAAAnM,IAAA;UAAA;QAAA,GAAAiM,SAAA;MAAA;IAEA;IAEAM,WAAA,WAAAA,YAAAlJ,IAAA;MACA,KAAAhH,aAAA,GAAAgH,IAAA,CAAAvI,IAAA;MACA,KAAA0R,UAAA;IACA;IACAC,WAAA,WAAAA,YAAApJ,IAAA;MACA,KAAA/G,aAAA,GAAA+G,IAAA,CAAAvI,IAAA;MACA,KAAA4R,UAAA;IACA;IACAC,WAAA,WAAAA,YAAAtJ,IAAA;MACA,KAAA9G,aAAA,GAAA8G,IAAA,CAAAvI,IAAA;MACA,KAAA8R,UAAA;IACA;IACAzN,cAAA,WAAAA,eAAAhD,WAAA;MAAA,IAAA0Q,OAAA;MAAA,WAAAnO,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAkO,UAAA;QAAA,IAAAhJ,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAiO,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/N,IAAA,GAAA+N,UAAA,CAAA9N,IAAA;YAAA;cAAA8N,UAAA,CAAA9N,IAAA;cAAA,OACA+E,YAAA,CAAAgJ,MAAA,CAAA9N,cAAA,CAAAhD,WAAA;YAAA;cAAA2H,GAAA,GAAAkJ,UAAA,CAAA5I,IAAA;cACAyI,OAAA,CAAAzQ,eAAA,GAAA0H,GAAA,CAAAxJ,IAAA;cACAuS,OAAA,CAAApP,OAAA,GAAAoP,OAAA,CAAAzQ,eAAA,CAAAgK,MAAA;cACAyG,OAAA,CAAAtQ,aAAA,GAAAsQ,OAAA,CAAAvQ,aAAA,GAAAuQ,OAAA,CAAAxQ,aAAA,GACAwQ,OAAA,CAAAzQ,eAAA,IAAAtB,IAAA;YAAA;YAAA;cAAA,OAAAkS,UAAA,CAAAhN,IAAA;UAAA;QAAA,GAAA8M,SAAA;MAAA;IACA;IACA1N,SAAA,WAAAA,UAAA;MAAA,IAAA8N,OAAA;MAAA,WAAAxO,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAuO,UAAA;QAAA,WAAAxO,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAsO,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApO,IAAA,GAAAoO,UAAA,CAAAnO,IAAA;YAAA;cACAgO,OAAA,CAAAlP,aAAA;cAAAqP,UAAA,CAAApO,IAAA;cAAAoO,UAAA,CAAAnO,IAAA;cAAA,OAEAoO,OAAA,CAAAC,GAAA,EACAL,OAAA,CAAAN,UAAA,IACAM,OAAA,CAAAR,UAAA,IACAQ,OAAA,CAAAM,UAAA,IACAN,OAAA,CAAAV,UAAA,GACA;YAAA;cAAAa,UAAA,CAAApO,IAAA;cAEAiO,OAAA,CAAAlP,aAAA;cAAA,OAAAqP,UAAA,CAAAjG,MAAA;YAAA;YAAA;cAAA,OAAAiG,UAAA,CAAArN,IAAA;UAAA;QAAA,GAAAmN,SAAA;MAAA;IAEA;IACAM,WAAA,WAAAA,YAAA;MAAA;MACA,KAAArQ,QAAA,QAAAuF,YAAA,mBAAApH,WAAA;MACA;MACA,KAAA2C,YAAA;MACA,KAAAd,QAAA,CAAA6F,OAAA;QACAC,IAAA;MACA;IACA;IACAsK,UAAA,WAAAA,WAAA;MAAA,IAAAE,OAAA;MAAA,WAAAhP,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAA+O,UAAA;QAAA,IAAA7J,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAA8O,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5O,IAAA,GAAA4O,UAAA,CAAA3O,IAAA;YAAA;cAAA2O,UAAA,CAAA3O,IAAA;cAAA,OACA+E,YAAA,CAAAC,KAAA,CAAA4J,OAAA;gBACAtR,SAAA,EAAAkR,OAAA,CAAAvS,cAAA;gBACAgB,WAAA,EAAAuR,OAAA,CAAAvR;cACA;YAAA;cAHA2H,GAAA,GAAA+J,UAAA,CAAAzJ,IAAA;cAIAsJ,OAAA,CAAAnS,WAAA,CAAAwS,KAAA,GAAAjK,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAoF,GAAA,CAAAuF,OAAA;cAAA;cACAN,OAAA,CAAAnS,WAAA,CAAAwH,MAAA,GAAAe,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAApH,KAAA;cAAA;cACAyR,OAAA,CAAAD,WAAA;YAAA;YAAA;cAAA,OAAAI,UAAA,CAAA7N,IAAA;UAAA;QAAA,GAAA2N,SAAA;MAAA;IACA;IACAjB,UAAA,WAAAA,WAAA;MAAA,IAAAuB,OAAA;MAAA,WAAAvP,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAsP,UAAA;QAAA,IAAApK,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAqP,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnP,IAAA,GAAAmP,UAAA,CAAAlP,IAAA;YAAA;cAAAkP,UAAA,CAAAlP,IAAA;cAAA,OACA+E,YAAA,CAAAC,KAAA,CAAAmK,OAAA;gBACA7R,SAAA,EAAAyR,OAAA,CAAA9S,cAAA;gBACAgB,WAAA,EAAA8R,OAAA,CAAA9R,WAAA;gBACA6O,WAAA,EAAAiD,OAAA,CAAA3R;cACA;YAAA;cAJAwH,GAAA,GAAAsK,UAAA,CAAAhK,IAAA;cAKA6J,OAAA,CAAAvS,cAAA,GAAAoI,GAAA,CAAAxJ,IAAA;cACA2T,OAAA,CAAA3S,WAAA,CAAAyS,KAAA,GAAAjK,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAoF,GAAA,CAAAuF,OAAA;cAAA;cACA;cACAC,OAAA,CAAAxK,iBAAA;YAAA;YAAA;cAAA,OAAA2K,UAAA,CAAApO,IAAA;UAAA;QAAA,GAAAkO,SAAA;MAAA;IACA;IACAtB,UAAA,WAAAA,WAAA;MAAA,IAAA0B,OAAA;MAAA,WAAA5P,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAA2P,UAAA;QAAA,IAAAzK,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAA0P,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxP,IAAA,GAAAwP,UAAA,CAAAvP,IAAA;YAAA;cAAAuP,UAAA,CAAAvP,IAAA;cAAA,OACA+E,YAAA,CAAAC,KAAA,CAAAwK,OAAA;gBACAlS,SAAA,EAAA8R,OAAA,CAAAnT,cAAA;gBACAgB,WAAA,EAAAmS,OAAA,CAAAnS,WAAA;gBACA6O,WAAA,EAAAsD,OAAA,CAAA/R;cACA;YAAA;cAJAuH,GAAA,GAAA2K,UAAA,CAAArK,IAAA;cAKAkK,OAAA,CAAA7S,cAAA,GAAAqI,GAAA,CAAAxJ,IAAA;cACAgU,OAAA,CAAAjT,WAAA,CAAA0S,KAAA,GAAAjK,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAoF,GAAA,CAAAuF,OAAA;cAAA;cACA;cACAM,OAAA,CAAA7L,iBAAA;YAAA;YAAA;cAAA,OAAAgM,UAAA,CAAAzO,IAAA;UAAA;QAAA,GAAAuO,SAAA;MAAA;IACA;IACAI,WAAA,WAAAA,YAAA;MACA,KAAApR,QAAA,QAAAoF,YAAA,mBAAAvH,WAAA;MACA,SAAAD,cAAA,eAAAA,cAAA;QACA;QACA,KAAA8C,YAAA;QACA,KAAAV,QAAA,CAAAqF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,QAAAA,KAAA,UAAAqG,OAAA;QACA;QACA,KAAA/E,QAAA,CAAAwF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,QAAAA,MAAA,CAAA/G,KAAA,UAAAqG,OAAA;QACA;MACA;QACA;QACA,KAAArE,YAAA;QACA,KAAAV,QAAA,CAAAqF,KAAA,IAAAC,SAAA,CAAAC,SAAA,aAAA7G,KAAA;UACA,QAAAA,KAAA,cAAAqG,OAAA;QACA;QACA,KAAA/E,QAAA,CAAAwF,MAAA,IAAA/G,KAAA,CAAA8G,SAAA,aAAAE,MAAA;UACA,QAAAA,MAAA,CAAA/G,KAAA,cAAAqG,OAAA;QACA;MAEA;MACA,KAAA/E,QAAA,CAAA0F,OAAA;QACAC,IAAA;MACA;IACA;IACAsJ,UAAA,WAAAA,WAAA;MAAA,IAAAoC,OAAA;MAAA,WAAAlQ,kBAAA,CAAA5E,OAAA,mBAAA6E,oBAAA,CAAA7E,OAAA,IAAA8E,IAAA,UAAAiQ,UAAA;QAAA,IAAA/K,GAAA;QAAA,WAAAnF,oBAAA,CAAA7E,OAAA,IAAAgF,IAAA,UAAAgQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9P,IAAA,GAAA8P,UAAA,CAAA7P,IAAA;YAAA;cAAA6P,UAAA,CAAA7P,IAAA;cAAA,OACA+E,YAAA,CAAAC,KAAA,CAAA8K,OAAA;gBACAxS,SAAA,EAAAoS,OAAA,CAAAzT,cAAA;gBACAgB,WAAA,EAAAyS,OAAA,CAAAzS,WAAA;gBACA6O,WAAA,EAAA4D,OAAA,CAAAvS;cACA;YAAA;cAJAyH,GAAA,GAAAiL,UAAA,CAAA3K,IAAA;cAMAwK,OAAA,CAAAxT,WAAA,CAAA2S,KAAA,GAAAjK,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAoF,GAAA,CAAAuF,OAAA;cAAA;cACAY,OAAA,CAAAxT,WAAA,CAAA2H,MAAA,GAAAe,GAAA,CAAAxJ,IAAA,CAAA8I,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAApH,KAAA;cAAA;cACA2S,OAAA,CAAAD,WAAA;YAAA;YAAA;cAAA,OAAAI,UAAA,CAAA/O,IAAA;UAAA;QAAA,GAAA6O,SAAA;MAAA;IACA;IACArK,YAAA,WAAAA,aAAA;MAAA,IAAAtI,KAAA,GAAA+S,SAAA,CAAA7I,MAAA,QAAA6I,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,SAAA,OAAAC,YAAA;MACAD,SAAA,CAAAE,QAAA,CAAAnT,KAAA;MACA,OAAAiT,SAAA,CAAArT,SAAA;IACA;IACA6G,YAAA,WAAAA,aAAA;MAAA,IAAAzG,KAAA,GAAA+S,SAAA,CAAA7I,MAAA,QAAA6I,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAAA,IAAA3U,IAAA,GAAA2U,SAAA,CAAA7I,MAAA,QAAA6I,SAAA,QAAAC,SAAA,GAAAD,SAAA;QAAAlB,KAAA;QAAAhL,MAAA;MAAA;MAAA,IAAAuM,IAAA,GAAAL,SAAA,CAAA7I,MAAA,QAAA6I,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAM,SAAA,OAAAC,YAAA;MACAD,SAAA,CAAAE,UAAA,CAAAnV,IAAA,CAAAyT,KAAA,EAAAzT,IAAA,CAAAyI,MAAA;MACAwM,SAAA,CAAAF,QAAA,CAAAnT,KAAA;MACA,IAAAqI,MAAA,GAAAgL,SAAA,CAAAzT,SAAA;MACA,KAAA4T,KAAA,CAAAC,OAAA,CAAApL,MAAA,CAAA3B,KAAA;QACA2B,MAAA,CAAA3B,KAAA;MACA;MACA,OAAA2B,MAAA;IACA;IACAqL,gBAAA,WAAAA,iBAAAC,MAAA;MAAA,IAAAC,GAAA,GAAAD,MAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,MAAA,CAAAE,MAAA;QAAAC,QAAA,GAAAH,MAAA,CAAAG,QAAA;QAAAC,WAAA,GAAAJ,MAAA,CAAAI,WAAA;MACA;MACA,IAAAD,QAAA,UAAAxS,YAAA,CAAA4I,MAAA;QACA,IAAA6J,WAAA;UAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;QACA,IAAAF,WAAA;UAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAF,WAAA;QACA,IAAAjF,WAAA,GAAA8E,GAAA,CAAA9E,WAAA;;QAEA;QACA,IAAAoF,UAAA,GAAAJ,QAAA;QACA,OAAAI,UAAA,aAAA5S,YAAA,CAAA4S,UAAA,MAAApF,WAAA,KAAAA,WAAA;UACAoF,UAAA;QACA;;QAEA;QACA,IAAAC,SAAA;QACA,SAAAC,CAAA,GAAAF,UAAA,EAAAE,CAAA,QAAA9S,YAAA,CAAA4I,MAAA,EAAAkK,CAAA;UACA,SAAA9S,YAAA,CAAA8S,CAAA,EAAAtF,WAAA,KAAAA,WAAA;YACAqF,SAAA;UACA;YACA;UACA;QACA;;QAEA;QACA,IAAAL,QAAA,KAAAI,UAAA;UACA;YACAF,OAAA,EAAAG,SAAA;YACAF,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;EACA;EACAI,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,IAAAnW,IAAA,QAAAmC,SAAA;MACA,IAAAiU,oBAAA,OAAA7U,uBAAA;MAEA,IAAAvB,IAAA,CAAAiL,IAAA;QACAmL,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;QACAF,oBAAA,CAAAG,UAAA,EAAAvW,IAAA,CAAAiL,IAAA;MACA;QACAmL,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;QACAF,oBAAA,CAAAG,UAAA,EAAAvW,IAAA,CAAAiL,IAAA;MACA;MAEA,IAAArE,KAAA,GAAAC,MAAA,CAAAC,UAAA;MACA,IAAAmD,MAAA,GAAAmM,oBAAA,CAAA5U,SAAA;MACAyI,MAAA,CAAAuM,KAAA;QACA5N,IAAA;QACA6N,OAAA;QACAC,IAAA,KAAAnQ,MAAA,MAAAyE,SAAA,CAAAhL,IAAA,CAAAiL,IAAA;QACAI,SAAA;UACAzJ,KAAA;UACAwI,QAAA,OAAAxD;QACA;QACA+P,YAAA;UACAvM,QAAA,OAAAxD,KAAA;QACA;QACAsF,OAAA,OAAAtF;MACA;MACAqD,MAAA,CAAAxB,MAAA,IAAAmO,QAAA,GAAA3M,MAAA,CAAAxB,MAAA,IAAAzI,IAAA;MACAiK,MAAA,CAAA4M,KAAA,CAAAC,MAAA;MACA,IAAA3L,WAAA,GAAAtE,MAAA,CAAAC,UAAA;MACA,IAAAsD,QAAA,GAAA9C,IAAA,CAAAG,KAAA,OAAA0D,WAAA;MACAlB,MAAA,CAAAtB,OAAA;QACAC,IAAA;QACAwC,OAAA;QACAC,SAAA;UACAjB,QAAA,EAAAA,QAAA;UACAkB,UAAA,EAAAhE,IAAA,CAAAG,KAAA,CAAA2C,QAAA;QACA;QACAC,OAAA,QAAAc,WAAA,gBAAAA,WAAA;QACA3C,SAAA,WAAAA,UAAAE,MAAA;UACA,IAAA6C,YAAA,YAAAA,aAAAC,MAAA;YACA,KAAAA,MAAA,IAAAA,MAAA;YACA,IAAA2K,OAAA,CAAA1V,aAAA;cAAA;cACA,UAAA8F,MAAA,EAAAiF,MAAA,UAAAxD,OAAA;YACA;YACA,UAAAzB,MAAA,EAAAiF,MAAA,cAAAxD,OAAA;UACA;UAEA,kCAAAzB,MAAA,CAAA6D,QAAA,oDAAA7D,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAAvG,IAAA,CAAA+W,aAAA,2DAAAxQ,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAAvG,IAAA,CAAAyL,QAAA,2DAAAlF,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAA4P,OAAA,CAAAzK,WAAA,CAAA1L,IAAA,CAAAiL,IAAA;QAEA;MACA;MACA,OAAAhB,MAAA;IACA;IACA+M,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,IAAAjX,IAAA,QAAAoC,SAAA;MACA,IAAAgU,oBAAA,OAAA7U,uBAAA;MAEA,IAAAvB,IAAA,CAAAiL,IAAA;QACAmL,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;QACAF,oBAAA,CAAAG,UAAA,EAAAvW,IAAA,CAAAiL,IAAA;MACA;QACAmL,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;QACAF,oBAAA,CAAAG,UAAA,EAAAvW,IAAA,CAAAiL,IAAA;MACA;MAEA,IAAArE,KAAA,GAAAC,MAAA,CAAAC,UAAA;MACA,IAAAmD,MAAA,GAAAmM,oBAAA,CAAA5U,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAyI,MAAA,CAAAuM,KAAA;QACA5N,IAAA;QACA6N,OAAA;QACAC,IAAA,KAAAnQ,MAAA,CAAAvG,IAAA,CAAAiL,IAAA;QACAI,SAAA;UACAzJ,KAAA;UACAwI,QAAA,OAAAxD;QACA;QACA+P,YAAA;UACAvM,QAAA,OAAAxD,KAAA;QACA;QACAsF,OAAA,OAAAtF;MACA;MACAqD,MAAA,CAAA4M,KAAA,CAAAC,MAAA;MACA,IAAA3L,WAAA,GAAAtE,MAAA,CAAAC,UAAA;MACA,IAAAsD,QAAA,GAAA9C,IAAA,CAAAG,KAAA,OAAA0D,WAAA;MACAlB,MAAA,CAAAtB,OAAA;QACAyC,OAAA;QACAC,SAAA;UACAjB,QAAA,EAAAA,QAAA;UACAkB,UAAA,EAAAhE,IAAA,CAAAG,KAAA,CAAA2C,QAAA;QACA;QACAC,OAAA,QAAAc,WAAA,gBAAAA,WAAA;QACA3C,SAAA,WAAAA,UAAAE,MAAA;UACA,IAAA6C,YAAA,YAAAA,aAAAC,MAAA;YACA,KAAAA,MAAA,IAAAA,MAAA;YACA,IAAAyL,OAAA,CAAAxW,aAAA;cAAA;cACA,UAAA8F,MAAA,EAAAiF,MAAA,UAAAxD,OAAA;YACA;YACA,UAAAzB,MAAA,EAAAiF,MAAA,cAAAxD,OAAA;UACA;UAEA,kCAAAzB,MAAA,CAAA6D,QAAA,oDAAA7D,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAAvG,IAAA,CAAA+W,aAAA,2DAAAxQ,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAAvG,IAAA,CAAAkX,QAAA,2DAAA3Q,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,gCAAA7D,MAAA,CAAA0Q,OAAA,CAAAvL,WAAA,CAAA1L,IAAA,CAAAiL,IAAA,sDAAA1E,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAAvG,IAAA,CAAAmX,MAAA;QAEA;MACA;MACA,OAAAlN,MAAA;IACA;IACAmN,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,IAAArX,IAAA,QAAAqC,MAAA,CAAAiV,OAAA;MACA,IAAAZ,IAAA;MACA,MAAA1W,IAAA,aAAAA,IAAA,KAAA4U,SAAA;QACA8B,IAAA,GAAA1W,IAAA;MACA;MAEA,IAAAoW,oBAAA,OAAA7U,uBAAA;MACA6U,oBAAA,CAAAG,UAAA,GAAAvW,IAAA,QAAAgI,OAAA;MAEA,IAAAhI,IAAA;QACAoW,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;MACA;QACAF,oBAAA,CAAAC,eAAA;QACAD,oBAAA,CAAAE,kBAAA;MACA;MAEA,IAAA1P,KAAA,GAAAC,MAAA,CAAAC,UAAA;MACA,IAAAmD,MAAA,GAAAmM,oBAAA,CAAA5U,SAAA;MACAyI,MAAA,CAAAxB,MAAA,IAAAmO,QAAA,GAAA3M,MAAA,CAAAxB,MAAA,IAAAzI,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAiK,MAAA,CAAAuM,KAAA;QACA5N,IAAA;QACA6N,OAAA;QACAC,IAAA,KAAAnQ,MAAA,CAAAmQ,IAAA;QACArL,SAAA;UACAzJ,KAAA;UACAwI,QAAA,OAAAxD;QACA;QACA+P,YAAA;UACAvM,QAAA,OAAAxD,KAAA;QACA;QACAsF,OAAA,OAAAtF;MACA;MACAqD,MAAA,CAAA4M,KAAA,CAAAC,MAAA;MACA,IAAA3L,WAAA,GAAAtE,MAAA,CAAAC,UAAA;MACA,IAAAsD,QAAA,GAAA9C,IAAA,CAAAG,KAAA,OAAA0D,WAAA;MACAlB,MAAA,CAAAtB,OAAA;QACAC,IAAA;QACA4D,QAAA;QACApB,OAAA;QACAC,SAAA;UACAjB,QAAA,EAAAA,QAAA;UACAkB,UAAA,EAAAhE,IAAA,CAAAG,KAAA,CAAA2C,QAAA;QACA;QACAC,OAAA,QAAAc,WAAA,gBAAAA,WAAA;QACA3C,SAAA,WAAAA,UAAAE,MAAA;UACA,IAAA6C,YAAA,YAAAA,aAAAC,MAAA;YACA,KAAAA,MAAA,IAAAA,MAAA;YACA,UAAAjF,MAAA,EAAAiF,MAAA,cAAAxD,OAAA;UACA;UAEA,kCAAAzB,MAAA,CAAA6D,QAAA,oDAAA7D,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,wDAAA7D,MAAA,CAAA8Q,OAAA,CAAAhV,MAAA,CAAAyP,cAAA,0DAAAvL,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,8DAAA7D,MAAA,CAAA8Q,OAAA,CAAAhV,MAAA,CAAA2P,WAAA,0DAAAzL,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,4CAAA7D,MAAA,CAAA8Q,OAAA,CAAAhV,MAAA,CAAA0P,YAAA,0DAAAxL,MAAA,CACAe,IAAA,CAAAG,KAAA,CAAA2C,QAAA,kDAAA7D,MAAA,CAAA8Q,OAAA,CAAA3L,WAAA,CAAA2L,OAAA,CAAAhV,MAAA,CAAAiV,OAAA;QAEA;MACA;MACA,OAAArN,MAAA;IACA;IACAsN,UAAA,WAAAA,WAAA;MAAA;MACA,YAAAxN,aAAA,MAAAzH,QAAA;MACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAQA;EACA;AACA", "ignoreList": []}]}