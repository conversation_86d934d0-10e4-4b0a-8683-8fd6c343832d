import request from '@/utils/request'

// 查询计划-节点模版列表
export function listNodeTemplate(query) {
  return request({
    url: '/plan/nodeTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询计划-节点模版详细
export function getNodeTemplate(id) {
  return request({
    url: '/plan/nodeTemplate/' + id,
    method: 'get'
  })
}

// 新增计划-节点模版
export function addNodeTemplate(data) {
  return request({
    url: '/plan/nodeTemplate',
    method: 'post',
    data: data
  })
}

// 修改计划-节点模版
export function updateNodeTemplate(data) {
  return request({
    url: '/plan/nodeTemplate',
    method: 'put',
    data: data
  })
}

// 删除计划-节点模版
export function delNodeTemplate(id) {
  return request({
    url: '/plan/nodeTemplate/' + id,
    method: 'delete'
  })
}
