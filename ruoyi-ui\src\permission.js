import router from './router/index'
import store from './store'
import {Message} from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import {getToken, setToken, getMobileToken, setMobileToken, getRantMobileToken, setRantMobileToken, redirectToOauth2Url, getAuthType} from '@/utils/auth'
import {wechatLogin as wechatLoginWeekly, getUserInfo} from '@/api/weekly/mobile-reportInfo' // 周报移动端登录
import {wechatLoginRant} from '@/api/rantMobile/common' // 督办移动端登录
NProgress.configure({showSpinner: false})
import {Toast, Dialog} from 'vant'

const whiteList = ['/login', '/auth-redirect', '/bind', '/register']
// weekly/weeklyApprove 周报审批
const directList = [
  '/IAMFeedback',
  '/plan/dashboard',
  '/projects',
  '/weekly/weeklyApprove',
  '/weekly/myWeekly',
  '/weekly/todoCenterWeeklyInput',
  '/weekly/weeklyCombineApprove',
  // 督办待办中心pc
  '/rant/look',
  '/rant/todoDetail',
  '/rant/todoCenterAddMatter',
  '/rant/todoCenterAddFeedback',
  '/rant/todoCenterConfirmFeedback',
  '/rant/todoCenterRejectFeedback',
  '/rant/todoCenterAddMatter',
  '/rant/todoCenterFeedback25Combine',
  '/rant/todoCenterFeedbackOverDateSingle',
  '/rant/todoCenterFeedbackRejectSingle',
  '/rant/todoCenterDeptApprove',
  '/rant/todoCenterManagerApprove',
  '/rant/todoCenterMattersNotice',
  '/rant/rantFinishDetail',
] // 直接访问的页面
//待办中心来源 中建通：open_source=zjtapp  企微小程序：open_source=wxapp
router.beforeEach((to, from, next) => {
  // 如果是微信端的页面，直接放行
  /*if (to.path.indexOf('wechatE/mobile/rant') !== -1) { // 督办移动端登录
    if (getRantMobileToken()) {
      if (store.getters.roles.length === 0) {
        store.dispatch('GetInfoMobileRant').then(() => {
          next({...to, replace: true})
        })
      } else {
        next()
      }
    } else {
      let {type, code} = getAuthType(to);
      if (type === 2 || type === 3 || type === 4) {
        wechatLoginRant({"code": code, "type": type}).then(res => {
          setRantMobileToken(res?.token?.access_token)
          store.dispatch('GetInfoMobileRant').then(() => {
            next({...to, replace: true})
          })
        }).catch(err => {
          Dialog.alert({
            title: '登录失败',
          })
        })
      } else { // 企业微信应用
        // 未登录
        if (!to.query.code) {
          redirectToOauth2Url();
        } else { // oauth2认证成功登录
          type = 1;
          code = to.query.code;
          wechatLoginRant({"code": code, "type": type}).then(res => {
            setRantMobileToken(res?.token?.access_token)
            store.dispatch('GetInfoMobileRant').then(() => {
              next({...to, replace: true})
            })
          }).catch(err => {
            Dialog.alert({
              title: '登录失败',
            })
          })
        }
      }
    }

  } else if (to.path.indexOf('wechatE/mobile') !== -1) { // 周报移动端登录
    if (getMobileToken()) {
      if (store.getters.roles.length === 0) {
        store.dispatch('GetInfoMobile').then(() => {
          next({...to, replace: true})
        })
      } else {
        next()
      }
    } else {
      let {type, code} = getAuthType(to);
      if (type === 2 || type === 3 || type === 4) {
        wechatLoginWeekly({"code": code, "type": type}).then(res => {
          setMobileToken(res?.token?.access_token)
          store.dispatch('GetInfoMobile').then(() => {
            next({...to, replace: true})
          })
        }).catch(err => {
          Dialog.alert({
            title: '登录失败',
          })
        })
      } else { // 企业微信应用
        // 未登录
        if (!to.query.code) {
          redirectToOauth2Url();
        } else { // oauth2认证成功登录
          type = 1;
          code = to.query.code;
          wechatLoginWeekly({"code": code, "type": type}).then(res => {
            setMobileToken(res?.token?.access_token)
            store.dispatch('GetInfoMobileRant').then(() => {
              next({...to, replace: true})
            })
          }).catch(err => {
            Dialog.alert({
              title: '登录失败',
            })
          })
        }
      }
    }
  } else { // pc端流程*/
    NProgress.start()
    if (getToken()) {
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      /* has token*/
      if (to.path === '/login') {
        next({path: '/'})
        NProgress.done()
      } else {
        if (store.getters.roles.length === 0) {
          // 判断当前用户是否已拉取完user_info信息
          // 本地token没有清除时的，页面刷新，重新进入
          store.dispatch('casLogin').then(res => {
            store.dispatch('GetInfo').then(() => {
              store.dispatch('GenerateRoutes').then(accessRoutes => {
                // 根据roles权限生成可访问的路由表
                router.addRoutes(accessRoutes) // 动态添加可访问路由表
                next({...to}) // hack方法 确保addRoutes已完成
              })
            }).catch(err => {
              store.dispatch('LogOut').then((err) => {
                Message.error(err)
                next({path: '/'})
              })
            })
          }).catch(err => {
            sessionStorage.setItem('callbackUrl', window.location.href) // 保存当前页面地址
            if (directList.indexOf(to.path) !== -1 || to.path.indexOf('weekly/weeklyApprove') !== -1) {
              // 将查询参数转换为URL参数字符串
              const queryString = Object.keys(to.query).length
                ? '?' + Object.entries(to.query)
                .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                .join('&')
                : ''
              sessionStorage.setItem('targetRoute', to.path + queryString) // 保存目标路由
            }
          })
        } else {
          if (to.query.ticket) {
            store.dispatch("refreshCasLogin", {'ticket': to.query.ticket, 'path': to.path}).then(res => {
              store.dispatch('GetInfo').then(() => {
                store.dispatch('GenerateRoutes').then(accessRoutes => {
                  // 根据roles权限生成可访问的路由表
                  router.addRoutes(accessRoutes) // 动态添加可访问路由表
                  if (to.path.indexOf('/plan/dashboard') !== -1) {
                    next({path: '/plan/dashboard'})
                  } else if (to.path.indexOf('/rant/look') !== -1) {
                    next({path: '/rant/look'})
                  } else {
                    next({...to}) // hack方法 确保addRoutes已完成
                  }
                })
              })
            })
          } else {
            next()
          }
        }
      }
    } else {
      // 没有token
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next()
      } else if (directList.indexOf(to.path) !== -1 || to.path.indexOf('weekly/weeklyApprove') !== -1) { // 直接访问的页面
        store.dispatch('casLogin').then(res => {
          const roles = res.roles
          next({...to})
          console.log('to.path-----------', to.path)
          store.dispatch('GenerateRoutes', {roles}).then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            // next({...to, replace: true}) // hack方法 确保addRoutes已完成
            NProgress.done()
          })
        }).catch(err => {
          sessionStorage.setItem('callbackUrl', window.location.href) // 保存当前页面地址
          if (directList.indexOf(to.path) !== -1 || to.path.indexOf('weekly/weeklyApprove') !== -1) {
            // 将查询参数转换为URL参数字符串
            const queryString = Object.keys(to.query).length
              ? '?' + Object.entries(to.query)
              .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
              .join('&')
              : ''
            sessionStorage.setItem('targetRoute', to.path + queryString) // 保存目标路由
          }
        })
      } else {
        store.dispatch('casLogin').then(res => {
          // 拉取user_info
          const roles = res.roles
          store.dispatch('GenerateRoutes', {roles}).then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({...to}) // hack方法 确保addRoutes已完成
            NProgress.done()
          })
        }).catch(err => {
        })
      }
    }
  // }
})

router.afterEach(() => {
  NProgress.done()
})

