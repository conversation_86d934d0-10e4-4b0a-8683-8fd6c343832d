<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="工程项目数据" name="gcProjectData" v-hasPermi="['gcProject:data:list']">
        <gc-project-data />
      </el-tab-pane>
      <el-tab-pane label="客关项目数据" name="kgProjectData" v-hasPermi="['kgProject:data:list']">
        <kg-project-data />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import GcProjectData from './gcProjectData.vue';
import KgProjectData from '../kgProjectData/kgProjectData.vue';

export default {
  name: "GcProject",
  components: {
    GcProjectData,
    KgProjectData
  },
  data() {
    return {
      activeTab: 'gcProjectData'
    };
  }
};
</script>
