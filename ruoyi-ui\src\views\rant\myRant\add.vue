<template>
  <div class="app-container" v-loading="loading">
    <section class="content-box">
      <div class="step-box">
        <rant-step :stepActive="stepActive"/>
      </div>
    <!-- 添加或修改督办事项对话框 -->
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" class="form-box">
      <el-row>
        <el-col :span="8">
          <el-form-item label="来源" prop="ranterName">
            <el-input v-model="form.ranterName" placeholder="请选择来源" clearable readonly="readonly">
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="分类" prop="rantClassify">
            <el-select v-model="form.rantClassify" placeholder="请选择吐槽分类" @keyup.enter.native="handleQuery"
                       class="w-100"
            >
              <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                         :value="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划完成时间" prop="planTime">
            <el-date-picker clearable size="small" v-model="form.planTime" type="date" class="w-100"
                            value-format="yyyy-MM-dd" placeholder="选择计划完成时间" disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="责任人" prop="responsiblePersonName">
            <el-input v-model="form.responsiblePersonName" placeholder="请选择责任人" disabled clearable @focus="selectUserFun('responsibilityer')"
            >
              <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('responsibilityer')"
                 style="cursor: pointer;"
              ></i></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任部门" prop="deptId">
            <treeselect v-model="form.deptId" :options="deptOptions" :normalizer="normalizer"
                        @input="selectDept" placeholder="选择责任部门" disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门负责人" prop="respDeptResponsiblerName">
            <el-input class="full-width-input" suffix-icon="el-icon-search" disabled
                      v-model="form.respDeptResponsiblerName" placeholder="请选择责任部门负责人" clearable
                      @focus="selectUserFun('respDeptResponsiblerEdit')"
            />
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="内容" prop="rantContent">
            <el-input v-model="form.rantContent" type="textarea" placeholder="请输入内容" :autosize="{ minRows: 4}"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="措施" prop="solution">
            <el-input v-model="form.solution" type="textarea"  :autosize="{ minRows: 4}" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-if="type === 'reject'">
          <el-form-item label="驳回原因" prop="approveDesc">
            <el-input v-model="form.approveDesc" type="textarea" placeholder="请输入" :autosize="{ minRows: 4}" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    </section>

    <div class="dialog-footer">
      <el-button v-if="isHavaSave" class="save-btn" type="primary" @click="submitForm(1)">
        提 交
      </el-button>
      <el-button v-if="isHavaSave" class="submit-btn" type="primary" @click="submitForm(0)">保 存
      </el-button>
      <el-button @click="cancel" class="cancel-btn">取 消</el-button>
    </div>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData"
    />
  </div>
</template>

<script>
import {
  getMatters,
  myRantAdd,
  myRantEdit
} from '@/api/rant/matters'
import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import { getInfo } from '@/api/login'
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listRespdet, getRespdet } from '@/api/rant/respdet'
import { listDept, listDeptExcludeChild } from '@/api/system/dept'
import RantStep from '@/views/rant/components/RantStep/index.vue'

export default {
  name: 'addRant',
  components: { SelectUser, StatusTag, Treeselect, RantStep },
  dicts: ['rant_classify'],
  data() {
    return {
      stepActive: 1, // 当前步骤
      // 遮罩层
      loading: false,
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: true,
      isHavaSave: true,
      isMultiple: false,
      // 责任部门列表
      deptOptions: [],
      // 表单参数
      form: {
        "id": null,
        "mattersType": null, // 类型
        "ranter": null, // 来源:人(多个用“,”隔开)
        "ranterName": null, // 来源:人(多个用“,”隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "status": 0 // 状态（0-草稿 1-提交）
      },
      // 表单校验
      rules: {
        ranter: [
          { required: true, message: '吐槽人不能为空', trigger: 'blur' }
        ],
        rantClassify: [
          { required: true, message: '吐槽分类不能为空', trigger: ['blur', 'change'] }
        ],
        /*  planTime: [
           { required: true, message: '计划完成时间不能为空', trigger: 'blur' }
         ], */
        responsiblePerson: [
          { required: true, message: '责任人不能为空', trigger: 'blur' }
        ],
        rantContent: [
          { required: true, message: '吐槽内容不能为空', trigger: 'blur' }
        ]
        /* solution: [
          { required: true, message: '解决方案不能为空', trigger: 'blur' }
        ] */
      },
      type: '', // update: 编辑， reject: 驳回后的编辑，需要展示驳回原因
      rantId: ''
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    }
  },
  created() {
    this.type = this.$route.query.type
    this.rantId = this.$route.query.id
    if (this.type === 'update' || this.type === 'reject') {
      console.log('this.rantId---', this.rantId, this.type)
      this.handleUpdate(this.rantId)
    }
    else {
      getInfo().then((res) => {
        this.form.ranter = res.user.userId
        this.form.ranterName = res.user.nickName
        listRespdet().then(response => {
          this.deptOptions = this.handleTree(response.data, 'id')
        })
      })
    }
  },
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      this.closeCurrentPage()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: '2',
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      }
      this.resetForm('form')
    },
    /** 编辑按钮操作 */
    handleUpdate(id) {
      this.reset()
      this.selectUserShow = true
      getMatters(id).then((response) => {
        this.form = response.data

        this.responsiblePersonName = response.data.responsiblePersonName
        this.responsiblePerson = response.data.responsiblePerson

      })
      listRespdet().then(response => {
        console.log('response---', response)
        this.deptOptions = this.handleTree(response.data, 'id')
        console.log('this.deptOptions---', this.deptOptions)
      })
    },
    closeCurrentPage() {
      this.$tab.closeOpenPage({ path: '/rant/myRant' })
    },
    /** 提交按钮 */
    submitForm(submitStatus) {
      this.loading = true
      console.log('form.id----------', this.form.id)
      this.$refs['form'].validate((valid) => {
        this.form.responsiblePerson = this.responsiblePerson
        this.form.responsiblePersonName = this.responsiblePersonName
        this.form.status = submitStatus
        this.form.mattersType = '2'
        if (valid) {
          if (this.form.id != null) {
            myRantEdit(this.form).then((response) => {
              this.loading = false
              this.$modal.msgSuccess('修改成功')
              this.open = false
              // this.getList()
              this.closeCurrentPage()
            })
              .finally(() => {
                this.loading = false
              })
          } else {
            myRantAdd(this.form).then((response) => {
              this.loading = false
              this.$modal.msgSuccess('新增成功')
              this.open = false
              // this.getList()
              this.closeCurrentPage()
            })
              .finally(() => {
                this.loading = false
              })
          }
        } else {
          this.loading = false
        }
      })
    },
    selectUserFun(type) {
      this.selectUserType = type
      this.$refs.userRef.show()
    },
    selectUserData(data) {
      if (this.selectUserType == 'ranter') {
        this.ranter = data.userId
        this.ranterName = data.nickName
        this.form.ranter = data.userId
        this.form.ranterName = data.nickName
      } else if (this.selectUserType == 'responsibilityer') {
        this.responsiblePerson = data.userId
        this.responsiblePersonName = data.nickName
        this.form.responsiblePerson = this.responsiblePerson
        this.form.responsiblePersonName = this.responsiblePersonName
        console.log(this.form.responsiblePersonName)
      } else if (this.selectUserType == 'responsibilityerQuery') {
        this.queryParams.responsiblePerson = data.userId
        this.responsiblePersonNameQuery = data.nickName

      }
    },
    changeresponsibilityer() {
      if (this.responsiblePersonNameQuery == '' || this.responsiblePersonNameQuery == null) {
        this.queryParams.responsiblePerson = null
      }

    },
    selectDept(value) {
      if (value) {
        this.form.deptId = value
        getRespdet(this.form.deptId).then(response => {
          this.form.deptName = response.data.name;
          this.form.respDeptResponsibler = response.data.respPeople;
          this.form.respDeptResponsiblerName = response.data.respPeopleName;
        });
      } else {
        this.form.deptId = null
        this.form.deptName = null
        this.form.respDeptResponsibler = null
        this.form.respDeptResponsiblerName = null
      }
      // console.log('value---', value, this.deptOptions)
      // this.form.deptId = value
      // // this.form.deptName = this.deptOptions.find(item => item.id === value)?.name
      //  // 使用递归函数在树形结构中查找部门
      // const findDeptInTree = (items, targetId) => {
      //   if (!items || !items.length) return null;
      //
      //   for (const item of items) {
      //     if (item.id === targetId) {
      //       return item;
      //     }
      //     if (item.children && item.children.length) {
      //       const found = findDeptInTree(item.children, targetId);
      //       if (found) return found;
      //     }
      //   }
      //   return null;
      // };
      //
      // // 在树中查找部门
      // const dept = findDeptInTree(this.deptOptions, value);
      // if (dept && dept.name) {
      //   this.form.deptName = dept.name;
      // }
      // if(!value){
      //   this.form.deptName = null
      //   return;
      // }
      // getRespdet(this.form.deptId).then(response => {
      //   this.form.respDeptResponsibler = response.data.respPeople;
      //   this.form.respDeptResponsiblerName = response.data.respPeopleName;
      //
      // });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.vue-treeselect--disabled .vue-treeselect__control){
  background-color: #F5F7FA;
}
:deep(.el-input.is-disabled .el-input__inner){
  color: #000;
}
:deep(.el-textarea.is-disabled .el-textarea__inner){
  color: #000;
}
.app-container {
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  border-radius: 0px 0px 0px 0px;
  height: calc(100vh - 84px);
  // max-height: calc(100vh - 84px);
 /*  .content-box {
    height: calc(100vh - 150px);
    overflow-y: auto;
  } */
  .step-box {
    margin-bottom: 12px;
    padding: 16px;
    width: 100%;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;

    :deep(.el-step__icon) {
      background-color: unset;
    }

    :deep(.el-step__title) {
      color: #666666;
    }


  }

  .form-box {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;
    padding: 32px;
  }

  .dialog-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 12px 304px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 0px 0px;
    display: flex;
    justify-content: center;
    z-index: 1000;
    gap: 40px;

    .submit-btn {
      width: 100px;
    }

    .save-btn {
      width: 100px;
    }

    .cancel-btn {
      width: 100px;
    }
  }
}
</style>
