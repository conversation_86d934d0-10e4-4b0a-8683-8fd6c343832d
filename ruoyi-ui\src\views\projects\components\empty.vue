<template>
  <div class="no-authority-status">
    <slot v-if="noAuthority"></slot>
    <section class="no-authority-block" v-else :style="{ minHeight: minHeight }">
      <!-- <img src="@/views/projects/assets/images/no-data.png" alt="无数据" /> -->
      <div class="authority-desc">暂无权限</div>
    </section>
  </div>
</template>
<script>
export default {
  name: "Empty",
  props: {
    title: {
      type: String,
      default: "暂无权限",
    },
    noAuthority: { // 权限标识
      type: Boolean,
      default: false,
    },
    minHeight: { // 最小高度
      type: String,
      default: "200px",
    },
    img: { // 图片
      type: String,
      default: "",
    },
  },
};
</script>
<style scoped lang="scss">
.no-authority-status {
  width: 100%;
  height: 100%;
  .no-authority-block {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.authority-desc {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 0.875rem;
  color: #333333;
  line-height: 1.625rem;
  text-align: center;
  font-style: normal;
  text-transform: none;

  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
