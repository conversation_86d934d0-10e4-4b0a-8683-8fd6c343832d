<template>
  <div class="app-container" :style="{ '--color': theme }">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      <!--    不清楚为什么，如果只有一个input，点击enter会触发页面刷新，丢失了路由的参数，引起页面报错    -->
        <el-input
          style="position: absolute; right: -100000px;"
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile(1)"
          :disabled="planUseTemplate"
        >覆盖导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile(2)"
          >增量导入
        </el-button>
      </el-col>
      <el-col :span="12">
          <div class="custom--title icon-primary"><span>当前计划：</span>{{ $route.query.planName }}</div>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <div v-loading="loading">
      <el-table
        :data="nodeList"
        @selection-change="handleSelectionChange"
        :row-class-name="rowClassName"
        max-height="500"
      >
        <el-table-column
          type="selection"
          width="45"
          align="center"
          :selectable="canSelect"
        />
        <el-table-column label="序号" align="center" prop="id" width="50">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <status-tag
              :status="scope.row.status"
              :options="nodeStatusOption"
            />
          </template>
        </el-table-column>
        <el-table-column label="节点名称" :render-header="renderHeader" align="center" prop="nodeName"  width="300" />
        <el-table-column
          label="计划开始时间"
          align="center"
          prop="startTime"
          width="150"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <div class="time">
              <el-date-picker
                clearable
                size="small"
                v-model="scope.row.startTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="计划开始时间"
                @change="changeData($event,scope.$index,1)"
              >
              </el-date-picker>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="计划完成时间"
          align="center"
          prop="endTime"
          width="150"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <div class="time">
              <el-date-picker
                clearable
                size="small"
                v-model="scope.row.endTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="计划完成时间"
                @change="changeData($event,scope.$index,2)"
              >
              </el-date-picker>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="责任部门" :render-header="renderHeader" align="center" prop="departmentArr" width="120">
          <template slot-scope="scope">
            <el-select v-model="scope.row.departmentArr" multiple placeholder="请选择" size="small"  @change="selectChange($event,scope.$index)">
              <el-option
                v-for="item in departmentsData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="反馈人"
          align="center"
          prop="feedbackUserName"
          show-overflow-tooltip
          width="100"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <div
              v-if="scope.row.feedbackUserName"
              class="pointer"
              @click="clickFeedback(scope.$index)"
            >
             <el-link type="primary">
               {{ scope.row.feedbackUserName }}
             </el-link>
            </div>
            <el-button v-else type="text" @click="clickFeedback(scope.$index)"
              >选择
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="是否可用" :render-header="renderHeader" align="center" prop="isValid" size="small" width="120">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.isValid"
              clearable
              :disabled="scope.row.isGrey == 0 && scope.row.isValid == 1"
            >
              <el-option
                v-for="option in isEditOption"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          class-name="hidden-column"
          label-class-name="hidden-column"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, scope.$index)"
              >修改
            </el-button>
            <el-button
              v-if="scope.row.isDel != 0"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row, scope.$index)"
              >删除
            </el-button>
            <i
              v-if="scope.$index !== 0"
              class="el-icon-top iconStyle"
              @click="moveUp(scope.$index)"
            >
            </i>
            <i
              class="el-icon-bottom iconStyle"
              style="margin-left: 5px"
              @click="moveDown(scope.$index)"
              v-if="scope.$index < nodeList.length - 1"
            ></i>
          </template>
        </el-table-column>
      </el-table>

<!--      <pagination-->
<!--        v-show="total > 0"-->
<!--        :total="total"-->
<!--        :page.sync="queryParams.pageNum"-->
<!--        :limit.sync="queryParams.pageSize"-->
<!--        @pagination="getList"-->
<!--      />-->

      <div style="margin-top:10px">
        <el-button
          v-if="planStatus != 5"
          type=""
          @click="saveFun"
          style="margin-right: 10px"
          >保存
        </el-button>
        <el-button slot="reference" type="primary" @click="submitApprovalHandle">提交</el-button>
<!--        <el-popconfirm title="是否提交？" @confirm="submitApproval">
          <el-button slot="reference" type="primary">提交</el-button>
        </el-popconfirm>-->
        <el-button
          type="warning"
          plain
          @click="handleClose"
          style="margin-left: 10px"
          >关闭
        </el-button>
      </div>
    </div>
    <!-- 添加或修改计划-节点对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="节点名称" prop="nodeName">
              <el-input
                v-model="form.nodeName"
                placeholder="请输入节点名称"
                style="width: 220px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="节点描述" prop="nodeDesc">
              <el-input
                v-model="form.nodeDesc"
                style="width: 220px"
                placeholder="请输入节点描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="责任部门" prop="departmentArr">
              <el-select v-model="form.departmentArr" multiple placeholder="请选择" size="small" @change="selectDepartFun" >
                <el-option
                  v-for="item in departmentsData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="反馈人" prop="feedbackUserName">
              <div style="width: 220px" class="flex-row">
                <div class="feedback-people-container" v-if="!!form.feedbackUserName ">
                  <div style="margin-right: 15px">
                    {{ form.feedbackUserName }}
                  </div>
                  <i class="el-icon-circle-close" @click="clearFeedbackUserInfo"></i>
                </div>

                <el-button type="primary" @click="selectFeedbackFun" size="mini"
                  >选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划开始时间" prop="startTime">
              <div style="width: 220px">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="form.startTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择计划开始时间"
                  @change="changeData($event,null,1)"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计划完成时间" prop="endTime">
              <div style="width: 220px">
                <el-date-picker
                  clearable
                  size="small"
                  v-model="form.endTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择计划完成时间"
                  @change="changeData($event,null,2)"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="isValid" label="是否可用" width="120">
              <el-select
                v-model="form.isValid"
                clearable
                :disabled="form.isGrey === 0 && form.isValid == 1"
              >
                <el-option
                  v-for="option in isEditOption"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准工期" prop="durationNum">
              <el-input
                type="number"
                v-model="form.durationNum"
                placeholder="请输入标准工期"
                style="width: 220px"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="完成标准" prop="completeCriteria">
              <el-input
                v-model="form.completeCriteria"
                placeholder="完成标准"
                type="textarea"
                autosize
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="专业成果设置" prop="resultConfigId">
              <div class="flex-row">
                <div>{{ form.resultConfigName }}</div>
                <el-button
                  size="mini"
                  type="primary"
                  @click="settingFun"
                  style="margin-left: 10px"
                  >设置
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <select-user
      ref="userRef"
      :roleId="roleName"
      @feedbackEmit="selectFeedbackData"
    />
    <result-config
      ref="resultRef"
      :defaultResult="defaultResult"
      @settingEmit="settingEmit"
    ></result-config>
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script>
import { listNode, save, submit, importNodeFile } from '@/api/plan/node'
import { getInfo } from '@/api/plan/info'
import { getRespPeople } from '@/api/plan/dept'
import SelectTree from '@/components/SelectTree.vue'
import { isEditOption } from '@/views/plan/constant'
import { nodeStatusOption } from '@/constant'
import variables from '@/assets/styles/element-variables.scss'
import SelectUser from '../components/SelectUser/index.vue'
import ResultConfig from '../components/ResultConfig/index.vue'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'

export default {
  name: 'nodeInfo',
  components: { StatusTag, SelectTree, SelectUser, ResultConfig },
  props: ['planId'],
  dicts: [],
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      nodeNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        nodeLevel: null,
        nodeName: null,
        planId: null
      },
      // 表单参数
      form: {},
      // 表单校验`
      rules: {
        nodeName: [
          { required: true, message: '节点名称不能为空', trigger: 'blur' }
        ],
        durationNum: [
          { required: false, message: '标准工期不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '计划开始时间不能为空', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '计划完成时间不能为空', trigger: 'change' }
        ],
        isValid: [
          { required: true, message: '是否可用不能为空', trigger: 'change' }
        ],
        departmentArr: [
          { required: true, message: '责任部门不能为空', trigger: 'change' }
        ],
        feedbackUserName: [
          { required: true, message: '反馈人不能为空', trigger: 'change' }
        ]
      },
      editIndex: null,
      clickIndex: null,
      //责任人
      theme: variables.theme,
      showDepart: false,
      planStatus: 0,
      departmentsData:[],
      paramData:null,
      planUseTemplate: false,
    }
  },
  created () {
    this.queryParams.planId = this.$route.query.planId
    this.planStatus = this.$route.query.planStatus
    this.getList()
    this.getDepartFun()
    this.nodeInfo()
  },
  computed: {
    nodeStatusOption () {
      return nodeStatusOption
    },
    isEditOption: () => isEditOption,

    roleName: function () {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        return this.form && this.form.feedbackUser
      } else { // 处理表单里面数据
        let itemData=this.nodeList[this.clickIndex]
        if (typeof (this.clickIndex) == 'number' &&itemData&& itemData.feedbackUser) {
          return itemData.feedbackUser
        }
      }
    },
    defaultResult: function () {
      let listConfig = this.form.resultConfigId ? this.form.resultConfigId.split(',') : []
      let listConfigName = this.form.resultConfigName ? this.form.resultConfigName.split(',') : []
      return listConfig.map((item, index) => {
        return {
          id: item,
          name: listConfigName[index]
        }
      })
    }
  },
  watch: {},
  methods: {
    changeData(val,index,type){
      let item=null
      if(typeof(index)=='number'){
         this.clickIndex=index
         item=this.nodeList[this.clickIndex]
      }else{
        item=this.form
      }
      if(!item.startTime||!item.endTime)return null;
      let date1 = new Date(item.startTime);
      let date2 = new Date(item.endTime);
      if (type==1&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('计划开始时间不能大于计划完成时间')
        item.startTime=null
      } else if (type==2&&date1.valueOf() > date2.valueOf()) {
        this.$message.warning('计划完成时间不能小于计划开始时间')
        item.endTime=null
      }
    },
    submitHandle(){
      console.log('submitHandle')
    },
    ///节点信息
    nodeInfo(){
      getInfo(this.queryParams.planId).then(response => {
        let data=response.data
        if(data){
          this.paramData = {
            cityCompanyNum: data.deptNum,//城市公司
            projectId: data.projectId,//项目
            stageId: data.stageId, //分期
            respDept: "" //部门
          }
          //  判断计划是否引用模板
          // this.planUseTemplate = !!data.templateId
        }
      })
    },
    //获取默认反馈人
    getDefautlFeedback(type){
      getRespPeople({...this.paramData}).then(response => {
        let data=response.data
        if(type){ //更新列表的反馈人
          this.nodeList[this.clickIndex].feedbackUser=data.respPeople
          this.nodeList[this.clickIndex].feedbackUserName = data.respPeopleName
          this.nodeList=[...this.nodeList]
        }else{
        this.form.feedbackUser = data.respPeople //特殊情况传的ID 正常传的账户
        this.form.feedbackUserName = data.respPeopleName
        }
      })
    },
    getDepartFun(){
    this.getDicts('resp_department').then(res => {
      let data = res.data
      this.departmentsData = data.map(item => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          }
        }
      )
    }).catch(err => {
      console.log(err)
    })},

    renderHeader(h, { column, $index }) {
      if (column.property === 'startTime' || column.property === 'endTime'
        || column.property === 'nodeName' || column.property === 'departmentArr'
        || column.property === 'feedbackUserName' || column.property === 'isValid') {
        return h('span', [
          h('span', {}, column.label),
          h('i', { class: 'el-icon-star-on required-icon' }) // 这里可以根据实际情况调整图标类名
        ]);
      } else {
        return h('span', {}, column.label);
      }
    },
    rowClassName (scope) {
      if (scope.row.adjustType === 3) {
        return 'showRow'
      }
      return
    },
    canSelect (row) {
      return row.isDel == 1 || !row.id
    },
    clearFeedbackUserInfo(){
      this.$refs.userRef.clearSelect()
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.form.feedbackUser = ''
        this.form.feedbackUserName = ''
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].feedbackUser =''
          this.nodeList[this.clickIndex].feedbackUserName = ''

        }
      }
    },
    selectFeedbackData (data) {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.form.feedbackUser = data.userName
        this.form.feedbackUserName = data.nickName
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].feedbackUser = data.userName
          this.nodeList[this.clickIndex].feedbackUserName = data.nickName

        }
      }
    },
    selectDepartData (data) {
      if (typeof (this.editIndex) == 'number' || this.editIndex === true) {
        this.form.departmentNames = data.map(item => item.departName)
        this.form.department = data.map(item => item.department)
      } else { // 处理table里面数据
        if (typeof (this.clickIndex) == 'number') {
          this.nodeList[this.clickIndex].department = data.map(item => item.department).join(',')
          this.nodeList[this.clickIndex].departmentNames = data.map(item => item.departName).join(',')
        }
      }
    },
    selectDepartFun (val){
      this.paramData.respDept=val[0]||'';
      if(val.length==0||val.length==1||this.singlevalue1!=val[0]){ //没选 或者多选变更了第一个选择
        this.getDefautlFeedback()
      }
      this.singlevalue1=val[0]
    },
    // 导入节点
    importFile (type) {
      this.importType=type
      this.$refs.fileInput.click() // 触发文件输入的点击事件
    },
    handleFileChange (event) {
      const files = event.target.files
      const formData = new FormData()
      formData.append('file', files[0])
      formData.append('type', this.importType)
      formData.append('planId', this.queryParams.planId)
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null
      this.loading=true
      importNodeFile(formData).then(response => {
        this.loading=false
        let da = response.data
        da.map(item=>{
          // item.status=0
          item.departmentArr=item.department.length?item.department.split(','):[]
        })
        if(this.importType==1){
          this.nodeList = []
          this.nodeList.push(...da)
        }else{
          this.nodeList=[...this.nodeList,...da]
        }

      }).catch(err=>{
        this.loading=false
      })
    },
    submitApprovalHandle(){
      if (this.validFun(1)) {
        return
      }
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitApproval()
      }).catch(() => {
      });
    },
    //提交审批
    submitApproval () {

      for(let i = 0; i < this.nodeList.length; i++){
        this.nodeList[i].nodeIndex = i + 1;
      }
      this.loading = true
      submit({
        id: this.queryParams.planId,
        planNodeList: this.nodeList
      }).then(res => {
        this.loading = false
        this.$message.success('提交成功')
        this.$bus.$emit('refreshInfoList')
        window.open(res.data)
        this.handleClose()
      }).catch(err => {
        // this.$emit('cancel')
        this.loading = false
      })
    },
    //关闭当前页面
    handleClose () {
      const obj = { path: '/plan/plans/info' }
      this.$tab.closeOpenPage(obj)
    },
    moveUp (index) {
      if (index > 0) {
        const row = this.nodeList.splice(index, 1)[0]
        this.nodeList.splice(index - 1, 0, row)
      }
    },
    moveDown (index) {
      if (index < this.nodeList.length - 1) {
        const row = this.nodeList.splice(index, 1)[0]
        this.nodeList.splice(index + 1, 0, row)
      }
    },
    settingFun () {
      this.$refs.resultRef.show()
    },

    settingEmit (data) {
      this.form.resultConfigId = data.map(item => item.id).join(',')
      this.form.resultConfigName = data.map(item => item.name).join(',')
    },
    clickFeedback (index) {
      this.clickIndex = index
      setTimeout(() => {
        this.selectFeedbackFun()
      }, 800)
    },
    selectFeedbackFun () {
      this.$refs.userRef.show()
    },
    validFun (type) {
      let nodes = this.nodeList
      let isReturn = false
      if (!nodes.length) {
        this.$message('没有提交数据！')
        return true
      }
      for (let i = 0; i < nodes.length; i++) {
        let index = i + 1
        if (!nodes[i].startTime) {
          isReturn = true
          this.$message('序号为' + index + '计划开始时间为空')
          break
        }
        if (!nodes[i].endTime) {
          isReturn = true
          this.$message('序号为' + index + '计划完成时间为空')
          break
        }
        if (!nodes[i].department) {
          isReturn = true
          this.$message('序号为' + index + '责任部门为空')
          break
        }
        if (!nodes[i].feedbackUser) {
          isReturn = true
          this.$message('序号为' + index + '反馈人为空')
          break
        }
      }
      return isReturn
    },
    saveFun () {
      //保存至本地
      /*if (this.validFun()) {
        return
      }*/
      this.loading = true
      console.log('nodeList', this.nodeList)
      for(let i = 0; i < this.nodeList.length; i++){
        this.nodeList[i].nodeIndex = i + 1;
      }
      save({
        id: this.queryParams.planId,
        planNodeList: this.nodeList
      }).then(res => {
        this.loading = false
        this.getList()
        //刷新计划编制列表
        this.$bus.$emit('refreshInfoList')
      }).catch(err => {
        console.log(err)
        this.loading = false
      })
    },
    getList () {
      this.loading = true
      listNode(this.queryParams).then(response => {
        let da = response.rows
        da.map(item=>{
          item.departmentArr=item.department.length?item.department.split(','):[]
        })
        this.nodeList=[...da]
        console.log(this.nodeList)
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.editIndex = null
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        nodeStage: null,//节点状态
        nodeCode: null, //节点顺序
        nodeLevel: null, //节点等级
        nodeName: null, //节点名字
        startTime: null, //开始时间
        endTime: null, //结束时间
        department: '', //部门ID
        responsibilityPeople: null, //责任人ID
        respPeopleName: null,//责任人名字
        durationNum: null,// 标准工期
        nodeDesc: null,//节点描述
        isValid: 1,
        isGrey: null,//控制是否可用
        resultConfigId: '',
        departmentNames: null, //部门ID
        feedbackUser: '',
        feedbackUserName: '',
        resultConfigName: '',
        departmentNameArr:[],// 名字数组
        departmentArr:[], // id数组
        status: null // 默认给未到期
      }
      this.resetForm('form')
      this.singlevalue1=''
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      console.log(selection)
      this.ids = selection.map(item => item.id)
      this.nodeNames = selection.map(item => item.nodeName)
      this.selections = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.getDepartFun()
      this.reset()
      this.open = true
      this.title = '添加计划-节点'
      this.form.id = null
      this.editIndex = true
    },
    /** 修改按钮操作 */
    handleUpdate (row, index) {
      this.getDepartFun()
      this.editIndex = index
      this.reset()
      this.form = JSON.parse(JSON.stringify(row))      // const id = row.id || this.ids
      this.open = true
      this.title = '修改计划-节点'
      this.handleFormData()
    },
    selectChange(val,index){
      this.clickIndex=index;
      let node=this.nodeList[this.clickIndex]
      if(val.length==0||val.length==1||(node.department.indexOf(val[0])>0)){ //没选 或者多选变更了第一个选择
        this.paramData.respDept=val[0]||'';
        this.getDefautlFeedback(1)
      }
      if (typeof (this.clickIndex) == 'number') {
        node.departmentNameArr=this.departmentsData.reduce((pre,curr,index)=>{
          if(val.includes(curr.value)){
            pre.push(curr.label)
          }
          return pre;
        },[])
        node.department = val.join(',')
        node.departmentNames = node.departmentNameArr.join(',')
      }
      this.singlevalue=val[0]
    },
    handleFormData(){
      this.form.departmentNameArr=this.departmentsData.reduce((pre,curr,index)=>{
        if(this.form.departmentArr.includes(curr.value)){
          pre.push(curr.label)
        }
        return pre;
      },[])
      this.form.department=this.form.departmentArr.join(',')
      this.form.departmentNames=this.form.departmentNameArr.join(',')
    },
    /** 提交按钮 */
    submitForm () {

      this.$refs['form'].validate(valid => {
        if (valid) {
          // 数据处理
          this.handleFormData()
          let data = JSON.parse(JSON.stringify(this.form))
          if (data.id != null || this.title.indexOf('修改') != -1) {
            this.nodeList.splice(this.editIndex, 1, data)
          } else {
            //新增后又编辑
            if (typeof (this.editIndex) == 'number') {
              this.nodeList.splice(this.editIndex, 1, data)
            } else {
              //新增
              this.nodeList.push(data)
            }
          }
          this.editIndex = null
          this.open = false
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      let that = this
      const ids = row.hasOwnProperty('id') ? row.id : this.ids
      const nodeNames = row.nodeName || this.nodeNames
      let message = '是否确认删除计划节点名称为"' + nodeNames + '"的数据项？'
      this.$modal.confirm(message).then(function () {
        that.nodeList = that.nodeList.filter(item => {
          if (Array.isArray(ids)) {
            return item.id?!ids.includes(item.id):item.id !== null
          } else {
            return item.id?item.id != ids: item.id !== null
          }
        })
        for (let i = 0; i < that.nodeList.length; i++) {
          const item = that.nodeList[0]
          if (Array.isArray(ids)) {
            if (ids.includes(item.id)) {
              item.adjustType = 3
            } else {
              item.adjustType = 0
            }
          } else {
            if (item.id === ids) {
              item.adjustType = 3
            } else {
              item.adjustType = 0
            }
          }
        }
        return that.nodeList
      }).catch((err) => {
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('plan/node/export', {
        ...this.queryParams
      }, `计划节点信息_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
<style>
.hidden-column {
  border: none;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";

.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 140px;
}

.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

::v-deep .showRow {
  display: none;
}
.custom--title{
  padding: 3px;
  font-weight: 700;
  color: #409eff;
  //background-color: #f8f8f9;
}

</style>
