
c5df297130fbefacc22e1305552e5ff151f003ab	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e704c478f8925de18274640b750497a2\"}","integrity":"sha512-Q8J3EGao4+F+mhFkv10tsXh0y1jrMCkaxQspDgnBRhyEbSsxtHu8fsTTjT6x5//ffjjcGjAMqcbxHTwixVtjMg==","time":1754311726307,"size":12043250}