<template>
  <div class="image-annotator-container">
    <div
      class="image-container"
      ref="imageContainer"
      @mousedown="onContainerMouseDown"
      @mousemove="onContainerMouseMove"
      @mouseup="onContainerMouseUp"
    >
      <img :src="imgSrc" @load="onImageLoad" class="annotator-image" draggable="false"/>

      <!-- 标注区域的div -->
      <div
        v-for="(ann, index) in localAnnotations"
        :key="ann.id"
        class="annotation-box"
        :style="getAnnotationStyle(ann)"
      >
        {{ index + 1 }}
      </div>

      <!-- 临时绘制的区域 -->
      <div
        v-if="drawing"
        class="annotation-box temp"
        :style="getTempRectStyle()"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    imgSrc: {
      type: String,
      required: true
    },
    annotations: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localAnnotations: [],
      isMouseDown: false,
      drawing: false,
      // 拖拽相关的变量移除
      // draggingAnn: null,
      // dragStartX: 0,
      // dragStartY: 0,
      // annStartXPercent: 0,
      // annStartYPercent: 0,
      startX: 0,
      startY: 0,
      tempRect: { x: 0, y: 0, width: 0, height: 0 },
      containerRect: { width: 0, height: 0, left: 0, top: 0 }
    }
  },
  watch: {
    annotations: {
      handler(newVal) {
        this.localAnnotations = JSON.parse(JSON.stringify(newVal))
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    onImageLoad() {
      this.$nextTick(() => {
        this.updateContainerRect()
      })
    },
    updateContainerRect() {
      const rect = this.$refs.imageContainer.getBoundingClientRect()
      this.containerRect = {
        width: rect.width,
        height: rect.height,
        left: rect.left + window.pageXOffset,
        top: rect.top + window.pageYOffset
      }
    },
    toPercentCoords(xPx, yPx) {
      const xPercent = (xPx / this.containerRect.width) * 100
      const yPercent = (yPx / this.containerRect.height) * 100
      return { xPercent, yPercent }
    },
    percentToPx(xPercent, yPercent) {
      const xPx = (xPercent / 100) * this.containerRect.width
      const yPx = (yPercent / 100) * this.containerRect.height
      return { xPx, yPx }
    },
    getAnnotationStyle(ann) {
      const { xPx, yPx } = this.percentToPx(ann.x, ann.y)
      const widthPx = (ann.width / 100) * this.containerRect.width
      const heightPx = (ann.height / 100) * this.containerRect.height
      return {
        left: xPx + 'px',
        top: yPx + 'px',
        width: widthPx + 'px',
        height: heightPx + 'px'
      }
    },
    getTempRectStyle() {
      return {
        left: this.tempRect.x + 'px',
        top: this.tempRect.y + 'px',
        width: this.tempRect.width + 'px',
        height: this.tempRect.height + 'px'
      }
    },
    onContainerMouseDown(e) {
      this.isMouseDown = true
      this.drawing = true
      const x = e.pageX - this.containerRect.left
      const y = e.pageY - this.containerRect.top
      this.startX = x
      this.startY = y
      this.tempRect = { x, y, width: 0, height: 0 }
    },
    onContainerMouseMove(e) {
      if (!this.isMouseDown || !this.drawing) return
      const currentX = e.pageX - this.containerRect.left
      const currentY = e.pageY - this.containerRect.top
      const width = Math.abs(currentX - this.startX)
      const height = Math.abs(currentY - this.startY)
      const x = Math.min(this.startX, currentX)
      const y = Math.min(this.startY, currentY)
      this.tempRect = { x, y, width, height }
    },
    onContainerMouseUp() {
      if (this.drawing) {
        this.drawing = false
        this.isMouseDown = false
        if (this.tempRect.width > 5 && this.tempRect.height > 5) {
          // 转换临时矩形为百分比
          const { xPercent, yPercent } = this.toPercentCoords(this.tempRect.x, this.tempRect.y)
          const wPercent = (this.tempRect.width / this.containerRect.width) * 100
          const hPercent = (this.tempRect.height / this.containerRect.height) * 100
          const newAnn = {
            id: Date.now(),
            x: xPercent,
            y: yPercent,
            width: wPercent,
            height: hPercent
          }
          this.localAnnotations.push(newAnn)
          this.$emit('annotation-change', this.localAnnotations)
        }
      }
    }
  }
}
</script>

<style scoped>
.image-annotator-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.image-container {
  position: relative;
  display: inline-block;
  border: 1px solid #ccc;
  user-select: none;
}

.annotator-image {
  display: block;
  width: 100%;
  height: auto;
}

.annotation-box {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid red;
  background: rgba(255,0,0,0.5);
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  line-height: 1.2;
  /* cursor: move; 移除拖拽光标，不再需要 */
}

.annotation-box.temp {
  border-style: dashed;
  pointer-events: none;
}
</style>
