<template>
  <div class="app-container"></div>
</template>

<script>
  export default {
    created() {
      var newWindow = window.open('http://iam.cscec1b.net/cas/login?service=https%3A%2F%2Foa.cscec1b.net%2Fseeyon%2Fcaslogin%2Fsso', '_blank')
      oaLogin();
      setTimeout(function() {
        newWindow.location.replace('https://oa.cscec1b.net/seeyon/collaboration/collaboration.do?method=listWaitSend')
      }, 2000)
      const obj = { path: "/index" };
      this.$tab.closeOpenPage(obj)
    },
  }
</script>
<style scoped lang="scss">
  .mrb-15 {
    margin-bottom: 15px;
  }

  .tip {
    margin-top: 15px;
  }
</style>
