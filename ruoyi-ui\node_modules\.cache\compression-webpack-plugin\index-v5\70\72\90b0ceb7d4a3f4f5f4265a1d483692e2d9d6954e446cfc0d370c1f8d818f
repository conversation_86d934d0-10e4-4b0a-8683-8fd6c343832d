
8954b882adf3b518bceb85bf9afc9e14121da7bd	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.ddce343b90bbba3f19c0.hot-update.js\",\"contentHash\":\"b474058128cb0c490f41fe3d80da532b\"}","integrity":"sha512-nPK/EDyPOFRgWmebxmWcTVhxfr5Ao3jUX7EsDxhl9ujBUKWpAmldym8PxJ5JNLYA3Ef0ZA+svX+Xu3H75bKo8w==","time":1754312412936,"size":77995}