class LineTrendOption {
    constructor(xAxisData = [], seriesData = []) {
      const screenWidth = window.innerWidth; // 获取当前屏幕宽度
      const designWidth = 1680; // 设计尺寸宽度
      const scale = screenWidth / designWidth; // 计算比例

      this.option = {
        tooltip: {
          trigger: 'axis',
        },
        grid:{
            top: `20%`,  // 按比例调整
            left: `15%`,
            right: `10%`,
            bottom: `25%`,
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLine: {
              lineStyle: {
                color: '#999',
                width: 1 * scale
              },
            },
            axisLabel: {
              interval: 'auto',
              rotate: 45,
              color: '#8C8C8C',
              margin: 15 * scale,  // 按比例调整
              fontSize: 12 * scale // 按比例调整字体大小
            },
            axisTick: {
              show: false,
              alignWithLabel: false,
              length: 10 * scale // 按比例调整
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            splitNumber: 4,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#DDD',
                width: 1 * scale
              },
            },
             axisLabel: {
              color: '#8C8C8C',
              fontSize: 12 * scale // 按比例调整字体大小
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#333',
              },
            },
            axisTick: {
              show: false,
            },
            nameTextStyle: {
              color: '#999',
              fontSize: 12 * scale // 按比例调整字体大小
            },
            splitArea: {
              show: false,
            },
          },
          {
            type: 'value',
            position: 'right',
            name: '',
            nameLocation: 'end',
            axisLine: {
              show: false
            },
            nameTextStyle: {
              color: '#999',
              fontSize: 12 * scale // 按比例调整字体大小
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            data: [],
            barWidth: 16 * scale,  // 按比例调整
            label: {
              show: true,
              distance: 5 * scale,  // 按比例调整
              position: 'top',
              fontSize: 12 * scale, // 按比例调整字体大小
            }
          },
        ],
      }
    }

    getOption() {
      return this.option;
    }

    setColor(color = []) {
      this.option.color = color;
    }

    updateData(xAxisData, seriesData) {
      this.option.xAxis[0].data = xAxisData;
      this.option.series[0].data = seriesData;
    }
  }

  export default LineTrendOption;
