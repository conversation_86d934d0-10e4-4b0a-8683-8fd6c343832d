<template>
    <div class="weekly-index">
        <div class="content">
            <WeeklyForm ref="weeklyFormRef"></WeeklyForm>
        </div>
      <div class="bottom-button" v-if="!isRead">
        <van-button type="primary" class="primary-btn btn" @click="saveDraft">保存草稿</van-button>
        <van-button type="primary" class="primary-btn btn" v-if="type === 'update'" @click="updateWeekly">提交</van-button>
        <van-button type="primary" class="primary-btn btn" v-else @click="submitWeekly">提交</van-button>
        <van-button type="default" class="second-btn btn" @click="cancelHandle">取消</van-button>
      </div>
    </div>
</template>
<script>
// http://localhost/wechatE/mobile/todoCenterWeeklyInput?type=update&todoNoticeId=655
import WeeklyForm from './components/weeklyForm.vue'
import {addDraft, addInfo, addInit, getInfo, updateInfo, addInitNew} from "@/api/weekly/mobile-reportInfo";
import { Cell, CellGroup, Col, Row, Field, Form, FormItem, Input, Button,Popup,Picker, Icon} from "vant";
export default {
    name: "WeeklyIndex",
    components: {
        WeeklyForm,
        [Cell.name]: Cell,
        [CellGroup.name]: CellGroup,
        [Col.name]: Col,
        [Row.name]: Row,
        [Field.name]: Field,
        [Button.name]: Button,
        [Popup.name]: Popup,
        [Picker.name]: Picker,
        [Icon.name]: Icon,
    },
    data() {
        return {
          form: {},
          loading: false,
          type: "", // 新增还是编辑模式
          id: "", // 周报ID
          todoNoticeId: "", // 待办ID
          isRead: false
        };
    },
  created() {
    // 获取路由参数
    const {type, todoNoticeId } = this.$route.query;
    this.type = type;
    this.todoNoticeId = todoNoticeId;
  },
  mounted() {
    /* if (this.type === "update" && this.id) { // 编辑模式
      this.getWeeklyDetail();
    } else { // 新增模式
      this.handleInit();
    } */
    this.handleInit();
  },
  methods: {
    async saveDraft() {
      if(this.loading) return
      this.loading = true;
      try {
        const formData = await this.$refs.weeklyFormRef.draftForm();
        formData.reflectInfo = formData.reflectInfo || {type: 4, progressScheme: ""};
        await addDraft(formData);
        this.$modal.msgSuccess("草稿保存成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("草稿保存失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async getWeeklyDetail() {
      this.loading = true;
      try {
        const { data } = await getInfo(this.id);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        this.$refs.weeklyFormRef.updateFormData(data);
      } catch (error) {
        // this.$modal.msgError("获取详情失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async addWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        await addInfo(data);
        this.$modal.msgSuccess("新增成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("新增失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },

    async handleInit() {
      try {
        // const { data } = await addInit();
        // 如果返回的周报信息中id存在，提交的时候就调用修改方法，不存在则调用新增方法
        const { data } = await addInitNew(this.todoNoticeId);

        this.updateAction = !!data.id;
        this.isRead = !!data.isRead;
        if(this.updateAction) {
          this.type = "update";
          this.id = data.id;
        }
        else {
          this.type = "add";
        }
        console.log("handleInit data:", data);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }

        const formFields = [
          "year",
          "week",
          "nickName",
          "deptId",
          "deptName",
          "postName",
          "startDate",
          "endDate",
          "workSummaryList",
          "workPlanList",
          "workSupportList",
          "userCode",
          "reportReviewDetails",
          "id",
          "reflectInfo"
        ];
        const formData = {};
        formFields.forEach((field) => (formData[field] = data[field]));
        if(!formData.reflectInfo){
          formData.reflectInfo = {type: 4, progressScheme: "" };
        }
        if(this.type === "update"){
          console.log("update data:", data);
          this.$refs.weeklyFormRef.setFormData(data);
        }
        else{
          console.log("add data:", formData);
          this.$refs.weeklyFormRef.setFormData(formData);
        }
      } catch (error) {
        // this.$modal.msgError("初始化失败：" + error.message);
      }
    },
    async updateWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        const formData = {
          id: this.id || data.id,
          year: data.year,
          week: data.week,
          userCode: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          // approveUserName: data.approveUserName,
          // approveUserCode: data.approveUserCode,
          workSummaryList: data.workSummaryList,
          workPlanList: data.workPlanList,
          workSupportList: data.workSupportList,
          reportReviewDetails: data.reportReviewDetails,
          reflectInfo: data.reflectInfo || {type: 4, progressScheme: ""},
        };
        await updateInfo(formData);
        this.$modal.msgSuccess("修改成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("修改失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 编辑周报
    async updateWeekly() {
      try{
        if(this.loading) return
        this.loading = true;
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await this.updateWeeklyHandle(formData);
      }
      finally {
        this.loading = false;
      }
    },
    // 新增周报
    async submitWeekly() {
      try{
        if(this.loading) return
        this.loading = true; // loading前置
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        formData.reflectInfo = formData.reflectInfo || {type: 4, progressScheme: ""};
        await this.addWeeklyHandle(formData);
      }
      finally {
        this.loading = false;
      }
    },
    closeCurrentPage() {
      // this.$tab.closeOpenPage({ path: "/weekly/myWeekly" });
      this.$router.push("/wechatE/mobile/myWeekly");
    },
    cancelHandle() {
      this.closeCurrentPage();
    },
    handleBeforeUnload(event) {
      // 组织默认的 beforeunload 行为 (某些浏览器可能需要)
      event.preventDefault();
      // Chrome, Firefox, Safari, IE 8+
      event.returnValue = '您确定要离开此页面吗？您所做的更改可能不会被保存。';
      // 返回的字符串会显示在确认对话框中
      return event.returnValue; // 必须返回，某些浏览器需要
    }
  },
};
</script>
<style scoped lang="scss">
:deep(.van-button--primary) {
  background-color: #007aff;
  border-color: #007aff;
}
.weekly-index {
    width: 100%;
    // height: 100%;
    min-height: 100vh;
    overflow-y: auto;
    background-image: url("~@/assets/weeklyMobile/weeklybac.png");
    background-size: cover;
    background-repeat: no-repeat;

    .header {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 17px;
        color: #333333;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        padding: 11px 0;
        //margin-bottom: 60px;
    }
    .content{
        padding: 16px 10px;
        .content-item{
            .content-item-image{
                width: 100%;
                margin-bottom: 42px;
            }
        }
    }
    .bottom-button{
      padding: 20px 10px;
      display: flex;
      gap : 10px;
      &.primary-btn{
        background: #3673FF;
      }
      .btn{
        flex: 1;
      }
    }
}
</style>
