<script>
export default {
  name: "update",
  data() {
    return {
      form: {},
      // 表单校验
      rules: {},
      // 城市公司列表
      cites: [],
      // 项目列表
      projects: [],
      detailDisabled: false
    }
  },
  methods:{
    getRouteQuery() {
      return this.$route.query
    },
  },
}
</script>

<template>
  <div>
    <div class="app-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="城市公司" prop="cityId">
              <!--              <el-input v-model="form.cityId" placeholder="请输入城市公司uuid"/>-->
              <el-select v-model="form.cityId" placeholder="请选择城市公司" :disabled="detailDisabled" @change="handleCityChange" class="width-100">
                <el-option
                  v-for="item in cites"
                  :key="item.cityId"
                  :label="item.cityName"
                  :value="item.cityId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目" prop="projectCode">
              <!--              <el-input v-model="form.projectName" placeholder="请输入项目名称"/>-->
              <el-select v-model="form.projectCode" placeholder="请选择项目" :disabled="detailDisabled" @change="handleProjectChange" class="width-100">
                <el-option
                  v-for="item in projects"
                  :key="item.projectCode"
                  :label="item.displayName"
                  :value="item.projectCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12">
                      <el-form-item label="项目编码" prop="projectCode">
                        <el-input v-model="form.projectCode" placeholder="请输入项目编码"/>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="城市公司" prop="cityId">
                        <el-input v-model="form.cityId" placeholder="请输入城市公司uuid"/>
                      </el-form-item>
                    </el-col>-->
          <!--          <el-col :span="12">
                      <el-form-item label="城市公司名称" prop="cityName">
                        <el-input v-model="form.cityName" placeholder="请输入城市公司名称"/>
                      </el-form-item>
                    </el-col>-->
          <el-col :span="12">
            <el-form-item label="案名" prop="name">
              <el-input v-model="form.name" :disabled="detailDisabled" placeholder="请输入案名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地块名" prop="plotName">
              <el-input v-model="form.plotName" :disabled="detailDisabled" placeholder="请输入地块名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目公司注册名" prop="companyRegisterName">
              <el-input v-model="form.companyRegisterName" :disabled="detailDisabled" placeholder="请输入项目公司注册名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人、总经理及归属公司" prop="belongCompanyName">
              <el-input v-model="form.belongCompanyName" :disabled="detailDisabled" placeholder="请输入法人、总经理及归属公司"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="股权占比" prop="shareholdRate">
              <el-input v-model="form.shareholdRate" :disabled="detailDisabled" placeholder="请输入股权占比"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操盘条线" prop="operateStripline">
              <el-input v-model="form.operateStripline" :disabled="detailDisabled" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="并表方">
              <!--              <editor v-model="form.combineTableContent" :height="192"/>-->
              <el-input
                :disabled="detailDisabled"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="form.combineTableContent"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拿地时间" prop="takeLandDate">
              <el-date-picker clearable size="small"
                              class="width-100"
                              :disabled="detailDisabled"
                              v-model="form.takeLandDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择拿地时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首次开盘时间" prop="firstOpenDate">
              <el-date-picker clearable size="small"
                              class="width-100"
                              :disabled="detailDisabled"
                              v-model="form.firstOpenDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择首次开盘时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首次竣备时间" prop="firstCompletedDate">
              <el-date-picker clearable size="small"
                              class="width-100"
                              :disabled="detailDisabled"
                              v-model="form.firstCompletedDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择首次竣备时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首批合同交付时间" prop="firstDeliverDate">
              <el-date-picker clearable size="small"
                              class="width-100"
                              :disabled="detailDisabled"
                              v-model="form.firstDeliverDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择首批合同交付时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="土地费用(亿，不含契税)" prop="landCost">
              <el-input v-model="form.landCost" :disabled="detailDisabled" placeholder="请输入土地费用(亿，不含契税)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计容面积㎡" prop="capacityArea">
              <el-input v-model="form.capacityArea" :disabled="detailDisabled" placeholder="请输入计容面积㎡"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可售面积㎡" prop="canSalesArea">
              <el-input v-model="form.canSalesArea" :disabled="detailDisabled" placeholder="请输入可售面积㎡"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="限价政策" prop="constraintPricePolicy">
              <el-input v-model="form.constraintPricePolicy" :disabled="detailDisabled" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计容楼面价" prop="capacityPrice">
              <el-input v-model="form.capacityPrice" :disabled="detailDisabled" placeholder="请输入计容楼面价"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可售楼面价" prop="canSalesPrice">
              <el-input v-model="form.canSalesPrice" :disabled="detailDisabled" placeholder="请输入可售楼面价"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用地面积" prop="useLandArea">
              <el-input v-model="form.useLandArea" :disabled="detailDisabled" placeholder="请输入用地面积"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="容积率/限高(米)" prop="plotRatio">
              <el-input v-model="form.plotRatio" :disabled="detailDisabled" placeholder="请输入容积率/限高(米)"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配套情况" prop="supportSituation">
              <el-input v-model="form.supportSituation" :disabled="detailDisabled" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="竣备交付形式" prop="completedDeliverModality">
              <el-input v-model="form.completedDeliverModality" :disabled="detailDisabled" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="创建人" prop="createdBy">
              <el-input v-model="form.createdBy" :disabled="detailDisabled" placeholder="请输入创建人"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createdTime">
              <el-date-picker clearable size="small"
                              :disabled="detailDisabled"
                              v-model="form.createdTime"
                              type="date"
                              class="width-100"
                              value-format="yyyy-MM-dd"
                              placeholder="选择创建时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新人" prop="updatedBy">
              <el-input v-model="form.updatedBy" :disabled="detailDisabled" placeholder="请输入更新人"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新时间" prop="updatedTime">
              <el-date-picker clearable size="small"
                              class="width-100"
                              :disabled="detailDisabled"
                              v-model="form.updatedTime"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择更新时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="楼栋图" prop="buildingPic">
              <!--              <el-input v-model="form.buildingPic" placeholder="请输入楼栋图"/>-->
              <label class="el-icon-plus cursor icon-primary upload" v-if="!form.buildingPic">
                <input type="file" class="display-none" style="display: none"
                       @change="(event) => uploadFileHandle(event, 'buildingPic')"/>
              </label>
              <el-image
                v-else
                style="width: 100px; height: 100px"
                :src="form.buildingPic"
                :preview-src-list="[form.buildingPic]">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="鸟瞰图" prop="birdPic">
              <!--              <el-input v-model="form.birdPic" placeholder="请输入鸟瞰图"/>-->
              <label class="el-icon-plus cursor icon-primary upload" v-if="!form.birdPic">
                <input type="file" class="display-none" style="display: none"
                       @change="(event) => uploadFileHandle(event, 'birdPic')"/>
              </label>
              <el-image
                v-else
                style="width: 100px; height: 100px"
                :src="form.birdPic"
                :preview-src-list="[form.birdPic]">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区位图" prop="locationPic">
              <!--              <el-input v-model="form.locationPic" placeholder="请输入区位图"/>-->
              <label class="el-icon-plus cursor icon-primary upload" v-if="!form.locationPic">
                <input type="file" class="display-none" style="display: none"
                       @change="(event) => uploadFileHandle(event, 'locationPic')"/>
              </label>
              <el-image
                v-else
                style="width: 100px; height: 100px"
                :src="form.locationPic"
                :preview-src-list="[form.locationPic]">
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
