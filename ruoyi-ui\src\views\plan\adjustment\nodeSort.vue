<template>
  <!-- 计划调整中，节点在提交前的排序 -->
  <el-dialog title="节点排序" v-loading="submitLoading" :visible.sync="showAllNode" width="1250px"  append-to-body v-if="showAllNode">
    <div class="app-container" :style="{ '--color': theme }">
      <el-table v-loading="loading" :data="nodeList"  height="65vh" ref="table" stripe>
        <el-table-column label="序号" align="center" prop="id" width="60" fixed>
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="节点名称" align="center" prop="nodeName" width="150" show-overflow-tooltip fixed />
        <el-table-column label="计划开始时间" align="center" prop="startTime" width="160">
        </el-table-column>
        <el-table-column label="计划完成时间" align="center" prop="endTime" width="160">
        </el-table-column>
        <el-table-column label="操作状态" align="center" prop="adjustType">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.adjustType == 1"
            >修改</el-tag
            >
            <el-tag v-if="scope.row.adjustType == 2">新增</el-tag>
            <el-tag type="info" v-if="scope.row.adjustType == 0"
            >未调整</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="责任部门" align="center" prop="departmentArr" width="250" show-overflow-tooltip>
          <template slot-scope="scope">
            {{scope.row.departmentArr.join(',')}}
          </template>
        </el-table-column>
        <el-table-column label="反馈人" align="center" prop="feedbackUserName" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>
              {{ scope.row.feedbackUserName || "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否可用" align="center" prop="isValid" width="140">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isValid == 1">是</el-tag>
            <el-tag v-if="scope.row.isValid == 0">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button @click="moveUp(scope.$index)" style="font-weight: bold;" type="text" size="small" icon="el-icon-top custom-bold" v-if="scope.$index !== 0"></el-button>
            <el-button @click="moveDown(scope.$index)" type="text" size="small" icon="el-icon-bottom custom-bold" v-if="scope.$index < (nodeList.length - 1)"></el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmFun">确 定</el-button>
      <el-button @click="cancelDailog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  isValidOption
} from '@/views/plan/constant'
import variables from '@/assets/styles/element-variables.scss'
import Template from "@/views/plan/template/index.vue";
import {listAllNode as  adjustList } from "@/api/plan/node"

export default {
  name: "Node",
  components: {
    Template,
  },
  props: ['defaultSelect','departmentsData'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      // 是否显示弹出层
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 20,
        nodeLevel: null,
        nodeName: null,
        planId: null,
      },
      editIndex: null,
      clickIndex: null,
      /*专业成果*/
      resultList: [],
      showResultDialog: false,
      ids2: [],
      //责任人
      respectPeople: [],
      resultConfigIds: [],
      theme: variables.theme,
      showDepart: false,
      planId: null,
      selectData: [],
      selectNodeCodes: [],
      showAllNode: false,
      submitLoading: false,
    }
  },
  created() {},
  computed: {
    isValidOption: () => isValidOption,
  },
  watch: {},
  methods: {
    moveUp(index) {
      if (index > 0) {
        const row = this.nodeList.splice(index, 1)[0];
        this.nodeList.splice(index - 1, 0, row);
      }
    },
    moveDown(index) {
      if (index < this.nodeList.length - 1) {
        const row = this.nodeList.splice(index, 1)[0];
        this.nodeList.splice(index + 1, 0, row);
      }
    },
    // 取消按钮
    cancel() {
      this.editIndex = null
    },
    async getList(id) {
      this.loading = true
      const response = await adjustList({ planId: id })
      let da=response.data
      da.map(item=>{
        item.departmentArr=item.department.length?item.department.split(','):[]
      })
      this.nodeList = da
      this.total = response.total
      this.loading = false
    },
    async show(planId) {
      this.clickIndex = null
      this.showAllNode = true
      // this.queryParams.planId = this.$route.query.planId
      this.queryParams.planId = planId
      // this.selectNodeCodes = this.defaultSelect
      await this.getList(planId)
    },
    confirmFun() {
      if (!this.nodeList.length) {
        return this.$message('节点数据为空')
      }
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 设置节点顺序字段
        for(let i = 0; i < this.nodeList.length; i++){
          this.nodeList[i].nodeIndex  = i + 1
        }
        this.setSubmitLoading(true);
        this.$emit('submit', this.queryParams.planId ,this.nodeList)
      }).catch(() => {
      });

    },
    setSubmitLoading(flag) {
      this.submitLoading = flag
    },
    closeShowNode() {
      this.showAllNode = false
    },
    cancelDailog() {
      this.showAllNode = false
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";

.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 150px;
}

.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
