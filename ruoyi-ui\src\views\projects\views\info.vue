<template>
  <div class="projects-content-container">
    <div v-if="!hasPermi(['project:project:info'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else>
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中">
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
        <div class="desc">暂无数据</div>
      </div>
      <section v-else>
        <div class="info-content-container">
          <div class="basic-info" v-loading="loading"  >
            <div class="basic-info-item mb-21">
              <div class="item" v-for="item in [locationPic, birdPic, buildingPic]">
                <el-image class="image" :src="item" v-if="item" :preview-src-list="[buildingPic, birdPic, locationPic]"/>
                <div v-else class="no-img"/>
              </div>
            </div>
            <div class="">
              <BaseInfoTable :baseInfo="baseInfo"/>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>



</template>
<script>
import BaseInfoTable from '@/views/projects/components/BaseInfoTable.vue'
import CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'
import API from '@/views/projects/api'

export default {
  name: 'BaseInfo',
  components: {
    BaseInfoTable,
    CumulativeIndicatorsCard
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      buildingPic: null, // 楼栋图
      birdPic: null, // 鸟瞰图
      locationPic: null, // 区位图
      baseInfo: {
        /*// 项目id
         projectId: '',
         // 项目编码
         projectCode: '',
         // 项目名称
         projectName: '',
         // 城市公司uuid
         cityId: '',
         // 城市公司名称
         cityName: '',
         // 案名
         name: '',
         // 地块名
         plotName: '',
         // 项目公司注册名
         companyRegisterName: '',
         // 法人、总经理及归属公司
         belongCompanyName: '',
         // 股权占比
         shareholdRate: 0,
         // 操盘条线
         operateStripline: '',
         // 并表方
         combineTableContent: '',
         // 拿地时间
         takeLandDate: '',
         // 首次开盘时间
         firstOpenDate: '',
         // 首次竣备时间
         firstCompletedDate: '',
         // 首批合同交付时间
         firstDeliverDate: '',
         // 土地费用(亿，不含契税)
         landCost: 0,
         // 计容面积㎡（不含地下）
         capacityArea: 0,
         // 可售面积㎡（不含地下）
         canSalesArea: 0,
         // 限价政策
         constraintPricePolicy: '',
         // 计容楼面价（元）
         capacityPrice: 0,
         // 可售楼面价（元）
         canSalesPrice: 0,
         // 用地面积
         useLandArea: 0,
         // 容积率/限高(米)
         plotRatio: 0,
         // 配套情况
         supportSituation: '',
         // 竣备交付形式
         completedDeliverModality: '',
         // 创建人
         createdBy: '',
         // 创建时间
         createdTime: '',
         // 更新人
         updatedBy: '',
         // 更新时间
         updatedTime: '',*/
      },
      cumulativeIndicators: {},
      indicatorsConfig: [
        {
          key: 'qyAmount',
          label: '累计签约金额',
          type: 'pink',
          currency: '¥',
          unit: '亿'
        },
        {
          key: 'hkAmount',
          label: '累计回款金额',
          type: 'blue',
          currency: '¥',
          unit: '亿'
        },
        {
          key: 'allHzAmount',
          label: '动态货值',
          type: 'purple',
          currency: '¥',
          unit: '亿'
        },
        {
          key: 'costAmount',
          label: '动态成本',
          type: 'green',
          currency: '¥',
          unit: '亿'
        },
        {
          key: 'firstRate',
          label: '一级节点按期完成率',
          type: 'cyan',
          unit: '%'
        },
        {
          key: 'secondRate',
          label: '二级节点按期完成率',
          type: 'blue',
          unit: '%'
        }
      ],
      projectName: '',
      loading: false,
    }
  },
  computed: {
    indicatorsList() {
      return this.indicatorsConfig.map(config => ({
        ...config,
        value: this.cumulativeIndicators[config.key] || '0'
      }))
    }
  },
  mounted() {
    this.projectName = this.$route.query.projectName
    this.fetchBaseInfo(this.$route.query.projectCode);
    // this.fetchCumulativeIndicators(this.params.projectCode);
  },
  methods: {
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions
      // Check for all permissions wildcard first
      if (permissions.includes('*:*:*')) {
        return true
      }
      // Check specific permissions
      return permissions.some(p => permission.includes(p))
    },
    initHandle(){
      this.fetchBaseInfo
    },
    async fetchBaseInfo(projectCode) { // 基本信息
      this.loading = true;
      try {
        const { data } = await API.City.getProjectInfo(projectCode)
        if (data) {
          this.baseInfo = data
          this.buildingPic = data.buildingPic;
          this.birdPic = data.birdPic;
          this.locationPic = data.locationPic;
        }
        else {
          this.hasData = false;
        }
      } finally {
        this.loading = false;
      }
    },
    async fetchCumulativeIndicators(projectCode) { // 全盘累计指标
      const {data} = await API.City.getProjectIndex(projectCode)
      if (data) {
        this.cumulativeIndicators = data
      }
    }
  },
}
</script>
<style scoped lang="scss">
@import '~@/views/projects/styles/projects.scss';
.info-content-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0;
  box-sizing: border-box;
  padding: 1.3125rem;


  .basic-info-item {
    display: flex;
    gap: 0.6875rem;

    .item {
      flex: 1;
      height: 17.8125rem;
      border-radius: 0.3125rem;
      background-color: #f5f5f5;

      .image {
        width: 100%;
        height: 100%;
      }

      .no-img {
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
