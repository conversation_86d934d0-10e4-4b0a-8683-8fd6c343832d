class ProgressCircle {
  constructor() {
    this.option = {
      tooltip: {
        show: true,
        // confine: true,  // 限制tooltip在图表区域内
      },
      title: [{
        text: '',
        subtext: '',
        left: 'center',
        top: '35%',
        textStyle: {
          fontSize: 21,
          fontFamily: 'PingFang SC, PingFang SC',
          fontWeight: 500,
        },
        subtextStyle: {
          fontSize: 12,
          color: '#666666',
          fontFamily: 'PingFang SC, PingFang SC',
          fontWeight: 400,
          padding: 8
        }
      }],
      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      },
      polar: {
        // radius: ['75%', '60%'],
        radius:  ['40%', '55%'],
        center: ['50%', '50%'],
      },
      angleAxis: {
        max: 100,
        show: false,
        startAngle: 90,
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        boundaryGap: [0, 0]
      },
      radiusAxis: {
        type: 'category',
        show: true,
        axisLabel: {
          show: true,
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: { show: false }
      },
      series: [
        {
          name: '',
          type: 'bar',
          animation: true,  // 启用动画
          animationDuration: 2000,  // 动画持续时间
          animationEasing: 'quarticOut',  // 动画缓动效果
          roundCap: true,
          barWidth: 40,
          showBackground: true,
          backgroundStyle: {
            color: '#D6DCF8',
            borderWidth: 0
          },
          data: [],
          coordinateSystem: 'polar',
          itemStyle: {
            borderWidth: 0,
            borderColor: 'transparent',
            shadowBlur: 0,
            shadowColor: 'transparent'
          },
          // startAngle: 90,
        },
         // 添加一个透明的饼图系列覆盖中心区域
         {
          type: 'pie',
          animation: true,  // 启用动画
          animationDuration: 2000,  // 动画持续时间
          animationEasing: 'quarticOut',  // 动画缓动效果
          radius: ['0%', '100%'],
          center: ['50%', '50%'],
          silent: false,
          label: {
            show: false
          },
          data: [100],
          itemStyle: {
            color: 'rgba(0,0,0,0)'  // 完全透明
          },
          tooltip: {
            show: true
          }
        }
      ]
    };

    // Add event listener for window resize
    // window.addEventListener('resize', this.updateFontSize.bind(this));
  }

  // calculateFontSize(scale = 1) {
  //   // Example calculation based on window width
  //   const baseSize = 21; // Base font size
  //   const width = window.innerWidth;
  //   // return Math.max(12, baseSize * (width / 1680) * scale); // Adjust based on a 1920px width
  //   return baseSize * (width / 1680) * scale
  // }

  // updateFontSize() {
  //   this.option.title[0].textStyle.fontSize = this.calculateFontSize();
  //   this.option.title[0].subtextStyle.fontSize = this.calculateFontSize(0.6);

  //   // Trigger a chart update if necessary
  // }

  getOption() {
    return this.option;
  }

  setOption(option) {
    this.option = option;
  }

  setBackgroundStyle(color) {
    this.option.series[0].backgroundStyle.color = color;
  }

  setBarItemStyle(color) {
    this.option.series[0].itemStyle.color = color;
  }

  setBarData(data) {
    this.option.series[0].data = data;
  }
}

export default ProgressCircle;
