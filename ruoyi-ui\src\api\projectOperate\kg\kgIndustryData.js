import request from '@/utils/request'

// 查询客观行业数据列表
export function listKgIndustryData(query) {
  return request({
    url: '/project/kgIndustryData/list',
    method: 'get',
    params: query
  })
}

// 查询客观行业数据详细
export function getKgIndustryData(id) {
  return request({
    url: '/project/kgIndustryData/' + id,
    method: 'get'
  })
}

// 新增客观行业数据
export function addKgIndustryData(data) {
  return request({
    url: '/project/kgIndustryData',
    method: 'post',
    data: data
  })
}

// 修改客观行业数据
export function updateKgIndustryData(data) {
  return request({
    url: '/project/kgIndustryData',
    method: 'put',
    data: data
  })
}

// 删除客观行业数据
export function delKgIndustryData(id) {
  return request({
    url: '/project/kgIndustryData/' + id,
    method: 'delete'
  })
}

// 客观行业数据导入
export function importFile(data) {
  return request({
    url: '/project/kgIndustryData/import',
    method: 'post',
    data: data
  })
}
