import request from '@/utils/request'

// 获取货值
function goodsValue(projectCode) {
  return request({
    url: `/project/goodsValue/${projectCode}`,
    method: 'get',
  })
}

// 获取库存
function inventory(projectCode) {
  return request({
    url: `/project/goodsValue/inventory/${projectCode}`,
    method: 'get',
  })
}

// 获取库存状态分布
function inventoryStatus(projectCode) {
  return request({
    url: `/project/goodsValue/inventoryStatus/${projectCode}`,
    method: 'get',
  })
}

// 获取库存结构分布
function inventoryStructure(projectCode) {
  return request({
    url: `/project/goodsValue/inventoryStructure/${projectCode}`,
    method: 'get',
  })
}

// 获取货值分布表 - 货值分析
function analyse(projectCode) {
  return request({
    url: `/project/goodsValue/analyse/${projectCode}`,
    method: 'get',
  })
}

// 货值分布图 - 货值数据
function analysePicValue(projectCode) {
  return request({
    url: `/project/goodsValue/analysePic/${projectCode}`,
    method: 'get',
  })
}

export default {
  goodsValue,
  inventory,
  inventoryStatus,
  inventoryStructure,
  analyse,
  analysePicValue
}
