import request from '@/utils/request'

// 查询报表列表
export function reportList (query) {
  return request({
    url: '/plan/report/list',
    method: 'get',
    params: query
  })
}

// 导出报表
export function projectRateExport (data) {
  return request({
    url: '/plan/report/projectRateExport',
    method: 'post',
    data
  })
}

export function cityRateExport (data) {
  return request({
    url: '/plan/report/cityRateExport',
    method: 'post',
    data
  })
}

// 获取城市达成率
export function getCityRate (query) {
  return request({
    url: '/plan/report/cityRate',
    method: 'get',
    params: query
  })
}

// 修改计划-节点
export function getProjectRate (query) {
  return request({
    url: '/plan/report/projectRate',
    method: 'get',
    params: query
  })
}
