import request from '@/utils/request'

// 全周期指标 - 货值
function allGoodsValue(projectCode) {
  return request({
    url: `/project/operateIndex/allGoodsValue/${projectCode}`,
    method: 'get',
  })
}

// 全周期指标 - 成本
function allCost(projectCode) {
  return request({
    url: `/project/operateIndex/allCost/${projectCode}`,
    method: 'get',
  })
}

// 全周期指标 - 营销费
function allMarketing(projectCode) {
  return request({
    url: `/project/operateIndex/allMarketing/${projectCode}`,
    method: 'get',
  })
}

// 全周期指标 - 签约
function allQy(projectCode) {
  return request({
    url: `/project/operateIndex/allQy/${projectCode}`,
    method: 'get',
  })
}

// 全周期指标 - 回款
function allHk(projectCode) {
  return request({
    url: `/project/operateIndex/allHk/${projectCode}`,
    method: 'get',
  })
}

// 年度指标 - 签约
function yearQyData(projectCode) {
  return request({
    url: `/project/operateIndex/yearQyData/${projectCode}`,
    method: 'get',
  })
}

// 年度指标 - 回款
function yearHkData(projectCode) {
  return request({
    url: `/project/operateIndex/yearHkData/${projectCode}`,
    method: 'get',
  })
}

// 年度指标 - 款齐
function yearKqData(projectCode) {
  return request({
    url: `/project/operateIndex/yearKqData/${projectCode}`,
    method: 'get',
  })
}

// 年度指标 - 利润
function yearLrData(projectCode) {
  return request({
    url: `/project/operateIndex/yearLrData/${projectCode}`,
    method: 'get',
  })
}

// 年度指标 - 现金流
function yearXhlData(projectCode) {
  return request({
    url: `/project/operateIndex/yearXhlData/${projectCode}`,
    method: 'get',
  })
}

// 指标数据考核目标
function examineTarget(projectCode) {
  return request({
    url: `/project/operateIndex/examineTarget/${projectCode}`,
    method: 'get',
  })
}

// 查询考核指标版本数据列表
export function getExamineDetail(data) {
  // 项目编码  projectCode;
  // 版本类型  versionType; 版本类型(1-可研版,2-投决版,3-目标版,4-动态版,5-后评价版)
  return request({
    url: '/project/examineTargetVersionData/detail',
    method: 'post',
    data
  })
}

export default {
  allGoodsValue,
  allCost,
  allMarketing,
  allQy,
  allHk,
  yearQyData,
  yearHkData,
  yearKqData,
  yearLrData,
  yearXhlData,
  examineTarget,
  getExamineDetail
}
