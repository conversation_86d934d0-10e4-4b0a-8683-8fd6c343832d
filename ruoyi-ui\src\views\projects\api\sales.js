import request from '@/utils/request'

// 全盘销售进度 - 签约
function allQyData(queryReq) {
  return request({
    url: '/project/sales/all/qyData',
    method: 'post',
    data: queryReq
  })
}

// 全盘销售进度 - 回款
function allHkData(queryReq) {
  return request({
    url: '/project/sales/all/hkData',
    method: 'post',
    data: queryReq
  })
}

// 全周期进度偏差-签约数据
function allTargetQyData(queryReq) {
  return request({
    url: '/project/sales/all/qyDeviationData',
    method: 'post',
    data: queryReq
  })
}
// 全周期进度偏差-回款数据
function allTargetHkData(queryReq) {
  return request({
    url: '/project/sales/all/hkDeviationData',
    method: 'post',
    data: queryReq
  })
}
// 年度签约数据
function yearTargetQyData(queryReq) {
  return request({
    url: '/project/sales/yearTarget/qyData',
    method: 'post',
    data: queryReq
  })
}

// 年度回款数据
function yearTargetHkData(queryReq) {
  return request({
    url: '/project/sales/yearTarget/hkData',
    method: 'post',
    data: queryReq
  })
}

// 款齐数据
function kqData(queryReq) {
  return request({
    url: '/project/sales/kqData',
    method: 'post',
    data: queryReq
  })
}

// 签约、到访数据
function qyDfData(queryReq) {
  return request({
    url: '/project/sales/qyDfData',
    method: 'post',
    data: queryReq
  })
}

// 认购数据
function rgData(queryReq) {
  return request({
    url: '/project/sales/rgData',
    method: 'post',
    data: queryReq
  })
}

// 签约业态分布
function bussDist(queryReq) {
  return request({
    url: '/project/sales/bussDist',
    method: 'post',
    data: queryReq
  })
}

// 到访趋势
function dfTrend(queryReq) {
  return request({
    url: '/project/sales/dfTrend',
    method: 'post',
    data: queryReq
  })
}

// 认购趋势
function rgTrend(queryReq) {
  return request({
    url: '/project/sales/rgTrend',
    method: 'post',
    data: queryReq
  })
}

// 签约趋势
function qyTrend(queryReq) {
  return request({
    url: '/project/sales/qyTrend',
    method: 'post',
    data: queryReq
  })
}

// 回款趋势
function hkTrend(queryReq) {
  return request({
    url: '/project/sales/hkTrend',
    method: 'post',
    data: queryReq
  })
}

export default {
  allQyData,
  allHkData,
  yearTargetQyData,
  yearTargetHkData,
  kqData,
  qyDfData,
  rgData,
  bussDist,
  dfTrend,
  rgTrend,
  qyTrend,
  hkTrend,
  allTargetHkData,
  allTargetQyData
}
