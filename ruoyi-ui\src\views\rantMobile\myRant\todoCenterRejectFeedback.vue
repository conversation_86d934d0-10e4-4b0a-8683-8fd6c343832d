<template>
  <div class="app-container app-container-feedback" v-loading="loading">
    <div class="step-box">
      <rant-step :stepActive="stepActive"/>
    </div>

    <div class="rant-container">
      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="rant-form-title">
            <img src="@/assets/rant/rant-info.png" class="icon">
            基本信息
          </div>

          <van-form class="rant-detail">
            <div class="form-group">
              <div class="form-item">
                <div class="form-label">来源</div>
                <div class="form-value">{{ form.ranterName || '未设置' }}</div>
              </div>

              <div class="form-item">
                <div class="form-label"><span class="required-mark">*</span>分类</div>
                <div class="form-value clickable" @click="!isRead && (showClassifyPicker = true)" :class="{'custom-disabled-mobile': isRead}">
                  <template v-if="form.rantClassify">
                    <van-icon name="more-o" />{{ form.rantClassify }}
                  </template>
                  <template v-else><van-icon name="more-o" />请选择吐槽分类</template>
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">计划完成时间</div>
<!--                @click="!isRead && (showDatePicker = true)"-->
                <div class="form-value align-right clickable" :class="{'custom-disabled-mobile': true}">
                  {{ form.planTime || '请选择计划完成时间' }}
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">责任人</div>
<!--                @click="!isRead && (selectUserFun('responsibilityer'))"-->
                <div class="form-value clickable"  :class="{'custom-disabled-mobile': true}">
                  {{ form.responsiblePersonName || '请选择责任人' }}
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">责任部门</div>
<!--                @click="!isRead && (showDeptPicker = true)"-->
                <div class="form-value clickable" :class="{'custom-disabled-mobile': true}">
                  {{ form.deptName || '请选择责任部门' }}
                </div>
              </div>

              <div class="form-item">
                <div class="form-label">部门负责人</div>
                <div class="form-value">{{ form.respDeptResponsiblerName || '未设置' }}</div>
              </div>
            </div>
          </van-form>
        </div>
      </div>

      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10"><span class="required-mark">*</span>内容</div>
            <div class="">
              <textarea
                v-model="form.rantContent"
                class="progress-textarea"
                rows="4"
                placeholder="请输入内容"
                :disabled="isRead"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10">措施</div>
            <div class="">
              <textarea
                v-model="form.solution"
                class="progress-textarea custom-disabled-mobile"
                rows="4"
                placeholder="请输入"
                disabled
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="rant-content">
        <div class="rant-detail-content">
          <div class="">
            <div class="form-label mb-10">审核意见</div>
            <div class="">
              <textarea
                v-model="form.approveDesc"
                class="progress-textarea custom-disabled-mobile"
                disabled
                rows="4"
                placeholder="请输入"
              ></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <van-button class="custom-btn-submit" @click="submitForm(1)" v-if="(status == 0 || status == 7) && !isRead">提 交</van-button>
      <van-button class="custom-btn-save" @click="submitForm(0)" v-if="(status == 0 || status == 7) && !isRead">保 存</van-button>
      <van-button class="custom-btn-cancel" @click="cancel" v-if="!isRead">取 消</van-button>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        :min-date="minDate"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 分类选择弹窗 -->
    <van-popup v-model="showClassifyPicker" position="bottom">
      <van-picker
        :columns="classifyOptions"
        @confirm="onClassifyConfirm"
        @cancel="showClassifyPicker = false"
        show-toolbar
        title="选择分类"
      />
    </van-popup>

    <!-- 部门选择弹窗 -->
    <van-popup v-model="showDeptPicker" position="bottom">
      <van-picker
        :columns="deptPickerOptions"
        @confirm="onDeptConfirm"
        @cancel="showDeptPicker = false"
        show-toolbar
        title="选择责任部门"
      />
    </van-popup>

    <!-- 选择用户组件 -->
    <select-user
      :show.sync="showUserSelector"
      :multiple="isMultiple"
      :value="[]"
      @select="onUserSelected"
      @close="onUserSelectorClose"
    />
  </div>
</template>

<script>
// http://localhost/rant/todoCenterRejectFeedback?todoNoticeId=20
// http://localhost/wechatE/mobile/rant/todoCenterRejectFeedback?todoNoticeId=20
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/
import { close } from '@/utils';
import {
  myRantEdit,
  rantDaiBanInfo,
} from '@/api/rantMobile/matters'
// import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import SelectUser from '@/views/rantMobile/components/SelectUserMobile.vue'
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listRespdet, getRespdet } from '@/api/rantMobile/respdet'
// import { listDept, listDeptExcludeChild } from '@/api/system/dept'
import RantStep from '@/views/rant/components/RantStep/index.vue'
import {getDicts} from '@/api/rantMobile/common'
import mixin from '../mixins'
// 导入Vant组件
import {
  Form,
  Field,
  Button,
  Popup,
  DatetimePicker,
  Picker,
  Toast,
  Icon
} from 'vant';

export default {
  name: 'MyRant',
  mixins: [mixin],
  components: {
    SelectUser,
    StatusTag,
    Treeselect,
    RantStep,
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Picker.name]: Picker,
    [Icon.name]: Icon
  },
  data() {
    return {
      stepActive: 1, // 当前步骤
      // 遮罩层
      loading: true,
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: true,
      isMultiple: false,
      // 责任部门列表
      deptOptions: [],
      status: null,
      // 日期选择器
      showDatePicker: false,
      currentDate: new Date(),
      minDate: new Date(2000, 0, 1),
      // 分类选择器
      showClassifyPicker: false,
      // classifyOptions: [],
      // 部门选择器
      showDeptPicker: false,
      deptPickerOptions: [],
      // 用户选择器
      showUserSelector: false,
      selectDeptId: null,
      // 表单参数
      form: {
        "todoNoticeId": null,
        "id": null,
        "ranter": null, // 来源:人(多个用","隔开)
        "ranterName": null, // 来源:人(多个用","隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "status": 0, // 状态（6-同意，7-驳回）
        "approveDesc": '', // 审批意见
      },
      // 表单校验
      rules: {
        ranter: [
          { required: true, message: '吐槽人不能为空', trigger: 'blur' }
        ],
        rantClassify: [
          { required: true, message: '吐槽分类不能为空', trigger: 'blur' }
        ],
        rantContent: [
          { required: true, message: '吐槽内容不能为空', trigger: 'blur' }
        ],
      },
      type: '',
      rantId: '',
      isRead: false
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    }
  },
  created() {
    this.todoNoticeId = this.$route.query.todoNoticeId
    this.form.todoNoticeId = this.todoNoticeId;
    this.fetchDaiBanInfo(this.todoNoticeId)
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, 'id')
      // 处理部门选择器数据
      this.prepareDeptPickerOptions(this.deptOptions)
    })
    this.filterRantClassify('rant_classify')
  },
  mounted() {

  },
  methods: {

    fetchDaiBanInfo (todoNoticeId) {
      this.loading = true;
      rantDaiBanInfo(todoNoticeId).then((res) => {
        if (res.code === 200) {
          this.form.id = res.data?.id;
          this.form.ranter = res.data?.ranter;
          this.form.ranterName = res.data?.ranterName;
          this.form.rantClassify = res.data?.rantClassify;
          this.form.rantContent = res.data?.rantContent;
          this.form.planTime = res.data?.planTime;
          this.form.deptId = res.data?.deptId;
          this.form.deptName = res.data?.deptName;
          this.form.responsiblePerson = res.data?.responsiblePerson;
          this.form.responsiblePersonName = res.data?.responsiblePersonName;
          this.form.respDeptResponsibler = res.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = res.data?.respDeptResponsiblerName;
          this.form.approveDesc = res.data?.approveDesc;
          this.status = res.data?.status;
          this.form.solution = res.data?.solution;
          this.form.isRead = res.data?.isRead;
          this.isRead = res.data?.isRead;
          // 初始化日期选择器当前值
          if (this.form.planTime) {
            const parts = this.form.planTime.split('-')
            if (parts.length === 3) {
              this.currentDate = new Date(parts[0], parts[1]-1, parts[2])
            }
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        Toast.fail('获取数据失败');
      })
    },
    // 确认日期选择
    confirmDate(value) {
      this.form.planTime = this.formatDate(value);
      this.showDatePicker = false;
    },

    // 格式化日期为字符串 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 确认分类选择
    onClassifyConfirm(value) {
      // Vant Picker返回的是整个选项对象，所以我们需要从中提取value
      if (typeof value === 'object') {
        this.form.rantClassify = value.value;
      } else {
        this.form.rantClassify = value;
      }
      this.showClassifyPicker = false;
    },

    // 准备部门选择器数据
    prepareDeptPickerOptions(deptList) {
      console.log('准备部门选择器数据:', deptList);
      const flattenDepts = (list, prefix = '') => {
        return list.reduce((acc, dept) => {
          // 使用name字段，因为后端返回的是name而不是label
          const deptName = prefix ? `${prefix} / ${dept.name || dept.label}` : (dept.name || dept.label);
          acc.push({
            text: deptName,
            value: dept.id
          });

          if (dept.children && dept.children.length > 0) {
            acc = acc.concat(flattenDepts(dept.children, deptName));
          }

          return acc;
        }, []);
      };

      this.deptPickerOptions = flattenDepts(deptList);
      console.log('部门选择器数据:', this.deptPickerOptions);
    },

    // 确认部门选择
    onDeptConfirm(value) {
      console.log('选择的部门:', value);
      this.form.deptId = value.value;
      this.form.deptName = value.text;
      this.showDeptPicker = false;
      this.selectDept(value.value);
    },

    // 选择用户
    selectUserFun(type) {
      this.selectUserType = type;

      if (type === 'ranter') {
        this.roleName = 'rantManager';
        this.isMultiple = true;
        this.selectDeptId = undefined;
        this.showUserSelector = true;
      } else if (type === 'responsibilityer') {
        // 如果已经从API获取了deptId但尚未在界面选择
        // 也允许选择责任人
        const hasDeptId = this.form.deptId || (this.deptPickerOptions && this.deptPickerOptions.length > 0);

        if (!hasDeptId) {
          Toast('请先选择责任部门');
          return false;
        }

        this.isMultiple = false;
        this.selectDeptId = this.form.deptId;
        console.log('选择责任人，部门ID:', this.selectDeptId);
        this.showUserSelector = true;
      }
    },

    // 用户选择回调
    selectUserData(data) {
      if (this.selectUserType === 'ranter') {
        this.form.ranter = data.userId;
        this.form.ranterName = data.nickName;
      } else if (this.selectUserType === 'responsibilityer') {
        this.form.responsiblePerson = data.userId;
        this.form.responsiblePersonName = data.nickName;
      }
    },

    // 新增用户选择处理方法
    onUserSelected(selectedUsers) {
      console.log('选择的用户:', selectedUsers);
      if (selectedUsers && selectedUsers.length > 0) {
        const user = selectedUsers[0];
        this.selectUserData({
          userId: user.userId,
          nickName: user.nickName
        });
      }
    },

    // 用户选择器关闭
    onUserSelectorClose() {
      this.showUserSelector = false;
    },

    // 选择责任部门回调
    selectDept(deptId) {
      if (!deptId) return;

      console.log('选择部门ID:', deptId);
      getRespdet(deptId).then(response => {
        console.log('获取部门负责人信息:', response);
        if (response.code === 200 && response.data) {
          this.form.respDeptResponsibler = response.data.respPeople || response.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = response.data.respPeopleName || response.data.respDeptResponsiblerName;
        }
      }).catch(err => {
        console.error('获取部门负责人失败:', err);
        Toast('获取部门负责人信息失败');
      });
    },

    // 取消按钮
    cancel() {
      close();
    },

    // 表单提交
    submitForm(submitStatus) {
      this.form.status = submitStatus;

      // 简单验证
      if (!this.form.rantContent) {
        Toast('请输入内容');
        return;
      }

      if (!this.form.rantClassify) {
        Toast('请选择分类');
        return;
      }

      this.loading = true;

      myRantEdit(this.form).then((response) => {
        if(response.code === 200){
          if (submitStatus === 0) {
            Toast.success('保存成功');
          } else {
            Toast.success('提交成功，即将关闭页面');
            setTimeout(() => {
              close();
            }, 1500);
          }
          return;
        }
        Toast.fail(response.msg || '操作失败');
      }).catch(() => {
        Toast.fail('操作失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // ... 保留其他原有方法
    normalizer(node) {
      return {
        id: node.id,
        label: node.name || node.deptName, // 使用name字段，兼容deptName
        value: node.id,
        children: node.children
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/common.scss';

.app-container-feedback {
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
  padding: 20px 16px;
  padding-bottom: 60px;
}

.step-box {
  margin-bottom: 16px;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .custom-btn-submit {
    width: 110px;
    height: 40px;
    background: #3673FF;
    color: #FFFFFF;
    border-radius: 20px;
  }

  .custom-btn-save {
    width: 110px;
    height: 40px;
    background: rgba(54,115,255,0.2);
    color: #3673FF;
    border-radius: 20px;
  }

  .custom-btn-cancel {
    width: 110px;
    height: 40px;
    background: #FFFFFF;
    color: #666666;
    border: 1px solid #DDDDDD;
    border-radius: 20px;
  }
}

.form-value {
  &.align-right {
    color: #3673FF;
    text-align: right;
  }
}

.clickable {
  position: relative;
  padding-right: 20px;

  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-top: 1px solid #999;
    border-right: 1px solid #999;
    transform: translateY(-50%) rotate(45deg);
  }
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.progress-textarea {
  width: 100%;
  border: 1px solid rgba(55, 109, 247, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F1F7FE;
  min-height: 80px;
  font-size: 14px;
  color: #333333;
  outline: none;
  resize: vertical;
}

.mb-10 {
  margin-bottom: 10px;
}
.form-value{
  text-align: right !important;
}
</style>
