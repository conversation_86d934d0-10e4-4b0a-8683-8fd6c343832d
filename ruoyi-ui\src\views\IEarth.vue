<template>
<div style="padding: 10px">
  <!--功能区域-->
  <div style="margin: 10px 0">
    <el-button type="primary" @click="add">新增</el-button>
  </div>
  <!--数据区域-->
  <el-table :data="tableData" border stripe style="width: 100%">
    <el-table-column
      type="index"
      width="50" label="ID">
    </el-table-column>
    <el-table-column prop="longitude" label="经度"/>
    <el-table-column prop="latitude" label="纬度"/>
    <el-table-column prop="description" label="描述" />
    <el-table-column label="图片">
      <template #default="scope">
        <el-image
          style="width: 100px; height: 100px"
          :src="scope.row.url"
          :preview-src-list="[scope.row.url]"
        >
        </el-image>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作">
      <template #default="scope">
<!--        <el-button size="mini" type="info" @click="handleClick(scope.row)" >查看详情</el-button>-->
        <el-button size='mini' @click="handleEdit(scope.row)">编辑</el-button>
        <el-popconfirm title="确认删除吗？" @confirm="handleDelete(scope.row)">
          <template #reference>
            <el-button size="mini" type="danger" >删除</el-button>
          </template>

        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <!--dialog弹窗-->
  <el-dialog  :visible.sync="dialogFormVisible" width="30%">
    <el-form :model="form" label-width="120px">
      <el-form-item label="经度"
                    :rules="{required: true, message: '请输入经度信息', trigger: 'blur'}"
      >
        <el-input v-model="form.longitude" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="纬度"
                    :rules="{required: true, message: '请输入纬度信息', trigger: 'blur'}">
        <el-input v-model="form.latitude" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="描述"
                    :rules="{required: true, message: '请输入描述信息', trigger: 'blur'}">
        <el-input type="textarea" v-model="form.description" autocomplete="off"></el-input>
      </el-form-item>
      <!---------------------改地址---------------------->
      <el-form-item label="图片">
        <el-upload
          :headers="upload.headers"
          :action="upload.url"
          :on-success="filesuploadSuccess"
          :before-upload="beforeAvatarUpload"
          :auto-upload="true"
          ref="upload"
        >
          <el-button type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import {delPic, getPic, updatePic, uploadPic} from "@/api/sm3d/pictureProperty";
import {getToken} from "@/utils/auth";

export default {
  name: "IEarth",
  data()
  {
    return{
      upload: {
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/file/upload",
        accept: ".jpg,.png,.JPG,.PNG,",
      },
      file:undefined,
      form:{},
      dialogFormVisible:false,
      tableData:[],
    }
  },
  created() {
   this.inittable()
  },
  methods:{
    inittable()
    {
      getPic().then((response)=>{
        this.tableData=response;
      })
    },
    beforeAvatarUpload(file)
    {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      // .jpg;.jpeg,.png,.gif,.bmp,.pdf
      const extension1 = testmsg === "jpg";
      const extension2 = testmsg === "png";
      const extension3 = testmsg === "jpeg";
      const extension4 = testmsg === "JPG";
      const extension5 = testmsg === "PNG";
      const extension6 = testmsg === "JPEG";
      if (
        !extension1 &&
        !extension2 &&
        !extension3 &&
        !extension4 &&
        !extension5 &&
        !extension6
      ) {
        this.$message({
          message:  " 上传文件只能是 jpg/png 格式!",
          type: "warning",
        });
      }else{
        this.file = file;
      }
    },
    handleClick(row) {
      this.dialogFormVisible=true;
    },
    add()
    {
      this.dialogFormVisible=true;
      this.form={}
    },
    handleEdit(row)//操作中的编辑
    {
      this.form=JSON.parse(JSON.stringify(row))
      this.dialogFormVisible=true;

    },
    handleDelete(id)//操作中的删除
    {
      delPic(id.id);
      this.inittable();

    },
    filesuploadSuccess(res)//上传文件
    {
      this.form.url=res.data.url
    },
    save()//确定
    {
      console.log(this.form.url)
      if(this.form.longitude==null||this.form.longitude=="")
      {
        this.$message({
          type:"warning",
          message:"经度不能为空"
        })
      }
     else if(this.form.latitude==null||this.form.latitude=="")
      {
        this.$message({
          type:"warning",
          message:"纬度不能为空"
        })
      }
      else if(this.form.description==null||this.form.description=="")
      {
        this.$message({
          type:"warning",
          message:"描述信息不能为空"
        })
      }
      else if(this.form.url==null||this.form.url=="")
      {
        this.$message({
          type:"warning",
          message:"图片不能为空"
        })
      }
      else{
        if(this.form.id!=undefined&&this.form.id!=null&&this.form.id!=""){
          updatePic(this.form.id,this.form.longitude,this.form.latitude,this.form.description,this.form.url)

        }else {
          uploadPic(this.form.longitude,this.form.latitude,this.form.description,this.form.url)
        }
      }
      this.inittable();
      this.$refs.upload.clearFiles();
      this.dialogFormVisible=false;

    },
  }
}
</script>

<style scoped>

</style>
