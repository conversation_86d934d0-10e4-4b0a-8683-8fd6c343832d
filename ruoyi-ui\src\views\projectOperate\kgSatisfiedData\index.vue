<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客关项目" prop="kgProjectId">
        <el-select
          v-model="queryParams.kgProjectId"
          placeholder="请选择客关项目"
          @change="handleQuery"
          clearable
          class="width-100">
          <el-option
            v-for="item in kgProjectDataList"
            :key="item.kgProjectId"
            :label="item.kgProjectName"
            :value="item.kgProjectId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年度" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年度"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="月度" prop="month">
        <el-select
          v-model="queryParams.month"
          placeholder="请选择月度"
          clearable
          class="width-100"
          @change="handleQuery">
          <el-option
            v-for="month in months"
            :key="month"
            :label="month === 13 ? '全年' : month + '月'"
            :value="month"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate"
        >导入模板下载
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile"
        >导入
        </el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="kgSatisfiedDataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客关项目" align="center" prop="kgProjectName" />
      <el-table-column label="年度" align="center" prop="year" />
      <el-table-column label="月度" align="center" prop="month">
        <template slot-scope="scope">
          <span>{{ scope.row.month === 13 ? '全年' : scope.row.month + '月' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总体" align="center" prop="overallScore" />
      <el-table-column label="准业主1" align="center" prop="prepareOwner1Score" />
      <el-table-column label="准业主2" align="center" prop="prepareOwner2Score" />
      <el-table-column label="准业主3" align="center" prop="prepareOwner3Score" />
      <el-table-column label="磨合期1" align="center" prop="period1Score" />
      <el-table-column label="磨合期2" align="center" prop="period2Score" />
      <el-table-column label="稳定期" align="center" prop="stabilityScore" />
      <el-table-column label="老业主" align="center" prop="oldOwnerScore" />
<!--      <el-table-column label="创建时间" align="center" prop="createdTime" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, scope.$index + 1)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改客关项目满意度数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="客关项目" prop="kgProjectId">
          <el-select
            v-model="form.kgProjectId"
            placeholder="请选择客关项目"
            clearable
            class="width-100">
            <el-option
              v-for="item in kgProjectDataList"
              :key="item.kgProjectId"
              :label="item.kgProjectName"
              :value="item.kgProjectId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年度" prop="year">
          <el-date-picker
            clearable
            size="small"
            v-model="form.year"
            type="year"
            value-format="yyyy"
            placeholder="选择年度"
            class="w-100"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="月度" prop="month">
          <el-select
            v-model="form.month"
            placeholder="请选择月度"
            clearable
            class="width-100"
            >
            <el-option
              v-for="month in months"
              :key="month"
              :label="month === 13 ? '全年' : month + '月'"
              :value="month"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="总体" prop="overallScore">
          <el-input v-model="form.overallScore" placeholder="请输入总体分数" />
        </el-form-item>
        <el-form-item label="准业主1" prop="prepareOwner1Score">
          <el-input v-model="form.prepareOwner1Score" placeholder="请输入准业主1分数" />
        </el-form-item>
        <el-form-item label="准业主2" prop="prepareOwner2Score">
          <el-input v-model="form.prepareOwner2Score" placeholder="请输入准业主2分数" />
        </el-form-item>
        <el-form-item label="准业主3" prop="prepareOwner3Score">
          <el-input v-model="form.prepareOwner3Score" placeholder="请输入准业主3分数" />
        </el-form-item>
        <el-form-item label="磨合期1" prop="period1Score">
          <el-input v-model="form.period1Score" placeholder="请输入磨合期1分数" />
        </el-form-item>
        <el-form-item label="磨合期2" prop="period2Score">
          <el-input v-model="form.period2Score" placeholder="请输入磨合期2分数" />
        </el-form-item>
        <el-form-item label="稳定期" prop="stabilityScore">
          <el-input v-model="form.stabilityScore" placeholder="请输入稳定期分数" />
        </el-form-item>
        <el-form-item label="老业主" prop="oldOwnerScore">
          <el-input v-model="form.oldOwnerScore" placeholder="请输入老业主分数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 隐藏的文件输入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script>
import {getKgSatisfiedData, listKgSatisfiedData, addKgSatisfiedData, updateKgSatisfiedData, delKgSatisfiedData, importFile} from "@/api/projectOperate/kg/kgSatisfiedData";
import {allList} from "@/api/projectOperate/kg/kgProjectData";

  export default {
  name: "KgSatisfiedData",
  data() {
    return {
      // 遮罩层
      loading: true,
      indexes: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客关项目满意度数据表格数据
      kgSatisfiedDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        kgProjectId: null,
        year: null,
        month: null,
        overallScore: null,
        prepareOwner1Score: null,
        prepareOwner2Score: null,
        prepareOwner3Score: null,
        period1Score: null,
        period2Score: null,
        stabilityScore: null,
        oldOwnerScore: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        kgProjectId: [
          { required: true, message: "客关项目不能为空", trigger: "blur" }
        ],
        year: [
          { required: true, message: "年度不能为空", trigger: "blur" }
        ],
        month: [
          { required: true, message: "月度不能为空", trigger: "change" }
        ],
      },
      kgProjectDataList: [],
      months: [...Array.from({ length: 13 }, (_, i) => i + 1)],
    };
  },
  created() {
    this.getProjectList();
    this.getList();
  },
  methods: {
    getProjectList() {
      this.loading = true;
      allList().then(response => {
        this.kgProjectDataList = response.data;
        this.loading = false;
      });
    },
    /** 查询客关项目满意度数据列表 */
    getList() {
      this.loading = true;
      listKgSatisfiedData(this.queryParams).then(response => {
        this.kgSatisfiedDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        kgProjectId: null,
        year: null,
        month: null,
        overallScore: null,
        prepareOwner1Score: null,
        prepareOwner2Score: null,
        prepareOwner3Score: null,
        period1Score: null,
        period2Score: null,
        stabilityScore: null,
        oldOwnerScore: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
      this.indexes = selection.map((item) => {
        const index = this.kgSatisfiedDataList.findIndex(row => row.id === item.id);
        return index !== -1 ? index + 1 : -1;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客关项目满意度数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getKgSatisfiedData(id).then(response => {
        this.form = response.data;
        this.form.year = response.data.year.toString();
        this.open = true;
        this.title = "修改客关项目满意度数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateKgSatisfiedData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addKgSatisfiedData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, index) {
      const ids = row.id || this.ids;
      const indexes = index || this.indexes;
      this.$modal.confirm('是否确认删除客关项目满意度数据序号为"' + indexes + '"的数据项？').then(function() {
        return delKgSatisfiedData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/kgSatisfiedData/export', {
        ...this.queryParams
      }, `满意度数据_${new Date().getTime()}.xlsx`)
    },
    //导入模版下载
    handleExportTemplate() {
      this.download('project/kgSatisfiedData/exportTemplate', {
        ...this.queryParams
      }, `客关项目满意度数据导入模版_${new Date().getTime()}.xlsx`)
    },

    // 导入文件
    importFile() {
      this.$refs.fileInput.click(); // 触发文件输入的点击事件
    },
    handleFileChange(event) {
      this.loading = true;
      const files = event.target.files;
      const formData = new FormData();
      formData.append("file", files[0]);
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null;
      importFile(formData)
        .then((response) => {
          if (response.msg) {
            this.$msgbox({
              title: '结果提示',
              message: response.msg.replace(/\n/g, '<br>'),
              showCancelButton: false, // 关键配置：隐藏取消按钮
              confirmButtonText: '确认',
              type: 'error',
              dangerouslyUseHTMLString: true
            }).then(() => {
              // 确认后的操作
              this.loading = false;
            })
          } else {
            this.$modal.msgSuccess("导入成功");
            this.getList()
            this.loading = false; // 关闭加载状态
          }
        })
        .catch(() => {
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  }
};
</script>
