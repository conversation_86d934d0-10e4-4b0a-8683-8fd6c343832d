<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" v-hasPermi="['rant:matters:myAllRantList']" type="card">
      <el-tab-pane label="我的" name="1"></el-tab-pane>
      <el-tab-pane label="所有" name="2"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="分类" prop="rantClassify">
        <el-select v-model="queryParams.rantClassify" placeholder="请选择吐槽分类" clearable
                   @keyup.enter.native="handleQuery"
                   style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                     :value="dict.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="责任人" prop="responsiblePerson">
        <el-input suffix-icon="el-icon-search" v-model="responsiblePersonNameQuery" placeholder="请选择责任人" clearable
                  @keyup.enter.native="handleQuery" @focus="selectUserFun('responsibilityerQuery')"
                  @change="changeresponsibilityer(responsiblePersonNameQuery)" style="width: 200px;"/>

      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable multiple size="small" style="width: 200px;">
          <el-option v-for="dict in rantStatusOption" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="rantContent">
        <el-input v-model="queryParams.rantContent" placeholder="请输入吐槽内容" clearable size="small"
                  @keyup.enter.native="handleQuery" style="width: 200px;" maxlength="100"/>
      </el-form-item>
      <el-form-item label="计划完成时间" prop="planTimeRange">
        <el-date-picker
          v-model="planTimeRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd" style="width: 400px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实际完成时间" prop="closingTimeRange">
        <el-date-picker
          v-model="closingTimeRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd" style="width: 400px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="activeTab === '2'" label="创建人" prop="createBys">
        <el-input suffix-icon="el-icon-search" v-model="createBys" placeholder="请选择创建人" clearable
                  @keyup.enter.native="handleQuery" @focus="selectUserFun('createBys')"
                  style="width: 200px;" @clear="clearCreateBys"/>
      </el-form-item>
      <el-form-item v-if="activeTab === '2'" label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="createTimeRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd" style="width: 400px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="activeTab === '1'">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
    </el-row>
    <div>
      <el-table v-loading="loading" :data="mattersList" @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column align="center" label="序号" prop="id" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <status-tag :status="scope.row.status" class="no-transition" :options="rantStatusOption"/>
          </template>
        </el-table-column>
        <el-table-column label="分类" align="center" prop="rantClassify" width="80"/>
        <el-table-column label="内容" align="center" prop="rantContent">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.rantContent" placement="top-start"
                        popper-class="custom-tooltip">
              <!-- <span>{{ scope.row.rantContent }}</span> -->
              <span
              :class="{ 'clickable-progress': scope.row.approveFlag == 1 }"
              @click="scope.row.approveFlag == 1 ? handleViewApproval(scope.row) : null">
              {{ scope.row.rantContent }}
            </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="措施" align="center" prop="solution">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.solution" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.solution }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="计划完成时间" align="center" prop="planTime" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="责任部门" align="center" prop="deptName">
          <template slot-scope="scope">
            <el-tooltip class="ellipsis" effect="dark" :content="scope.row.deptName" placement="top-start"
                        popper-class="custom-tooltip">
              <span>{{ scope.row.deptName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="责任人" align="center" prop="responsiblePersonName"/>
        <el-table-column label="实际完成时间" align="center" prop="closingTime" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.closingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab === '2'" label="创建人" align="center" prop="createByName"/>
        <el-table-column v-if="activeTab === '2'" label="创建时间" align="center" prop="createTime"/>

        <el-table-column fixed="right" label="操作" align="center" class-name="hidden-column" min-width="80"
                         label-class-name="hidden-column">

          <template slot-scope="scope">
            <el-button v-if="scope.row.status == 2 || scope.row.status == 3" size="mini" type="text" icon="el-icon-view"
                       @click="handleEvaluation(scope.row)">评价
            </el-button>
            <el-button v-if="scope.row.status != 0" size="mini" type="text" icon="el-icon-view"
                       @click="handleView(scope.row)">查看
            </el-button>
            <el-button v-if="scope.row.status == 0" size="mini" type="text" icon="el-icon-edit"
                       @click="handleUpdate(scope.row)">编辑
            </el-button>
            <el-button v-if="scope.row.status == 0  || scope.row.status == 7" size="mini" type="text"
                       icon="el-icon-delete"
                       @click="handleDelete(scope.row,scope.$index + 1)">删除
            </el-button>

          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  :page-sizes="[10, 20, 50, 100, 200, 500, 1000]" @pagination="getList"/>
    </div>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData"/>

    <!-- 添加评价对话框 -->
    <el-dialog title="事项评价" :visible.sync="evaluationVisible" width="500px" append-to-body v-loading="evaluationLoading">
      <el-form ref="evaluationForm" :model="evaluationForm" :rules="evaluationRules">
        <el-form-item label="评分" prop="evaluationScore">
          <div class="rate-display-container" @mouseleave="hoverRating = null">
            <el-rate v-model="evaluationForm.evaluationScore" :max="5" allow-half :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                     @hover-change="handleHoverChange" @change="handleRateChange">
            </el-rate>
            <span class="score-text">{{ displayRating }}</span>
          </div>
        </el-form-item>
        <el-form-item label="评价内容" prop="evaluationContent">
          <el-input v-model="evaluationForm.evaluationContent" type="textarea" :rows="4" placeholder="请输入评价内容"
                    maxlength="200" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEvaluation">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMatters,
  listMatters,
  addMatters,
  updateMatters,
  delMatters,
  myTaskList,
  myRantList
} from '@/api/rant/matters'
import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import {rantStatusOption} from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { addEvaluative } from '@/api/rant/evaluative'

export default {
  name: 'MyRant',
  components: {SelectUser, StatusTag, Treeselect},
  dicts: ['rant_classify'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督办事项表格数据
      mattersList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: true,
      isHavaSave: true,
      isMultiple: false,
      // 责任部门列表
      deptOptions: [],
      planTimeRange: [],
      closingTimeRange: [],
      createTimeRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranterId: null,
        rantClassify: null,
        beginPlanTime: null,
        endPlanTime: null,
        deptId: null,
        responsiblePerson: null,
        rantContent: null,
        status: [],
        createBy: null,
        beginClosingTime: null,
        endClosingTime: null,
        beginCreateTime: null,
        endCreateTime: null,
        createBys: null,
      },
      createBys: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ranter: [
          {required: true, message: '吐槽人不能为空', trigger: 'blur'}
        ],
        rantClassify: [
          {required: true, message: '吐槽分类不能为空', trigger: 'blur'}
        ],
        planTime: [
          {required: true, message: '计划完成时间不能为空', trigger: 'blur'}
        ],
        responsiblePerson: [
          {required: true, message: '责任人不能为空', trigger: 'blur'}
        ],
        rantContent: [
          {required: true, message: '吐槽内容不能为空', trigger: 'blur'}
        ],
        solution: [
          {required: true, message: '解决方案不能为空', trigger: 'blur'}
        ]
      },
      evaluationVisible: false,
      evaluationLoading: false,
      evaluationForm: {
        id: null,
        score: 0,
        evaluationScore: null,
        evaluationContent: ''
      },
      hoverRating: null,
      evaluationRules: {
        evaluationScore: [
          {
            required: true,
            message: '请选择评分',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === 0) {
                callback(new Error('请选择评分'))
              } else {
                callback()
              }
            }
          }
        ],
        evaluationContent: [
          { required: true, message: '请输入评价内容', trigger: 'blur' }
        ]
      },
      activeTab: '1'
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    },
    displayRating() {
      const value = this.hoverRating !== null ? this.hoverRating : this.evaluationForm.evaluationScore
      return (value !== null && value !== undefined && value > 0) ? `${(value * 2).toFixed(1)}分` : '0.0分'
    }
  },
  created() {
    this.getList()
  },
  activated() {
    console.log('activated-----------------')
    this.getList()
  },
  methods: {
    clearCreateBys() {
      this.queryParams.createBys = null;
      this.createBys = null;
    },
    handleTabClick(tab, event) {
      this.activeTab = tab.name;
      this.queryParams.isPerson  = this.activeTab;
      this.queryParams.pageNum = 1;
      this.getList();
    },
     /** 查看审批详情 */
     handleViewApproval(row) {
      this.$router.push({
        path: '/rant/myLookApproval',
        query: {
          id: row.id
        }
      })
    },
    /** 查询督办事项列表 */
    getList() {
      this.loading = true
      if (this.activeTab === '1') {
        this.queryParams.createBys = null
        this.createBys = null;
        this.createTimeRange = []
        this.queryParams.beginCreateTime = null
        this.queryParams.endCreateTime = null
      }
      myRantList(this.queryParams).then((response) => {
        this.mattersList = response.rows
        this.total = response.total
        this.loading = false
        this.$nextTick(() => {
          this.$refs.tableRef && this.$refs.tableRef.doLayout()
        })
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {

      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: "2",
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // 拆分时间范围
      if (this.planTimeRange && this.planTimeRange.length === 2 && this.activeTab === '2') {
        this.queryParams.beginPlanTime = this.planTimeRange[0];
        this.queryParams.endPlanTime = this.planTimeRange[1];
      } else {
        this.queryParams.beginPlanTime = null;
        this.queryParams.endPlanTime = null;
      }
      if (this.closingTimeRange && this.closingTimeRange.length === 2 && this.activeTab === '2')  {
        this.queryParams.beginClosingTime = this.closingTimeRange[0];
        this.queryParams.endClosingTime = this.closingTimeRange[1];
      } else {
        this.queryParams.beginClosingTime = null;
        this.queryParams.endClosingTime = null;
      }
      if (this.createTimeRange && this.createTimeRange.length === 2 && this.activeTab === '2') {
        this.queryParams.beginCreateTime = this.createTimeRange[0];
        this.queryParams.endCreateTime = this.createTimeRange[1];
      } else {
        this.queryParams.beginCreateTime = null;
        this.queryParams.endCreateTime = null;
      }

      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.responsiblePersonNameQuery = ''
      this.planTimeRange = [];
      this.closingTimeRange = [];
      this.createTimeRange = [];
      this.createBys = null;
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: '/rant/addRant',
      })
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push({
        path: '/rant/rantDetail',
        query: {
          id: row.id,
          type: "myRant",
        },
      })
    },
    /** 编辑按钮操作 */
    handleUpdate(row) {
      if (row.mattersType === '2') {
        this.$router.push({
          path: '/rant/addRant',
          query: {
            id: row.id,
            type: "update",
          },
        })
      } else {
        this.$router.push({
          path: "/rant/addMatter",
          query: {
            type: "edit",
            id: row.id,
            isHavaSave: true,
            deptId: row.deptId,
          },
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row, index) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除督办事项序号为"' + index + '"的数据项？')
        .then(function () {
          return delMatters(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'rant/matters/export',
        {
          ...this.queryParams
        },
        `matters_${new Date().getTime()}.xlsx`
      )
    },
    selectUserFun(type) {
      this.selectUserType = type
      this.isMultiple = type == "createBys";
      this.$refs.userRef.show()
    },
    selectUserData(data) {
      if (this.selectUserType == 'createBys') {
        this.createBys = data.map((item) => item.nickName).join(",");
        this.queryParams.createBys = data.map((item) => item.userName).join(",");
      } else if (this.selectUserType == 'responsibilityerQuery') {
        this.queryParams.responsiblePerson = data.userId
        this.responsiblePersonNameQuery = data.nickName
      }
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);
    },
    changeresponsibilityer() {
      if (this.responsiblePersonNameQuery == '' || this.responsiblePersonNameQuery == null) {
        this.queryParams.responsiblePerson = null
      }

    },
    selectDept(value) {
      this.form.deptId = value
      this.form.deptName = this.deptOptions.find(item => item.id === value)?.name
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    /** 评价按钮操作 */
    handleEvaluation(row) {
      this.evaluationForm = {
        rantMattersId: row.id,
        score: 0,
        evaluationScore: null,
        evaluationContent: ''
      }
      this.evaluationVisible = true
      // 清除之前的验证状态
      this.$nextTick(() => {
        if (this.$refs.evaluationForm) {
          this.$refs.evaluationForm.clearValidate()
        }
      })
    },
    // 新增悬停事件处理
    handleHoverChange(value) {
      this.hoverRating = value
    },
    // 处理评分改变事件
    handleRateChange(value) {
      // 手动触发表单验证以清除错误提示
      this.$nextTick(() => {
        if (this.$refs.evaluationForm) {
          this.$refs.evaluationForm.validateField('evaluationScore')
        }
      })
    },
    /** 提交评价 */
    submitEvaluation() {
      this.$refs.evaluationForm.validate(valid => {
        if (valid) {
          // 转换评分值（前端保存0-5分，允许0.5分）
          this.evaluationForm.score = this.evaluationForm.evaluationScore * 2 // 转换为10分制
          const payload = {
            ...this.evaluationForm,
          };
          this.evaluationLoading = true
          addEvaluative(payload).then(response => {
            this.$modal.msgSuccess("评价成功")
            this.evaluationVisible = false
            this.getList()
          })
            .finally(() => {
              this.evaluationLoading = false
            })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.matters-type-box .el-tag) {
  display: inline-block;
  width: 70px;
  text-align: center;
}

.matters-type-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
}

:deep(.el-table__cell .cell) {
  display: flex;
  justify-content: center;
}

.rate-display-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-text {
  color: #606266;
  font-size: 14px;
  min-width: 60px;
}
.clickable-progress {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
}

.clickable-progress:hover {
    color: #66b1ff;
}
</style>
