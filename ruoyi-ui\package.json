{"name": "ruoyi", "version": "3.4.0", "description": "智信管理系统", "author": "智信", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "dev2": "vue-cli-service serve", "build:dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode development", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:prod-node-16": "vue-cli-service build", "build:prod-hash": "vue-cli-service build --mode production --modern --dest dist --publicPath /your-path/ --hash", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@flyfish-group/file-viewer": "^1.0.4", "@pansy/vue-watermark": "^1.2.0", "@pansy/watermark": "^2.3.0", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.19.1", "dayjs": "^1.11.13", "echarts": "4.9.0", "element-ui": "2.15.6", "file-saver": "2.0.5", "file-viewer3": "^1.0.8", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "lodash": "^4.17.21", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "^1.10.2", "uuidv4": "^6.2.13", "vant": "^2.13.6", "vue": "^2.7.16", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.6", "vue-doc-preview": "^0.3.2", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "watermark-js-plus": "^1.5.7"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.0.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}