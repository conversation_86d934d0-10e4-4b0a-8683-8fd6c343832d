@import '~@/views/projects/styles/projects.scss';
.sales-content {
  .card {
    padding: 1.3125rem; // 21px
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.6875rem; // 11px

    .flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 4rem; // 64px
      padding: 1.3125rem 0 2rem; // 21px 0px 32px
    }

    .flex-item {
      flex: 1 1 calc(50% - 2rem); // 32px
      box-sizing: border-box;
    }
  }

  .progress-item-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem; // 14px
    color: #222222;
    line-height: 1.375rem; // 22px
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .target-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem; // 14px
    color: #222222;
    line-height: 1.375rem; // 22px
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 2.875rem; // 46px
  }

  .progress-timeline {
    .time-line-wrapper {
      display: flex;
      gap: 1.1875rem; // 19px
      .label {
        margin: 1.25rem 0 4.375rem; // 20px 0 70px
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 0.875rem; // 14px
        color: #222222;
        line-height: 1rem; // 16px
      }
    }

    .timeline {
      flex: 1;
      position: relative;
      height: 1.25rem; // 20px
      background: rgba(0, 106, 255, 0.3);
      margin: 1.25rem 0 4.375rem; // 20px 0 70px
      &.number {
        &::before {
          content: '1';
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.875rem; // 14px
          color: #666666;
          line-height: 1rem; // 16px
          position: absolute;
          left: 0;
          top: 1.5625rem; // 25px
        }

        &::after {
          content: '12';
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.875rem; // 14px
          color: #666666;
          line-height: 1rem; // 16px
          position: absolute;
          right: 0;
          top: 1.5625rem; // 25px
        }
      }

      &.number-de {
        &::before {
          content: var(--timeline-before-content);
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.875rem; // 14px
          color: #666666;
          line-height: 1rem; // 16px
          position: absolute;
          left: 0;
          top: 1.5625rem; // 25px
          z-index: 100;
        }

        &::after {
          content: var(--timeline-after-content);
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.875rem; // 14px
          color: #666666;
          line-height: 1rem; // 16px
          position: absolute;
          right: 0;
          top: 1.5625rem; // 25px
          z-index: 100;
        }
      }
    }

    .marker {
      position: absolute;
      top: -0.3125rem; // -5px
      cursor: pointer;
      width: 0.625rem; // 10px
      height: 1.875rem; // 30px
      border-radius: 100px 100px 100px 100px;
    }

    .blue {
      background: #006AFF;
    }

    .yellow {
      background: #FF974C;
    }

    .down {
      position: absolute;
      left: 0; // 30%
      bottom: -0.375rem; // -6px
      width: 0;
      height: 0;
      border-left: 0.375rem solid transparent; // 6px
      border-right: 0.375rem solid transparent; // 6px
      border-top: 0.375rem solid #006AFF; // 6px
      top: -0.625rem; // -10px
    }

    .up {
      position: absolute;
      left: 0; // 30%
      top: 2.5625rem; // 25px
      width: 0;
      height: 0;
      border-left: 0.375rem solid transparent; // 6px
      border-right: 0.375rem solid transparent; // 6px
      border-bottom: 0.375rem solid #FF974C; // 6px
    }

    .time-progress {
      .tooltip-progress.blue {
        // width: 10.625rem; // 170px
        top: -4.25rem !important; // -68px
      }
    }

    .tooltip-progress {
      //z-index: 100;
      position: absolute;
      // left: 0;
      // width: 10.625rem; // 170px
      background-color: #fff;
      // border: 0.125rem solid #ccc;
      padding: 0.3125rem; // 5px
      box-sizing: border-box;
      //border-radius: 0rem; // 5px
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 0.875rem; // 14px
      color: #FFFFFF;
      line-height: 1rem; // 16px
      text-align: center;
      font-style: normal;
      text-transform: none;

      &.blue {
        top: -2.1875rem; // -35px
        background: #006AFF;
        border-radius: 0.25rem 0.25rem 0 0; // 4px
      }

      &.sign-tooltip-1,
      &.hk-tooltip-1 {
        top: -4.25rem !important; // -35px
      }

      &.yellow {
        top: 2.875rem; // 30px
        background: #FF974C;
        border-radius: 0 0 0.25rem 0.25rem; // 4px
      }
    }

    .details {
      font-size: 0.875rem; // 14px
      color: #333;

      td {
        padding: 0.3rem 0.3125rem; // 5px
      }
      .pl-date{
        padding-left: 2.5rem;
      }
      //.detail-grid{
      //  display: flex;
      //  flex-wrap: wrap;
      //  .grid-item{
      //    .progress-label,
      //    .progress-text{
      //      white-space: nowrap;
      //    }
      //  }
      //}
    }
  }
  .sales {
    .progress-item-content-item {
      display: flex;
      justify-content: center;

      .progress-item-content-chart {
        display: flex;
        flex-direction: column;
        align-items: center;

        .chart {
          width: 8.75rem; // 140px
          height: 8.75rem; // 140px
        }

        .progress-item-content-detail {
          margin-top: 1.25rem; // 20px
          display: flex;
          justify-content: space-around;
          gap: 1rem; // 16px

          .progress-item-content-detail-item {
            .progress-item-content-detail-item-title {
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 1.25rem; // 20px
              color: #006AFF;
              line-height: 1.375rem; // 22px
              text-align: center;
              font-style: normal;
              text-transform: none;
              margin-bottom: 0.5rem; // 8px
            }

            .progress-item-content-detail-item-value {
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              font-size: 0.75rem; // 12px
              color: #666666;
              line-height: 1.375rem; // 22px
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
          }
        }
      }

      .progress-item-content-data {
        table {
          width: 100%;

          tr {
            margin-bottom: 0.5rem; // 8px
            padding: 0 0.5rem; // 8px
          }

          th {
            padding: 0 3.125rem; // 50px
          }

          .progress-label {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 0.875rem; // 14px
            color: #000000;
            line-height: 2.1875rem; // 35px
            text-align: center;
            font-style: normal;
            text-transform: none;
            white-space: nowrap;
          }

          .progress-text {
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 1rem; // 16px
            color: #006AFF;
            line-height: 1.375rem; // 22px
            text-align: center;
            font-style: normal;
            text-transform: none;
            white-space: nowrap;
          }
        }
      }
    }

    .hk-item {
      .progress-item-content-detail-item-title,
      .progress-text {
        color: #FF974C !important;
      }
    }
  }

  .total-target {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem; // 14px
    color: #CC1414;
    line-height: 1.375rem; // 22px
    text-align: center;
    font-style: normal;
    text-transform: none;
    float: right;
    border-radius: 0;
    border: 0.125rem solid #CC1414;
    padding: 0 0.25rem; // 4px
  }

  .receiving-payments {
    width: 100%;
    display: flex;
    gap: 1rem; // 16px

    .card {
      width: 100%;
    }

    .basic-info-item {
      display: flex;
      justify-content: space-between;
      gap: 5.5rem; // 88px
    }

    .card-wrapper {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem; // 24px

      .data-card {
        flex: 1;
        padding: 1.5rem 0; // 24px
        box-sizing: border-box;
        box-shadow: 0 0.5rem 1.25rem 0 rgba(90, 204, 166, 0.5); // 8px 20px
        border-radius: 0.25rem; // 4px

        .card-label,
        .currency,
        .unit {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.125rem; // 18px
          color: #FFFFFF;
          line-height: 1rem; // 16px
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

        .card-label {
          margin-bottom: 0.5rem; // 8px
          font-size: 1.125rem; // 18px
        }

        .card-value {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.125rem; // 18px
          color: #FFFFFF;
          line-height: 1rem; // 16px
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }

  .circle-chart {
    width: 6.5625rem; // 105px
    height: 6.5625rem; // 105px
  }

  .chart-size {
    width: 6.5625rem; // 105px
    height: 6.5625rem; // 105px
  }

  .trend {
    padding: 1rem; // 16px
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.5rem; // 8px

    .flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0;

      .flex-item {
        min-height: 13.875rem; // 222px
        width: 50%;
      }
    }
  }

  .chart-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem; // 14px
    color: #222222;
    line-height: 1.375rem; // 22px
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .table-container {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.5rem; // 8px
    padding: 1.125rem 1rem 1rem; // 18px 16px 16px
  }

  .cost {
    &.flex-container {
      display: flex;
      gap: 1rem; // 16px

      .flex-item {
        &:first-child {
          flex: 1;
        }

        &:last-child {
          flex: 2.7;
        }

        &.chart {
          flex-shrink: 0;
          width: 25rem;
          height: 22.75rem; // 364px
        }

        .chart-size-220 {
          width: 25rem;
          //width: 16.25rem;
          //height: 16.25rem;
          height: 31.25rem;
        }
      }
    }
  }

  .trend-chart {
    height: 13.875rem; // 222px
  }

  .flex-row-container {
    display: flex;
    justify-content: center;
  }






  .vertical-tabs {
    height: fit-content;
    flex-shrink: 0;
    flex-grow: 0;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    // border-radius: 0.25rem; // 4px
    overflow: hidden;
    border: 0.125rem solid #CC1414;
    box-sizing: border-box;
  }

  .tab {
    // 8px 6px
    text-align: center;
    cursor: pointer;
    color: white;
    writing-mode: vertical-rl;
    transition: background-color 0.3s;

    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 0.75rem; // 12px
    color: #222222;
    //line-height: 1.125rem; // 18px
    font-style: normal;
    text-transform: none;
    min-width: 1.5rem;
    .tab-text {
      text-align: center;
      padding: 0.5rem 0.375rem;
    }
  }

  .tab.active {
    background: #CC1414;
    // border-radius: 0.25rem; // 4px
    // font-family: PingFang SC, PingFang SC;
    // font-weight: 400;
    // font-size: 0.75rem; // 12px
    color: #FCFDFD;
    // line-height: 1.125rem; // 18px
    // text-align: center;
    // font-style: normal;
    // text-transform: none;
  }
}


.sign-chart-block {
  display: flex;
  align-items: center;
  flex: 1;
}

.details-flex-container {
  display: flex;
  justify-content: space-between;

  .flex-column {
    flex: 1;
    display: flex;
    flex-direction: column;

    .flex-item {
      flex: 1;

      .card {
        border-radius: 0.25rem;
        padding: 1rem;
        height: 100%;

        .card-title {
        }

        .card-content {
          font-size: 1.25rem;
          color: #666;
          line-height: 1.5;
        }
      }
    }
  }
}
