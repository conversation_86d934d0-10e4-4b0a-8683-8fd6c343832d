<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="计划模版" prop="templateId">
        <el-select v-model="queryParams.templateId" placeholder="请选择计划模版" clearable @change="handleQuery">
          <el-option
            v-for="template in allTemplateList"
            :key="template.id"
            :value="template.id"
            :label="template.templateName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="节点CODE" prop="nodeCode">
        <el-input
          v-model="queryParams.nodeCode"
          placeholder="请输入节点CODE"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['plan:nodeTemplate:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['plan:nodeTemplate:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plan:nodeTemplate:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:nodeTemplate:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="nodeTemplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column label="节点名称" align="center" prop="nodeName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.nodeName)">{{ scope.row.nodeName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点CODE" align="center" prop="nodeCode">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.nodeCode)">{{ scope.row.nodeCode || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点描述" align="center" prop="nodeDesc">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.nodeDesc)">{{ scope.row.nodeDesc || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成标准" align="center" prop="completeCriteria" />
      <el-table-column label="标准工期(天)" align="center" prop="durationNum" />
      <el-table-column label="责任部门" align="center" prop="department">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.department)">{{ scope.row.department || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否可删除" align="center" prop="isDel">
        <template slot-scope="scope">
          <StatusTag :status="scope.row.isDel" :options="isDelOption"></StatusTag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plan:nodeTemplate:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['plan:nodeTemplate:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计划-节点模版对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1100px" append-to-body>
      <el-row v-if="optType !== 'update'">
        <el-col>
          <el-form :rules="rules" >
            <el-form-item label="计划模版" prop="id">
              <el-select v-model="form.id" placeholder="请选择计划模版" clearable @change="handleNodeList">
                <el-option
                  v-for="template in allTemplateList"
                  :key="template.id"
                  :value="template.id"
                  :label="template.templateName"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <NodeForm ref="updateNodeRef" :existNodeList="existNodeList" :templateId="form.id" :optType="optType" :loading="planNodeSubmitLoading"></NodeForm>
      <div slot="footer" class="dialog-footer" v-if="optType !== 'update'">
        <el-button type="" @click="saveNodeForm">保存</el-button>
        <el-button type="primary" @click="submitNodeForm">提交</el-button>
        <el-button type="" @click="cancleNodeForm">取消</el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-if="optType === 'update'">
        <el-button type="primary" @click="updateNodeForm">确定</el-button>
        <el-button type="" @click="cancleNodeForm">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNodeTemplate, getNodeTemplate, delNodeTemplate, addNodeTemplate, updateNodeTemplate } from "@/api/plan/nodeTemplate";
import {
  saveTemplate,
  submitTemplate,
  listNode, // 查询模板下的所有节点
} from "@/api/plan/template";
import NodeForm from '@/views/plan/components/NodeForm/index.vue'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import {isDelOption, isEditOption} from '@/views/plan/constant'
export default {
  name: "NodeTemplate",
  components: {
    NodeForm,
    StatusTag
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      nodeNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点模版表格数据
      nodeTemplateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        templateId: null,
        nodeStage: null,
        nodeCode: null,
        nodeName: null,
        nodeLevel: null,
        nodeIndex: null,
        completeCriteria: null,
        nodeDesc: null,
        outcomeDocumentsDesc: null,
        department: null,
        isEdit: null,
        tenantId: null,
      },
      // 表单参数
      form: {
        id: '', // 模板id
        planNodeTemplateList: [], // 节点列表
      },
      // 表单校验
      rules: {
        id: [
          { required: true, message: "计划模版不能为空", trigger: "blur" }
        ],
      },
      optType: null,
      existNodeList: [],
      planNodeSubmitLoading: false,
    };
  },
  computed: {
    allTemplateList() {
      return this.$store.state.plan.allTemplateList;
    },
    isDelOption: () => isDelOption,
    isEditOption: () => isEditOption,
  },
  created() {
    // 当组件创建时获取数据
    this.$store.dispatch('plan/fetchAllTemplateData');
    this.getList();
  },
  methods: {
    // 计划节点保存
    async saveNodeForm() {
      this.planNodeSubmitLoading = true;
      try {
        let response = await saveTemplate(this.$refs.updateNodeRef.getUpdateNodeList());
        if(response.code === 200){
          this.planNodeSubmitLoading = false;
          this.open = false;
          this.$message.success('添加节点成功');
          this.queryParams.pageNum = 1;
          this.getList();
        }
        else {
          this.planNodeSubmitLoading = false;
          this.$message.error('添加节点失败');
        }
        // Handle success

      } catch (error) {
        this.$message.error('添加节点失败');
        // Handle error
        this.planNodeSubmitLoading = false;
      }
    },
    async updateNodeForm(){
      this.planNodeSubmitLoading = true;
      try {
        let response = await updateNodeTemplate(this.$refs.updateNodeRef.getUpdateNodeList()?.planNodeTemplateList[0]);
        if(response.code === 200){
          this.planNodeSubmitLoading = false;
          this.open = false;
          this.$message.success('修改节点成功');
          this.queryParams.pageNum = 1;
          this.getList();
        }
        else {
          this.planNodeSubmitLoading = false;
          this.$message.error('修改节点失败');
        }


        // Handle success
      } catch (error) {
        this.$message.error('修改节点失败');
        // Handle error
        this.planNodeSubmitLoading = false;
      }
    },
    // 计划节点提交
    async submitNodeForm(){
      // nodeIndex 节点顺序
      this.planNodeSubmitLoading = true;
      let data = this.$refs.updateNodeRef.getUpdateNodeList();
      // 对节点输入进行校验
      let valid = true;
      let missingFields = [];
      data.planNodeTemplateList.forEach(item => {
        if (!item.nodeName) {
          missingFields.push("节点名称");
          valid = false;
        }
        if (!item.nodeDesc) {
          missingFields.push("节点描述");
          valid = false;
        }
        if (!item.completeCriteria) {
          missingFields.push("完成标准");
          valid = false;
        }
        if (!item.department) {
          missingFields.push("责任部门");
          valid = false;
        }
        if (item.isDel === "" || item.isDel === undefined) {
          missingFields.push("是否可删除");
          valid = false;
        }
      });

      if(valid){
        try {
          let response = await submitTemplate(this.$refs.updateNodeRef.getUpdateNodeList());
          if(response.code === 200){
            this.planNodeSubmitLoading = false;
            this.open = false;
            this.$message.success('提交节点成功');
            this.queryParams.pageNum = 1;
            this.getList();
          }
          else {
            this.planNodeSubmitLoading = false;
            this.$message.error('提交节点失败');
          }


          // Handle success
        } catch (error) {
          this.$message.error('提交节点失败');
          // Handle error
          this.planNodeSubmitLoading = false;
        }
      }
      else{
        this.$modal.msgWarning(missingFields.join("、") + "必填");
        this.planNodeSubmitLoading = false;
      }

    },
    // 取消计划节点
    cancleNodeForm(){
      this.open = false;
      // this.templateObjOpen = false;
    },
    /** 查询计划-节点模版列表 */
    getList() {
      this.loading = true;
      listNodeTemplate(this.queryParams).then(response => {
        this.nodeTemplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        templateId: null,
        nodeStage: null,
        nodeCode: null,
        nodeName: null,
        nodeLevel: null,
        nodeIndex: null,
        completeCriteria: null,
        nodeDesc: null,
        outcomeDocumentsDesc: null,
        department: null,
        isEdit: null,
        tenantId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleNodeList(){
      this.loading = true;
      listNode({
        templateId: this.form.id,
      }).then(response => {
        this.existNodeList = response.rows;
        this.loading = false;
        if(response.rows?.length === 0){
          this.$message.warning('该模板下暂无节点');
        }
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.nodeNames = selection.map(item => item.nodeName)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.existNodeList = [];
      this.optType = 'add';
      this.reset();
      this.open = true;
      this.title = "添加计划节点";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      this.optType = 'update';
      getNodeTemplate(id).then(response => {
        this.form = response.data;
        this.existNodeList =  [{...response.data}];
        this.open = true;
        this.title = "修改计划节点";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNodeTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNodeTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const nodeNames = row.nodeName || this.nodeNames;
      this.$modal.confirm('是否确认删除计划-节点名称"' + nodeNames + '"的数据项？').then(function() {
        return delNodeTemplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('plan/nodeTemplate/export', {
        ...this.queryParams
      }, `nodeTemplate_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped lang="scss">
.mrb-15{
  margin-bottom: 15px;
}
.tip{
  margin-top: 15px;
}
</style>
