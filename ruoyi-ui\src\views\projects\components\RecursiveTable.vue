<template>
  <el-table
    :data="tableData"
    :show-header="isRoot"
    style="width: 100%"
    :border="true"
    :header-cell-style="{
        background: '#E1EFFD',
        color: '#666666',
        fontWeight: '400',
        textAlign: 'center',
        padding: '8px 0'
    }"
    :cell-style="{
        textAlign: 'center',
        borderColor: '#E1EFFD'
    }"
    >
    <el-table-column type="expand" v-if="hasChildren">
      <template slot-scope="props">
        <recursive-table
          :table-data="props.row.childList"
          :is-root="false"
          class="nested-table" />
      </template>
    </el-table-column>
    <el-table-column
      prop="subjectName"
      label="科目名称"
      width="180">
      <template slot-scope="scope">
        <span :style="{ paddingLeft: !isRoot ? '20px' : '0' }">{{ scope.row.subjectName }}</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="targetAmount"
      label="目标版">
      <template slot-scope="scope">
        {{ formatAmount(scope.row.targetAmount) }}
      </template>
    </el-table-column>
    <el-table-column
      prop="dynamicAmount"
      label="动态版">
      <template slot-scope="scope">
        {{ formatAmount(scope.row.dynamicAmount) }}
      </template>
    </el-table-column>
    <el-table-column
      prop="diffAmount"
      label="差额">
      <template slot-scope="scope">
        {{ formatAmount(scope.row.diffAmount) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'RecursiveTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    isRoot: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    hasChildren() {
      return this.tableData.some(item => item.childList && item.childList.length > 0)
    }
  },
  methods: {
    formatAmount(value) {
      return value ? Number(value).toLocaleString() : '0'
    }
  }
}
</script>

<style lang="scss" scoped>
.nested-table {
  margin-left: -10px;
  margin-right: -10px;

  :deep(.el-table__expanded-cell) {
    padding: 0 30px;
  }
}

:deep(.el-table__expand-icon) {
  transform: rotate(0deg);
  &.el-table__expand-icon--expanded {
    transform: rotate(90deg);
  }
}

:deep(.el-table) {
  &::before {
    display: none;
  }

  .el-table__body-wrapper {
    background-color: transparent;
  }

  .el-table__row {
    background-color: transparent !important;
  }
}
</style>
