<script>
import {importNodeTemplateFile} from "@/api/plan/template";
import {uuid} from "uuidv4";
import Template from '@/views/plan/template/index.vue'
import {isDelOption, isEditOption} from '@/views/plan/constant'
export default {
  components: { Template},
  dicts: ['resp_department' /*责任部门*/],
  props: {
    existNodeList: {
      type: Array,
      default: () => []
    },
    templateId: {
      type: Number,
      default: null
    },
    optType: {
      type: String,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data: function (){
    return {
      // 遮罩层
      planNodeSubmitLoading: false,
      nodeSelection: [], // 选择的节点
      selectDepartIds: [], // 选择的责任部门id数组
      updateRow: null, // 暂存要修改的阶段对象
      updateIndex: null,
      rules: {
        nodeName: [
          { required: true, message: '请输入节点名称', trigger: 'blur' },
        ],
        nodeDesc: [
          { required: true, message: '请输入节点描述', trigger: 'blur' },
        ],
        completeCriteria: [
          { required: true, message: '请输入完成标准', trigger: 'blur' },
        ],
        department: [
          { required: true, message: '请选择责任部门', trigger: 'blur' },
        ],
      },
      inputFileLoading: false,
      isFormSubmitted: false,
      importType: 'over', // over: 覆盖导入， add: 增量导入
      // planNodeTemplateList: [], // 计划节点模板列表
    }

  },
  computed: {
    planNodeTemplateList: function (){
      this.localNodeList = this.deepCopy(this.existNodeList);
      return this.localNodeList.map(item => {
        let newItem = Object.assign({}, item);
        // newItem.departmentName = newItem.department ? newItem.department.split(',') : [];
        newItem.department = newItem.department ? newItem.department.split(',') : [];
        newItem.uuid = uuid();
        return newItem;
      });
    },
    isDelOption: () => isDelOption,
    isEditOption: () => isEditOption,
    //转换成数组，选择部门时候传过去
    /*selectDepartIds: function () {
        console.log(this.form, this.form.department)
      return this.form && this.form.department && this.form.department.split(',')
    }*/
  },
  methods: {
    deepCopy(arr) {
      return JSON.parse(JSON.stringify(arr));
    },
    renderHeader(h, { column, $index }) {
      return h('span', [
        h('span', {}, column.label),
        h('i', { class: 'el-icon-star-on required-icon' }) // 这里可以根据实际情况调整图标类名
      ]);
    },
    setIsFormSubmitted(val){
      this.isFormSubmitted = val;
    },
    shouldHighlight(row, columnName) {
      // 基于校验逻辑判断是否需要高亮
      // 例如，如果某个字段为空，则返回 true
      return this.isFormSubmitted && !row[columnName];
    },
    selectDepartFun (row, index) {
      this.updateRow = row;
      this.updateIndex = index;
      // this.selectDepartIds = this.selectDepartIds.splice(0, this.selectDepartIds.length, ...row.department.split(','));
      this.departmentArr = row.department ? row.department.split(',') : [];
      this.departmentNameArr = row.departmentName ?  row.departmentName.split(',') : [];
      this.selectDepartIds = this.selectDepartIds.splice(0, this.selectDepartIds.length, ...this.departmentArr.map((item, index) => {
        return {
          department: item,
          departName: this.departmentNameArr[index]
        }
      }));
      this.$refs.departRef.show()
    },
    selectDepartData (data) {
      this.updateRow.department = data.map(item => item.department).join(',');
      this.updateRow.departmentName = data.map(item => item.departName).join(',');
      this.planNodeTemplateList.splice(this.updateIndex, 1, this.updateRow);
    },
    getUpdateNodeList(){
      this.localPlanNodeTemplateList = this.deepCopy(this.planNodeTemplateList);
      for(let i = 0; i < this.planNodeTemplateList.length; i++){
        this.localPlanNodeTemplateList[i].nodeIndex = i + 1;
        this.localPlanNodeTemplateList[i].department = this.planNodeTemplateList[i].department.join(',');
        // this.localPlanNodeTemplateList[i].departmentName = this.planNodeTemplateList[i].department.join(',');
      }
      return {
        id: this.templateId,
        planNodeTemplateList: this.localPlanNodeTemplateList
      };
    },
    handleNodeSelectionChange(selection){
      this.nodeSelection = selection
    },
    // 设置计划节点是否有效
    setStatusHandle(row){
      if(row.delFlag === '0'){
        row.delFlag = '2';
      }else{
        row.delFlag = '0';
      }
    },
    moveUp(index) {
      if (index > 0) {
        const row = this.planNodeTemplateList.splice(index, 1)[0];
        this.planNodeTemplateList.splice(index - 1, 0, row);
      }
    },
    moveDown(index) {
      if (index < this.planNodeTemplateList.length - 1) {
        const row = this.planNodeTemplateList.splice(index, 1)[0];
        this.planNodeTemplateList.splice(index + 1, 0, row);
      }
    },
    departNameHandle(value){
      if(!!value){
        return value.split(',')
      }
      else{
        return []
      }
    },
    // 添加节点
    addNodeHandle(){
      if(!this.templateId) {
        this.$message.warning('请选择计划模板');
        return;
      }
      this.planNodeTemplateList.push({
        templateId: this.templateId,
        nodeName: '', // 节点名称
        nodeDesc: '', // 节点描述
        completeCriteria: '', // 完成标准
        durationNum: '', // 标准工期
        department: [], // 责任部门
        // departmentName: [], // 责任部门
        delFlag: '0', // 0有效 2无效
        isDel: 0, // 是否可删除 0可删除 1不可删除
        uuid: uuid()
      });
    },
    sectionNodeDeleteHandle(){ // 批量删除节点
      // 创建一个集合来存储 nodeSelection 中所有对象的 uuid
     /* const selectionUuids = new Set(this.nodeSelection.map(node => node.uuid));
      // 过滤掉 planNodeTemplateList 中已被选择的对象
      this.planNodeTemplateList = this.planNodeTemplateList.filter(node => !selectionUuids.has(node.uuid));*/

      // 假设 planNodeTemplateList 和 nodeSelection 是已经定义好的数组
      const toDeleteIndices = [];

      // 找出所有需要删除的索引
      this.planNodeTemplateList.forEach((item, index) => {
        if (this.nodeSelection.some(selection => selection.uuid === item.uuid)) {
          toDeleteIndices.push(index);
        }
      });

      // 按索引删除，从后往前删除以避免影响之前的索引
      for (let i = toDeleteIndices.length - 1; i >= 0; i--) {
        this.planNodeTemplateList.splice(toDeleteIndices[i], 1);
      }
    },
    // 删除单个节点
    delNodeHandle(index){ // table中操作直接删除节点，根据索引删除
      this.planNodeTemplateList.splice(index, 1);
      for(let i = 0; i < this.planNodeTemplateList.length; i++){
        this.planNodeTemplateList[i].nodeLevel = i + 1;
      }
    },
    // 导出节点
    async exportNodeFile(){
      this.download('plan/nodeTemplate/export', { // 是否需要传参
        templateId: this.templateId
      }, `template_${new Date().getTime()}.xlsx`)
    },
    // 导入节点
    importNodeFile(type){
      this.importType = type;
      this.$refs.fileInput.click(); // 触发文件输入的点击事件
    },
    handleFileChange(event) {
      this.inputFileLoading = true;
      const files = event.target.files;
      const formData = new FormData();
      formData.append('file', files[0]);
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null;
      importNodeTemplateFile(formData).then(response => {
        if(!!response.data){
          for(let item of response.data){
            item.department = !!item.department ?   item.department?.split(',') : [];
          }
        }
        if(this.importType === 'over'){
          this.planNodeTemplateList.splice(0, this.planNodeTemplateList.length, ...response.data);
        }else{
          this.planNodeTemplateList.push(...response.data);
        }
        this.inputFileLoading = false;
      }).catch(() => {
        this.inputFileLoading = false;
      });
    },
    handleInput(row) {
      let inputVal = row.durationNum;
      if (!isNaN(+inputVal) && inputVal.trim() !== '') {
        // 输入的是一个有效数字
      } else {
        // 输入的不是一个有效数字
        this.$message.warning('请输入有效数字');
      }
    },
    receiveData (data, row) {
      console.log('data--------', data);
      row.department = data;
    },

  }
}

</script>

<template>
  <div>
    <el-row class="mrb-15" v-if="optType !== 'update'">
      <el-col>
        <el-button type="success" plain icon="el-icon-plus" size="mini" @click="addNodeHandle">添加</el-button>
        <el-button type="danger" plain icon="el-icon-minus" size="mini"  @click="sectionNodeDeleteHandle">删除</el-button>
        <el-button type="primary" plain icon="el-icon-download" size="mini"  @click="exportNodeFile">导出</el-button>
        <el-button type="primary" plain icon="el-icon-upload" size="mini"  @click="importNodeFile('over')">覆盖导入</el-button>
        <el-button type="primary" plain icon="el-icon-upload" size="mini"  @click="importNodeFile('add')">增量导入</el-button>
      </el-col>
    </el-row>
    <el-form ref="form" label-width="80px" :rules="rules">
      <el-table :data="planNodeTemplateList" @selection-change="handleNodeSelectionChange" v-loading="loading || inputFileLoading" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="nodeName" label="节点名称" :render-header="renderHeader">
          <template slot-scope="scope">
              <el-input v-model="scope.row.nodeName" :class="{ 'highlight': shouldHighlight(scope.row, 'nodeName') }"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="nodeDesc" label="节点描述" :render-header="renderHeader">
          <template slot-scope="scope">
              <el-input v-model="scope.row.nodeDesc" :class="{ 'highlight': shouldHighlight(scope.row, 'nodeDesc') }"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="completeCriteria" label="完成标准" :render-header="renderHeader">
          <template slot-scope="scope">
              <el-input v-model="scope.row.completeCriteria" :class="{ 'highlight': shouldHighlight(scope.row, 'completeCriteria') }"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="durationNum" label="标准工期(天)" :render-header="renderHeader">
          <template slot-scope="scope">
            <el-input v-model="scope.row.durationNum" @input="handleInput(scope.row)" clearable></el-input>
          </template>
        </el-table-column>
<!--        <el-table-column prop="departmentName" label="责任部门" width="150" :render-header="renderHeader">
          <template slot-scope="scope">
            <section class="flex-align-center" :class="{ 'highlight': shouldHighlight(scope.row, 'completeCriteria') }">
              <div class="mr5">
                <div v-for="name in departNameHandle(scope.row.departmentName)">{{name}}</div>
              </div>
              <span class="dialog-select" @click="selectDepartFun(scope.row, scope.$index)">
                {{departNameHandle(scope.row.departmentName).length === 0 ? '请选择' : ''}}
              <i class="el-icon-search"></i>
              </span>
            </section>
          </template>
        </el-table-column>-->
        <el-table-column prop="department" label="责任部门" width="150" :render-header="renderHeader">
          <template slot-scope="scope">
            <el-select clearable multiple v-model="scope.row.department" placeholder="请选择责任部门">
              <el-option v-for="dict in dict.type.resp_department"
               :key="dict.value"
               :label="dict.label"
               :value="dict.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="isDel" label="是否可删除" :render-header="renderHeader">
          <template slot-scope="scope">
            <el-select v-model="scope.row.isDel" clearable>
              <el-option v-for="option in isDelOption" :key="option.value" :label="option.label" :value="option.value"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="optType !== 'update'">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-delete" @click="delNodeHandle(scope.$index)">删除</el-button>
            <el-button @click="moveUp(scope.$index)" style="font-weight: bold;" type="text" size="small" icon="el-icon-top custom-bold" v-if="scope.$index !== 0"></el-button>
            <el-button @click="moveDown(scope.$index)" type="text" size="small" icon="el-icon-bottom custom-bold" v-if="scope.$index < (planNodeTemplateList.length - 1)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 隐藏的文件输入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none;"
      @change="handleFileChange"
    />
  </div>
</template>

<style scoped lang="scss">
.highlight {
  background-color: yellow; /* 或者你选择的高亮颜色 */
}
.mrb-15{
  margin-bottom: 15px;
}
.tip{
  margin-top: 15px;
}
</style>
