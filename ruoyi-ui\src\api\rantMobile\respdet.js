import request from '@/utils/request'
// import request from '@/utils/request-rant-mobile'
// 查询责任人责任部门分管领导关系列表
export function listRespdet(query) {
  return request({
    url: '/rant/respdet/list',
    method: 'get',
    params: query
  })
}

// 查询责任人责任部门分管领导关系详细
export function getRespdet(id) {
  return request({
    url: '/rant/respdet/' + id,
    method: 'get'
  })
}

// 新增责任人责任部门分管领导关系
export function addRespdet(data) {
  return request({
    url: '/rant/respdet',
    method: 'post',
    data: data
  })
}

// 修改责任人责任部门分管领导关系
export function updateRespdet(data) {
  return request({
    url: '/rant/respdet',
    method: 'put',
    data: data
  })
}

// 删除责任人责任部门分管领导关系
export function delRespdet(id) {
  return request({
    url: '/rant/respdet/' + id,
    method: 'delete'
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}
