<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24" class="right-container">
        <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="68px">
          <el-row style="width: 100%;" :gutter="20">
            <el-col :span="6">
              <el-form-item label="人员选择" prop="userCode">
                <el-select
                  @focus="handleOpenUserDialog"
                  v-model="queryParams.userCode"
                  multiple
                  placeholder="请选择"
                  :popper-append-to-body="false"
                  :popper-class="'select-hidden-dropdown'"
                >
                  <el-option v-for="item in reportReviewDetails" :key="item.userName" :label="item.nickName"
                             :value="item.userName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="填报状态" prop="approveStatus">
                <el-select
                  v-model="queryParams.approveStatus"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="(label, value) in approveStatusFilter"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="填报周期" prop="year">
                <custom-week-picker @change="handleDateRangeChange" ref="weekPicker"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              type="success"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['weekly:group:exportExcel']"
            >导出EXCEL
            </el-button>
            <el-button
              type="primary"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExportPdf"
              v-hasPermi="['weekly:group:exportPdf']"
            >导出PDF
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="userList" class="dept-list">
          <el-table-column align="center" label="序号" prop="" width="60">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="年份" align="center" key="year" prop="year" width="60"/>
          <el-table-column label="第几周" align="center" key="week" prop="week" width="80"/>
          <el-table-column label="填报周期" align="center"  prop="writeCycle"/>
          <el-table-column label="单位名称" align="center" key="unitName" prop="unitName"/>
          <el-table-column label="部门上级" align="center" key="deptParentName" prop="deptParentName"/>
          <el-table-column label="部门" align="center" key="deptName" prop="deptName" width="100"/>
          <el-table-column label="人员编码" align="center" key="userCode" prop="userCode" width="150"/>
          <el-table-column label="姓名" align="center" key="nickName" prop="nickName" width="100"/>
          <el-table-column label="填报状态" align="center" key="approveStatus" prop="approveStatus"
                           :show-overflow-tooltip="true" width="150"
          >
            <template slot-scope="scope">
              <span>
                {{ approveStatusFilter[scope.row.approveStatus] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="100"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.approveStatus === 1"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleDetail(scope.row)"
              >详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <select-user
      ref="userRef"
      :selectMultiple="true"
      @feedbackEmit="handleSelectUser"
    />
  </div>
</template>

<script>
// import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import SelectUser from "./components/selectUserStatictis.vue";
import { deptTree, groupList } from '@/api/weekly/reportInfo'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CustomWeekPicker from '../components/customWeekPicker.vue'
export default {
  name: 'weeklyStatictis',
  components: { Treeselect, SelectUser, CustomWeekPicker },
  data() {
    return {
      defaultExpandedKeys: [],
      // 遮罩层
      loading: true,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 用户导入参数
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        userCode: undefined, // 员工编码
        deptId: undefined, // 部门ID
        startDate: undefined, //
        endDate: undefined, //
        approveStatus: undefined
      },
      // 表单校验
      approveStatusFilter: {
        0: '未填报',
        1: '已填报'
      },
      dateRange: [],
      deptName: '',
      reportReviewDetails: [],
      selectUserIds: []
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleDateRangeChange({ startDate, endDate, startWeek, endWeek }) {
      console.log('选择的日期范围：', startDate, '至', endDate)
      if(startWeek, endWeek){
        this.queryParams.startDate = startWeek
        this.queryParams.endDate = endWeek
      }
      else{
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }
    },
    setDefaultExpandedKeys() {
      // 如果有数据，默认展开第一层的子节点（即第二层级）

      if (this.deptOptions && this.deptOptions.length > 0) {
        // 获取第一层节点的ID
        const firstLevelNode = this.deptOptions[0];
        if (firstLevelNode.children && firstLevelNode.children.length > 0) {
          // 将第一层节点的ID添加到默认展开的keys中
          this.defaultExpandedKeys = [firstLevelNode.id];
          console.log("defaultExpandedKeys", this.defaultExpandedKeys);
        }
      }
    },
    handleDateChange(val) {
      console.log('val---------', val)
      if(!!val){
        this.queryParams.startDate = val[0]
        this.queryParams.endDate = val[1]
      }
      else{
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      // listUser
      const data = JSON.parse(JSON.stringify(this.queryParams))
      console.log('queryParams---------', this.queryParams)
      data.userCode = this.queryParams.userCode?.join(',')
      groupList(data).then(response => {
          this.userList = response.rows
          this.total = response.total
          this.loading = false
        }
      )
      .catch(error => {
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      deptTree().then(response => {
        this.deptOptions = response.data
        this.setDefaultExpandedKeys()
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.handleQuery()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.queryParams = {
        userCode: '',
        approveStatus: undefined,
        startDate: undefined,
        endDate: undefined,
        pageNum: 1,
        pageSize: 20
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.$refs.weekPicker.clearDates();
      this.handleDateChange(null);
      this.handleQuery()
    },
    /** 导出excel按钮操作 */
    handleExport() {
      // 检查导出条件：必须选择人员或填写填报周期
      if ((!this.queryParams.userCode || this.queryParams.userCode.length === 0) &&
        (!this.queryParams.startDate || !this.queryParams.endDate)) {
        this.$message.warning('请至少选择人员或填报周期再导出');
        return;
      }
      const params = {
        userCode: this.queryParams.userCode?.join(','), // 员工编码
        startDate: this.queryParams.startDate, //
        endDate: this.queryParams.endDate, //
        approveStatus: this.queryParams.approveStatus
      }
      this.download('weekly/weeklyReport/groupExportExcel', {
        ...params
      }, `群组周报_${new Date().getTime()}.xlsx`)
    },
    /** 导出pdf按钮操作 */
    handleExportPdf() {
      // 检查导出条件：必须选择人员或填写填报周期
      if ((!this.queryParams.userCode || this.queryParams.userCode.length === 0) &&
        (!this.queryParams.startDate || !this.queryParams.endDate)) {
        this.$message.warning('请至少选择人员或填报周期再导出');
        return;
      }
      const params = {
        userCode: this.queryParams.userCode?.join(','), // 员工编码
        startDate: this.queryParams.startDate, //
        endDate: this.queryParams.endDate, //
        approveStatus: this.queryParams.approveStatus
      }
      this.download('weekly/weeklyReport/groupExportPdf', {
        ...params
      }, `群组周报_${new Date().getTime()}.pdf`)
    },
    handleSelectUser(data) {
      console.log('data---------', data)
      // 过滤掉已经存在的用户
      const newUsers = data.filter(
        (newUser) =>
          !this.reportReviewDetails.some(
            (existingUser) => existingUser.userName === newUser.userName
          )
      )

      // 只添加新的用户
      this.reportReviewDetails.push(...newUsers)

      // 更新选中的用户ID数组
      this.queryParams.userCode = this.reportReviewDetails.map(
        (item) => item.userName
      )
    },
    handleOpenUserDialog() {
      // this.$refs.userRef.queryParams.userName = this.approveUserName
      this.$refs.userRef.show()
    },
    handleDetail(row) {
      this.$router.push({
        path: `/weekly/weeklyDetail/${row.id}`,
        query: { from: this.$route.path }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.app-container {
  height: calc(100vh - 90px);
}
.head-container{
  .tree-container{
    max-height: calc(100vh - 90px - 90px);
    overflow-y: auto;
  }
}
.right-container{
  max-height: calc(100vh - 90px - 90px);
}
.dept-list{
  max-height: calc(100vh - 260px - 90px);
  overflow-y: auto;
}

/* 添加树节点文本换行样式 */
.tree-container {
  :deep(.el-tree-node__label) {
    white-space: normal;
    word-break: break-all;
  }

  :deep(.el-tree-node__content) {
    height: auto;
    min-height: 26px;
  }
}
:deep(.el-table) {
  border-collapse: collapse;
}

:deep(.el-table td, .el-table th) {
  padding: 12px 0;
  margin: 0;
}

:deep(.el-table::before) {
  height: 0; /* 移除底部伪元素边框 */
}

.el-select-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 30px 0 5px;
  min-height: 36px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  background-color: #fff;

  &:hover {
    border-color: #c0c4cc;
  }

  .el-select__tags-container {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 5px 0;
    min-height: 28px;
  }

  .el-select__placeholder {
    color: #c0c4cc;
    font-size: 14px;
  }

  .el-select__caret {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    font-size: 14px;
    transition: transform .3s;
  }
}

/* Hide dropdown options */
:deep(.select-hidden-dropdown) {
  display: none !important;
}
</style>
