<template>
  <div class="amount-card" :class="type">
    <div class="card-title">
      {{ cardData.title }}
      <img v-if="icon === 'file'" src="@/views/projects/assets/images/File-editing.png" alt="info" class="info-icon">
      <img v-if="icon === 'amount'" src="@/views/projects/assets/images/Income.png" alt="info" class="info-icon">
      <img v-if="icon === 'price'" src="@/views/projects/assets/images/Stock-market.png" alt="info" class="info-icon">
    </div>
    <div class="amount">
      <span class="currency">¥</span>
      <!--      <span class="value">{{ formatAmount(cardData.amount) }}</span>-->
      <span class="value">{{ $toFixed2(cardData.amount) }}</span>
      <span class="unit">{{ cardData.unit }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AmountCard',
  props: {
    cardData: {
      type: Object,
      required: true,
      /*validator: obj => {
        return (
          typeof obj.title === 'string' &&
          typeof obj.amount === 'string' &&
          (obj.unit === null || typeof obj.unit === 'string')
        )
      }*/
    },
    type: {
      type: String,
      default: 'orange', // 'orange' 或 'blue' 或 'purple'
      validator: value => ['orange', 'blue', 'purple'].includes(value)
    },
    icon: {
      type: String,
      default: 'file' // amouont sales
    }
  },
  methods: {
    formatAmount(value) {
      return value.toFixed(2)
    }
  }
}
</script>

<style scoped lang="scss">

.amount-card {
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  width: 12.4375rem;
  position: relative;
  padding: 1rem 1.5rem;
  box-sizing: border-box;
  border-radius: 0.5rem;
  color: #fff;
  flex: 1;

  &.orange {
    background: linear-gradient(180deg, #FAAF64 0%, #E57A45 100%);
  }

  &.blue {
    background: linear-gradient(180deg, #62C4F5 0%, #2469F2 100%);
  }

  &.purple {
    background: linear-gradient(180deg, #B87AF5 0%, #735CE5 100%);
  }

  .card-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 0.875rem;
    color: #FFFFFF;
    line-height: 1rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .amount {
    display: flex;
    align-items: baseline;
    gap: 0.5625rem;

    .value {
      font-weight: 400;
      font-size: 1.125rem;
      color: #FFFFFF;
      line-height: 1.375rem;
    }

    .currency,
    .unit {
      font-weight: 400;
      font-size: 0.875rem;
      color: #FFFFFF;
      line-height: 1.375rem;
    }
  }

  .info-icon {
    width: 2rem;
    height: 2rem;
  }
}
</style>
