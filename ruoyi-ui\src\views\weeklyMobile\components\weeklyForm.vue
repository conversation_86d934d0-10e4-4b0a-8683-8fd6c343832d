<template>
  <div>
    <section class="head-block">
      <!-- 标题部分 -->
      <div class="weekly-title">
        {{ currentYear }}年第{{ weekNumber }}周（{{ dateRange }}）个人总结
      </div>
      <!-- 个人信息部分 -->
      <van-cell-group inset class="form-section" style="margin: 0px">
        <!-- 第一行：员工姓名和员工所属部门 -->
        <van-row>
          <van-col span="24">
            <van-field
              label="员工姓名"
              v-model="weeklyForm.nickName"
              readonly
              label-width="70px"
            />
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" class="custom-info-row">
            <div class="label-text">所属部门</div>
            <div class="label-content">{{ weeklyForm.deptName }}</div>
            <!-- <van-field
                label="所属部门"
                v-model="weeklyForm.deptName"
                readonly
                label-width="70px"
                type="textarea"
                autosize
              /> -->
          </van-col>
        </van-row>

        <!-- 第二行：员工职位和其他字段 -->
        <van-row>
          <van-col span="24">
            <van-field
              label="员工职位"
              v-model="weeklyForm.postName"
              placeholder="请输入员工职位"
              :rules="[{ required: true, message: '请输入员工职位' }]"
              label-width="70px"
            />
          </van-col>
        </van-row>
      </van-cell-group>
    </section>
    <!-- 本周工作总结 -->
    <section class="section-content">
      <div>
        <div class="section-title">本周工作总结</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workSummaryList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">{{ index + 1 }}</div>
            <van-icon
              class="delete-btn"
              @click.stop.prevent="removeRow(weeklyForm.workSummaryList, index)"
              name="delete-o"
            ></van-icon>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workSummaryList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workSummaryList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                  getCompleteStatusText(
                    weeklyForm.workSummaryList[index].completeStatus
                  )
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>
        </div>

        <!-- 新增按钮 -->
        <div
          class="add-button"
          @click.stop.prevent="addRow(weeklyForm.workSummaryList)"
        >
          <van-icon name="plus" />新增
        </div>
      </div>

      <!-- 状态选择器弹窗 -->
      <van-popup v-model="showStatusPickerVisible" position="bottom">
        <van-picker
          show-toolbar
          :columns="statusColumns"
          @confirm="onStatusConfirm"
          @cancel="showStatusPickerVisible = false"
          title="选择完成及预警情况"
        />
      </van-popup>
    </section>
    <!-- 下周工作计划 -->
    <section class="section-content">
      <div>
        <div class="section-title">下周工作计划</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workPlanList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">{{ index + 1 }}</div>
            <van-icon
              class="delete-btn"
              @click.stop.prevent="removeRow(weeklyForm.workPlanList, index)"
              name="delete-o"
            ></van-icon>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workPlanList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workPlanList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                  getCompleteStatusText(
                    weeklyForm.workPlanList[index].completeStatus
                  )
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showPlanStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>
        </div>

        <!-- 新增按钮 -->
        <div
          class="add-button"
          @click.stop.prevent="addRow(weeklyForm.workPlanList)"
        >
          <van-icon name="plus" />新增
        </div>
      </div>

      <!-- 状态选择器弹窗 -->
      <van-popup v-model="showPlanStatusPickerVisible" position="bottom">
        <van-picker
          show-toolbar
          :columns="statusColumns"
          @confirm="onPlanStatusConfirm"
          @cancel="showPlanStatusPickerVisible = false"
          title="选择完成及预警情况"
        />
      </van-popup>
    </section>
    <section class="section-content">
      <div>
        <div class="section-title">每周反思</div>
      </div>
      <div class="card-content">
        <van-field
          v-model="weeklyForm.reflectInfo.progressScheme"
          type="textarea"
          placeholder="请填写反思内容（必填）"
          :rules="[{ required: true, message: '请填写每周反思' }]"
          class="card-field"
          autosize
        />
      </div>
    </section>
    <section class="section-content">
      <!-- 需部门支持事项 -->
      <div>
        <div class="section-title">需部门支持事项</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workSupportList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">{{ index + 1 }}</div>
            <van-icon
              class="delete-btn"
              @click.stop.prevent="removeRow(weeklyForm.workSupportList, index)"
              name="delete-o"
            ></van-icon>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workSupportList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                v-model="weeklyForm.workSupportList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                  getCompleteStatusText(
                    weeklyForm.workSupportList[index].completeStatus
                  )
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showSupportStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>
        </div>

        <!-- 新增按钮 -->
        <div
          class="add-button"
          @click.stop.prevent="addRow(weeklyForm.workSupportList)"
        >
          <van-icon name="plus" />新增
        </div>
      </div>

      <!-- 状态选择器弹窗 -->
      <van-popup v-model="showSupportStatusPickerVisible" position="bottom">
        <van-picker
          show-toolbar
          :columns="statusColumns"
          @confirm="onSupportStatusConfirm"
          @cancel="showSupportStatusPickerVisible = false"
          title="选择完成及预警情况"
        />
      </van-popup>
    </section>

    <el-row>
      <section class="section-content">
        <!-- 审阅人 -->
        <div>
          <div class="section-title">审阅人</div>
        </div>
        <van-cell-group class="form-section">
          <van-field
            readonly
            label="审阅人"
            placeholder="请选择审阅人"
            @click="handleOpenUserDialog"
            :rules="[{ required: true, message: '请选择审阅人' }]"
          >
            <template #input>
              <div
                class="selected-users-container"
                @click="handleOpenUserDialog"
              >
                <template v-if="approveUserNamesArray.length > 0">
                  <div
                    v-for="(name, index) in approveUserNamesArray"
                    :key="index"
                    class="user-tag"
                  >
                    {{ name }}
                    <span
                      class="close-icon"
                      @click.stop="removeApproveUser(index)"
                      >×</span
                    >
                  </div>
                </template>
                <div v-else class="placeholder-text">请选择审阅人</div>
              </div>
            </template>
          </van-field>
        </van-cell-group>
      </section>
    </el-row>

    <!-- 使用新的移动端人员选择组件 -->
    <select-user-mobile
      ref="userRef"
      :show="showUserPickerVisible"
      :multiple="true"
      :value="selectedUsers"
      @select="handleSelectUser"
      @close="showUserPickerVisible = false"
    />
  </div>
</template>

<script>
import SelectUserMobile from "./SelectUserMobile.vue";
import Sortable from "sortablejs";
import {
  Cell,
  CellGroup,
  Col,
  Row,
  Field,
  Form,
  FormItem,
  Input,
  Button,
  Popup,
  Picker,
  Icon,
} from "vant";
import { getDicts } from "@/api/weekly/mobile-reportInfo";
export default {
  name: "WeeklyForm",
  // dicts: ["work_complete_status"],
  components: {
    SelectUserMobile,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Col.name]: Col,
    [Row.name]: Row,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Icon.name]: Icon,
  },
  props: {
    action: {
      type: String,
      default: "add", // add: 新增, detail: 详情 update: 编辑
    },
  },
  data() {
    return {
      roleName: "",
      labelPosition: "right",
      weeklyForm: {
        id: null,
        year: null, // 年份不能为空
        week: null, // 第几周不能为空
        userId: "", // 员工编码不能为空
        nickName: "", // 员工名称不能为空
        deptId: null, // 部门ID不能为空
        deptName: "", // 部门名称不能为空
        postName: "", // 岗位名称
        startDate: "", // 周开始日期不能为空
        endDate: "", // 周结束日期不能为空
        approveUserCode: "", // 审阅人编码不能为空
        approveUserName: "", // 审阅人名称不能为空
        circulateUserCodes: "", // 传阅人（多个用逗号隔开）
        userCode: "", // 员工编码不能为空
        // 本周工作总结
        workSummaryList: [
          /*  {
             workMatter: "", // 工作板块
             progressScheme: "", // 工作事项/完成进度
             completeStatus: "", // 完成及预警情况
             type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
             serialNumber: 1, // 序号
           }, */
        ],
        // 下周工作计划
        workPlanList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 2,
            serialNumber: 1,
          }, */
        ],
        // 需部门支持事项
        workSupportList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 3,
            serialNumber: 1,
          }, */
        ],
        reportReviewDetails: [],
        selectUserIds: [], // 弹窗中选择的多个用户id数组
        reflectInfo: { // 1=本周工作总结,2=下周工作计划,3=需部门支持事项,4=每周反思
          type: 4,
          progressScheme: null
        },
      },
      rules: {
        selectUserIds: [
          { required: true, message: "请选择审阅人", trigger: "change" },
        ],
      },
      approveUserName: "",
      // selectUserIds: [], // 弹窗中选择的多个用户id数组
      showStatusPickerVisible: false,
      showPlanStatusPickerVisible: false,
      showSupportStatusPickerVisible: false,
      currentEditIndex: -1,
      currentEditType: "", // 'summary', 'plan', 'support'
      statusColumns: [],
      showUserPickerVisible: false,
      selectedUsers: [],
      approveUserNamesArray: [], // 用于存储审阅人名称数组
    };
  },
  computed: {
    /*statusColumns() {
      return this.dict.type.work_complete_status.map(item => ({
        text: item.label,
        value: item.value
      }));
    },*/
    currentYear() {
      return this.weeklyForm.year;
    },
    weekNumber() {
      // 这里需要实现获取当前周数的逻辑
      return this.weeklyForm.week;
    },
    dateRange() {
      // 这里需要实现获取日期范围的逻辑
      // return `${this.weeklyForm.startDate}-${this.weeklyForm.endDate}`;

      return this.formatDateRange(
        this.weeklyForm.startDate,
        this.weeklyForm.endDate
      );
    },
    department() {
      return this.weeklyForm.deptName;
    },
  },
  created() {
    // 初始化状态选择器数据
    this.initStatusColumns();
  },
  mounted() {},

  methods: {
    formatDateRange(startDate, endDate) {
      return `${new Date(startDate).getMonth() + 1}月${new Date(
        startDate
      ).getDate()}日-${new Date(endDate).getMonth() + 1}月${new Date(
        endDate
      ).getDate()}日`;
    },
  /*  handleOpenUserDialog() {
      this.showUserPickerVisible = true;
    },*/
    handleSelectUser(data) {
      console.log("handleSelectUser data---", data);
      // 保存选中的用户
      this.selectedUsers = data;

      // 更新审阅人显示名称
      this.approveUserNamesArray = data.map((item) => item.nickName);

      // 更新表单数据
      this.weeklyForm.reportReviewDetails = data.map((item) => ({
        /*
          按照组件格式在form表单中保存选中的用户，注意实际在取值时在submitForm做了转换
          approveUserCode: item.userName
          approveUserName: item.nickName
        */
        userId: item.userId,
        userName: item.userName,
        nickName: item.nickName,
      }));

      // 更新选中的用户ID数组
      this.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );
      /*console.log("selectedUsers---", this.selectUserIds);
      console.log("approveUserNamesArray---", this.approveUserNamesArray);
      console.log("weeklyForm---", this.weeklyForm);
      console.log("selectUserIds---", this.selectUserIds);*/
    },
    getFormData() {
      return this.submitForm();
    },
    addRow(array) {
      // 防止 array 为 undefined 或 null
      if (!array) {
        console.error("传入的数组为空");
        return;
      }

      const type =
        array === this.weeklyForm.workSummaryList
          ? 1
          : array === this.weeklyForm.workPlanList
          ? 2
          : 3;

      // 直接添加，不使用 $nextTick
      array.push({
        workMatter: "",
        progressScheme: "",
        completeStatus: "",
        type,
        serialNumber: array.length + 1,
      });

      /*  this.$nextTick(() => {
        this.initSortable("workSummaryTable", this.weeklyForm.workSummaryList);
        this.initSortable("workPlanTable", this.weeklyForm.workPlanList);
        this.initSortable("workSupportTable", this.weeklyForm.workSupportList);
      }); */
    },
    removeRow(array, index) {
      array.splice(index, 1);
    },
    submitForm() {
      return new Promise((resolve, reject) => {
        console.log("weeklyForm.selectUserIds", this.weeklyForm.selectUserIds);
        this.weeklyForm.workSummaryList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workPlanList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workSupportList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        // 本周工作总结，下周工作计划非空校验
        if (
          !this.weeklyForm.workSummaryList ||
          this.weeklyForm.workSummaryList.length === 0 ||
          !this.weeklyForm.workPlanList ||
          this.weeklyForm.workPlanList.length === 0
        ) {
          this.$modal.msgWarning("本周工作总结和下周工作计划不能为空");
          reject(new Error("本周工作总结和下周工作计划不能为空"));
          return;
        }
        // 每周反思不能为空
        if (!this.weeklyForm?.reflectInfo?.progressScheme) {
          this.$modal.msgWarning("每周反思不能为空");
          reject(new Error("每周反思不能为空"));
          return;
        }
        resolve({
          ...this.weeklyForm,
          // 过滤选择的审批人,修改approveUserCode和userName对应关系
          reportReviewDetails: this.weeklyForm.reportReviewDetails
            .filter((item) =>
              this.selectUserIds.includes(item.userName)
            )
            .map((item) => ({
              approveUserCode: item.userName,
              approveUserName: item.nickName,
            })),
        });
      });
    },

    draftForm() {
      return new Promise((resolve, reject) => {
        console.log("weeklyForm.selectUserIds", this.weeklyForm.selectUserIds);
        this.weeklyForm.workSummaryList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workPlanList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workSupportList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        resolve({
          ...this.weeklyForm,
          // 过滤选择的审批人,修改approveUserCode和userName对应关系
          reportReviewDetails: this.weeklyForm.reportReviewDetails
            .filter((item) =>
              this.selectUserIds.includes(item.userName)
            )
            .map((item) => ({
              approveUserCode: item.userName,
              approveUserName: item.nickName,
            })),
        });
      });
    },
    // 编辑模式下更新表单数据
    updateFormData(data) {
      // this.approveUserName = data.approveUserName
      this.weeklyForm = {
        ...this.weeklyForm,
        ...data,
        workSummaryList: data.workSummaryList || [], // 无数据时兜底，解决table报错
        workPlanList: data.workPlanList || [],
        workSupportList: data.workSupportList || [],
        reportReviewDetails: data.reportReviewDetails.map((item) => ({
          userName: item.approveUserCode,
          nickName: item.approveUserName,
        })),
        reflectInfo: data.reflectInfo || {
          type: 4, progressScheme: ""
        },
      };

      this.handleSelectUser(this.weeklyForm.reportReviewDetails);
      this.weeklyForm.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );

      this.$nextTick(() => {
        this.initSortable(
          "workSummaryTable",
          this.weeklyForm.workSummaryList || []
        );
        this.initSortable("workPlanTable", this.weeklyForm.workPlanList || []);
        this.initSortable(
          "workSupportTable",
          this.weeklyForm.workSupportList || []
        );
      });
    },
    setFormData(data) {
      if (data) {
        this.weeklyForm = {
          ...this.weeklyForm,
          ...data,
          year: data.year,
          week: data.week,
          userId: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          userCode: data.userCode,
          workSummaryList: data.workSummaryList || [
            {
              workMatter: "", // 工作板块
              progressScheme: "", // 工作事项/完成进度
              completeStatus: "", // 完成及预警情况
              type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
              serialNumber: 1, // 序号
            },
          ],
          reportReviewDetails: (data?.reportReviewDetails || []).map(
            (item) => ({
              userName: item.approveUserCode,
              nickName: item.approveUserName,
            })
          ),
          workPlanList:
            data.workPlanList ||
            [
              /*{
              workMatter: "",
              progressScheme: "",
              completeStatus: "",
              type: 2,
              serialNumber: 1,
            },*/
            ],
          workSupportList:
            data.workSupportList ||
            [
              /*{
              workMatter: "",
              progressScheme: "",
              completeStatus: "",
              type: 3,
              serialNumber: 1,
            },*/
            ],
          reflectInfo: data.reflectInfo || {
            type: 4, progressScheme: ""
          },
          id: data.id,
        };
        console.log(
          "weeklyForm.reportReviewDetails",
          this.weeklyForm.reportReviewDetails
        );
        this.handleSelectUser(this.weeklyForm.reportReviewDetails);
        this.weeklyForm.selectUserIds = (
          this.weeklyForm?.reportReviewDetails || []
        ).map((item) => item.userName);
      }
    },
    validateField(row, field) {
      if (!row[field]) {
        this.$message.warning(
          `${
            field === "workMatter"
              ? "工作板块"
              : field === "progressScheme"
              ? "工作事项/完成进度"
              : "完成及预警情况"
          }`
        );
        return false;
      }
      return true;
    },
    initSortable(refName, dataList) {
      const el = this.$refs[refName]?.$el?.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      if (!el) return;
      this.sortable = Sortable.create(el, {
        handle: ".el-table__row",
        animation: 150,
        onEnd: ({ newIndex, oldIndex }) => {
          // 创建新数组并重新赋值以触发响应式更新
          const targetRow = dataList.splice(oldIndex, 1)[0];
          dataList.splice(newIndex, 0, targetRow);
        },
      });
    },
    // 初始化状态选择器数据
    initStatusColumns() {
      getDicts("work_complete_status").then((res) => {
        this.statusColumns = res.data?.map((item) => ({
          text: item.dictLabel,
          value: item.dictValue,
        }));
      });
    },

    // 获取状态文本
    getCompleteStatusText(value) {
      if (!value && value !== 0) return "请选择完成及预警情况";
      const option = this.statusColumns.find((item) => item.value === value);
      return option ? option.text : "请选择完成及预警情况";
    },

    // 显示状态选择器 - 本周工作总结
    showStatusPicker(index) {
      this.currentEditIndex = index;
      this.currentEditType = "summary";
      this.showStatusPickerVisible = true;
    },

    // 确认状态选择 - 本周工作总结
    onStatusConfirm(value) {
      if (this.currentEditIndex >= 0) {
        this.weeklyForm.workSummaryList[this.currentEditIndex].completeStatus =
          value.value;
      }
      this.showStatusPickerVisible = false;
    },

    // 显示状态选择器 - 下周工作计划
    showPlanStatusPicker(index) {
      this.currentEditIndex = index;
      this.currentEditType = "plan";
      this.showPlanStatusPickerVisible = true;
    },

    // 确认状态选择 - 下周工作计划
    onPlanStatusConfirm(value) {
      if (this.currentEditIndex >= 0) {
        this.weeklyForm.workPlanList[this.currentEditIndex].completeStatus =
          value.value;
      }
      this.showPlanStatusPickerVisible = false;
    },

    // 显示状态选择器 - 需部门支持事项
    showSupportStatusPicker(index) {
      this.currentEditIndex = index;
      this.currentEditType = "support";
      this.showSupportStatusPickerVisible = true;
    },

    // 确认状态选择 - 需部门支持事项
    onSupportStatusConfirm(value) {
      if (this.currentEditIndex >= 0) {
        this.weeklyForm.workSupportList[this.currentEditIndex].completeStatus =
          value.value;
      }
      this.showSupportStatusPickerVisible = false;
    },

    // 打开用户选择器
    handleOpenUserDialog() {
      this.showUserPickerVisible = true;
    },

    // 移除单个审阅人
    removeApproveUser(index) {
      // 移除指定索引的审阅人
      this.weeklyForm.selectUserIds?.splice(index, 1);
      this.approveUserNamesArray?.splice(index, 1);
      this.selectedUsers?.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.selected-users-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-height: 24px;
  width: 100%;
  padding: 2px 0;
  .user-tag {
    padding: 6px 8px;
    margin-bottom: 4px;
    background-color: #f2f3f5;
  }
}
.weekly-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
}
.section-title {
  margin: 0 0 18px 10px;
  font-weight: 600;
  text-align: left;
  display: inline-flex;
  align-items: center;
  position: relative;
  z-index: 2; // 添加 z-index 确保文字在上层

  &::before {
    content: " ";
    display: inline-block;
    height: 10px;
    background: #d8e4fb;
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    z-index: -1; // 将伪元素放到文字下方
  }
}
.head-block {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffffff;
  padding: 16px;
  margin-bottom: 12px;
}
.custom-input {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #dddddd;
}
.custom-label,
.custom-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.info-row {
  :deep(.el-form-item__label) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  display: flex;
  align-items: center;
}
.weekly-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}

.submit-section {
  margin-top: 20px;
  text-align: center;
}

/* 添加深度选择器来修改 element-plus 表头样式 */
/* :deep(.el-table th) {
  background-color: #f0f2f5 !important;
} */

.weekly-form {
  /* 可以根据实际需要调整减去的高度 */
  /*  max-height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 20px; */
}

.approve-row {
  margin-top: 20px;
  text-align: center;
}

/* 添加以下样式来修复 label 和 input 换行的问题 */
.weekly-form :deep(.el-form-item) {
  display: flex;
  margin-bottom: 0px;
}

.weekly-form :deep(.el-form-item__label) {
  float: none;
  display: inline-flex;
  align-items: center;
}

.weekly-form :deep(.el-form-item__content) {
  flex: 1;
  margin-left: 0 !important;
}

.section-content {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffffff;
  padding: 16px;
  margin-bottom: 12px;

  /*  .section-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
  } */
}

:deep(.el-table__header-wrapper .el-table__cell) {
  background: rgba(54, 115, 255, 0.05);
  border-radius: 0px 0px 0px 0px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* Add these new styles */
/* :deep(.el-textarea__inner) {
  min-height: 80px !important;
}

:deep(.el-textarea__inner) {
  max-height: 150px !important;
} */
.weekly-form :deep(th.el-table__cell) {
  padding: 0;
}

.info-row {
  /* margin-bottom: 10px; */
}

.mb-20 {
  margin-bottom: 20px !important;
}

.font-size-15 {
  font-size: 15px;
}

.sortable-table .el-table__row {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.8;
  background: #f0f9eb;
}

.el-table__body tr {
  cursor: move;
}
.align-center {
  display: flex;
  align-items: center;
}

.weekly-card-container {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.weekly-card {
  padding: 16px;
  background-color: #fff;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.delete-btn {
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
}

.card-item {
  margin-bottom: 12px;
}

.card-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.card-content {
  color: #666;
}

.card-field {
  padding: 0;

  :deep(.van-field__control) {
    color: #666;
    font-size: 14px;
  }
}

.card-divider {
  height: 1px;
  background-color: #ebedf0;
  margin: 12px 0;
}

.add-button {
  padding: 16px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 500;
  font-size: 14px;
  color: #3673ff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 16px;
}
.delete-btn {
  color: red;
  width: 16px;
  height: 16px;
}
.custom-info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  .label-text {
    font-size: 13px;
    color: #646566;
    margin-bottom: 8px;
    word-break: keep-all;
    width: 70px;
    margin-right: 12px;
  }
  .label-content {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    word-break: break-all;
    flex: 1;
    text-align: left;
  }
}

.approver-field {
  .selected-users-container {
    display: flex;
    flex-wrap: wrap;
    min-height: 24px;
    width: 100%;
    padding: 2px 0;
  }

  .user-tag {
    background-color: #f2f3f5;
    border-radius: 2px;
    padding: 0 8px;
    margin-right: 8px;
    margin-bottom: 4px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    color: #323233;

    .close-icon {
      margin-left: 4px;
      font-size: 16px;
      color: #969799;
      font-weight: bold;
    }
  }

  .placeholder-text {
    color: #c8c9cc;
    font-size: 14px;
  }
}
</style>
