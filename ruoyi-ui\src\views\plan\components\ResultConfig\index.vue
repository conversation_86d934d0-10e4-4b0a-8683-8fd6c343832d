<template>
  <el-dialog
    title="专业成果"
    :visible.sync="visible"
    width="900px"
    append-to-body
    v-if="visible"
  >
    <el-table
      ref="resultTable"
      v-loading="loading"
      :data="resultList"
      @select="selectRow"
      :row-key="getRowKey"
      @select-all="onSelectAll"
    >
      <el-table-column type="selection" width="45" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="60">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="阶段成果名称" align="center" prop="name" />
      <el-table-column label="阶段成果类型" align="center" prop="type">
      </el-table-column>
      <el-table-column label="专业成果描述" align="center" prop="remark" />
      <el-table-column label="是否必须上传" align="center" prop="status">
        <template slot-scope="scope">
          <el-select v-model="scope.row.status" clearable disabled>
            <el-option
              v-for="option in isEditOption"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitSetting">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listConfig } from "@/api/plan/config"
import { isEditOption } from '@/views/plan/constant'

export default {
  props: {
    // 角色编号
    defaultResult: {
      type: [Array]
    }
  },
  data () {
    return {
      // 遮罩层
      visible: false,
      // 总条数
      total: 0,
      // 未授权用户数据
      resultList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
      },
      loading: false,
      selectIds: [],
      selectData: []
    }
  },
  computed: {
    isEditOption: () => isEditOption,
  },
  methods: {
    getIds () {
      this.selectIds = this.selectData.map(item => item.id)
      console.log(this.selectData, this.selectIds)
    },
    getRowKey (row) {
      return row.id
    },
    onSelectAll (selection) {
      //全选
      if (selection.length > 0) {
        selection.forEach(item => {
          if (!this.selectIds.includes(item.id)) {
            this.selectData.push(item)
          }
        })
      } else {
        //取消
        this.resultList.forEach(item => {
          this.selectData.forEach((it, index) => {
            if (item.id == it.id) {
              this.selectData.splice(index, 1)
            }
          })
        }
        )
      }
      this.selectData = [...this.selectData]
      this.getIds()
    },

    selectRow (section, row) {
      console.log(row)
      let { id } = row
      //已存在则表示取消
      let index = this.selectData.findIndex(i => i.id === id)
      if (index > -1) {
        this.selectData.splice(index, 1)
      } else {
        this.selectData.push(row)
      }
    },
    // 显示弹框
    show () {
      this.selectData = this.defaultResult
      this.getIds()
      this.getList()
      this.visible = true
    },
    // 查询表数据
    getList () {
      this.loading = true
      listConfig().then(res => {
        this.loading = false
        this.resultList = res.rows
        this.$nextTick(() => {
          this.resultList.filter(item => {
            console.log(this.selectIds.includes(item.id))
            if (this.selectIds.includes(item.id)) {
              this.$refs.resultTable.toggleRowSelection(item, true)
            }
          })
        })
      }).catch(err => {
        this.loading = false
        console.log(err)
      })
    },
    /** 选择授权用户操作 */
    submitSetting () {
      this.$emit('settingEmit', this.selectData)
      this.visible = false
    }
  }
};
</script>
