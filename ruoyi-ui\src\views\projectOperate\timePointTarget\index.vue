<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="签约目标" name="qyTarget">
        <qy-target></qy-target>
      </el-tab-pane>
      <el-tab-pane label="回款目标" name="hkTarget">
        <hk-target></hk-target>
      </el-tab-pane>
      <el-tab-pane label="现金流目标" name="xjlTarget">
        <xjl-target></xjl-target>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import qyTarget from './qyTarget.vue';
import hkTarget from './hkTarget.vue';
import xjlTarget from './xjlTarget.vue';

export default {
  name: 'TimePointTarget',
  components: {
    qyTarget,
    hkTarget,
    xjlTarget
  },
  data() {
    return {
      activeTab: 'qyTarget', // 默认激活的标签页
    };
  },
};
</script>
