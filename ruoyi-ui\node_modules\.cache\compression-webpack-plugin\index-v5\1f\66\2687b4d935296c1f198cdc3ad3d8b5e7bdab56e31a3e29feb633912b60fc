
9a526a74a5a00c09dfd14322333eaef3b032af2e	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.7574591d8766442e21bc.hot-update.js\",\"contentHash\":\"4641336bab3c0f11aca5c9252c0398be\"}","integrity":"sha512-yhNRkd1FG3Eg4CmTWkOLhbxhR+tcUTEJt69FE/ighTj283HEuIh82Yl6JmmTRPifY/wUGVagfxNzSGLXFsBh5A==","time":1754311557228,"size":78284}