import request from '@/utils/request'

// 查询过程评估数据列表
export function listData(query) {
  return request({
    url: '/project/gcDeliverAssessData/list',
    method: 'get',
    params: query
  })
}

// 查询过程评估数据详细
export function getData(id) {
  return request({
    url: '/project/gcDeliverAssessData/' + id,
    method: 'get'
  })
}

// 新增过程评估数据
export function addData(data) {
  return request({
    url: '/project/gcDeliverAssessData',
    method: 'post',
    data: data
  })
}

// 修改过程评估数据
export function updateData(data) {
  return request({
    url: '/project/gcDeliverAssessData',
    method: 'put',
    data: data
  })
}

// 删除过程评估数据
export function delData(id) {
  return request({
    url: '/project/gcDeliverAssessData/' + id,
    method: 'delete'
  })
}

// 交付评估数据导入
export function importFile(data) {
  return request({
    url: '/project/gcDeliverAssessData/import',
    method: 'post',
    data: data
  })
}

// 交付评估导入数据提交
export function importDataSubmit(data) {
  return request({
    url: '/project/gcDeliverAssessData/importDataSubmit',
    method: 'post',
    data: data
  })
}
