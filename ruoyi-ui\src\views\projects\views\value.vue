<template>
  <div class="projects-content-container w-100">
    <div v-if="!hasPermi(['project:goods:value'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限" />
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else class="w-100">
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中" />
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据" />
        <div class="desc">暂无数据</div>
      </div>
      <section v-else class="w-100">
        <div class="card-title mb-16">货值指标</div>
        <div class="cost-content" v-loading="loading">
          <div class="card-content mb-12" style="padding-left: 0;padding-right: 0;">
            <div class="cost-card-item">
              <div class="cost-card-item-content cost" v-loading="valueLoading">
                
                <div class="title">货值</div>
                <Empty :no-authority="hasPermi(['goods:value:data'])">
                  <section>
                    <section class="card-with-title">
                      <Chart class="chart-size-220" :option="valueOption" />
                      <section class="chart-title-block">
                        <div
                          class="title-1"
                          :style="{ color: valueOption.title.textStyle.color }"
                        >
                          {{ valueOption.title.text }}
                        </div>
                        <div class="title-2">
                          {{ valueOption.title.subtext }}
                        </div>
                      </section>
                    </section>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(valueData.targetAmount) }}亿
                        </div>
                        <div class="label">目标货值</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(valueData.dynamicAmount) }}亿
                        </div>
                        <div class="label">动态货值</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content change"
                v-loading="stockLoading"
              >
                <div class="title">库存</div>
                <Empty :no-authority="hasPermi(['goods:value:kc'])">
                  <section class="card-with-title">
                    <Chart class="chart-size-220" :option="stockOption" />
                    <section class="chart-title-block">
                      <div
                        class="title-1"
                        :style="{ color: stockOption.title.textStyle.color }"
                      >
                        {{ stockOption.title.text }}
                      </div>
                      <div class="title-2">{{ stockOption.title.subtext }}</div>
                    </section>
                  </section>
                  <div class="data-content">
                    <div class="value-item">
                      <div class="value mb-8">
                        {{ $formatNull(stockData.yjbwsAmount) }}亿
                      </div>
                      <div class="label">其中：已竣未售</div>
                    </div>
                  </div>
                </Empty>
              </div>
              <div
                class="cost-card-item-content value"
                v-loading="statusLoading"
              >
                <div class="title">库存状态分布</div>
                <Empty :no-authority="hasPermi(['goods:value:kcStatus'])">
                  <Chart
                    class="chart-size"
                    :option="statusOption"
                    type="customPie"
                  />
                  <div class="data-content">
                    <div
                      class="legend-item"
                      v-for="(item, index) in statusLegend"
                      :key="'status-' + index"
                    >
                      <div
                        class="icon"
                        :style="{ backgroundColor: item.color }"
                      ></div>
                      {{ item.name }}
                    </div>
                  </div>
                </Empty>
              </div>
              <div
                class="cost-card-item-content value"
                v-loading="structureLoading"
              >
                <div class="title">库存结构分布</div>
                <Empty :no-authority="hasPermi(['goods:value:kcStructure'])">
                  <Chart class="chart-size" :option="structureOption" />
                  <div class="data-content">
                    <div
                      class="legend-item"
                      v-for="item in structureLegend"
                      :key="'structure-' + item.name"
                    >
                      <div
                        class="icon"
                        :style="{ backgroundColor: item.color }"
                      ></div>
                      {{ item.name }}
                    </div>
                  </div>
                </Empty>
              </div>
            </div>
          </div>
          <div class="card-content-grid mb-12">
            <div class="analysis-grid">
              <div class="grid-item tool-bar">
                <div class="card-title mb-16">货值分布图</div>
              </div>
              <div class="grid-item tool-bar">
                <div class="card-title mb-16">货值分布表</div>
                <CardTab
                  v-model="tabActiveName"
                  :tabs="tabs"
                  @click="handleTabClick"
                  class="mb-16"
                />
              </div>
            </div>
            <div class="content-grid">
              <div class="grid-item">
                <Empty :no-authority="hasPermi(['goods:value:analysePic'])">
                  <div class="cost-card-item" v-loading="imgLoading">
                    <template v-if="buildingConfigPic">
                      <BuildingViewer
                        class="custom-height"
                        :buildingConfigPic="buildingConfigPic"
                        :buildingConfigList="buildingConfigList"
                      />
                    </template>
                    <template v-else>
                      <div class="empty-status">
                        <div class="desc">暂无货值分布图</div>
                      </div>
                    </template>
                  </div>
                </Empty>
              </div>
              <div class="grid-item">
                <div class="grid-card-item" v-if="tabActiveName === '货值分析'">
                  <Empty :no-authority="hasPermi(['goods:value:analyse'])">
                    <el-table
                      :data="analysisData"
                      v-if="tabActiveName === '货值分析'"
                      v-loading="tableLoading"
                      :key="'货值分析'"
                      class="project-table custom-max-height"
                      :border="true"
                      :span-method="handleSpanMethod"
                      :header-cell-style="{
                        background: '#E1EFFD',
                        color: '#666666',
                        fontWeight: '400',
                        padding: '0.4rem 0',
                        fontSize: '0.875rem',
                        textAlign: 'center',
                      }"
                      :cell-style="{
                        borderColor: 'rgba(0,106,255,0.2);',
                        fontSize: '0.875rem',
                        padding: '0.4rem 0',
                        lineHeight: '1rem',
                        textAlign: 'center',
                      }"
                    >
                      <el-table-column
                        prop="projectType"
                        label="业态"
                        min-width="10%"
                      ></el-table-column>
                      <el-table-column
                        prop="hxName"
                        label="户型"
                        min-width="20%"
                      ></el-table-column>
                      <el-table-column
                        prop="totalNum"
                        label="总套数"
                        min-width="15%"
                      ></el-table-column>
                      <el-table-column
                        prop="allHzAmount"
                        min-width="15%"
                        align="center">
                        <template #header>
                          <div>
                            <div>总货值</div>
                            <div>(万元)</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="ysNum"
                        label="已售套数"
                        min-width="15%"
                      ></el-table-column>
                      <el-table-column
                        prop="ysHzAmount"
                        min-width="15%"
                        align="center">
                        <template #header>
                          <div>
                            <div>已售货值</div>
                            <div>(万元)</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="wsNum"
                        label="未售套数"
                        min-width="15%"
                      ></el-table-column>
                      <el-table-column
                        prop="wsHzAmount"
                        min-width="15%"
                        align="center">
                        <template #header>
                          <div>
                            <div>未售货值</div>
                            <div>(万元)</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="ygwsNum"
                        label="其中：已供未售套数"
                        min-width="15%"
                      ></el-table-column>
                      <el-table-column
                        prop="wsBottomNum"
                        label="其中：未供套数"
                        min-width="15%"
                      ></el-table-column>
                    </el-table>
                  </Empty>
                </div>
                <div class="grid-card-item" v-if="tabActiveName === '顶底分析'">
                  <Empty :no-authority="hasPermi(['goods:value:ddAnalyse'])">
                    <el-table
                      :data="analysisData"
                      v-if="tabActiveName === '顶底分析'"
                      v-loading="tableLoading"
                      :key="'顶底分析'"
                      class="project-table custom-max-height"
                      style="width: 100%"
                      :border="true"
                      :span-method="handleTopBottomSpanMethod"
                      :header-cell-style="{
                        background: '#E1EFFD',
                        color: '#666666',
                        fontWeight: '400',
                        padding: '0.4rem 0',
                        fontSize: '0.875rem',
                        textAlign: 'center',
                      }"
                      :cell-style="{
                        borderColor: 'rgba(0,106,255,0.2);',
                        fontSize: '0.875rem',
                        padding: '0.4rem 0',
                        lineHeight: '1rem',
                        textAlign: 'center',
                      }"
                    >
                      <el-table-column
                        prop="projectType"
                        label="业态"
                        min-width="8%"
                      ></el-table-column>
                      <el-table-column
                        prop="hxName"
                        label="户型"
                        min-width="19%"
                      ></el-table-column>
                      <el-table-column label="总套数" align="center">
                        <el-table-column
                          prop="totalNum"
                          label="合计"
                          align="center"
                          min-width="8%"
                        ></el-table-column>
                        <el-table-column label="其中" align="center">
                          <el-table-column
                            prop="topTotal"
                            label="顶层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.allTopNum || "--"
                            }}</template>
                          </el-table-column>
                          <el-table-column
                            prop="bottomTotal"
                            label="底层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.allBottomNum || "--"
                            }}</template>
                          </el-table-column>
                        </el-table-column>
                      </el-table-column>
                      <el-table-column label="已售套数" align="center">
                        <el-table-column
                          prop="ysNum"
                          label="合计"
                          align="center"
                          min-width="8%"
                        ></el-table-column>
                        <el-table-column label="其中" align="center">
                          <el-table-column
                            prop="topSold"
                            label="顶层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.ysTopNum || "--"
                            }}</template>
                          </el-table-column>
                          <el-table-column
                            prop="bottomSold"
                            label="底层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.ysBottomNum || "--"
                            }}</template>
                          </el-table-column>
                        </el-table-column>
                      </el-table-column>
                      <el-table-column label="未售套数" align="center">
                        <el-table-column
                          prop="wsNum"
                          label="合计"
                          align="center"
                          min-width="8%"
                        ></el-table-column>
                        <el-table-column label="其中" align="center">
                          <el-table-column
                            prop="topUnsold"
                            label="顶层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.wsTopNum || "--"
                            }}</template>
                          </el-table-column>
                          <el-table-column
                            prop="bottomUnsold"
                            label="底层"
                            align="center"
                            min-width="8%"
                          >
                            <template #default="scope">{{
                              scope.row.wsBottomNum || "--"
                            }}</template>
                          </el-table-column>
                        </el-table-column>
                      </el-table-column>
                    </el-table>
                  </Empty>
                </div>
              </div>
            </div>
          </div>
          <!-- 已移除，不需要加载 -->
          <div class="card-content analysis mb-12" v-if="false">
            <section class="analysis-item max-height">
              <div class="card-title mb-16">货值分布图</div>
              <div class="cost-card-item" v-loading="imgLoading">
                <!-- <el-image class="image" :src="ValueMap" :preview-src-list="[ValueMap]"/> -->
                <!--          <img class="image" :src="buildingConfigPic" />-->
                <BuildingViewer
                  :buildingConfigPic="buildingConfigPic"
                  :buildingConfigList="buildingConfigList"
                />
              </div>
            </section>
            <section class="analysis-item max-height">
              <div class="tool-bar">
                <div class="card-title mb-16">货值分布表</div>
                <CardTab
                  v-model="tabActiveName"
                  :tabs="tabs"
                  @click="handleTabClick"
                  class="mb-16"
                />
              </div>
              <div class="cost-card-item" v-if="tabActiveName === '货值分析'">
                <!-- style="width: 100%"
                height="31.25rems" -->
                <el-table
                  :data="analysisData"
                  v-if="tabActiveName === '货值分析'"
                  v-loading="tableLoading"
                  :key="'货值分析'"
                  class="project-table"
                  :border="true"
                  :span-method="handleSpanMethod"
                  :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.4rem 0',
                    fontSize: '0.875rem',
                    textAlign: 'center',
                  }"
                  :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontSize: '0.875rem',
                    padding: '0.4rem 0',
                    lineHeight: '1rem',
                    textAlign: 'center',
                  }"
                >
                  <el-table-column
                    prop="projectType"
                    label="业态"
                    min-width="10%"
                  ></el-table-column>
                  <el-table-column
                    prop="hxName"
                    label="户型"
                    min-width="20%"
                  ></el-table-column>
                  <el-table-column
                    prop="totalNum"
                    label="总套数"
                    min-width="15%"
                  ></el-table-column>
                  <el-table-column
                    prop="ysNum"
                    label="已售套数"
                    min-width="15%"
                  ></el-table-column>
                  <el-table-column
                    prop="wsNum"
                    label="未售套数"
                    min-width="15%"
                  ></el-table-column>
                  <el-table-column
                    prop="ygwsNum"
                    label="其中：已供未售套数"
                    min-width="15%"
                  ></el-table-column>
                  <el-table-column
                    prop="wsBottomNum"
                    label="其中：未供套数"
                    min-width="15%"
                  ></el-table-column>
                </el-table>
              </div>
              <div class="cost-card-item" v-if="tabActiveName === '顶底分析'">
                <el-table
                  :data="analysisData"
                  v-if="tabActiveName === '顶底分析'"
                  v-loading="tableLoading"
                  :key="'顶底分析'"
                  class="project-table"
                  style="width: 100%"
                  height="31.25rem"
                  :border="true"
                  :span-method="handleTopBottomSpanMethod"
                  :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.4rem 0',
                    fontSize: '0.875rem',
                    textAlign: 'center',
                  }"
                  :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontSize: '0.875rem',
                    padding: '0.4rem 0',
                    lineHeight: '1rem',
                    textAlign: 'center',
                  }"
                >
                  <el-table-column
                    prop="projectType"
                    label="业态"
                    min-width="8%"
                  ></el-table-column>
                  <el-table-column
                    prop="hxName"
                    label="户型"
                    min-width="19%"
                  ></el-table-column>
                  <el-table-column label="总套数" align="center">
                    <el-table-column
                      prop="totalNum"
                      label="合计"
                      align="center"
                      min-width="8%"
                    ></el-table-column>
                    <el-table-column label="其中" align="center">
                      <el-table-column
                        prop="topTotal"
                        label="顶层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                      <el-table-column
                        prop="bottomTotal"
                        label="底层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="已售套数" align="center">
                    <el-table-column
                      prop="ysNum"
                      label="合计"
                      align="center"
                      min-width="8%"
                    ></el-table-column>
                    <el-table-column label="其中" align="center">
                      <el-table-column
                        prop="topSold"
                        label="顶层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                      <el-table-column
                        prop="bottomSold"
                        label="底层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="未售套数" align="center">
                    <el-table-column
                      prop="wsNum"
                      label="合计"
                      align="center"
                      min-width="8%"
                    ></el-table-column>
                    <el-table-column label="其中" align="center">
                      <el-table-column
                        prop="topUnsold"
                        label="顶层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                      <el-table-column
                        prop="bottomUnsold"
                        label="底层"
                        align="center"
                        min-width="8%"
                      >
                        <template #default="scope">{{
                          scope.row.topTotal || "--"
                        }}</template>
                      </el-table-column>
                    </el-table-column>
                  </el-table-column>
                </el-table>
              </div>
            </section>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import Chart from "@/views/projects/components/Chart.vue";
import BuildingViewer from "@/views/projects/components/BuildingViewer.vue";
import ProgressCircleOption from "@/views/projects/constants/ProgressCircle";
import PieOption from "@/views/projects/constants/Pie";
import API from "@/views/projects/api";
import ValueMap from "../assets/images/valueMap.png";
import CardTab from "@/views/projects/components/CardTab.vue";
import Empty from "@/views/projects/components/empty.vue";

export default {
  name: "Value",
  components: {
    Chart,
    BuildingViewer,
    CardTab,
    Empty,
  },
  data() {
    return {
      projectCode: this.$route.query.projectCode,
      valueData: {}, // 货值
      stockData: {}, // 库存
      statusData: {}, // 库存状态
      structureData: [], // 结构
      analysisData: [], // 货值分析
      loading: false, // Add this line for loading state
      buildingConfigPic: "", // 楼栋配置图
      buildingConfigList: [], // 楼栋配置列表
      tabActiveName: "货值分析",
      tabs: ["货值分析", "顶底分析"],
      imgLoading: false,
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      valueLoading: false, // 货值图表loading
      stockLoading: false, // 库存图表loading
      statusLoading: false, // 库存状态loading
      structureLoading: false, // 结构loading
      statusLegend: [
        // ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3']
        { name: "未开工", color: "#8af3a9" },
        { name: "已推未售", color: "#f8cb7f" },
        { name: "已供未推", color: "#fa9f92" },
        { name: "已开未供", color: "#6bc0f3" },
      ],
      structureLegend: [],
      tableLoading: false, // 添加表格loading状态控制
    };
  },
  computed: {
    ValueMap() {
      return ValueMap;
    },
    valueOption() {
      return this.getValueOption();
    },
    stockOption() {
      return this.getStockOption();
    },
    statusOption() {
      return this.getStatusOption();
    },
    structureOption() {
      return this.getStructureOption();
    },
  },
  mounted() {
    this.fetchData(this.projectCode);
  },
  methods: {
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions;
      // Check for all permissions wildcard first
      if (permissions.includes("*:*:*")) {
        return true;
      }
      // Check specific permissions
      return permissions.some((p) => permission.includes(p));
    },
    handleTabClick(tab) {
      this.tabActiveName = tab;
      this.fetchAnalyse(this.projectCode); // 切换tab时重新获取数据
    },
    // 查询货值分布图信息
    getAnalysePicValue(projectCode) {
      this.imgLoading = true;
      API.Value.analysePicValue(projectCode)
        .then((res) => {
          // this.ValueMap = res.data;
          this.buildingConfigPic = res.data.buildingPic;
          this.buildingConfigList = res.data.buildingConfigList;
        })
        .finally(() => {
          this.imgLoading = false;
        });
    },
    fetchData(projectCode) {
      this.loading = true; // Start loading
      Promise.all([
        this.fetchValueData(projectCode),
        this.fetchStockData(projectCode),
        this.fetchStatusData(projectCode),
        this.fetchStructureData(projectCode),
        this.fetchAnalyse(projectCode),
        this.getAnalysePicValue(projectCode),
      ]).finally(() => {
        this.loading = false; // Stop loading
      });
    },
    getValueOption() {
      const option = this.getProgressCircleOption(
        this.valueData.rate,
        100,
        this.$toFixed2(this.valueData.deviationRate) + "%",
        "偏差率",
        "#CCE4FF",
        "#376DF7",
        "40%",
        "#1433CC"
      );
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        formatter: (params) => {
          return `<div  style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">目标货值: ${this.$formatNull(
            this.valueData.targetAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">动态货值: ${this.$formatNull(
            this.valueData.dynamicAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">偏差率: ${this.$formatNull(
            this.valueData.deviationRate
          )}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(this.valueData.rate)}%</div>
          </div>`;
        },
      };
      return option;
    },
    getStockOption() {
      const option = this.getProgressCircleOption(
        this.stockData.rate,
        100,
        this.$toFixed2(this.stockData.kcAmount) + "亿",
        "",
        "#BDE8D1",
        "#3DCC85",
        "60%",
        "#0F9954"
      );
      // if(window.innerWidth < 1680){
      //   option.title[0].top = '42%';
      // }
      // else{
      //   option.title[0].top = '40%';
      // }
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">库存金额: ${this.$formatNull(this.stockData.kcAmount)}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">已竣未售金额: ${this.$formatNull(
            this.stockData.yjbwsAmount
          )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(this.stockData.rate)}%</div>
          </div>`;
        },
      };
      return option;
    },
    getProgressCircleOption(
      finishNum = 0,
      total = 100,
      text = "",
      subText = "",
      bgColor = "#3DCC85",
      color = "376DF7",
      top = "50%",
      color2 = "#4D7EF7"
    ) {
      let scale = window.innerWidth / 1680;
      let actualBgColor = bgColor;
      let actualColor = color;
      let displayNum = finishNum;

      if (finishNum > total) {
        actualBgColor = color;
        actualColor = color2;
        displayNum = (finishNum % total).toFixed(2);
      }

      const instance = new ProgressCircleOption();
      instance.setBackgroundStyle(actualBgColor);
      instance.setBarItemStyle(actualColor);
      const option = JSON.parse(JSON.stringify(instance.getOption()));
      option.series[0].data = [displayNum];
      option.series[0].roundCap = option.series[0].data[0] > 3;
      option.angleAxis.max = total;
      // option.title[0].subtext = subText;
      // option.title[0].text = text;
      // option.title[0].textStyle.fontSize = 21 * scale;
      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;
      // option.title[0].itemGap = 10 * scale;
      // option.title[0].textStyle.color = actualColor;
      // option.title[0].left = 'center';
      // option.title[0].itemGap = 8 * scale;
      option.title = {
        show: false,
        text: text,
        subtext: subText,
        textStyle: {
          color: actualColor,
        },
      };

      option.color = [color];

      return option;
    },

    getStatusOption() {
      const option = this.getPieOption();
      option.color = ["#8af3a9", "#f8cb7f", "#fa9f92", "#6bc0f3"];
      const scale = window.innerWidth / 1680;
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      const rich = {
        yellow: {
          color: "#ffc72b",
          fontSize: 12,
          padding: [2, 0],
          align: "center",
        },
        total: {
          color: "#ffc72b",
          fontSize: 40 * scale,
          align: "center",
        },
        labelColor: {
          color: "#333333",
          align: "center",
          fontSize: 12 * scale,
          padding: [2 * scale, 0],
        },
        blue: {
          color: "#49dff0",
          fontSize: 16,
          align: "center",
        },
        hr: {
          borderColor: "#0b5263",
          width: "100%",
          borderWidth: 1,
          height: 0,
          margin: [5, 0],
        },
      };
      console.log(rich.labelColor);
      option.series[0].label = {
        show: true, // 显示标签
        position: "outside", // 标签位置
        distance: 5 * scale,
        fontSize: 12 * scale,
        normal: {
          formatter: function (params) {
            return (
              "{labelColor|" +
              params.name +
              "}\n{labelColor|" +
              params.value +
              "亿 " +
              params.data.rate +
              "%" +
              "}"
            );
          },
          rich: rich,
        },
      };

      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          const dataMap = {
            未开工: {
              amount: this.statusData.wkgAmount,
              rate: this.statusData.wkgRate,
            },
            已推未售: {
              amount: this.statusData.ytwsAmount,
              rate: this.statusData.ytwsRate,
            },
            已供未推: {
              amount: this.statusData.ygwsAmount,
              rate: this.statusData.ygwsRate,
            },
            已开未供: {
              amount: this.statusData.ykwgAmount,
              rate: this.statusData.ykwgRate,
            },
          };

          const data = dataMap[params.name] || { amount: 0, rate: 0 };

          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">${
            params.name
          }金额: ${this.$formatNull(data.amount)}亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">${
            params.name
          }占比: ${this.$formatNull(data.rate)}%</div>
          </div>`;
        },
      };
      option.series[0].labelLine = {
        show: true, // 显示连接线
        minTurnAngle: 90,
        normal: {
          length: 12 * scale,
          length2: 12 * scale,
          lineStyle: {
            width: 1 * scale,
          },
        },
      };
      // option.legend.data = ['未开工', '已推未售', '已供未推', '已开未供'];
      option.series[0].data = [
        {
          value: this.statusData.wkgAmount,
          name: "未开工",
          rate: this.statusData.wkgRate,
        },
        {
          value: this.statusData.ytwsAmount,
          name: "已推未售",
          rate: this.statusData.ytwsRate,
        },
        {
          value: this.statusData.ygwsAmount,
          name: "已供未推",
          rate: this.statusData.ygwsRate,
        },
        {
          value: this.statusData.ykwgAmount,
          name: "已开未供",
          rate: this.statusData.ykwgRate,
        },
      ];
      return option;
    },
    getStructureOption() {
      const option = this.getPieOption();
      option.color = ["#8af3a9", "#f8cb7f", "#fa9f92", "#6bc0f3"];
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">业态名称: ${this.$formatNull(params.name)}</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">金额: ${this.$formatNull(params.value)}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(params.data.rate)}%</div>
          </div>`;
        },
      };
      const scale = window.innerWidth / 1680;
      const rich = {
        yellow: {
          color: "#ffc72b",
          fontSize: 12,
          padding: [2, 0],
          align: "center",
        },
        total: {
          color: "#ffc72b",
          fontSize: 40 * scale,
          align: "center",
        },
        labelColor: {
          color: "#333333",
          align: "center",
          fontSize: 12 * scale,
          padding: [2 * scale, 0],
        },
        blue: {
          color: "#49dff0",
          fontSize: 16,
          align: "center",
        },
        hr: {
          borderColor: "#0b5263",
          width: "100%",
          borderWidth: 1,
          height: 0,
          margin: [5, 0],
        },
      };
      option.series[0].label = {
        show: true, // 显示标签
        position: "outside", // 标签位置
        normal: {
          formatter: function (params) {
            return (
              "{labelColor|" +
              params.name +
              "}\n{labelColor|" +
              params.value +
              "亿 " +
              params.data.rate +
              "%" +
              "}"
            );
          },
          rich: rich,
        },
      };

      option.series[0].labelLine = {
        show: true, // 显示连接线
        minTurnAngle: 90,
        normal: {
          length: 12 * scale,
          length2: 12 * scale,
          lineStyle: {
            width: 1 * scale,
          },
        },
      };

      // option.legend.data = this.structureData.map(item => item.projectType)
      this.structureLegend = [];
      for (let i = 0; i < this.structureData?.length; i++) {
        this.structureLegend.push({
          name: this.structureData[i].projectType,
          color: option.color[i],
        });
      }

      // option.legend.data = ['住宅', '下跃', '车位', '仓储', '商业', '其他'];

      option.series[0].data = this.structureData.map((item) => ({
        ...item,
        value: item.amount,
        name: item.projectType,
      }));
      return option;
    },
    getPieOption(color = ["#376DF7", "#53B997", "#6750AA", "#F8C541"]) {
      const pieOption = new PieOption();
      pieOption.setColor(color);
      return pieOption.getOption();
    },
    fetchValueData(projectCode) {
      // 货值
      this.valueLoading = true;
      return API.Value.goodsValue(projectCode)
        .then((res) => {
          this.valueData = res.data;
        })
        .finally(() => {
          this.valueLoading = false;
        });
    },
    fetchStockData(projectCode) {
      // 库存
      this.stockLoading = true;
      return API.Value.inventory(projectCode)
        .then((res) => {
          this.stockData = res.data;
        })
        .finally(() => {
          this.stockLoading = false;
        });
    },
    fetchStatusData(projectCode) {
      // 库存状态
      this.statusLoading = true;
      return API.Value.inventoryStatus(projectCode)
        .then((res) => {
          this.statusData = res.data;
        })
        .finally(() => {
          this.statusLoading = false;
        });
    },
    fetchStructureData(projectCode) {
      // 结构
      this.structureLoading = true;
      return API.Value.inventoryStructure(projectCode)
        .then((res) => {
          this.structureData = res.data;
        })
        .finally(() => {
          this.structureLoading = false;
        });
    },
    fetchAnalyse(projectCode) {
      // 货值分析
      this.tableLoading = true; // 开始加载
      API.Value.analyse(projectCode)
        .then((res) => {
          this.analysisData = res.data.map((item) => ({
            projectType: item.projectType || "--",
            hxName: item.hxName || "--",
            totalNum: item.totalNum === null ? "--" : item.totalNum,
            ysNum: item.ysNum === null ? "--" : item.ysNum,
            wsNum: item.wsNum === null ? "--" : item.wsNum,
            ygwsNum: item.ygwsNum === null ? "--" : item.ygwsNum,
            allTopNum: item.allTopNum === null ? "--" : item.allTopNum,
            allBottomNum: item.allBottomNum === null ? "--" : item.allBottomNum,
            ysTopNum: item.ysTopNum === null ? "--" : item.ysTopNum,
            ysBottomNum: item.ysBottomNum === null ? "--" : item.ysBottomNum,
            wsTopNum: item.wsTopNum === null ? "--" : item.wsTopNum,
            wsBottomNum: item.wsBottomNum === null ? "--" : item.wsBottomNum,
            allHzAmount: item.allHzAmount === null ? '--' : item.allHzAmount,
            ysHzAmount: item.ysHzAmount === null ? '--' : item.ysHzAmount,
            wsHzAmount: item.wsHzAmount === null ? '--' : item.wsHzAmount,
          }));
        })
        .finally(() => {
          this.tableLoading = false; // 结束加载
        });
    },
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断是否是最后一行
      if (rowIndex === this.analysisData.length - 1) {
        // 最后一行，合并第一列和第二列
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          };
        } else if (columnIndex === 1) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }

      // 原有的合并逻辑
      if (columnIndex === 0) {
        // 只处理第一列（业态列）
        const projectType = row.projectType;

        // 向上查找相同业态的起始位置
        let startIndex = rowIndex;
        while (
          startIndex > 0 &&
          this.analysisData[startIndex - 1].projectType === projectType
        ) {
          startIndex--;
        }

        // 计算相同业态的行数
        let spanCount = 0;
        for (let i = startIndex; i < this.analysisData.length; i++) {
          if (this.analysisData[i].projectType === projectType) {
            spanCount++;
          } else {
            break;
          }
        }

        // 只在每组的一行显示，其他行隐藏
        if (rowIndex === startIndex) {
          return {
            rowspan: spanCount,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    handleTopBottomSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断是否是最后一行
      if (rowIndex === this.analysisData.length - 1) {
        // 最后一行，合并第一列和第二列
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          };
        } else if (columnIndex === 1) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      if (columnIndex === 0) {
        // Only handle the first column (projectType)
        const projectType = row.projectType;

        // Find the starting position of the same projectType
        let startIndex = rowIndex;
        while (
          startIndex > 0 &&
          this.analysisData[startIndex - 1].projectType === projectType
        ) {
          startIndex--;
        }

        // Calculate the number of rows with the same projectType
        let spanCount = 0;
        for (let i = startIndex; i < this.analysisData.length; i++) {
          if (this.analysisData[i].projectType === projectType) {
            spanCount++;
          } else {
            break;
          }
        }

        // Only show in the first row of each group, hide others
        if (rowIndex === startIndex) {
          return {
            rowspan: spanCount,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
@import "~@/views/projects/styles/projects.scss";
.tool-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.max-height {
  height: 34.25rem;
}

.projects-content-container {
  .cost-content {
    .card-content {
      padding: 1rem;
      box-sizing: border-box;
    }

    .chart-size-220 {
      height: 16.25rem;
    }

    .chart-size {
      height: 16.25rem;
    }

    .card-title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 0.875rem;
      color: #222222;
      line-height: 1.375rem;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .cost-card-item {
      display: flex;
      justify-content: space-between;
      gap: 1rem;
    }

    .cost-card-item-content {
      .title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 0.875rem;
        color: #222222;
        line-height: 1.375rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      flex: 1;
      padding: 0.75rem;
      border-radius: 0.25rem;

      &:first-child {
        background: rgba(61, 204, 133, 0.05);
      }

      &:nth-child(2) {
        background: rgba(0, 106, 255, 0.05);
      }

      &:nth-child(3) {
        background: rgba(255, 151, 77, 0.05);
      }

      &:nth-child(4) {
        background: rgba(109, 61, 204, 0.05);
      }

      .data-content {
        font-family: PingFang SC, PingFang SC;
        line-height: 1.375rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        justify-content: space-evenly;
        margin-top: -1.25rem;

        .value-item {
          .value {
            font-weight: 500;
            font-size: 1.25rem;
            color: #3dcc85;
          }

          .label {
            font-weight: 400;
            font-size: 0.75rem;
            color: #666666;
          }
        }

        .legend-item {
          margin-top: 1.5rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 0.75rem;
          color: rgba(44, 53, 66, 0.65);
          line-height: 0.85rem;
          display: flex;
          align-items: center;

          .icon {
            margin-right: 0.3rem;
            display: inline-block;
            width: 0.7rem;
            height: 0.7rem;
            border-radius: 50%;
          }
        }
      }

      &.cost {
        .value-item {
          .value {
            font-weight: 500;
            font-size: 1.25rem;
            color: #006aff;
          }
        }
      }

      &.change {
        .value-item {
          .value {
            font-weight: 500;
            font-size: 1.25rem;
            color: #3dcc85;
          }
        }
      }

      &.value {
        .value-item {
          .value {
            font-weight: 500;
            font-size: 1.25rem;
            color: #ff974c;
          }
        }
      }
    }

    .data-content {
      padding: 0 0.75rem 0.75rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .custom-height {
      height: 32rem;
    }
    .custom-max-height {
      max-height: 32rem;
      overflow-y: auto;
    }
    .card-content-grid {
      display: grid;
      grid-template-rows: auto 1fr;
      gap: 1rem;
      .analysis-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        flex-shrink: 0;
      }
      .content-grid {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0.25rem;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        .grid-item {
          .grid-card-item {
            height: 27rem;
            .project-table {
            }
          }
        }
      }
    }
  }
}
</style>
