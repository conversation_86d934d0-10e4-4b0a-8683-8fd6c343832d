
3e4bd9c216aa2404e8abe050b5537f977f3efdd9	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"060515815549ae5b5f8fce2c842ba7da\"}","integrity":"sha512-if3IZhnHP5FV02a3Ok+9ibGxkRHx0bRB/krB77q8vaH1cgvhcofqMpph8Mq7NMyF/7TqBs9HX5ihytiXQheQ7w==","time":1754311739681,"size":12043627}