<!-- AutoTrackNodes.vue -->
<template>
  <div class="track-container">
    <!-- 根据计算的行数自动渲染行 -->
    <div
      v-for="(row, rowIndex) in splitRows"
      :key="rowIndex"
      class="track-row"
      :class="[`row-${rowIndex + 1}`, { completed: isRowCompleted(rowIndex) }]"
    >

      <!-- 轨道线 -->
      <div class="track-line">
        <div class="track-progress" :style="getProgressStyle(rowIndex)" />
      </div>

      <!-- 节点容器 -->
      <div class="milestones-container">
        <div
          v-for="(node, nodeIndex) in row"
          :key="getNodeKey(rowIndex, nodeIndex)"
          class="milestone-item"
          :class="['status-' + node.status]"
          @mouseenter="showTooltip(node, $event)"
          @mouseleave="hideTooltip"
        >

          <!-- 节点圆点 -->
          <div
            class="milestone-point"
            :class="{
              'has-detail': node.detail,
              'last-node': isLastNode(rowIndex, nodeIndex),
              'first-node': isFirstNode(rowIndex, nodeIndex)
            }"
          >
            <div v-if="node.detail" class="detail-indicator" />
          </div>

          <!-- 节点信息 -->
          <div class="milestone-label">
            <div class="milestone-date">{{ node.plannedDate }}</div>
            <div class="milestone-title">{{ node.title }}</div>
            <div class="milestone-actual-date">
              {{ node.actualDate || '未到达' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 连接器 -->
      <template v-if="!isLastRow(rowIndex)">
        <!-- 奇数行使用右侧连接器 -->
        <div v-if="isOddRow(rowIndex)" class="connector right-connector">
          <div class="connector-line" />
        </div>
        <!-- 偶数行使用左侧连接器 -->
        <div v-else class="connector left-connector">
          <div class="connector-line" />
        </div>
      </template>
    </div>

    <!-- 提示框 -->
    <div
      v-if="activeTooltip.visible"
      class="tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-content">
        <h3>{{ activeTooltip.node.title }}</h3>
        <div class="tooltip-info">
          <p>计划完成日期：{{ activeTooltip.node.plannedDate }}</p>
          <p>实际完成日期：{{ activeTooltip.node.actualDate || '未完成' }}</p>
          <p>状态：{{ getStatusText(activeTooltip.node.status) }}</p>
          <p v-if="activeTooltip.node.responsible">
            责任部门：{{ activeTooltip.node.responsible }}
          </p>
          <div v-if="activeTooltip.node.detail" class="detail-content">
            <p
              v-for="(item, idx) in activeTooltip.node.detail"
              :key="idx"
            >{{ item }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AutoTrackNodes',

  props: {
    // 每行最多显示的节点数
    nodesPerRow: {
      type: Number,
      default: 4
    },
    // 节点间最小间距（px）
    minNodeSpacing: {
      type: Number,
      default: 150
    }
  },

  data() {
    return {
      // 所有节点数据放在一个数组中
      nodes: [
        {
          id: 1,
          title: '获取项目',
          plannedDate: '计划完成日期',
          actualDate: '实际完成日期',
          status: 'completed',
          detail: ['用时']
        },
        {
          id: 2,
          title: '方案批复',
          plannedDate: '2024-2-12',
          actualDate: '2024-2-12',
          status: 'completed',
          detail: ['用时：60天']
        },
        {
          id: 3,
          title: '工程规划许可证',
          plannedDate: '2024-2-12',
          actualDate: '2024-1-25',
          status: 'completed',
          responsible: '刘学娇',
          detail: ['按时完成']
        },
        {
          id: 4,
          title: '工程施工许可证',
          plannedDate: '2024-2-12',
          actualDate: '2024-1-25',
          status: 'completed',
          detail: ['按时完成']
        },
        {
          id: 5,
          title: '国有土地使用证',
          plannedDate: '2024-2-12',
          actualDate: '2024-2-12',
          status: 'completed'
        },
        {
          id: 6,
          title: '展示区开放',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'current',
          detail: ['进行中']
        },
        {
          id: 7,
          title: '实景展示区',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 8,
          title: '预售许可证',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 9,
          title: '首次开盘销售',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 10,
          title: '精装修工程完成(户内)',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 11,
          title: '室外综合管线施工完成',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 12,
          title: '外立面完成',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 13,
          title: '规划验收',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 14,
          title: '工程竣工备案',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 15,
          title: '破备后改造',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 16,
          title: '土地开放日',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 17,
          title: '运营交付',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 18,
          title: '办理大产证',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        },
        {
          id: 19,
          title: '合同交付',
          plannedDate: '2024-2-12',
          actualDate: null,
          status: 'pending'
        }
      ],

      activeTooltip: {
        visible: false,
        node: null,
        x: 0,
        y: 0
      },

      containerWidth: 0
    }
  },

  computed: {
    // 根据容器宽度和节点间距计算每行实际可显示的节点数
    actualNodesPerRow() {
      const maxNodes = Math.floor(this.containerWidth / this.minNodeSpacing)
      return Math.min(maxNodes, this.nodesPerRow)
    },

    // 将节点数据分割成多行
    splitRows() {
      const rows = []
      let currentRow = []

      this.nodes.forEach((node, index) => {
        currentRow.push(node)

        if (currentRow.length === this.actualNodesPerRow || index === this.nodes.length - 1) {
          rows.push(currentRow)
          currentRow = []
        }
      })

      return rows
    },

    tooltipStyle() {
      return {
        left: this.activeTooltip.x + 'px',
        top: this.activeTooltip.y + 'px'
      }
    }
  },

  mounted() {
    this.updateContainerWidth()
    window.addEventListener('resize', this.updateContainerWidth)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.updateContainerWidth)
  },

  methods: {
    updateContainerWidth() {
      this.containerWidth = this.$el.getBoundingClientRect().width
    },

    getNodeKey(rowIndex, nodeIndex) {
      const node = this.splitRows[rowIndex][nodeIndex]
      return node.id || `${rowIndex}-${nodeIndex}`
    },

    isRowCompleted(rowIndex) {
      return this.splitRows[rowIndex].every(node => node.status === 'completed')
    },

    getProgressStyle(rowIndex) {
      const row = this.splitRows[rowIndex]
      const completedNodes = row.filter(node => node.status === 'completed').length
      const progress = (completedNodes / row.length) * 100
      return { width: `${progress}%` }
    },

    isLastRow(rowIndex) {
      return rowIndex === this.splitRows.length - 1
    },

    isOddRow(rowIndex) {
      return rowIndex % 2 === 0
    },

    isFirstNode(rowIndex, nodeIndex) {
      return nodeIndex === 0
    },

    isLastNode(rowIndex, nodeIndex) {
      return nodeIndex === this.splitRows[rowIndex].length - 1
    },

    showTooltip(node, event) {
      this.activeTooltip.node = node
      this.activeTooltip.visible = true

      const rect = event.target.getBoundingClientRect()
      this.activeTooltip.x = rect.left + window.scrollX
      this.activeTooltip.y = rect.top + window.scrollY - 120
    },

    hideTooltip() {
      this.activeTooltip.visible = false
    },

    getStatusText(status) {
      const statusMap = {
        completed: '已完成',
        current: '进行中',
        pending: '未开始'
      }
      return statusMap[status] || '未知状态'
    }
  }
}
</script>

<style scoped>
.track-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

.track-row {
  position: relative;
  display: flex;
  margin: 40px 0;
  min-height: 100px;
}

/* 奇数行布局 */
.track-row:nth-child(odd) {
  justify-content: flex-start;
  padding-right: 50px;
}

/* 偶数行布局 */
.track-row:nth-child(even) {
  justify-content: flex-end;
  padding-left: 50px;
  flex-direction: row-reverse;
}

.track-line {
  position: absolute;
  top: 50%;
  height: 2px;
  background: #E5E7EB;
  left: 0;
  right: 0;
}

.track-progress {
  position: absolute;
  height: 100%;
  background: #3B82F6;
  transition: width 0.3s ease;
}

.milestones-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  z-index: 1;
}

.track-row:nth-child(even) .milestones-container {
  flex-direction: row-reverse;
}

.milestone-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  cursor: pointer;
  flex: 1;
  min-width: 0;
  margin: 0 10px;
}

.milestone-point {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-bottom: 8px;
  transition: transform 0.2s ease;
  position: relative;
  z-index: 2;
}

.milestone-point:hover {
  transform: scale(1.2);
}

.status-completed .milestone-point {
  background-color: #3B82F6;
}

.status-current .milestone-point {
  background-color: #EF4444;
}

.status-pending .milestone-point {
  background-color: #D1D5DB;
}

.milestone-label {
  text-align: center;
  min-width: 0;
  padding: 0 5px;
}

.milestone-date {
  font-size: 12px;
  color: #6B7280;
  margin-bottom: 4px;
}

.milestone-title {
  font-size: 14px;
  font-weight: 500;
  color: #1F2937;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.milestone-actual-date {
  font-size: 12px;
  color: #6B7280;
}

/* 连接器样式 */
.connector {
  position: absolute;
  width: 50px;
  height: 50px;
  bottom: -50px;
}

.right-connector {
  right: 0;
}

.left-connector {
  left: 0;
}

.connector-line {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid #E5E7EB;
  border-bottom: none;
}

.right-connector .connector-line {
  border-left: none;
  border-top-right-radius: 50px;
}

.left-connector .connector-line {
  border-right: none;
  border-top-left-radius: 50px;
}

.completed .connector-line {
  border-color: #3B82F6;
}

/* 提示框样式 */
.tooltip {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 12px;
  min-width: 200px;
  transform: translateX(-50%);
}

.tooltip-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8px;
}

.tooltip-info {
  font-size: 14px;
  color: #4B5563;
}

.tooltip-info p {
  margin: 4px 0;
}

.detail-content {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #E5E7EB;
}

@media (max-width: 768px) {
  .track-container {
    padding: 10px;
  }

  .milestone-title {
    font-size: 12px;
  }

  .milestone-item {
    margin: 0 5px;
  }
}
</style>
