<template>
  <div class="chart-container">
    <Chart ref="changeChart" :resizeChartFlag="resizeChartFlag"></Chart>
  </div>
</template>

<script>
import Chart from './Chart'
// import * as echarts from "echarts";
/* const screenWidth = window.innerWidth; // 获取当前屏幕宽度
const designWidth = 1680; // 设计尺寸宽度
const scale = screenWidth / designWidth; // 计算比例 */
export default {
  name: '<PERSON><PERSON>ate<PERSON><PERSON>',
  components: {
    Chart
  },
  props: {
    // 图表数据
    chartData: {
      type: Array,
      required: true
      // [{name: '京四区一所', value: 95, isProject: true}, ...]
    },
    // 主题颜色
    themeColor: {
      type: String,
      default: '#91d5ff'
    },
    // 高亮颜色
    highlightColor: {
      type: String,
      default: '#ff4d4f'
    },
    // 平均分
    averageScore: {
      type: Number,
      default: 83
    },
    // 图表标题
    title: {
      type: String,
      required: true
    },
    resizeChartFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chartId: `chart-${Date.now()}`, // 确保多个图表实例ID唯一
      chart: null,
      option: null
    }
  },
  mounted() {
    this.initChart()
    // 监听窗口大小变化，重绘图表
    // window.addEventListener("resize", this.resizeChart);
  },
  beforeDestroy() {
    // window.removeEventListener("resize", this.resizeChart);
    /* if (this.chart) {
      this.chart.dispose();
    } */
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.updateChart()
      }
    }
    /*  themeColor() {
       this.updateChart();
     }, */
  },
  methods: {
    initChart() {
      // this.chart = echarts.init(document.getElementById(this.chartId))
      // this.chart = echarts.init(this.$refs.evaluateChart);
      // this.updateChart();
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    findMinMax(data) {
      let min = Infinity
      let max = -Infinity
      data.forEach(item => {
        if (item.value < min) {
          min = item.value
        }
        if (item.value > max) {
          max = item.value
        }
      })
      return { min, max }
    },
    updateChart() {
      const screenWidth = window.innerWidth // 获取当前屏幕宽度
      const designWidth = 1680 // 设计尺寸宽度
      const scale = screenWidth / designWidth // 计算比例
      // if (!this.chart) return;
      const windowWidth = window.innerWidth
      const fontSize = Math.floor(18 * (windowWidth / 1680))
      const { min, max } = this.findMinMax(this.chartData)
      // 处理数据，设置高亮
      const seriesData = this.chartData.map((item, index) => {
        const isProject = item.isProject
        return {
          value: item.value,
          isProject: isProject,
          rankNum: item.rankNum,
          itemStyle: {
            color: isProject ? this.highlightColor : this.themeColor
          },
          label: {
            show: true,
            position: 'top',
            color: isProject ? 'red' : '#666666'
          }
        }
      })

      this.option = {
        title: {
          text: this.title,
          left: 'center',
          top: '0px',
          textStyle: {
            color: '#333333',
            fontSize: fontSize,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
          textStyle: {
            fontSize: fontSize,
            lineHeight: Math.floor(fontSize * 1.5) + 'px'
          },
          formatter: (params) => {
            // console.log('params---', params);
            const name = params[0].name
            const value = params[0].value
            const rankNum = params[0].data.rankNum
            return `${name}<br/>分数：${value}分<br/>排名：${rankNum}`
          }
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map((item) => item.name),
          axisLabel: {
            interval: 0,
            rotate: 45,
            margin: 15 * scale,  // 按比例调整
            fontSize: 12 * scale, // 按比例调整字体大小
            color: (value, index) => {
              return this.chartData[index].isProject ? 'red' : '#999999'
            },
            // fontSize: fontSize,
            formatter: (value) => {
              if (value.length > 20) {
                // 截取前20个字符，分成两行，并添加省略号
                return value.substring(0, 10) + '\n' + value.substring(10, 20) + '...'
              } else if (value.length > 10) {
                // 超过10个字符但不超过20个字符，分成两行
                return value.substring(0, 10) + '\n' + value.substring(10)
              }
              return value
            }
          },
          axisTick: {
            alignWithLabel: false,
            show: false,
            length: 10 * scale // 按比例调整
          },
          axisLine: {
            onZero: true,
            show: false,
            lineStyle: {
              color: '#999',
              width: 1 * scale
            }
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#DDD',
              width: 1 * scale
            }
          },
          axisTick: {
            alignWithLabel: false,
            show: false,
            color: '#8C8C8C',
            fontSize: 12 * scale // 按比例调整字体大
          },
          axisLine: {
            onZero: true,
            show: false
          },
          axisLabel: {
            interval: 0,
            color: '#999999',
            fontSize: fontSize
          },
          min: function(value) {
            return Math.floor(value.min - 0.5);
          },
          max: function(value) {
            return Math.ceil(value.max + 0.5);
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            barWidth: 16 * scale,
            barGap: 15 * scale,
            // barCategoryGap: 16 * scale,
            label: {
              show: true,
              position: 'top',
              // fontSize: fontSize,
              distance: 5 * scale,  // 按比例调整
              // position: 'top',
              fontSize: 12 * scale // 按比例调整字体大小
            },
            markLine: {
              symbol: 'none',
              silent: true,
              lineStyle: {
                color: this.highlightColor,
                width: 1 * scale,
                type: 'solid'
              },
              data: [
                {
                  yAxis: this.averageScore,
                  label: {
                    formatter: (params) => {
                      // 使用换行符将每个字符分开，实现垂直显示
                      return this.averageScore + '\n平\n均\n分\n'
                      // return `<div>${this.averageScore}</div><div>平</div><div>均</div><div>分</div>`;
                    },
                    position: 'end',
                    rotate: 0,
                    fontSize: 14 * scale,
                    lineHeight: 14 * scale,
                    padding: [0, 0, 0, 0],
                    color: '#666666'
                  }
                }
              ]
            }
          }
        ]
      }
      this.$refs.changeChart?.updateChart(this.option)
      // this.chart.setOption(option);
    }
  }
}
</script>
<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
