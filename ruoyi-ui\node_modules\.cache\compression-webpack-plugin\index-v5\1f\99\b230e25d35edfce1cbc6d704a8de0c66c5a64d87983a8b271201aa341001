
ed5a59fc0d0741f1309560600c4464c3ace68a17	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"f0094cc8e77335744d2b313aabcb6477\"}","integrity":"sha512-UuhX+lYy4XRj3J9unCbU6XoMQ5v6oj9N4rnVs60u5HXtMVjDuSiV7zK4Porw7YwJi2b+WYl9rZTgVvSMrsGM3A==","time":1754311742856,"size":12045532}