// ProgressBar.vue
<template>
  <div class="progress-container">
    <div class="progress-wrapper"  v-if="!showScale">
      <!-- 进度条主体 不带客户的粗进度条-->
      <div class="progress-bar" :style="{ width: progressWidth}">
        <div v-if="showBubble" class="progress-bubble">
          {{ progressValue }}亿 ({{ percentage }}%)
        </div>
      </div>
    </div>
    <div class="progress-wrapper-scale"  v-if="showScale">
      <!--   进度条主体，带刻度的细进度条   -->
      <div class="progress-bar-scale" :style="{ width: progressWidth}" v-if="showScale">
        <div v-if="showBubble" class="progress-bubble">
          {{ progressValue }}亿 ({{ percentage }}%)
        </div>
      </div>

      <!-- 刻度标记 -->
      <div v-if="showScale" class="scale-container">
        <div v-for="n in 12" :key="n" class="scale-mark">
          <div class="mark" :class="{'mark-active': n / 12 <= percentage / 100}"></div>
          <span class="mark-text" v-show="n === 1 || n === 12">{{ n }}</span>
        </div>
      </div>
    </div>

    <!-- 目标值显示 -->
    <div class="target-value" v-if="showTarget">
      年签约目标￥<span class="value">{{ target }}</span>亿
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressBar',
  props: {
    // 当前进度值
    value: {
      type: Number,
      required: true
    },
    // 目标值
    target: {
      type: Number,
      default: 0
    },
    // 是否显示刻度
    showScale: {
      type: Boolean,
      default: false
    },
    // 是否显示气泡
    showBubble: {
      type: Boolean,
      default: true
    },
    // 是否显示目标值
    showTarget: {
      type: Boolean,
      default: true
    },
    // 进度条颜色
    color: {
      type: String,
      default: '#006AFF'
    }
  },
  computed: {
    // 计算进度条宽度
    progressWidth() {
      const percentage = (this.value / this.target) * 100
      return `${Math.min(percentage, 100)}%`
    },
    // 计算百分比
    percentage() {
      return Math.round((this.value / this.target) * 100)
    },
    // 格式化进度值
    progressValue() {
      return Math.round(this.value)
    }
  },
  mounted() {
    this.$el.style.setProperty('--progress-color', this.color)
  },
  watch: {
    color(newValue) {
      this.$el.style.setProperty('--progress-color', newValue)
    }
  }
}
</script>

<style scoped>
.progress-container {
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  width: 100%;
  margin: 20px 0;
}

.progress-wrapper {
  position: relative;
  height: 8px;
  background: #E8EFF8;
  border-radius: 4px;
  overflow: visible;
}

.progress-bar {
  position: relative;
  height: 100%;
  background: var(--progress-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-wrapper-scale{
  position: relative;
  background: #DDDDDD;
}
.progress-bar-scale {
  position: relative;
  height: 3px;
  background: var(--progress-color);
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 0 0 1px #fff;
}
.progress-bubble {
  position: absolute;
  right: 0;
  top: 16px;
  transform: translateX(50%);
  background: var(--progress-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.progress-bubble::after {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%) rotate(180deg);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--progress-color);
}

.scale-container {
  position: absolute;
  top: 11px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}

.scale-mark {
  position: relative;
}

.mark {
  width: 2px;
  height: 13px;
  background: #D9D9D9;
  position: absolute;
  top: -23px;
}
.mark-active {
  background: var(--progress-color);
}
.mark-text {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  position: absolute;
}

.target-value {
  margin-top: -44px;
  text-align: right;
  font-size: 19px;
  color: #222;

  .value {
    font-size: 24px;
  }
}
</style>
