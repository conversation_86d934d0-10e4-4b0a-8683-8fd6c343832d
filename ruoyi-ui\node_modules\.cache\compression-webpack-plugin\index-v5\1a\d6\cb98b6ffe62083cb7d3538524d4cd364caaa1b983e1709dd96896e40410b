
b7cf8c21cdd0fb7a4f90457a41374c125ed0510e	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"6d59d8628da482ffcdbfdf643f72c8a4\"}","integrity":"sha512-NVjkmFT1n72BktaythjQoOHBSnnGHVKgyi3cOdGt+yc8iTyWxbnzOnLshTv528PjeKkgiEOgz6p+PAyBWw+bBQ==","time":1754312355948,"size":12043158}