<script>
export default {
  name: "ExapndTable",
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      expandedRows: new Set()
    }
  },
  methods: {
    toggleRow(row) {
      if (this.expandedRows.has(row)) {
        this.expandedRows.delete(row)
        row.childList.forEach(child => {
          const index = this.tableData.indexOf(child)
          if (index > -1) {
            this.tableData.splice(index, 1)
          }
        })
      } else {
        this.expandedRows.add(row)
        const index = this.tableData.indexOf(row)
        this.tableData.splice(index + 1, 0, ...row.childList)
      }
    },
    isExpanded(row) {
      return this.expandedRows.has(row)
    }
  }
}
</script>

<template>
  <div class="expand-table">
    <table>
      <thead>
        <tr>
          <th>科目名称</th>
          <th>目标版</th>
          <th>动态版</th>
          <th>差额</th>
        </tr>
      </thead>
      <tbody>
        <tr class="parent-row" v-for="(item, index) in tableData" :key="index" @click="toggleRow(item)" :class="{ 'child-row': !!item.parentSubjectCode }">
          <td>
            <span class="expand-icon" v-if="!item.parentSubjectCode">{{ isExpanded(item) ? '▼' : '▶' }}</span>
            {{ item.subjectName }}
          </td>
          <td>{{ item.targetAmount }}</td>
          <td>{{ item.dynamicAmount }}</td>
          <td>{{ item.diffAmount }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">
.expand-table {
  width: 100%;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 8px 24px;
    text-align: left;
    border: 1px solid rgba(0,106,255,0.2);
  }

  th {
    background: rgba(0,106,255,0.1);
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  tr{
    background: #FFFFFF;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .parent-row {
    cursor: pointer;
    &:hover {
      background-color: #f5f7fa;
    }
  }

  .child-row {
    background-color: #f9f9f9;
    >td:first{
      padding-left: 48px;
    }
  }

  .expand-icon {
    margin-right: 8px;
    display: inline-block;
    width: 16px;
  }

  .expanded-content {
    padding: 16px;
  }
}
</style>
