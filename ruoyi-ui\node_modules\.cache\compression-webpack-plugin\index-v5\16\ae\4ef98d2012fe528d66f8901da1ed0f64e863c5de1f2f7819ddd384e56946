
c0d216178405d1259481d6c278905293fa194e5e	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.7428297803b885b2728d.hot-update.js\",\"contentHash\":\"27b4b2d38325ad98dda074dc51120ef1\"}","integrity":"sha512-eyGBB+Kys0EoojTxIuZv942iJeK+aruSKDTbjOedXii4ioROBNMLRMdQAZaik1q7N0xECuuxtKbb1rP9jikY8g==","time":1754312347208,"size":78315}