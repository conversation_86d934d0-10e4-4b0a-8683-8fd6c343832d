<template>
  <div class="projects-content-container w-100">
    <div v-if="!hasPermi(['project:plan:manage'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限" />
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else class="w-100">
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中" />
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据" />
        <div class="desc">暂无数据</div>
      </div>
      <section v-else class="w-100">
        <div class="plan-content">
          <!--          v-if="qjPlanStages.length > 0"-->
          <div v-loading="loading" class="node-info">
              <CardTab
              v-model="activeName"
              :tabs="qjPlanStages"
              @click="handlePlanStagesClick"
              class="mb-16"
            />
            <section class="main-content">
              <div class="card-content">
              <div class="card-title mb-12">关键节点</div>
              <Empty :no-authority="hasPermi(['plan:keyNode'])">
                <div class="qj-card-wrapper" v-loading="keyNodeLoading">
                  <QJCard
                    class="gj-card-block"
                    v-for="item in keyNodeInfo"
                    :data="item"
                    :bg-color="item.bgColor"
                  ></QJCard>
                </div>
              </Empty>
            </div>
            <div class="card-content mb-12">
              <div class="card-title mb-12">计划节点轨道图</div>
              <div class="status-indicators">
                <section class="indicator-item">
                  <div class="indicator no-involved"></div>
                  不涉及/未到期
                </section>
                <section class="indicator-item">
                  <div class="indicator in-progress"></div>
                  进行中
                </section>
                <section class="indicator-item">
                  <div class="indicator on-time"></div>
                  按期完成
                </section>
                <section class="indicator-item">
                  <div class="indicator delayed-complete"></div>
                  延期完成
                </section>
                <section class="indicator-item">
                  <div class="indicator delayed-incomplete"></div>
                  延期未完成
                </section>
              </div>
              <Empty :no-authority="hasPermi(['plan:nodeTrackMap'])">
                <div
                  class="stage-diagram"
                  ref="stageDiagram"
                  v-loading="nodeTrackLoading"
                >
                  <TrackNodeDiagram
                    ref="trackNode"
                    :canvasWidth="canvasWidth"
                    :nodeSpacing="nodeSpacing"
                    :nodeData="nodeData"
                  ></TrackNodeDiagram>
                </div>
              </Empty>

              <!-- 统计信息 -->
              <div class="statistics-wrapper" v-loading="statisticsLoading">
                <div class="card-title mb-12">计划统计</div>
                <CardTab
                  v-model="statisticsActiveName"
                  :tabs="statisticsTabs"
                  @click="handleStatisticsTabClick"
                  class="mb-14"
                />
                <div class="statistics-content">
                  <div class="statistics-item all">
                    <div class="statistics-item-title mb-12">全周期统计</div>
                    <Empty :no-authority="hasPermi(['plan:nodeCount'])">
                      <div class="statistics-item-content">
                        <section class="card-with-title">
                          <Chart
                            class="chart-size"
                            :option="valueOption"
                          ></Chart>
                          <section class="chart-title-block">
                            <div
                              class="title-1"
                              :style="{
                                color: valueOption.title.textStyle.color,
                              }"
                            >
                              {{ valueOption.title.text }}
                            </div>
                            <div class="title-2">
                              {{ valueOption.title.subtext }}
                            </div>
                          </section>
                        </section>
                        <div class="statistics-item-text all">
                          <div class="number">
                            {{ $formatNull(statisticsData.totalExpireNum) }}
                          </div>
                          <div class="label">到期节点总数</div>
                        </div>
                        <div class="statistics-item-text all">
                          <div class="number">
                            {{ $formatNull(statisticsData.totalFinishNum) }}
                          </div>
                          <div class="label">按时完成个数</div>
                        </div>
                      </div>
                    </Empty>
                  </div>
                  <div class="statistics-item year">
                    <div class="statistics-item-title mb-12">本年统计</div>
                    <Empty :no-authority="hasPermi(['plan:nodeCount'])">
                      <div class="statistics-item-content year">
                        <section class="card-with-title">
                          <Chart
                            class="chart-size"
                            :option="yearOption"
                          ></Chart>
                          <section class="chart-title-block">
                            <div
                              class="title-1"
                              :style="{
                                color: yearOption.title.textStyle.color,
                              }"
                            >
                              {{ yearOption.title.text }}
                            </div>
                            <div class="title-2">
                              {{ yearOption.title.subtext }}
                            </div>
                          </section>
                        </section>
                        <div class="statistics-item-text year">
                          <div class="number">
                            {{ $formatNull(statisticsData.yearExpireNum) }}
                          </div>
                          <div class="label">到期节点总数</div>
                        </div>
                        <div class="statistics-item-text year">
                          <div class="number">
                            {{ $formatNull(statisticsData.yearFinishNum) }}
                          </div>
                          <div class="label">按时完成个数</div>
                        </div>
                      </div>
                    </Empty>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-content mb-100px">
              <div class="card-title mb-12">延期未完成节点</div>
              <CardTab
                v-model="expiredStageActiveName"
                :tabs="expiredStageTabs"
                @click="handleExpiredStageTabClick"
                class="mb-14"
              />
              <div
                class="expired-stage-wrapper"
                v-loading="expiredStageLoading"
              >
                <Empty :no-authority="hasPermi(['plan:qjDelayNoFinish'])">
                  <section>
                    <table class="expired-table">
                      <colgroup>
                        <col class="col-1" />
                        <col class="col-2" />
                        <col class="col-3" />
                        <col class="col-4" />
                        <col class="col-5" />
                      </colgroup>
                      <tr class="head">
                        <th>序号</th>
                        <th>状态</th>
                        <th>节点名称</th>
                        <th>责任部门</th>
                        <th>计划完成日期</th>
                      </tr>
                      <tbody>
                        <tr
                          v-for="(item, index) in expiredStageData"
                          :key="index"
                        >
                          <td>{{ index + 1 }}</td>
                          <td>
                            <img
                              src="@/views/projects/assets/images/alarm.png"
                              alt=""
                              class="alarm"
                            />
                          </td>
                          <td>{{ item.nodeName || "--" }}</td>
                          <td>{{ item.deptName || "--" }}</td>
                          <td>{{ item.planFinishDate || "--" }}</td>
                        </tr>
                      </tbody>
                    </table>
                    <div
                      v-if="!expiredStageData || expiredStageData.length === 0"
                      class="no-data"
                    >
                      暂无数据
                    </div>
                  </section>
                </Empty>
              </div>
            </div>
            </section>

          </div>
          <!-- <div v-else>
            <div class="no-data">暂无数据</div>
          </div> -->
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import API from "@/views/projects/api";
import CardTab from "@/views/projects/components/CardTab.vue";
import QJCard from "@/views/projects/components/QJCard.vue";
import Chart from "@/views/projects/components/Chart.vue";
import ProgressCircleOption from "@/views/projects/constants/ProgressCircle";
import TrackNodeDiagram from "@/views/projects/components/TrackNodeDiagram.vue";
import Empty from "@/views/projects/components/empty.vue";
export default {
  name: "Plan",
  components: {
    CardTab,
    QJCard,
    Chart,
    TrackNodeDiagram,
    Empty,
  },
  data() {
    return {
      nodeTrackLoading: false,
      activeName: null,
      qjPlanStages: [],
      qjPlanStageList: [],
      projectDataPlan: {},
      projectCode: this.$route.query.projectCode,
      keyNodeInfo: [],
      projectState: null, // 当前选中节点
      statisticsActiveName: "一级",
      statisticsTabs: ["一级", "二级", "三级"], // 数据统计节点层级
      statisticsData: {},
      expiredStageTabs: ["一级", "二级" /*, '三级'*/], // 延期未完成节点层级
      expiredStageActiveName: "一级",
      expiredStageData: null,
      canvasWidth: 1300,
      nodeData: [],
      nodeTracShow: false,
      loading: false, // Add a loading state
      nodeSpacing: 158,
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      keyNodeLoading: false,
      statisticsLoading: false,
      expiredStageLoading: false,
    };
  },
  computed: {
    valueOption() {
      return this.getProgressCircleOption(
        this.statisticsData.totalFinishRate,
        "按时完成率",
        "#D6DCF8",
        "#376DF7",
        "#1433CC"
      );
    },
    yearOption() {
      return this.getProgressCircleOption(
        this.statisticsData.yearFinishRate,
        "按时完成率",
        "#F8DEBC",
        "#FF9B47",
        "#e56d16"
      );
    },
  },
  async mounted() {
    this.loading = true; // Start loading

    try {
      await this.getQJPlanStages(this.projectCode); // 获取分期数据
      if (this.qjPlanStages.length !== 0) {
        this.updateCanvasDimensions();
        Promise.all([
          this.getKeyNodeInfo(this.projectState),
          this.fetchStatistics(this.projectState),
          this.fetchExpiredStage(this.projectState),
          this.getNodeTrackMap(this.projectState),
        ]);
      }
    } finally {
      this.loading = false; // End loading
    }

    window.addEventListener("resize", this.handleResize); // Add resize event listener
     document.querySelector('.com-content').style.overflow = 'hidden'
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize); // Remove resize event listener
    document.querySelector('.com-content').style.overflow = 'auto'
  },
  methods: {
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions;
      // Check for all permissions wildcard first
      if (permissions.includes("*:*:*")) {
        return true;
      }
      // Check specific permissions
      return permissions.some((p) => permission.includes(p));
    },
    fetchExpiredStage(data) {
      // 获取延期未完成节点
      this.getQJDelayNoFinish({
        ...data,
        nodeLevel: this.expiredStageActiveName,
      });
    },
    handleExpiredStageTabClick(tab) {
      // 过期节点统计tab切换
      this.statisticsActiveName = tab;
      this.expiredStageActiveName = tab;
      this.fetchExpiredStage(this.projectState);
      this.fetchStatistics(this.projectState);
    },
    fetchStatistics(data) {
      // 获取数据统计
      this.getNodeCount({
        ...data,
        nodeLevel: this.statisticsActiveName,
      });
    },
    handleStatisticsTabClick(tab) {
      // 统计信息tab切换
      this.statisticsActiveName = tab;
      if (tab !== "三级") {
        this.expiredStageActiveName = tab;
        this.fetchExpiredStage(this.projectState);
      }
      this.fetchStatistics(this.projectState);
    },
    async getQJPlanStages(projectCode) {
      // 获取全景项目分期集合
      this.loading = true;
      try {
        const res = await API.Plan.qjPlanStages(projectCode);
        this.qjPlanStageList = res.data;
        this.qjPlanStages = res.data.map((item) => {
          return item.qjProjectStageDisplayName;
        });
        this.hasData = this.qjPlanStages.length > 0;

        this.activeName = this.qjPlanStages[0]; // 设置默认选中分期
        this.projectState = this.qjPlanStageList[0]; // 设置默认选中分期对象
      } catch (error) {
        console.error("Error fetching QJ Plan Stages:", error);
      } finally {
        this.loading = false;
      }
    },
    async getKeyNodeInfo(data) {
      this.keyNodeLoading = true;
      try {
        const res = await API.Plan.keyNodes(data);
        const bgColors = [
          "#A579D4",
          "#3EB6CF",
          "#499DF2",
          "#53BD88",
          "#7B6FF2",
        ];
        const nodeArray = Object.values(res.data).map((node) => node);
        this.keyNodeInfo = nodeArray.map((item, index) => ({
          ...item,
          bgColor: bgColors[index % 5],
        }));
      } finally {
        this.keyNodeLoading = false;
      }
    },
    getNodeTrackMap(data) {
      // 获取节点轨迹
      this.nodeTracShow = false;
      this.nodeTrackLoading = true;
      API.Plan.nodeTrackMap(data).then((res) => {
        this.nodeData = res.data;
        // this.nodeData = [
        // {
        //     "trackMapName": "获取项目",
        //     "status": "按期完成",
        //     "deptName": "投资",
        //     "planFinishDate": "2021-10-13",
        //     "actualFinishDate": "2021-10-13",
        //     "days": 0,
        //     "dotType": 2,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "方案批复",
        //     "status": "按期完成",
        //     "deptName": "设计",
        //     "planFinishDate": "2021-12-07",
        //     "actualFinishDate": "2021-11-30",
        //     "days": 48,
        //     "dotType": 2,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "工程规划许可证",
        //     "status": "按期完成",
        //     "deptName": "开发",
        //     "planFinishDate": "2021-12-15",
        //     "actualFinishDate": "2021-12-02",
        //     "days": 50,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "工程施工许可证",
        //     "status": "按期完成",
        //     "deptName": "开发",
        //     "planFinishDate": "2022-02-11",
        //     "actualFinishDate": "2022-01-19",
        //     "days": 98,
        //     "dotType": 2,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "国有土地使用证",
        //     "status": "按期完成",
        //     "deptName": "开发",
        //     "planFinishDate": "2021-12-01",
        //     "actualFinishDate": "2021-11-26",
        //     "days": 44,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "展示区开放",
        //     "status": "延期完成",
        //     "deptName": "营销",
        //     "planFinishDate": "2022-01-31",
        //     "actualFinishDate": "2022-03-12",
        //     "days": 150,
        //     "dotType": 3,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "实景展示区",
        //     "status": null,
        //     "deptName": null,
        //     "planFinishDate": null,
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 1,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "预售许可证",
        //     "status": "按期完成",
        //     "deptName": "开发",
        //     "planFinishDate": "2022-03-05",
        //     "actualFinishDate": "2022-03-04",
        //     "days": 142,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "首次开盘销售",
        //     "status": "延期完成",
        //     "deptName": "营销",
        //     "planFinishDate": "2022-03-06",
        //     "actualFinishDate": "2022-03-26",
        //     "days": 164,
        //     "dotType": 3,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "土方开挖完成",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2022-08-22",
        //     "actualFinishDate": "2022-07-29",
        //     "days": 289,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "主体结构±0.00（全部）",
        //     "status": "延期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2022-11-10",
        //     "actualFinishDate": "2023-02-25",
        //     "days": 500,
        //     "dotType": 3,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "主体结构封顶（首批）",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2023-03-25",
        //     "actualFinishDate": "2023-03-18",
        //     "days": 521,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "主体结构封顶（全部）",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2023-05-24",
        //     "actualFinishDate": "2023-05-24",
        //     "days": 588,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "二次结构完成",
        //     "status": null,
        //     "deptName": null,
        //     "planFinishDate": null,
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 1,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "首批次工作面移交精装",
        //     "status": null,
        //     "deptName": null,
        //     "planFinishDate": null,
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 1,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "外立面完成",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2023-10-25",
        //     "actualFinishDate": "2023-10-25",
        //     "days": 742,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "室外综合管线施工完成",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2024-04-06",
        //     "actualFinishDate": "2023-11-01",
        //     "days": 749,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "精装修工程完成（户内）",
        //     "status": null,
        //     "deptName": null,
        //     "planFinishDate": null,
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 1,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "规划验收",
        //     "status": "按期完成",
        //     "deptName": "开发",
        //     "planFinishDate": "2024-05-26",
        //     "actualFinishDate": "2023-11-23",
        //     "days": 771,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "工程竣工备案",
        //     "status": "按期完成",
        //     "deptName": "工程",
        //     "planFinishDate": "2024-06-25",
        //     "actualFinishDate": "2023-12-25",
        //     "days": 803,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "办理大产证",
        //     "status": "未到期",
        //     "deptName": "开发",
        //     "planFinishDate": "2025-03-29",
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 5,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "竣备后改造",
        //     "status": null,
        //     "deptName": null,
        //     "planFinishDate": null,
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 1,
        //     "lineColour": 2
        // },
        // {
        //     "trackMapName": "工地开放日",
        //     "status": "按期完成",
        //     "deptName": "工程、客关",
        //     "planFinishDate": "2024-11-15",
        //     "actualFinishDate": "2024-11-05",
        //     "days": 1119,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "运营交付",
        //     "status": "按期完成",
        //     "deptName": "设计、工程、客关、物业",
        //     "planFinishDate": "2024-09-30",
        //     "actualFinishDate": "2024-09-30",
        //     "days": 1083,
        //     "dotType": 2,
        //     "lineColour": 1
        // },
        // {
        //     "trackMapName": "合同交付",
        //     "status": "未到期",
        //     "deptName": "客关",
        //     "planFinishDate": "2024-12-31",
        //     "actualFinishDate": null,
        //     "days": null,
        //     "dotType": 5,
        //     "lineColour": 2
        // }
        //   ]
        // console.log('nodeSpacing------', this.nodeSpacing, '-----canvasWidth-----', this.canvasWidth)
        // this.$refs.trackNode.setNodeData(this.nodeTrackMap);
        this.nodeTracShow = true;
        this.$nextTick(() => {
          // this.canvasWidth = this.$refs.stageDiagram.clientWidth
          this.$refs.trackNode.initCanvas();
          this.nodeTrackLoading = false;
        });
      });
    },
    async getNodeCount(data) {
      this.statisticsLoading = true;
      try {
        const res = await API.Plan.nodeCount(data);
        this.statisticsData = res.data;
      } finally {
        this.statisticsLoading = false;
      }
    },
    async getQJDelayNoFinish(data) {
      this.expiredStageLoading = true;
      try {
        const res = await API.Plan.qjDelayNoFinish(data);
        this.expiredStageData = res.data;
      } finally {
        this.expiredStageLoading = false;
      }
    },
    getProgressCircleOption(
      finishNum = 0,
      subText = "",
      bgColor = "#D6DCF8",
      color = "376DF7",
      color2 = "#1433CC"
    ) {
      const instance = new ProgressCircleOption();
      if (finishNum > 100) {
        instance.setBackgroundStyle(color);
        instance.setBarItemStyle(color2);
      } else {
        instance.setBackgroundStyle(bgColor);
        instance.setBarItemStyle(color);
      }
      const option = JSON.parse(JSON.stringify(instance.getOption()));
      // option.series[0].data = [
      //   finishNum
      // ];
      const scale = window.innerWidth / 1680;
      option.series[0].data = [
        finishNum > 100 ? (finishNum % 100).toFixed(2) : finishNum,
      ];
      option.series[0].roundCap = option.series[0].data[0] > 3;
      option.polar.radius = ["100%", "75%"];
      option.angleAxis.max = 100;
      option.title = {
        text: `${this.$toFixed2(finishNum)}%`,
        subtext: this.$formatNull(subText),
        show: false,
        textStyle: {
          color: color,
          fontSize: 21 * scale,
        },
        subtextStyle: {
          fontSize: 21 * scale * 0.6,
        },
        itemGap: 10 * scale,
      };
      // option.title.subtext = this.$formatNull(subText);
      // option.title.text = `${this.$formatNull(finishNum)}%`;
      // option.title.textStyle.color = color;
      // option.title.textStyle.fontSize = 21 * scale
      // option.title.subtextStyle.fontSize = 21 * scale * 0.6;
      // option.title.itemGap = 10 * scale;
      // option.title.show = true;
      // if(window.innerWidth < 1680){
      //   option.title[0].top = '20%';
      // }
      // else{
      //   option.title[0].top = '40%';
      // }
      option.tooltip = {
        show: false,
      };
      option.series[1].tooltip = {
        show: false,
      };
      return option;
    },
    handlePlanStagesClick(tab) {
      // 项目分期tab切换
      this.qjPlanStageList.forEach((item) => {
        if (item.qjProjectStageDisplayName === tab) {
          this.projectState = item;
        }
      });
      this.getKeyNodeInfo(this.projectState);
      this.fetchStatistics(this.projectState);
      this.fetchExpiredStage(this.projectState);
      this.getNodeTrackMap(this.projectState);
    },
    updateCanvasDimensions() {
      const designWidth = 1440;
      const currentWidth = document.querySelector(".stage-diagram").offsetWidth;
      const scaleFactor = currentWidth / designWidth;

      this.canvasWidth = currentWidth - 90 * scaleFactor;
      // this.nodeSpacing = (this.canvasWidth / 9) - (10 * scaleFactor);
      this.nodeSpacing = (this.canvasWidth - 200 * scaleFactor) / 8;
    },
    handleResize() {
      this.nodeTracShow = false;
      this.updateCanvasDimensions();
      console.log(
        "Plan-----handleResize-----",
        this.canvasWidth,
        this.nodeSpacing
      );
      this.nodeTracShow = true;
      this.$nextTick(() => {
        this.$refs.trackNode.initCanvas(); // Re-render the TrackNodeDiagram component
      });
    },
  },
};
</script>
<style scoped lang="scss">
@import "~@/views/projects/styles/projects.scss";
.plan-content {
  .node-info {
    padding: 1rem  1rem 0 1rem;
    background: rgba(255, 255, 255, 0.7);
  }
  .card-content {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.25rem;
  }
  .qj-card-wrapper {
    padding: 0.75rem 1rem;
    display: flex;
    gap: 0.75rem;
    .gj-card-block {
      flex: 1;
    }
  }
  .main-content{
    max-height: calc(100vh - 19.75rem) !important;
    overflow-y: auto;
  }
  .statistics-wrapper {
    .statistics-content {
      display: flex;
      gap: 1rem;
      .statistics-item {
        flex: 1;
        padding: 0.75rem;
        box-sizing: border-box;
        &.all {
          background: rgba(0, 106, 255, 0.05);
          border-radius: 0.25rem;
        }
        &.year {
          background: rgba(255, 155, 71, 0.05);
          border-radius: 0.25rem;
        }
        .statistics-item-content {
          display: flex;
          align-content: center;
          justify-content: space-between;
          padding: 0 6.25rem;
          .chart-size {
            width: 8.75rem;
            height: 8.75rem;
          }
          .statistics-item-text {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 0.5rem;
            &.all,
            &.year {
              .number {
                text-align: center;
                font-size: 1.25rem;
                line-height: 1.375rem;
              }
              .label {
                text-align: center;
                font-size: 0.75rem;
                line-height: 1.375rem;
              }
            }
            &.all .number {
              color: #006aff;
            }
            &.all .label {
              color: #666666;
            }
            &.year .number {
              color: #ff9b47;
            }
            &.year .label {
              color: #666666;
            }
          }
        }
      }
    }
  }
  .expired-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 0.0625rem solid rgba(0, 106, 255, 0.2);
    colgroup {
      .col-1 {
        width: 5rem;
      }
      .col-2 {
        width: 6.25rem;
      }
      .col-4,
      .col-5 {
        width: 18.75rem;
      }
    }
    .head {
      background: rgba(0, 106, 255, 0.1);

      > th {
        padding: 0.5rem 0.375rem;
        font-size: 0.875rem;
        line-height: 1rem;
      }
    }
    > tbody {
      tr {
        border: 0.0625rem solid rgba(0, 106, 255, 0.2);
      }
      td {
        padding: 0.5rem 0;
        font-size: 0.75rem;
        line-height: 0.875rem;
        text-align: center;
        .alarm {
          width: 0.875rem;
          height: 1.125rem;
        }
      }
    }
  }
  .status-indicators {
    padding-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1.5rem;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    line-height: 1.375rem;
    text-align: center;
    .indicator-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .indicator {
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      display: inline-block;
    }
    .no-involved {
      // border: 0.125rem solid #DDDDDD;
      background-color: #dddddd;
    }

    .in-progress {
      border: 0.065rem solid #006aff;
      background-color: transparent;
    }

    .on-time {
      background-color: #24b253;
    }

    .delayed-complete {
      background-color: #ff8000;
    }

    .delayed-incomplete {
      background-color: #ff4040;
    }
  }
}
.no-data {
  width: 100%;
  td {
    text-align: center;
    padding: 1rem 0;
  }
}
</style>
