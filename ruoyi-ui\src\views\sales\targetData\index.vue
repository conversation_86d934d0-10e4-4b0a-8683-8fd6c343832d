<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="城市公司" prop="cityCompanyName">
        <el-select
          v-model="queryParams.cityCompanyName"
          placeholder="请选择城市公司"
          clearable
          @change="handleQueryProject"
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict"
            :label="dict"
            :value="dict"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in projects"
            :key="dict.projectId"
            :label="dict.projectName"
            :value="dict.projectId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate"
        >导入模板下载</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="targetList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="城市公司名称" align="center" prop="cityCompanyName" />
      <el-table-column label="项目id" align="center" prop="projectId" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="全盘签约目标金额（元）" align="center" prop="qyTargetAmount" />
      <el-table-column label="全盘回款目标金额（元）" align="center" prop="hkTargetAmount" />
      <el-table-column label="住宅签约目标金额（元）" align="center" prop="zzQyTargetAmount" />
      <el-table-column label="住宅回款目标金额（元）" align="center" prop="zzHkTargetAmount" />
      <el-table-column label="非住签约目标金额（元）" align="center" prop="noZzQyTargetAmount" />
      <el-table-column label="非住回款目标金额（元）" align="center" prop="noZzHkTargetAmount" />
      <el-table-column label="款齐目标金额（元）" align="center" prop="kqTargetAmount" />
      <el-table-column label="利润目标金额（元）" align="center" prop="lrTargetAmount" />
      <el-table-column label="现金流目标金额（元）" align="center" prop="xjlTargetAmount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改销售目标对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <el-form-item label="城市公司" prop="cityCompanyName">
          <el-select
            v-model="form.cityCompanyName"
            placeholder="请选择城市公司"
            clearable
            @change="handleQueryProject"
          >
            <el-option
              v-for="dict in cityCompanys"
              :key="dict"
              :label="dict"
              :value="dict"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-select
            v-model="form.projectName"
            placeholder="请选择项目"
            clearable
            filterable
            @change="changeProject"
          >
            <el-option
              v-for="dict in projects"
              :key="dict.projectId"
              :label="dict.projectName"
              :value="dict.projectId"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="项目id" prop="projectId" >
          <el-input v-model="form.projectId" disabled="disabled" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="年份" prop="year">
          <el-input v-model="form.year" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="全盘签约目标金额（元）" prop="qyTargetAmount">
          <el-input v-model="form.qyTargetAmount" placeholder="请输入签约目标金额" />
        </el-form-item>
        <el-form-item label="全盘回款目标金额（元）" prop="hkTargetAmount">
          <el-input v-model="form.hkTargetAmount" placeholder="请输入回款目标金额" />
        </el-form-item>
        <el-form-item label="非住签约目标金额（元）" prop="noZzQyTargetAmount">
          <el-input v-model="form.noZzQyTargetAmount" placeholder="请输入非住签约目标金额" />
        </el-form-item>
        <el-form-item label="非住回款目标金额（元）" prop="noZzHkTargetAmount">
          <el-input v-model="form.noZzHkTargetAmount" placeholder="请输入非住回款目标金额" />
        </el-form-item>
        <el-form-item label="款齐目标金额（元）" prop="kqTargetAmount">
          <el-input v-model="form.kqTargetAmount" placeholder="请输入款齐目标金额" />
        </el-form-item>
        <el-form-item label="利润目标金额（元）" prop="lrTargetAmount">
          <el-input v-model="form.lrTargetAmount" placeholder="请输入利润目标金额" />
        </el-form-item>
        <el-form-item label="现金流目标金额（元）" prop="xjlTargetAmount">
          <el-input v-model="form.xjlTargetAmount" placeholder="请输入现金流目标金额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 销售目标导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>



<script>
  import {getTarget, listTarget, addTarget, updateTarget, delTarget, getCity, getProject} from "@/api/sales/target";
  import { getToken } from "@/utils/auth";
  export default {
  name: "Target",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售目标表格数据
      targetList: [],
      selectedValue: '',
      //年度类型
      yearTypeList: [{"value":1,"label":"半年度"},{"value":2,"label":"年度"}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 销售目标导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "销售目标导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/sales/targetData/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        cityCompanyName: null,
        projectId: null,
        projectName: null,
        year: null,
        yearType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cityCompanyName: [
          { required: true, message: "城市公司不能为空", trigger: "blur" }
        ],
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "blur" }
        ],
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        year: [
          { required: true, message: "年份不能为空", trigger: "blur" }
        ],
        qyTargetAmount: [
          { required: true, message: "全盘签约目标数(元)不能为空", trigger: "blur" }
        ],
        hkTargetAmount: [
          { required: true, message: "全盘回款目标数(元)不能为空", trigger: "blur" }
        ],
        noZzQyTargetAmount: [
          { required: true, message: "非住签约目标金额(元)不能为空", trigger: "blur" }
        ],
        noZzHkTargetAmount: [
          { required: true, message: "非住回款目标金额(元)不能为空", trigger: "blur" }
        ],
        kqTargetAmount: [
          { required: true, message: "款齐目标金额(元)不能为空", trigger: "blur" }
        ],
        lrTargetAmount: [
          { required: true, message: "利润目标金额(元)不能为空", trigger: "blur" }
        ],
        xjlTargetAmount: [
          { required: true, message: "现金流目标金额(元)不能为空", trigger: "blur" }
        ],
      },
      cityCompanys: [],
      projects: [],
    };
  },
  mounted(value) {
    this.selectedValue = value;
  },
  created() {
    this.getList();
    this.getCity();
  },
  methods: {
    /** 查询销售目标列表 */
    getList() {
      this.loading = true;
      listTarget(this.queryParams).then(response => {
        this.targetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getCity() {
      getCity().then(response => {
        this.cityCompanys = response.data
      })
    },
    handleQueryProject(value) {
      if (value == null || value == "") {
        return;
      }
      getProject(value).then(response => {
        this.projects = response.data
      })
      this.form.projectId = null
      this.form.projectName = null
    },
    changeProject(projectId) {
      this.form.projectId = projectId
      this.form.projectName = this.projects.find(item => item.projectId === projectId)?.projectName
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        cityCompanyName: null,
        projectId: null,
        projectName: null,
        year: null,
        yearType: null,
        qyTargetAmount: null,
        hkTargetAmount: null,
        noZzQyTargetAmount: null,
        noZzHkTargetAmount: null,
        kqTargetAmount: null,
        lrTargetAmount: null,
        xjlTargetAmount: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加销售目标";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTarget(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改销售目标";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTarget(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTarget(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除销售目标编号为"' + ids + '"的数据项？').then(function() {
        return delTarget(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/targetData/export', {
        ...this.queryParams
      }, `销售目标_${new Date().getTime()}.xlsx`)
    },
    handleExportTemplate() {
      if (this.queryParams.cityCompanyName == null || this.queryParams.cityCompanyName == '') {
        this.$modal.msgWarning("导入模板前请选择城市公司");
        return;
      }
      this.download('sales/targetData/exportTemplate', {
        ...this.queryParams
      }, `销售目标导入模板_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "销售目标导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    /*importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },*/
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  }
};
</script>
