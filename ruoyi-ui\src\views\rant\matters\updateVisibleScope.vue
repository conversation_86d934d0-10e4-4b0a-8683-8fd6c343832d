<template>
  <div class="app-container">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px" class="form-block">
        <div class="card-title">{{formTitle}}</div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="来源" prop="ranterName">
              <div class="ranter-input-container">
                <el-tag
                  v-for="(tag, index) in ranterTags"
                  :key="index"
                  class="ranter-tag">
                  {{tag}}
                </el-tag>
                <!-- <el-input
                  v-if="inputVisible"
                  v-model="inputValue"
                  ref="saveTagInput"
                  size="small"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                  @focus="selectUserFun('ranter')"
                  placeholder="请选择来源">
                  <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('ranter')"
                    style="cursor: pointer;"></i>
                </el-input>
                <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 添加来源</el-button> -->
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类型" prop="mattersTypeList" required>
              <el-select class="w-100 custom-form-item-bac" v-model="form.mattersTypeList" multiple placeholder="请选择类型" disabled>
                <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分类" prop="rantClassify">
              <el-select class="w-100 custom-form-item-bac" v-model="form.rantClassify" placeholder="请选择分类" disabled>
                <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                  :value="dict.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="内容" prop="rantContent">
              <el-input class="custom-form-item-bac" v-model="form.rantContent" type="textarea" :autosize="{ minRows: 3, maxRows: 20}" disabled placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-if="!hasTalkTypeOnly">
            <el-form-item label="措施" prop="solution" :rules="[{ required: true, message: '措施不能为空', trigger: ['blur', 'change'] }]">
              <el-input class="custom-form-item-bac" v-model="form.solution" type="textarea" placeholder="请输入内容" :autosize="{ minRows: 3, maxRows: 20}" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-else>
            <el-form-item label="措施" prop="solution">
              <el-input class="custom-form-item-bac" v-model="form.solution" type="textarea" placeholder="请输入内容" :autosize="{ minRows: 3, maxRows: 20}" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="责任人" prop="responsiblePersonName">
              <el-input class="custom-form-item-bac" v-model="form.responsiblePersonName" placeholder="请选择责任人" clearable disabled
                        @keyup.enter.native="handleQuery" @focus="selectUserFun('responsibilityer')">
                <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('responsibilityer')"
                   style="cursor: pointer;"></i></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任部门" prop="deptId">
              <treeselect class="custom-form-item-bac" v-model="form.deptId" :options="deptOptions" :normalizer="normalizer" @input="selectDept"
                placeholder="选择责任部门" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门负责人" prop="respDeptResponsiblerName">
              <el-input class="custom-form-item-bac" readonly="readonly" v-model="form.respDeptResponsiblerName" placeholder="" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>

          <el-col :span="8" v-show="isShowRespDeptLeader">
            <el-form-item label="分管领导" prop="respDeptLeaderName">
              <el-input class="custom-form-item-bac" readonly="readonly" v-model="form.respDeptLeaderName" placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!hasTalkTypeOnly">
            <el-form-item label="计划完成时间" prop="planTime" :rules="[{ required: true, message: '计划完成时间不能为空', trigger: ['blur', 'change'] }]">
              <el-date-picker class="w-100  custom-form-item-bac" clearable size="small" v-model="form.planTime" type="date" disabled
                value-format="yyyy-MM-dd" placeholder="选择计划完成时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-else>
            <el-form-item label="计划完成时间" prop="planTime">
              <el-date-picker class="w-100" clearable size="small" v-model="form.planTime" type="date" disabled
                value-format="yyyy-MM-dd" placeholder="选择计划完成时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否私密" prop="isPrivate">
              <el-select class="w-100" v-model="form.isPrivate" placeholder="请选择是否私密">
                <el-option v-for="item in isPrivateOption" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="form.isPrivate == 1" :span="8">
            <el-form-item label="可见部门">
<!--              :normalizer="normalizerVisible"-->
              <treeselect v-model="form.visibleDepts" :multiple="true" :options="deptVisibleOptions"
                 placeholder="选择可见部门" :defaultExpandLevel="1"/>
            </el-form-item>
          </el-col>
          <el-col v-if="form.isPrivate == 1" :span="8">
            <el-form-item label="可见人员">
<!--              <el-input v-model="visibleUserNames" placeholder="请选择可见人员" clearable @keyup.enter.native="handleQuery"
                @focus="selectUserFun('visibleUser')">
                <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('visibleUser')"
                  style="cursor: pointer;"></i></el-input>-->

                <div class="ranter-input-container">
                    <el-tag
                      v-for="(tag, index) in visibleUserTags"
                      :key="index"
                      closable
                      @close="handleVisibleUserTagClose(index)"
                      class="ranter-tag">
                      {{tag}}
                    </el-tag>
                    <el-input
                      v-if="inputUserVisible"
                      v-model="inputUserValue"
                      ref="saveUserTagInput"
                      size="small"
                      @keyup.enter.native="handleVisibleUserInputConfirm"
                      @blur="handleVisibleUserInputConfirm"
                      @focus="selectUserFun('visibleUser')"
                      placeholder="请选择可见人员">
                      <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('visibleUser')"
                         style="cursor: pointer;"></i>
                    </el-input>
                    <el-button class="button-new-tag" size="small" @click="showVisibleUserInput">+ 添加可见人员</el-button>
                  </div>

            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div class="dialog-footer">
        <el-button class="submit-btn" type="primary" @click="submitForm">提 交
        </el-button>
       <!--  <el-button class="submit-btn" v-if="!isHavaSave" type="primary" @click="submitInForm()">提 交
        </el-button>
        <el-button class="save-btn" v-if="isHavaSave" type="primary" @click="submitForm(0)">保 存
        </el-button> -->
        <el-button class="cancel-btn" @click="cancel">取 消</el-button>
      </div>

    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
      @feedbackEmit="selectUserData" />
  </div>
</template>

<script>
import {
  getMatters,
  listMatters,
  addMatters,
  updateMatters,
  delMatters,
  changeRantMattersStatus,
  submitIn,
  deptTree,
  changeVisible
} from "@/api/rant/matters";
import SelectUser from "@/views/rant/components/SelectUser/index.vue";
import { getToken } from "@/utils/auth";
import { rantStatusOption, isPrivateOption } from "@/constant";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listRespdet, getRespdet } from "@/api/rant/respdet";
import { listDept, listDeptExcludeChild } from "@/api/system/dept";
export default {
  name: "Matters",
  components: { SelectUser, StatusTag, Treeselect },
  dicts: ['rant_classify', 'rant_matters_type'],
  data() {
    return {
      selectedRanterList: [], // 来源选择的人员列表
      selectRanterUserIds: [], // 来源选择的人员id列表
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      indexes: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督办事项表格数据
      mattersList: [],
      // 弹出层标题
      title: "",
      formTitle: "新增督办事项",
      // 是否显示弹出层
      open: false,
      //是否显示保存
      isHavaSave: true,

      //选择用户
      selectUserType: "",
      ranter: null,
      ranterName: "",
      responsiblePerson: null,
      responsiblePersonName: "",
      deptId: null,
      deptName: "",
      roleName: "",
      isMultiple: false,
      responsiblePersonNameQuery: "",
      // 责任部门列表
      deptOptions: [],
      deptVisibleOptions: [],
      //可见部门
      visibleDepts: [],
      visibleUserNames: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranter: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        responsiblePerson: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: null,
        isPrivate: null,
      },
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "销售目标导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/rant/matters/importData",
      },
      // 表单参数
      form: {
        id: null,
        ranter: null,
        ranterName: null,
        mattersTypeList: [],
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        respDeptResponsibler: null,
        respDeptResponsiblerName: null,
        respDeptLeader: null,
        respDeptLeaderName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        visibleDepts: [],
        visibleDeptNames: [],
        visibleUsers: null,
        visibleUserNames: null,
        isPrivate: 0,
      },
      // 表单校验
      rules: {
        ranterName: [
          {
            required: true,
            message: "来源不能为空",
            trigger: ["change"],
          },
        ],
        deptId: [
          {
            required: true,
            message: "责任部门不能为空",
            trigger: ["blur", "change"],
          },
        ],
        isPrivate: [
          {
            required: true,
            message: "是否私密不能为空",
            trigger: ["blur", "change"],
          },
        ],
        mattersTypeList: [
          {
            validator: (rule, value, callback) => {
              if (value && value.length > 0) {
                callback();
              } else {
                callback(new Error('类型不能为空'));
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        rantClassify: [
          {
            required: true,
            message: "分类不能为空",
            trigger: ["blur", "change"],
          },
        ],
      /*   planTime: [
          {
            required: true,
            message: "计划完成时间不能为空",
            trigger: ["blur", "change"],
          },
        ], */
        responsiblePersonName: [
          {
            required: true,
            message: "责任人不能为空",
            trigger: ["blur", "change"],
          },
        ],
        rantContent: [
          {
            required: true,
            message: "内容不能为空",
            trigger: ["blur", "change"],
          },
        ],
        /* solution: [
          {
            required: true,
            message: "措施不能为空",
            trigger: ["blur", "change"],
          },
        ], */
      },
      selectUserShow: true,
      inputVisible: false,
      inputValue: '',
      inputUserVisible: false,
      inputUserValue: '',
    };
  },
  created() {
    // this.getList();
    this.type = this.$route.query.type;
    this.id = this.$route.query.id;
    this.isHavaSave = this.$route.query.isHavaSave === 'true';
   /*  if (this.type === 'add') {
      this.formTitle = '新增督办事项';
      this.handleAdd()
    }
    else  */if(this.type === 'edit'){
      this.formTitle = '编辑督办事项';
      this.handleUpdate(this.id,this.isHavaSave)
    }
  },
  computed: {
    hasTalkTypeOnly(){
      return this.form.mattersTypeList && this.form.mattersTypeList.length === 1 && this.form.mattersTypeList.includes('1');
    },
    rantStatusOption() {
      return rantStatusOption;
    },
    isPrivateOption: function () {
      return isPrivateOption
    },
    ranterTags() {
      return this.form.ranterName ? this.form.ranterName.split(',') : [];
    },
    visibleUserTags() {
      // return this.form.visibleUserNames ? this.form.visibleUserNames?.split(',') : [];
      console.log('this.form.visibleUserNames---', this.form.visibleUserNames);
      return this.form.visibleUserNames || [];
    },
    isShowRespDeptLeader(){
      return this.form?.mattersTypeList?.includes('3')
    }
  },
  watch: {
    "form.visibleDepts"(val) {
      console.log("form.visibleDepts------------", val)
    },
  },
  methods: {
    handleDialogClose() {
      console.log("handleDialogClose");
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);

    },
    handleClear() {
      this.selectUserShow = false;
      this.form.ranter = null;
      this.form.ranterName = null;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);
    },
    /** 查询督办事项列表 */
    getList() {
      this.loading = true;
      listMatters(this.queryParams).then((response) => {
        this.mattersList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      // this.open = false;
      // this.reset();
      this.$tab.closeOpenPage({ path: '/rant/matters' });
    },
    // 表单重置
    reset() {
      this.mattersType = [];
      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        respDeptResponsibler: null,
        respDeptResponsiblerName: null,
        respDeptLeader: null,
        respDeptLeaderName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        visibleDepts: [],
        visibleDeptNames: [],
        visibleUsers: [],
        visibleUserNames: [],
        isPrivate: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.indexes = selection.map((item) => {
        const index = this.mattersList.findIndex(row => row.id === item.id);
        return index !== -1 ? index + 1 : -1;
      });
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
      deptTree().then(response => { // deptVisibleOptions
        this.deptVisibleOptions = response.data;
      });
      // this.open = true;
      this.isHavaSave = true;
      this.selectUserShow = true;
    },
    /** 修改按钮操作 */
    handleUpdate(id, isHavaSave, deptId) {
      // this.reset();
      // const id = row.id;
      this.isHavaSave = isHavaSave;
      this.selectUserShow = true;
      getMatters(id).then((response) => {
        this.form = response.data;
        this.ranter = response.data.ranter;
        this.ranterName = response.data.ranterName;
        this.responsiblePersonName = response.data.responsiblePersonName;
        this.responsiblePerson = response.data.responsiblePerson;
        this.deptId = response.data.deptId;
        this.deptName = response.data.deptName;
        this.open = true;
        if(response.data.visibleDepts && Array.isArray(response.data.visibleDepts) && response.data.visibleDepts.length ) { // 解决treeselect已选中选项无法操作问题
          this.form.visibleDepts = response.data.visibleDepts.map(item => parseInt(item)).filter(item => !isNaN(item));
        }
        if (response.data.visibleUserNames && response.data.visibleUserNames != null && response.data.visibleUserNames != '') {
          // this.visibleUserNames = response.data.visibleUserNames.join(',');
          this.visibleUserNames = response.data.visibleUserNames;
          // this.form.visibleUsers = response.data.visibleUsers.join(',');
          this.form.visibleUsers = response.data.visibleUsers;
          // this.form.visibleUserNames = response.data.visibleUserNames.join(',');
          this.form.visibleUserNames = response.data.visibleUserNames;
        }
        if (typeof this.form.mattersType == 'string' && this.form.mattersType != null && this.form.mattersType != '') {

          this.$set(this.form, 'mattersTypeList', this.form.mattersType.split(','));
          // this.form.mattersTypeList = this.form.mattersType.split(',');
        }

        this.$nextTick(() => {
         if (this.$refs.form) {
           this.$refs.form.clearValidate();
         }
       });

        this.title = "修改督办事项";
      });
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
      deptTree().then(response => { // deptVisibleOptions
        this.deptVisibleOptions = response.data;
        console.log('deptVisibleOptions------------', this.deptVisibleOptions);
      });
    },

    /** 提交按钮 */
    submitForm(submitStatus) {

      this.$refs["form"].validate((valid) => {
        if (valid) {
          const validNames = []
          const targetIds = this.form.visibleDepts;
          const findDeptById = (items, targetId) => {
            for (const item of items) {
              if (item.id == Number(targetId)) {
                return item;
              }
              if (item.children) {
                const found = findDeptById(item.children, targetId);
                if (found) return found;
              }
            }
            return null;
          };
          for (const id of targetIds || []) {
            for (const item of this.deptVisibleOptions) {
              if (item.id == Number(id)) {
                validNames.push(item.label);
                break;
              } else if (item.children) {
                const found = findDeptById(item.children, id);
                if (found) {
                  validNames.push(found.label);
                  break;
                };
              }
            }
          }

          this.form.visibleDeptNames = validNames
          this.form.mattersType = this.form.mattersTypeList?.join(',');

          if (this.form.id != null) {
            changeVisible({
              id: this.form.id,
              isPrivate: this.form.isPrivate,
              visibleDepts: this.form.visibleDepts,
              visibleDeptNames: this.form.visibleDeptNames,
              visibleUserNames: this.form.visibleUserNames,
              visibleUsers: this.form.visibleUsers,
            }).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$tab.closeOpenPage({ path: '/rant/matters' });
            });
          }
        }
      });
    },
    /** 提交按钮进心中的 */
    submitInForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const findDept = (items) => {
            for (const item of items) {
              if (item.id === this.form.deptId) {
                return item;
              }
              if (item.children) {
                const found = findDept(item.children);
                if (found) return found;
              }
            }
            return null;
          };
          const dept = findDept(this.deptOptions);
          this.form.deptName = dept.name;
          const validNames = []
          const targetIds = this.form.visibleDepts;
          const findDeptById = (items, targetId) => {
            for (const item of items) {
              if (item.id == Number(targetId)) {
                return item;
              }
              if (item.children) {
                const found = findDeptById(item.children, targetId);
                if (found) return found;
              }
            }
            return null;
          };
          for (const id of targetIds || []) {
            for (const item of this.deptVisibleOptions) {
              if (item.id == Number(id)) {
                validNames.push(item.label);
                break;
              } else if (item.children) {
                const found = findDeptById(item.children, id);
                if (found) {
                  validNames.push(found.label);
                  break;
                };
              }
            }

          }
          this.form.visibleDeptNames = validNames

          this.form.mattersType = this.form.mattersTypeList.join(',');
          const formData = JSON.parse(JSON.stringify(this.form))
          if (formData.visibleUserNames !== null && formData.visibleUserNames !== '') {
            formData.visibleUserNames = formData.visibleUserNames?.split(',') || [];
          }
          else{
            formData.visibleUserNames = []
          }
          if (formData.visibleUsers !== null && formData.visibleUsers !== '') {
            formData.visibleUsers = formData.visibleUsers?.split(',') || [];
          }
          else{
            formData.visibleUsers = []
          }
          submitIn(formData).then((response) => {
            this.$modal.msgSuccess("修改成功");
            // this.open = false;
            // this.getList();
            this.$tab.closeOpenPage({ path: '/rant/matters' });
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除督办事项编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMatters(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    //状态修改--终止、启用
    handleStatusChange(row, status) {
      let text = status === 5 ? "终止" : "启用";

      this.$modal
        .confirm('确认要"' + text + '""' + row.rantContent + '"此督办事项吗？')
        .then(function () {
          return changeRantMattersStatus(row.id, status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
          this.getList();
        })
        .catch(function () {
          //row.status = row.status === "0" ? "1" : "0"
        });
    },

    selectUserFun(type) {
      this.selectUserType = type;
      this.isMultiple = type == "ranter" || type == "visibleUser" || type == "ranterEdit";
      this.$refs.userRef.show();
    },
    selectUserData(data) {
      if (this.selectUserType == "ranter") {
        // 处理ranter字段 - 过滤重复的用户ID
        let existingRanters = this.form.ranter ? this.form.ranter.split(',') : [];
        let newRanters = data.map(item => item.userId);
        // 合并现有和新的用户ID，并过滤掉重复项
        let uniqueRanters = [...new Set([...existingRanters, ...newRanters])];
        this.form.ranter = uniqueRanters.filter(id => id).join(',');

        // 处理ranterName字段 - 过滤重复的用户昵称
        // let existingRanterNames = this.form.ranterName ? this.form.ranterName.split(',') : [];
        let existingRanterNames = this.form.ranterName ? this.form.ranterName.split(',') : [];
        let newRanterNames = data.map(item => item.nickName);
        // 合并现有和新的用户昵称，并过滤掉重复项
        let uniqueRanterNames = [...new Set([...existingRanterNames, ...newRanterNames])];
        this.form.ranterName = uniqueRanterNames.filter(name => name).join(',');

        // 强制触发表单验证重新检查
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.validateField('ranterName');
          }
        });
      }
      else if (this.selectUserType == "responsibilityer") {
        this.responsiblePerson = data.userId;
        this.responsiblePersonName = data.nickName;
        this.form.responsiblePerson = data.userId;
        this.form.responsiblePersonName = data.nickName;
        this.deptId = data.deptId;
        this.deptName = data.dept.deptName;
      }
      else if (this.selectUserType == "responsibilityerQuery") {
        this.queryParams.responsiblePerson = data.userId;
        this.responsiblePersonNameQuery = data.nickName;
      }
      else if (this.selectUserType == "visibleUser") {
        /*this.form.visibleUsers = data.map((item) => item.userId);
        this.form.visibleUserNames = data.map((item) => item.nickName);
        this.visibleUserNames = this.form.visibleUserNames.join(',');*/

        // 处理ranter字段 - 过滤重复的用户ID
        let existingRanters = this.form.visibleUsers ? this.form.visibleUsers : [];
        let newRanters = data.map(item => item.userId);
        // 合并现有和新的用户ID，并过滤掉重复项
        let uniqueRanters = [...new Set([...existingRanters, ...newRanters])];
        // this.form.visibleUsers = uniqueRanters.filter(id => id).join(',');
        this.form.visibleUsers = uniqueRanters;

        // 处理ranterName字段 - 过滤重复的用户昵称
        let existingRanterNames = this.form.visibleUserNames ? this.form.visibleUserNames : [];
        let newRanterNames = data.map(item => item.nickName);
        // 合并现有和新的用户昵称，并过滤掉重复项
        let uniqueRanterNames = [...new Set([...existingRanterNames, ...newRanterNames])];
        // this.form.visibleUserNames = uniqueRanterNames.filter(name => name).join(',');
        this.form.visibleUserNames = uniqueRanterNames;


      }
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);
    },

    async selectDept(value) {
      if (value == null) {
        this.form.deptId = null;
        this.form.deptName = null;
        this.form.respDeptLeader = null;
        this.form.respDeptLeaderName = null;
        this.form.respDeptResponsibler = null;
        this.form.respDeptResponsiblerName = null;
        return;
      }

      const id = value;
      this.form.deptId = id;
      this.$refs.form.validateField('deptId');
      getRespdet(id).then(response => {
        this.form.deptName = response.data.name;
        this.form.respDeptLeader = response.data.responsibleLeader;
        this.form.respDeptLeaderName = response.data.responsibleLeaderName;
        this.form.respDeptResponsibler = response.data.respPeople;
        this.form.respDeptResponsiblerName = response.data.respPeopleName;
      });

    },

    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    handleRanterTagClose(index) {
      const tags = this.ranterTags;
      const ranters = this.form.ranter ? this.form.ranter?.split(',') : [];
      tags.splice(index, 1);
      ranters.splice(index, 1);
      this.form.ranterName = tags.join(',');
      this.form.ranter = ranters.join(',');
    },
    handleVisibleUserTagClose(index) {
      const tags = this.visibleUserTags;
      // const visibleUsers = this.form.visibleUsers ? this.form.visibleUsers?.split(',') : [];
      const visibleUsers = this.form.visibleUsers || [];
      tags.splice(index, 1);
      visibleUsers.splice(index, 1);
      this.form.visibleUserNames = tags;
      this.form.visibleUsers = visibleUsers;
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      if (this.inputValue) {
        this.selectUserFun('ranter');
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    showVisibleUserInput() {
      this.inputUserVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveUserTagInput.$refs.input.focus();
      });
    },
    handleVisibleUserInputConfirm() {
      if (this.inputUserValue) {
        this.selectUserFun('visibleUser');
      }
      this.inputUserVisible = false;
      this.inputUserValue = '';
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.custom-pop .el-select-dropdown){
  display: none;
}
:deep(.custom-form-item-bac input) {
  background-color: #FFFFFF!important;
  color: #333333!important;
}
:deep(.custom-form-item-bac textarea) {
  background-color: #FFFFFF!important;
  color: #333333!important;
}
:deep(.custom-form-item-bac .vue-treeselect__control) {
  background-color: #FFFFFF!important;
  color: #333333!important;
}
.app-container {
  background: linear-gradient( 180deg, #CCDDFF 0%, #DAEEF2 100%);
  border-radius: 0px 0px 0px 0px;
  height: calc(100vh - 84px);
  .card-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 32px;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 13px;
      height: 17px;
      background: url("~@/assets/rant/rant-item.png") no-repeat;
      background-size: contain;
      margin-right: 10px;
    }
  }
}
.form-block{
  background: rgba(255,255,255,0.7);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  max-height: calc(100vh - 160px);
  overflow: auto;
  height: calc(100vh - 20px);
}
.dialog-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 12px 304px;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  display: flex;
  justify-content: center;
  gap: 40px;

  .submit-btn {
    width: 100px;
  }

  .save-btn {
    width: 100px;
  }

  .cancel-btn {
    width: 100px;
  }
}
:deep(.el-table__cell .cell) {
  display: flex;
  justify-content: center;
}

/* 添加批量修改对话框的样式 */
.batch-edit-input {
  margin-top: 10px;
  width: 100%;
}

.full-width-input {
  width: 100%;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-switch) {
  margin-right: 10px;
}

.ranter-input-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
  padding: 4px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background-color: #fff;

  .ranter-tag {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .el-input {
    width: 200px;
    margin-right: 0;
  }

  .button-new-tag {
    margin-left: 0;
    height: 24px;
    line-height: 22px;
    padding-top: 0;
    padding-bottom: 0;
  }
}
</style>
