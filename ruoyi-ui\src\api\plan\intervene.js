import request from '@/utils/request'

// 查询计划列表
export function listPlan(query) {
  return request({
    url: '/plan/nodeIntervene/list',
    method: 'get',
    params: query
  })
}

// 查询节点详细
export function getNodeDetail(planId, id) {
  return request({
    url: '/plan/nodeIntervene/' + planId + '/' + id,
    method: 'get'
  })
}

// 新增计划反馈
export function intervene(data) {
  return request({
    url: '/plan/nodeIntervene/intervene',
    method: 'post',
    data: data
  })
}

/*// 修改计划反馈
export function updateFeedback(data) {
  return request({
    url: '/plan/feedback',
    method: 'put',
    data: data
  })
}

// 删除计划反馈
export function delFeedback(id) {
  return request({
    url: '/plan/feedback/' + id,
    method: 'delete'
  })
}

// 查询最新版本计划编制列表
export function latestVersionList() {
  return request({
    url: '/plan/info/latestVersionList/',
    method: 'get'
  })
}*/
