import request from '@/utils/request'

// 查询督办类型负责人列表
export function listLeader(query) {
  return request({
    url: '/rant/typeLeader/list',
    method: 'get',
    params: query
  })
}

// 查询督办类型负责人详细
export function getLeader(id) {
  return request({
    url: '/rant/typeLeader/' + id,
    method: 'get'
  })
}

// 新增督办类型负责人
export function addLeader(data) {
  return request({
    url: '/rant/typeLeader',
    method: 'post',
    data: data
  })
}

// 修改督办类型负责人
export function updateLeader(data) {
  return request({
    url: '/rant/typeLeader',
    method: 'put',
    data: data
  })
}

// 删除督办类型负责人
export function delLeader(id) {
  return request({
    url: '/rant/typeLeader/' + id,
    method: 'delete'
  })
}
