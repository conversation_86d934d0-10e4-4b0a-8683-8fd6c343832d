
6324e73a0b5eb573c3b9db7448d29e52685e229a	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"efaf53bbcd06dda0a55b51bb02e20208\"}","integrity":"sha512-Xi9v48LAa6ODLTi6iXMhNvkgBc/FRK1CaJm0+cqsTjsHDXIJufMDpXRZ1EWVmuM6jFgVGIlJ+S6u6+kdNN3MPQ==","time":1754312348584,"size":12046725}