<template>
  <div class="file-preview-container">
    <!-- Loading state -->
    <div v-if="loading" class="loading-container">
      <el-loading text="文件加载中..." />
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-container">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
        show-icon
      />
      <el-button @click="retry" type="primary" style="margin-top: 10px;">
        重新加载
      </el-button>
    </div>

    <!-- File preview content -->
    <div v-else class="preview-content">
      <!-- DOCX files -->
      <vue-doc-preview
        v-if="fileType === 'docx'"
        :value="fileUrl"
        type="office"
        style="height: 100vh;"
      />

      <!-- XLSX files -->
      <vue-doc-preview
        v-else-if="fileType === 'xlsx'"
        :value="fileUrl"
        type="office"
        style="height: 100vh;"
      />

      <!-- PDF files -->
      <iframe 
        v-else-if="fileType === 'pdf'" 
        :src="fileUrl" 
        style="width: 100%; height: 100vh;" 
        frameborder="0"
        @load="renderedHandler"
        @error="errorHandler"
      />

      <!-- Excel files (xls only) -->
      <vue-office-excel
        v-else-if="['xls'].includes(fileType)"
        :src="fileUrl"
        style="height: 100vh;"
        @rendered="renderedHandler"
        @error="errorHandler"
      />

      <!-- PowerPoint files (ppt, pptx) -->
      <vue-office-pdf
        v-else-if="['ppt', 'pptx'].includes(fileType)"
        :src="fileUrl"
        style="height: 100vh;"
        @rendered="renderedHandler"
        @error="errorHandler"
      />

      <!-- Image files -->
      <div v-else-if="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType)" class="image-preview">
        <img 
          :src="fileUrl" 
          :alt="fileName"
          style="max-width: 100%; max-height: 100vh; object-fit: contain;"
          @load="renderedHandler"
          @error="errorHandler"
        />
      </div>

      <!-- Text files -->
      <div v-else-if="['txt', 'md', 'json', 'xml', 'csv'].includes(fileType)" class="text-preview">
        <pre v-if="textContent" style="padding: 20px; white-space: pre-wrap; height: 100vh; overflow-y: auto;">{{ textContent }}</pre>
        <div v-else class="loading-text">正在加载文本内容...</div>
      </div>

      <!-- Video files -->
      <video 
        v-else-if="['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(fileType)"
        :src="fileUrl"
        controls
        style="width: 100%; max-height: 100vh;"
        @loadeddata="renderedHandler"
        @error="errorHandler"
      />

      <!-- Audio files -->
      <audio 
        v-else-if="['mp3', 'wav', 'ogg', 'aac', 'flac'].includes(fileType)"
        :src="fileUrl"
        controls
        style="width: 100%;"
        @loadeddata="renderedHandler"
        @error="errorHandler"
      />

      <!-- Unsupported file types -->
      <div v-else class="unsupported-file">
        <el-alert
          title="不支持预览此文件类型"
          :description="`文件类型: ${fileType.toUpperCase()}`"
          type="warning"
          :closable="false"
          show-icon
        />
        <el-button @click="downloadFile" type="primary" style="margin-top: 10px;">
          <i class="el-icon-download"></i>
          下载文件
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
// 引入VueDocPreview组件
import VueDocPreview from 'vue-doc-preview'
// 引入VueOffice组件 (仅用于xls和ppt)
import VueOfficeExcel from '@vue-office/excel'
import VueOfficePdf from '@vue-office/pdf'

// 引入相关样式
// import '@vue-office/excel/lib/index.css'
// import '@vue-office/pdf/lib/index.css'

export default {
  name: 'FilePreview',
  components: {
    VueDocPreview,
    VueOfficeExcel,
    VueOfficePdf
  },
  props: {
    // 文件URL或文件对象
    file: {
      type: [String, Object],
      required: true
    },
    // 文件名（可选，用于显示和下载）
    fileName: {
      type: String,
      default: ''
    },
    // 文件类型（可选，如果不提供会自动从文件名或URL中提取）
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: true,
      error: false,
      errorMessage: '',
      textContent: '',
      fileUrl: '',
      fileType: ''
    }
  },
  watch: {
    file: {
      handler(newFile) {
        this.initializeFile(newFile)
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 初始化文件预览
     */
    initializeFile(file) {
      this.loading = true
      this.error = false
      this.textContent = ''

      try {
        // 处理文件URL
        if (typeof file === 'string') {
          this.fileUrl = file
          console.log(111,  this.fileUrl)
        } else if (file && file.url) {
          this.fileUrl = file.url
          console.log(222,this.fileUrl)
        } else {
          throw new Error('无效的文件参数')
        }

        // 获取文件类型
        this.fileType = this.getFileType()

        // 对于文本文件，需要加载内容
        if (['txt', 'md', 'json', 'xml', 'csv'].includes(this.fileType)) {
          this.loadTextContent()
        } else {
          this.loading = false
        }
      } catch (err) {
        this.handleError(err.message)
      }
    },

    /**
     * 获取文件类型
     */
    getFileType() {
      // 优先使用传入的type参数
      if (this.type) {
        return this.type.toLowerCase()
      }

      // 从文件名获取扩展名
      let fileName = this.fileName
      if (!fileName && typeof this.file === 'string') {
        fileName = this.file.split('/').pop().split('?')[0]
      }

      if (fileName) {
        const extension = fileName.split('.').pop()
        if (extension) {
          return extension.toLowerCase()
        }
      }

      // 从URL获取扩展名
      try {
        const url = new URL(this.fileUrl)
        const pathname = url.pathname
        const extension = pathname.split('.').pop()
        if (extension) {
          return extension.toLowerCase()
        }
      } catch (e) {
        // URL解析失败，继续其他方法
      }

      return 'unknown'
    },

    /**
     * 加载文本文件内容
     */
    async loadTextContent() {
      try {
        const response = await fetch(this.fileUrl)
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        this.textContent = await response.text()
        this.loading = false
        this.renderedHandler()
      } catch (err) {
        this.handleError(`加载文本内容失败: ${err.message}`)
      }
    },

    /**
     * 文件渲染成功回调
     */
    renderedHandler() {
      this.loading = false
      this.error = false
      console.log('文件渲染完成')
      this.$emit('rendered', {
        fileUrl: this.fileUrl,
        fileType: this.fileType,
        fileName: this.fileName
      })
    },

    /**
     * 文件渲染失败回调
     */
    errorHandler(err) {
      console.error('文件渲染失败:', err)
      this.handleError('文件渲染失败，请检查文件是否损坏或网络连接')
    },

    /**
     * 处理错误
     */
    handleError(message) {
      this.loading = false
      this.error = true
      this.errorMessage = message
      this.$emit('error', {
        message,
        fileUrl: this.fileUrl,
        fileType: this.fileType
      })
    },

    /**
     * 重试加载
     */
    retry() {
      this.initializeFile(this.file)
    },

    /**
     * 下载文件
     */
    downloadFile() {
      try {
        const link = document.createElement('a')
        link.href = this.fileUrl
        link.download = this.fileName || `file.${this.fileType}`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$emit('download', {
          fileUrl: this.fileUrl,
          fileName: this.fileName
        })
      } catch (err) {
        this.$message.error('下载失败: ' + err.message)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.file-preview-container {
  width: 100%;
  height: 100%;
  position: relative;
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  
  .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
  }
  
  .preview-content {
    width: 100%;
    height: 100%;
  }
  
  .image-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f5f5f5;
  }
  
  .text-preview {
    height: 100vh;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    
    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.5;
      background-color: #fafafa;
    }
    
    .loading-text {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #909399;
    }
  }
  
  .unsupported-file {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
  }
}
</style>
