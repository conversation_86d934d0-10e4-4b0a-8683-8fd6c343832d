<template>
  <project-layout>
    <div class="dashboard-container">
<!--      <Watermark
        style="width: 100vw; height: 100vh;"
        text="机密文件"
        :font-size="20"
        font-family="Arial"
        color="red"
        :opacity="0.1"
        :rotate="-45"
        :width="220"
        :height="150"
      >-->
        <Header/>
        <div class="dashboard-content" v-loading.fullscreen.lock="loading">
          <div class="tab-container mb-12">
            <div class="sub-tabs">
              <div
                v-for="tab in cardDataArr"
                :key="tab.cityId"
                class="tab-item"
                :class="{
              'highlight': tab.cityId === activeCategoryId
            }"
                @click="setActiveTab(tab)"
              >
                {{ tab.cityName }}
              </div>
            </div>
            <div
              class="indicator"
              :style="indicatorStyle"
            ></div>
          </div>
          <div class="projects-content-container">
            <div v-if="!hasPermi(['project:city'])" class="empty-status">
              <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
              <div class="desc">暂无权限</div>
              <div class="desc">请联系管理员</div>
            </div>

            <!-- 有权限时显示的内容 -->
            <div v-else class="width-100">
              <!-- 开发中状态 -->
              <div v-if="isDeveloping" class="empty-status">
                <img src="@/views/projects/assets/images/developing.png" alt="开发中">
                <div class="desc">正在开发中</div>
                <div class="desc">敬请期待</div>
              </div>

              <!-- 无数据状态 -->
              <div v-else-if="!hasData" class="empty-status">
                <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
                <div class="desc">暂无数据</div>
              </div>
              <section v-else>
                <section class="mb-21">
                  <!--                <section v-for="(item, index) in cardDataArr" class="mb-21">-->
                  <!--                <div class="company mb-21">-->
                  <!--                  <div class="company-name">{{ item.cityName }}</div>-->
                  <!--                </div>-->
                  <div class="card-content">
                    <AmountCardWrapper
                      v-for="(card, index) in cardData"
                      :card-data="card"
                      class="amount-card-wrapper"
                      @clickHandler="handleNavigate('/projects/baseInfo', card)"
                    >
                      <AmountCard :card-data="formatCardData(card.projectSalesDataDto, '全周期累计签约')" type="orange"
                                  icon="file" class="item-card flex-1"/>
                      <AmountCard :card-data="formatCardData(card.projectSalesDataDto, '全周期累计回款')" type="blue"
                                  icon="amount" class="item-card flex-1"/>
                      <AmountCard :card-data="formatCardData(card.projectSalesDataDto, '全周期住宅均价')" type="purple"
                                  icon="price" class="item-card flex-1"/>
                    </AmountCardWrapper>
                  </div>
                </section>
              </section>
            </div>
          </div>
        </div>
<!--      </Watermark>-->

    </div>
  </project-layout>
</template>
<script>
// 城市公司
import Header from '@/views/projects/components/Header.vue'
import CompanyTree from '@/views/projects/components/CompanyTree.vue'
import AmountCard from '@/views/projects/components/AmountCard.vue'
import AmountCardWrapper from '@/views/projects/components/AmountCardWrapper.vue'
import API from '@/views/projects/api'
import ProjectLayout from '@/views/projects/layout/ProjectLayout.vue'
import Watermark from "@/views/projects/components/Watermark.vue";

export default {
  name: 'Dashboard',
  components: {
    Watermark,
    Header,
    CompanyTree,
    AmountCard,
    AmountCardWrapper,
    ProjectLayout
  },
  props: {
    user: {
      type: Object,
      required: false,
      default: () => ({
        nickName: '',
        role: '',
        permissions: []
      })
    }
  },
  data() {
    return {
      cardDataArr: [],
      loading: false,
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: false, // 控制是否有数据
      hasPermiData: true,
      allTabs: [],
      cardData: [],
      activeCategoryId: null
    }
  },
  computed: {
    // ...mapState({
    //   user: state => state.projects.user
    // }),
    indicatorStyle() {
    const currentIndex = this.cardDataArr.findIndex(tab => tab.cityId === this.activeCategoryId);
    if (currentIndex === -1) return {};

    const width = 100 / this.cardDataArr.length;

    return {
      left: `${currentIndex * width}%`,
      width: `${width}%`,
      backgroundColor: '#CC1414'
      };
    }
  },
  mounted() {
    this.getAllProjects()
    this.handleUserRecord();
  },
  methods: {
    handleUserRecord(data){ // 埋点
      API.UserTrack.userRecord({
        recordModule: '城市公司'
      })
    },
    setActiveTab(tab){
      this.activeCategoryId = tab.cityId;
      this.cardData = tab.cityProjectInfoDtoList;
    },
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions
      // Check for all permissions wildcard first
      if (permissions.includes('*:*:*')) {
        return true
      }
      // Check specific permissions
      return permissions.some(p => permission.includes(p))
    },
    async getAllProjects() {
      this.loading = true;
      try {
        const res = await API.City.getAllProjects();
        this.cardDataArr = res.data
        this.cardData = this.cardDataArr[0]?.cityProjectInfoDtoList ?? []
        this.activeCategoryId = this.cardDataArr[0]?.cityId;
        this.hasData = this.cardDataArr?.length > 0
      } catch (error) {
        this.$message.error('Failed to fetch projects');
      } finally {
        this.loading = false;
      }
    },
    formatCardData(data, type) {
      if (type === '全周期累计签约') {
        return {
          title: type,
          amount: data?.qyAmount,
          unit: '亿'
        }
      }
      if (type === '全周期累计回款') {
        return {
          title: type,
          amount: data?.hkAmount,
          unit: '亿'
        }
      }
      if (type === '全周期住宅均价') {
        return {
          title: type,
          amount: data?.averagePrice,
          unit: '元'
        }
      }
    },
    handleNavigate(path, query = {}, replace = false) {
      this.$router.push({path, query: {
        projectCode: query.projectCode,
        projectName: query.projectName
      }}, replace)
    },
    handleCompanyChange(company) {
      this.getCityProjectInfo(company)
    },
    getCityProjectInfo(company) {  // 获取城市公司下所有项目信息
      this.loading = true;
      API.City.allProjectData(company?.cityId).then(res => {
        if (res.code === 200) {
          this.cardDataArr = [
            {
              cityId: company.cityId,
              cityName: company.cityName,
              cityProjectInfoDtoList: res.data
            }
          ]
        } else {
          this.$message.error(res.message || '获取城市项目信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取城市项目信息失败')
      }).finally(() => {
        this.loading = false;
      })
    }

  }
}
</script>
<style scoped lang="scss">
@import '@/views/projects/styles/projects.scss';
.dashboard-container {
  width: 100%;  // 确保容器占满宽度
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .dashboard-content {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    background-image: url('~@/views/projects/assets/images/dashboard-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 1.3125rem 2rem;

    .company {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .company-name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.125rem;
        color: #222222;
        line-height: 1.3125rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: inline-block;
          width: 2rem;
          height: 2rem;
          background-image: url('~@/views/projects/assets/images/building.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-right: 0.6875rem;
        }
      }
    }

    .card-content {
      display: flex;
      flex-wrap: wrap;
      gap: 1.3125rem;

      width: 100%;

      .amount-card-wrapper {
        width: calc(50% - 0.65625rem);
        min-width: 0;
        margin: 0;
      }
    }
  }
}
.tab-container {
  display: inline-block;
  position: relative;
  font-family: PingFang SC, PingFang SC;
}

.category-tabs {
  display: flex;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.category-tab {
  padding: 0.25rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1rem;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.category-tab.category-active {
  position: relative;
}

.category-tab.category-active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  border-left: 0.375rem solid transparent;
  border-right: 0.375rem solid transparent;
  border-top: 0.375rem solid currentColor;
}

.sub-tabs {
  display: inline-flex;
  position: relative;
  gap: 1.5625rem;
}

.tab-item {
  padding: 0.1875rem 0.5rem 0.5rem;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 1.125rem;
  color: #222222;
  line-height: 1.3125rem;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.tab-item:hover {
  color: #C00000;
}

.tab-item.highlight {
  color: #C00000;
}
.highlight {
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    background-image: url('~@/views/projects/assets/images/building.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 0.6875rem;
  }
}

.tab-item.active {
  font-size: 1rem;
  color: #CC1414;
}

.indicator {
  position: absolute;
  bottom: 0;
  height: 0.25rem;
  background-color: #CC1414;
  transition: all 0.3s ease;
}
</style>

