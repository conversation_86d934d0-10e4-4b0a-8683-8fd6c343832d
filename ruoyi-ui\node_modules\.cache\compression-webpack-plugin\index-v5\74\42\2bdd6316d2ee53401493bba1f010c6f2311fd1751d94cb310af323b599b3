
15dd06ff6404b32cd98749f9b1f51659032804f8	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.cbde3e61fc1d53cf9cfb.hot-update.js\",\"contentHash\":\"dfb22e0c5e045e4a2bd691f039ddf6c7\"}","integrity":"sha512-ddl1zDvy7X8y2joSLsvWFGwJIslN+jeJ6iuvk4kxj8yyWfTmCgozlGHiCtAoCxJ9zgU0OVuuaodhdRGXLr5AMg==","time":1754311742003,"size":78135}