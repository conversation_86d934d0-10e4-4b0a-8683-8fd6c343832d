<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="工程项目" prop="gcProjectName">
        <el-input
          v-model="queryParams.gcProjectName"
          placeholder="请输入工程项目"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="城市公司" prop="cityId">
        <el-select
          v-model="queryParams.cityId"
          placeholder="请选择城市公司"
          @change="handleCitySearch"
          clearable
          class="width-100">
          <el-option
            v-for="item in cites"
            :key="item.cityId"
            :label="item.cityName"
            :value="item.cityId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主数据项目" prop="projectCode">
        <el-select
          v-model="queryParams.projectCode"
          placeholder="请选择项目"
          @change="handleProjectSearch"
          clearable
          class="width-100"
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.displayName"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工程项目名称" align="center" prop="gcProjectName" />
      <el-table-column label="城市公司" align="center" prop="cityName" />
      <el-table-column label="主数据项目编码" align="center" prop="projectCode" />
      <el-table-column label="主数据项目名称" align="center" prop="projectName" />
      <el-table-column label="创建时间" align="center" prop="createdTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, scope.$index + 1)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改工程项目信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="工程项目名称" prop="gcProjectName">
          <el-input v-model="form.gcProjectName" placeholder="请输入工程项目名称" />
        </el-form-item>
        <el-form-item label="城市公司" prop="cityId">
          <!--              <el-input v-model="form.cityId" placeholder="请输入城市公司uuid"/>-->
          <el-select v-model="form.cityId" placeholder="请选择城市公司"
                     @change="handleCityChange" class="width-100">
            <el-option
              v-for="item in cites"
              :key="item.cityId"
              :label="item.cityName"
              :value="item.cityId"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="主数据项目" prop="projectCode">
            <el-select v-model="form.projectCode" placeholder="请选择项目"
                       @change="handleProjectChange" class="width-100">
              <el-option
                v-for="item in projects"
                :key="item.projectCode"
                :label="item.displayName"
                :value="item.projectCode"
              />
            </el-select>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getData, listData, addData, updateData, delData} from "@/api/projectOperate/gc/gcProejctData";
  import API from '@/views/projects/api'

  export default {
  name: "Data",
  data() {
    return {
      // 遮罩层
      loading: true,
      indexes: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工程项目信息表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        gcProjectName: null,
        cityId: null,
        cityName: null,
        projectCode: null,
        projectName: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 城市公司列表
      cites: [],
      // 项目列表
      projects: [],
    };
  },
  created() {
    this.getList();
    this.getCities();
  },
  methods: {
    /** 查询工程项目信息列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getProjects(cityId) {
      API.Common.getProject(cityId).then(res => {
        if (res.code === 200) {
          this.projects = res.data
        }
        else {
          this.$message.error(res.message || '获取项目信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取项目信息失败')
      })
    },
    getCities() {
      API.Common.getCity().then(res => {
        if (res.code === 200) {
          this.cites = res.data
        }
        else {
          this.$message.error(res.message || '获取城市公司信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取城市公司信息失败')
      })
    },
    handleCitySearch(val) {
      this.projects = null;
      this.queryParams.projectCode = null;
      !!this.queryParams.cityId ? this.getProjects(this.queryParams.cityId) : null;
      this.handleQuery();
    },
    handleProjectSearch(val) {
      this.handleQuery()
    },
    handleCityChange() {
      this.form.cityName = this.cites.find(item => item.cityId === this.form.cityId)?.cityName
      this.projects = []
      this.form.projectName = null
      this.form.projectCode = null
      this.getProjects(this.form.cityId)
    },
    handleProjectChange() {
      const project = this.projects.find(item => item.projectCode === this.form.projectCode)
      this.form.projectName = project.displayName
      this.$forceUpdate()
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        gcProjectId: null,
        gcProjectName: null,
        cityId: null,
        cityName: null,
        projectCode: null,
        projectName: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.gcProjectId)
      this.single = selection.length!==1
      this.multiple = !selection.length
      this.indexes = selection.map((item) => {
        const index = this.dataList.findIndex(row => row.gcProjectId === item.gcProjectId);
        return index !== -1 ? index + 1 : -1;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工程项目信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const gcProjectId = row.gcProjectId || this.ids
      getData(gcProjectId).then(response => {
        this.form = response.data;
        this.projects = this.getProjects(this.form.cityId)
        this.open = true;
        this.title = "修改工程项目信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.gcProjectId != null) {
            updateData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, index) {
      const gcProjectIds = row.gcProjectId || this.ids;
      const indexes = index || this.indexes;
      this.$modal.confirm('是否确认删除工程项目信息序号为"' + indexes + '"的数据项？').then(function() {
        return delData(gcProjectIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/gcProject/export', {
        ...this.queryParams
      }, `工程项目_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
