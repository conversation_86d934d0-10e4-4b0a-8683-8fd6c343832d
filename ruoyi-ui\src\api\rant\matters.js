import request from '@/utils/request'

// 查询督办事项列表
export function listMatters(query) {
  return request({
    url: '/rant/matters/list',
    method: 'get',
    params: query
  })
}
// 我的任务事项列表
export function myTaskList(query) {
  return request({
    url: '/rant/matters/myTaskList',
    method: 'get',
    params: query
  })
}

// 我的督办事项列表
export function myRantList(query) {
  return request({
    url: '/rant/matters/myRantList',
    method: 'get',
    params: query
  })
}

// 查看督办事项列表
export function lookList(query) {
  return request({
    url: '/rant/matters/lookList',
    method: 'get',
    params: query
  })
}



// 查询督办事项详细
export function getMatters(id) {
  return request({
    url: '/rant/matters/' + id,
    method: 'get'
  })
}

// 反馈填报要加载的督办事项信息
export function feedbackDetail(feedbackRecordId) {
  return request({
    url: '/rant/matters/feedbackDetail/' + feedbackRecordId,
    method: 'get'
  })
}

// 反馈填报要加载的督办事项信息
export function feedbackRankMatter(id) {
  return request({
    url: '/rant/matters/feedbackRankMatter/' + id,
    method: 'get'
  })
}

// 查看督办事项信息
export function lookRankMatter(id) {
  return request({
    url: '/rant/matters/lookRankMatter/' + id,
    method: 'get'
  })
}

// 待办查看督办事项信息
export function daiBanLookRankMatter(readTodoNoticeId) {
  return request({
    url: '/rant/matters/daiBanLookRankMatter/' + readTodoNoticeId,
    method: 'get'
  })
}

// 待阅转已阅
export function readDaiBanChange(readTodoNoticeId) {
  return request({
    url: '/rant/matters/readDaiBanChange/' + readTodoNoticeId,
    method: 'get'
  })
}

// 新增督办事项
export function addMatters(data) {
  return request({
    url: '/rant/matters',
    method: 'post',
    data: data
  })
}

// 修改督办事项
export function updateMatters(data) {
  return request({
    url: '/rant/matters',
    method: 'put',
    data: data
  })
}

// 删除督办事项
export function delMatters(id) {
  return request({
    url: '/rant/matters/' + id,
    method: 'delete'
  })
}

// 状态修改
export function changeRantMattersStatus (id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/rant/matters/changeStatus',
    method: 'put',
    data: data
  })
}

// 反馈填报要加载的督办事项信息
export function feedbackRankMatterList(todoNoticeId) {
  return request({
    url: '/rant/matters/feedbackRankMatterList/' + todoNoticeId,
    method: 'get'
  })
}

// 进行中的提交
export function submitIn(data) {
  return request({
    url: '/rant/matters/submitIn',
    method: 'post',
    data: data
  })
}

// 批量修改责任人
export function editResponsiblePerson(data) {
  return request({
    url: '/rant/matters/editResponsiblePerson',
    method: 'post',
    data: data
  })
}

// 批量修改
export function batchEdit(data) {
  return request({
    url: '/rant/matters/batchEdit',
    method: 'post',
    data: data
  })
}
// 批量提交
export function batchSubmit(data) {
  return request({
    url: '/rant/matters/batchSubmit',
    method: 'post',
    data: data
  })
}

// 批量删除
export function batchRemove(data) {
  return request({
    url: '/rant/matters/batchRemove',
    method: 'post',
    data: data
  })
}

// 获取待办我的吐槽事项详情
export function rantDaiBanInfo(todoNoticeId) {
  return request({
    url: '/rant/matters/myRant/daiBan/info/' + todoNoticeId,
    method: 'get'
  })
}

// 新增吐槽
export function myRantAdd(data=null) {
  return request({
    url: '/rant/matters/myRant/add',
    method: 'post',
    data
  })
}

// 修改吐槽
export function myRantEdit(data=null) {
  return request({
    url: '/rant/matters/myRant/edit',
    method: 'post',
    data
  })
}

// 吐槽事项初审
export function myRantApprove(data=null) {
  return request({
    url: '/rant/matters/MyRantApprove',
    method: 'post',
    data
  })
}

// 吐槽事项责任人确认
export function myRantConfirm(data=null) {
  return request({
    url: '/rant/matters/MyRantConfirm',
    method: 'post',
    data
  })
}

//  待办中心获取督办事项详细信息
export function rantDaiBanDetailForTodoCenter(todoNoticeId) {
  return request({
    url: '/rant/matters/daiDanInfo/' + todoNoticeId,
    method: 'get'
  })
}

// 谈话反馈事项确认
export function talkFeedbackConfirm(data={}) {
  return request({
    url: '/rant/matters/talkFeedbackConfirm',
    method: 'post',
    data
  })
}

//  反馈驳回待办 获取反馈事项信息
export function approveRejectDaiInfo(todoNoticeId) {
  return request({
    url: '/rant/matters/approveRejectDaiInfo/' + todoNoticeId,
    method: 'get'
  })
}

//  获取审批待办中督办事项进度集合
export function approveTodoInfo(todoNoticeId) {
  return request({
    url: '/rant/matters/approveTodoInfo/' + todoNoticeId,
    method: 'get'
  })
}

// 督办提醒列表
export function remindList(params= {}) {
  return request({
    url: '/rant/matters/remindList',
    method: 'get',
    params
  })
}
//获取部门树
export function deptTree() {
  return request({
    url: '/rant/common/dept/tree',
    method: 'get'
  })
}

// 查看审批详情
export function getApproverRecord(id) {
  return request({
    url: `/rant/matters/getApproveRecord/${id}`,
    method: 'get',
  })
}

// 已完成事项修改可见范围
export function changeVisible(data) {
  return request({
    url: `/rant/matters/changeVisible`,
    method: 'post',
    data
  })
}



