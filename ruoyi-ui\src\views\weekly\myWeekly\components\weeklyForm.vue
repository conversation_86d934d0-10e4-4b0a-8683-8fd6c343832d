<template>
  <div style="background: #F5F8FA;">
    <el-form :model="weeklyForm" :rules="rules" ref="weeklyFormRef" label-width="auto" class="weekly-form">

      <section class="head-block">
        <!-- 标题部分 -->
        <div class="weekly-title">
          {{ currentYear }}年第{{ weekNumber }}周（{{ dateRange }}）个人总结
        </div>

        <!-- 个人信息部分 -->
        <el-row :gutter="20" class="info-row">
          <el-col :span="8">
            <span class="custom-label">员工姓名：</span>
            <span class="custom-text">{{ weeklyForm.nickName }}</span>
            <!--  <el-form-item label="员工姓名">
              <el-input v-model="weeklyForm.nickName" disabled></el-input>
            </el-form-item> -->
          </el-col>
          <el-col :span="8">
            <span class="custom-label">员工所属部门：</span>
            <span class="custom-text">{{ weeklyForm.deptName }}</span>
            <!--  <el-form-item label="员工所属部门">
              <el-input v-model="weeklyForm.deptName" disabled></el-input>
            </el-form-item> -->
          </el-col>
          <el-col :span="8">
            <el-form-item class="align-center" label="员工职位：" prop="postName" :rules="[
      { required: true, message: '请输入员工职位', trigger: 'blur' },
    ]">
              <el-input v-model="weeklyForm.postName" class="custom-input"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </section>


      <el-row>
        <!-- 本周工作总结 -->
        <section class="section-content">
          <div>
            <div class="section-title">本周工作总结</div>
          </div>
          <el-table :data="weeklyForm.workSummaryList" border ref="workSummaryTable" row-key="serialNumber"
            class="sortable-table">
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="工作板块" prop="workMatter" align="center" width="350">
              <template #default="scope">
                <el-form-item :prop="`workSummaryList.${scope.$index}.workMatter`" :rules="[
      {
        required: true,
        message: '请输入工作板块',
        trigger: 'blur',
      },
    ]">
                  <el-input :autosize="true" class="font-size-15" placeholder="请输入工作板块" resize="none"
                    v-model="weeklyForm.workSummaryList[scope.$index].workMatter" type="textarea"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
              <template #default="scope">
                <el-form-item :prop="`workSummaryList.${scope.$index}.progressScheme`" :rules="[
      {
        required: true,
        message: '请输入工作事项/完成进度',
        trigger: 'blur',
      },
    ]">
                  <el-input :autosize="true" class="font-size-15" placeholder="请输入工作事项/完成进度"
                    v-model="weeklyForm.workSummaryList[scope.$index].progressScheme" type="textarea"  resize="none"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="完成及预警情况" prop="completeStatus" width="250" align="center">
              <template #default="scope">
                <el-form-item :prop="`workSummaryList.${scope.$index}.completeStatus`" :rules="[
      {
        required: true,
        message: '请选择完成及预警情况',
        trigger: 'change',
      },
    ]">
                  <el-select v-model="weeklyForm.workSummaryList[scope.$index].completeStatus
      " placeholder="请选择完成及预警情况">
                    <el-option v-for="dict in dict.type.work_complete_status" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #header>
                <el-button type="text" @click="addRow(weeklyForm.workSummaryList)">
                  新增
                  <i class="el-icon-plus"></i>
                </el-button>
              </template>
              <template #default="scope">
                <el-button type="text" @click="removeRow(weeklyForm.workSummaryList, scope.$index)"
                  :disabled="weeklyForm.workSummaryList.length === 1">
                  <i class="el-icon-delete" style="color: #f56c6c"></i>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </el-row>

      <el-row>
        <!-- 下周工作计划 -->
        <section class="section-content">
          <div>
            <div class="section-title">下周工作计划</div>
          </div>
          <el-table :data="weeklyForm.workPlanList" border ref="workPlanTable" row-key="serialNumber"
            class="sortable-table">
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="工作板块" prop="workMatter" align="center" width="350">
              <template #default="scope">
                <el-form-item :prop="`workPlanList.${scope.$index}.workMatter`" :rules="[
      {
        required: true,
        message: '请输入工作板块',
        trigger: 'blur',
      },
    ]">
                  <el-input :autosize="true" class="font-size-15" placeholder="请输入工作板块"  resize="none"
                    v-model="weeklyForm.workPlanList[scope.$index].workMatter" type="textarea"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
              <template #default="scope">
                <el-form-item :prop="`workPlanList.${scope.$index}.progressScheme`" :rules="[
      {
        required: true,
        message: '请输入工作事项/完成进度',
        trigger: 'blur',
      },
    ]">
                  <el-input :autosize="true" class="font-size-15" placeholder="请输入工作事项/完成进度"  resize="none" v-model="weeklyForm.workPlanList[scope.$index].progressScheme
      " type="textarea"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="完成及预警情况" prop="completeStatus" width="250" align="center">
              <template #default="scope">
                <el-form-item :prop="`workPlanList.${scope.$index}.completeStatus`" :rules="[
      {
        required: true,
        message: '请选择完成及预警情况',
        trigger: 'change',
      },
    ]">
                  <el-select v-model="weeklyForm.workPlanList[scope.$index].completeStatus
      " placeholder="请选择完成及预警情况">
                    <el-option v-for="dict in dict.type.work_complete_status" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #header>
                <el-button type="text" @click="addRow(weeklyForm.workPlanList)">
                  新增
                  <i class="el-icon-plus"></i>
                </el-button>
              </template>
              <template #default="scope">
                <el-button type="text" @click="removeRow(weeklyForm.workPlanList, scope.$index)"
                  :disabled="weeklyForm.workPlanList.length === 1">
                  <i class="el-icon-delete" style="color: #f56c6c"></i>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </el-row>


      <el-row>
        <section class="section-content">
          <!-- 每周反思 -->
          <div>
            <div class="section-title">每周反思</div>
          </div>
          <el-input :autosize="true" class="font-size-15"  resize="none" placeholder="请填写反思内容（必填）"
            type="textarea"
            :rules="[
              {
                required: true,
                message: '请填写反思内容',
                trigger: 'blur',
              },
            ]"
            v-model="weeklyForm.reflectInfo.progressScheme"
            >
          </el-input>
        </section>
      </el-row>

      <el-row>
        <section class="section-content">
          <!-- 需部门支持事项 -->
          <div>
            <div class="section-title">需部门支持事项</div>
          </div>
          <el-table :data="weeklyForm.workSupportList" border ref="workSupportTable" row-key="serialNumber"
            class="sortable-table">
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="工作板块" prop="workMatter" align="center" width="350">
              <template #default="scope">
                <el-input :autosize="true" class="font-size-15"  resize="none" placeholder="请输入工作板块" v-model="scope.row.workMatter"
                  type="textarea" :rules="[
      {
        required: true,
        message: '请输入工作板块',
        trigger: 'blur',
      },
    ]" @blur="validateField(scope.row, 'workMatter')"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="工作事项/完成进度" prop="progressScheme" align="center">
              <template #default="scope">
                <el-input :autosize="true" class="font-size-15" placeholder="请输入工作事项/完成进度"  resize="none"
                  v-model="scope.row.progressScheme" type="textarea" :rules="[
      {
        required: true,
        message: '请输入工作事项/完成进度',
        trigger: 'blur',
      },
    ]" @blur="validateField(scope.row, 'progressScheme')"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="完成及预警情况" prop="completeStatus" width="250" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.completeStatus" placeholder="请选择完成及预警情况" clearable>
                  <el-option v-for="dict in dict.type.work_complete_status" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #header>
                <el-button type="text" @click="addRow(weeklyForm.workSupportList)">
                  新增
                  <i class="el-icon-plus"></i>
                </el-button>
              </template>
              <template #default="scope">
                <el-button type="text" @click="removeRow(weeklyForm.workSupportList, scope.$index)">
                  <i class="el-icon-delete" style="color: #f56c6c"></i>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </el-row>

      <el-row>
        <section class="section-content">
          <!-- 审阅人 -->
          <div>
            <div class="section-title">审阅人</div>
          </div>
          <el-form-item label="审阅人" prop="selectUserIds" label-width="65px">
            <el-select @focus="handleOpenUserDialog" v-model="weeklyForm.selectUserIds" multiple clearable size="medium"
              style="margin-left: 0px; width: calc(100% - 28px)" placeholder="请选择">
              <el-option v-for="item in weeklyForm.reportReviewDetails" :key="item.userName" :label="item.nickName"
                :value="item.userName">
              </el-option>
            </el-select>
          </el-form-item>
        </section>
      </el-row>
    </el-form>
    <select-user ref="userRef" :roleId="roleName" :selectMultiple="true" @feedbackEmit="handleSelectUser" />
  </div>
</template>

<script>
// import SelectUser from "@/views/plan/components/SelectUser/index.vue";
import SelectUser from "./selectUser.vue";
import Sortable from 'sortablejs'

export default {
  name: "WeeklyForm",
  dicts: ["work_complete_status"],
  components: { SelectUser },
  data() {
    return {
      roleName: "",
      labelPosition: "right",
      weeklyForm: {
        id: null,
        year: null, // 年份不能为空
        week: null, // 第几周不能为空
        userId: "", // 员工编码不能为空
        nickName: "", // 员工名称不能为空
        deptId: null, // 部门ID不能为空
        deptName: "", // 部门名称不能为空
        postName: "", // 岗位名称
        startDate: "", // 周开始日期不能为空
        endDate: "", // 周结束日期不能为空
        approveUserCode: "", // 审阅人编码不能为空
        approveUserName: "", // 审阅人名称不能为空
        circulateUserCodes: "", // 传阅人（多个用逗号隔开）
        userCode: "", // 员工编码不能为空
        // 本周工作总结
        workSummaryList: [
          /*  {
             workMatter: "", // 工作板块
             progressScheme: "", // 工作事项/完成进度
             completeStatus: "", // 完成及预警情况
             type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
             serialNumber: 1, // 序号
           }, */
        ],
        // 下周工作计划
        workPlanList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 2,
            serialNumber: 1,
          }, */
        ],
        // 需部门支持事项
        workSupportList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 3,
            serialNumber: 1,
          }, */
        ],
        reportReviewDetails: [],
        selectUserIds: [], // 弹窗中选择的多个用户id数组
        reflectInfo: { // 1=本周工作总结,2=下周工作计划,3=需部门支持事项,4=每周反思
          type: 4,
          progressScheme: null
        },
      },
      rules: {
        selectUserIds: [
          { required: true, message: "请选择审阅人", trigger: "change" },
        ],
      },
      approveUserName: "",
      // selectUserIds: [], // 弹窗中选择的多个用户id数组
    };
  },
  computed: {
    currentYear() {
      return this.weeklyForm.year;
    },
    weekNumber() {
      // 这里需要实现获取当前周数的逻辑
      return this.weeklyForm.week;
    },
    dateRange() {
      // 这里需要实现获取日期范围的逻辑
      // return `${this.weeklyForm.startDate}-${this.weeklyForm.endDate}`;

      return this.formatDateRange(
        this.weeklyForm.startDate,
        this.weeklyForm.endDate
      );
    },
    department() {
      return this.weeklyForm.deptName;
    },
  },
  mounted() {

  },
  methods: {
    formatDateRange(startDate, endDate) {
      return `${new Date(startDate).getMonth() + 1}月${new Date(
        startDate
      ).getDate()}日-${new Date(endDate).getMonth() + 1}月${new Date(
        endDate
      ).getDate()}日`;
    },
    handleOpenUserDialog() {
      // this.$refs.userRef.queryParams.userName = this.approveUserName
      this.$refs.userRef.show();
    },
    handleSelectUser(data) {
      // 过滤掉已经存在的用户
      const newUsers = data.filter(
        (newUser) =>
          !this.weeklyForm.reportReviewDetails.some(
            (existingUser) => existingUser.userName === newUser.userName
          )
      );

      // 只添加新的用户
      this.weeklyForm.reportReviewDetails.push(...newUsers);

      // 更新选中的用户ID数组
      this.weeklyForm.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );

      // 清除校验提示
      this.$nextTick(() => {
        this.$refs.weeklyFormRef.clearValidate(["selectUserIds"]);
      });
    },
    getFormData() {
      return this.submitForm();
      // return {...this.submitForm(), reportReviewDetails: this.weeklyForm.reportReviewDetails.map((item) => ({approveUserCode: item.userName, approveUserName: item.nickName}))};
      // return this.weeklyForm;
    },
    addRow(array) {
      const type =
        array === this.weeklyForm.workSummaryList
          ? 1
          : array === this.weeklyForm.workPlanList
            ? 2
            : 3;

      this.$nextTick(() => {
        array.push({
          workMatter: "",
          progressScheme: "",
          completeStatus: "",
          type,
          serialNumber: array.length + 1,
        });
      });

      this.$nextTick(() => {
        this.initSortable('workSummaryTable', this.weeklyForm.workSummaryList);
        this.initSortable('workPlanTable', this.weeklyForm.workPlanList);
        this.initSortable('workSupportTable', this.weeklyForm.workSupportList);
      })
    },
    removeRow(array, index) {
      array.splice(index, 1);
    },
    submitForm() {
      return new Promise((resolve, reject) => {
        console.log("weeklyForm.selectUserIds", this.weeklyForm.selectUserIds);
        this.weeklyForm.workSummaryList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workPlanList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workSupportList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        // 本周工作总结，下周工作计划非空校验
        if (
          this.weeklyForm?.workSummaryList?.length === 0 ||
          this.weeklyForm?.workPlanList?.length === 0
        ) {
          this.$modal.msgWarning("本周工作总结和下周工作计划不能为空");
          reject(new Error("本周工作总结和下周工作计划不能为空"));
          return;
        }

        if(!this.weeklyForm.reflectInfo || !this.weeklyForm.reflectInfo.progressScheme){
          this.$modal.msgWarning("每周反思不能为空");
          reject(new Error("每周反思不能为空"));
          return;
        }
        this.$refs.weeklyFormRef.validate((valid) => {
          if (valid) {
            resolve({
              ...this.weeklyForm,
              // 过滤选择的审批人
              reportReviewDetails: this.weeklyForm.reportReviewDetails
                .filter((item) =>
                  this.weeklyForm.selectUserIds.includes(item.userName)
                )
                .map((item) => ({
                  approveUserCode: item.userName,
                  approveUserName: item.nickName,
                })),
            });
          } else {
            reject(new Error("请完善必填项"));
          }
        });
      });
    },
    draftForm() {
      return new Promise((resolve, reject) => {
        console.log("weeklyForm.selectUserIds", this.weeklyForm.selectUserIds);
        this.weeklyForm.workSummaryList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workPlanList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        this.weeklyForm.workSupportList.forEach((item, index) => {
          item.serialNumber = index + 1;
        });
        resolve({
          ...this.weeklyForm,
          // 过滤选择的审批人
          reportReviewDetails: this.weeklyForm.reportReviewDetails
            .filter((item) =>
              this.weeklyForm.selectUserIds.includes(item.userName)
            )
            .map((item) => ({
              approveUserCode: item.userName,
              approveUserName: item.nickName,
            })),
        });
      });
    },
    // 编辑模式下更新表单数据
    updateFormData(data) {
      // this.approveUserName = data.approveUserName
      this.weeklyForm = {
        ...this.weeklyForm,
        ...data,
        workSummaryList: data.workSummaryList || [], // 无数据时兜底，解决table报错
        workPlanList: data.workPlanList || [],
        workSupportList: data.workSupportList || [],
        reportReviewDetails: data.reportReviewDetails.map((item) => ({
          userName: item.approveUserCode,
          nickName: item.approveUserName,
        })),
        reflectInfo: data.reflectInfo || { type: 4, progressScheme: "" },
      };

      this.weeklyForm.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );

      this.$nextTick(() => {
        this.initSortable('workSummaryTable', this.weeklyForm.workSummaryList || []);
        this.initSortable('workPlanTable', this.weeklyForm.workPlanList|| []);
        this.initSortable('workSupportTable', this.weeklyForm.workSupportList|| []);
      })
    },
    setFormData(data) {
      if (data) {
        this.weeklyForm = {
          ...this.weeklyForm,
          ...data,
          year: data.year,
          week: data.week,
          userId: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          userCode: data.userCode,
          workSummaryList: data.workSummaryList || [
            {
              workMatter: "", // 工作板块
              progressScheme: "", // 工作事项/完成进度
              completeStatus: "", // 完成及预警情况
              type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
              serialNumber: 1, // 序号
            },
          ],
          reportReviewDetails: (data?.reportReviewDetails || []).map((item) => ({
            userName: item.approveUserCode,
            nickName: item.approveUserName,
          })),
          workPlanList: data.workPlanList || [
            /*{
              workMatter: "",
              progressScheme: "",
              completeStatus: "",
              type: 2,
              serialNumber: 1,
            },*/
          ],
          workSupportList: data.workSupportList || [
            /*{
              workMatter: "",
              progressScheme: "",
              completeStatus: "",
              type: 3,
              serialNumber: 1,
            },*/
          ],
          reflectInfo: data.reflectInfo || {
            type: 4,
            progressScheme: "",
          },
          id: data.id,
        };
        this.weeklyForm.selectUserIds = (this.weeklyForm?.reportReviewDetails || []).map(
          (item) => item.userName
        );
      }
    },
    validateField(row, field) {
      if (!row[field]) {
        this.$message.warning(
          `${field === "workMatter"
            ? "工作板块"
            : field === "progressScheme"
              ? "工作事项/完成进度"
              : "完成及预警情况"
          }`
        );
        return false;
      }
      return true;
    },
    initSortable(refName, dataList) {
      const el = this.$refs[refName].$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      if (!el) return
      this.sortable = Sortable.create(el, {
        handle: '.el-table__row',
        animation: 150,
        onEnd: ({ newIndex, oldIndex }) => {
          // 创建新数组并重新赋值以触发响应式更新
          const targetRow = dataList.splice(oldIndex, 1)[0];
          dataList.splice(newIndex, 0, targetRow);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.weekly-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
}
.section-title {
  margin: 0 0 18px 10px;
  font-weight: 600;
  text-align: left;
  display: inline-flex;
  align-items: center;
  position: relative;
  z-index: 2; // 添加 z-index 确保文字在上层

  &::before {
    content: " ";
    display: inline-block;
    height: 10px;
    background: #D8E4FB;
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    z-index: -1; // 将伪元素放到文字下方
  }
}
.head-block {
  background: linear-gradient(270deg, rgba(54, 115, 255, 0.05) 0%, rgba(54, 115, 255, 0.2) 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}
.custom-input {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #DDDDDD;
}
.custom-label,
.custom-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.info-row {
  :deep(.el-form-item__label) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  display: flex;
  align-items: center;
}
.weekly-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}


.submit-section {
  margin-top: 20px;
  text-align: center;
}

/* 添加深度选择器来修改 element-plus 表头样式 */
/* :deep(.el-table th) {
  background-color: #f0f2f5 !important;
} */

.weekly-form {
  max-height: calc(100vh - 150px);
  /* 可以根据实际需要调整减去的高度 */
  overflow-y: auto;
  padding: 20px;
}

.approve-row {
  margin-top: 20px;
  text-align: center;
}

/* 添加以下样式来修复 label 和 input 换行的问题 */
.weekly-form :deep(.el-form-item) {
  display: flex;
  margin-bottom: 0px;
}

.weekly-form :deep(.el-form-item__label) {
  float: none;
  display: inline-flex;
  align-items: center;
}

.weekly-form :deep(.el-form-item__content) {
  flex: 1;
  margin-left: 0 !important;
}

.section-content {
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;

  /*  .section-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
  } */
}

:deep(.el-table__header-wrapper .el-table__cell) {
  background: rgba(54, 115, 255, 0.05);
  border-radius: 0px 0px 0px 0px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}


/* Add these new styles */
/* :deep(.el-textarea__inner) {
  min-height: 80px !important;
}

:deep(.el-textarea__inner) {
  max-height: 150px !important;
} */
.weekly-form :deep(th.el-table__cell) {
  padding: 0;
}

.info-row {
  /* margin-bottom: 10px; */
}

.mb-20 {
  margin-bottom: 20px !important;
}

.font-size-15 {
  font-size: 15px;
}

.sortable-table .el-table__row {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.8;
  background: #f0f9eb;
}

.el-table__body tr {
  cursor: move;
}
.align-center{
  display: flex;
  align-items: center;
}
</style>
