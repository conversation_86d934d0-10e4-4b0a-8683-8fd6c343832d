<template>
  <div class="tab-container">
    <div class="category-tabs">
      <div
        v-for="(category, index) in categories"
        :key="category.id"
        class="category-tab"
        :style="{ backgroundColor: category.bgColor, color: category.color, flex: tabsConfig[category.id].length }"
        @click="setActiveCategory(category.id)"
      >
        {{ category.name }}
      </div>
    </div>

    <div class="sub-tabs">
      <div
        v-for="tab in allTabs"
        :key="tab.id"
        class="tab-item"
        :class="{
          'active': value === tab.id,
          'highlight': tab.categoryId === activeCategoryId
        }"
        :style="{
          color: value === tab.id ? categories.find(cat => cat.id === tab.categoryId).color : '#222222',
          borderBottomColor: value === tab.id ? categories.find(cat => cat.id === tab.categoryId).bgColor : ''
        }"
        @click="setActiveTab(tab.id, tab.categoryId)"
      >
        {{ tab.name }}
      </div>
    </div>

    <div
      class="indicator"
      :style="indicatorStyle"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'TabComponent',
  props: {
    tabs: {
      type: Array,
      required: false
    },
    value: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      activeCategoryId: 'basic',
      categories: [
        {
          id: 'basic',
          name: '基本信息',
          bgColor: 'rgba(204,20,20,0.15)',
          color: '#CC1414'
        },
        {
          id: 'quality',
          name: '品质',
          bgColor: 'rgba(247,165,49,0.2)',
          color: '#FF4D00'
        },
        {
          id: 'turnover',
          name: '周转',
          bgColor: 'rgba(71,178,71,0.2)',
          color: '#36B236'
        },
        {
          id: 'profit',
          name: '利润',
          bgColor: 'rgba(74,132,247,0.2)',
          color: '#2F6DEB'
        }
      ],
      tabsConfig: {
        'basic': [
          { id: 'project', name: '项目信息' },
          { id: 'operation', name: '经营指标' }
        ],
        'quality': [
          { id: 'quality-manage', name: '品质管理' },
          { id: 'risk', name: '风险管理' },
          { id: 'engineering', name: '工程评估' },
          { id: 'satisfaction', name: '满意度' }
        ],
        'turnover': [
          { id: 'plan', name: '计划管理' },
          { id: 'sales', name: '销售管理' },
          { id: 'cashflow', name: '现金流' }
        ],
        'profit': [
          { id: 'value', name: '货值' },
          { id: 'cost', name: '成本' },
          { id: 'tax', name: '费用' },
          { id: 'profit-analysis', name: '利润' }
        ]
      }
    }
  },
  computed: {
    allTabs() {
      const tabs = []
      Object.entries(this.tabsConfig).forEach(([categoryId, categoryTabs]) => {
        categoryTabs.forEach(tab => {
          tabs.push({
            ...tab,
            categoryId
          })
        })
      })
      return tabs
    },
    indicatorStyle() {
      const currentIndex = this.allTabs.findIndex(tab => tab.id === this.value)
      if (currentIndex === -1) return {}

      const width = 100 / this.allTabs.length
      const activeCategory = this.categories.find(cat => cat.id === this.activeCategoryId)

      return {
        left: `${currentIndex * width}%`,
        width: `${width}%`,
        backgroundColor: activeCategory ? activeCategory.bgColor : '#CC1414'
      }
    }
  },
  methods: {
    setActiveCategory(categoryId) {
      if (this.activeCategoryId === categoryId) return
      
      this.activeCategoryId = categoryId
      const firstTab = this.tabsConfig[categoryId][0]
      if (firstTab) {
        this.setActiveTab(firstTab.id, categoryId)
      }
      this.$emit('category-change', categoryId)
    },
    setActiveTab(tabId, categoryId) {
      if (this.value === tabId) return
      
      this.activeCategoryId = categoryId
      this.$emit('tab-change', tabId)
      this.$emit('category-change', categoryId)
      this.$emit('input', tabId)
    },
    getTabCategory(tabId) {
      for (const [categoryId, tabs] of Object.entries(this.tabsConfig)) {
        if (tabs.some(tab => tab.id === tabId)) {
          return categoryId
        }
      }
      return null
    }
  },
  created() {
    this.$emit('category-change', this.activeCategoryId)
    this.$emit('tab-change', this.value)
  }
}
</script>

<style scoped>
.tab-container {
  position: relative;
  width: 100%;
  font-family: PingFang SC, PingFang SC;
}

.category-tabs {
  display: flex;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.category-tab {
  padding: 0.25rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1rem;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.category-tab.category-active {
  position: relative;
}

.category-tab.category-active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  border-left: 0.375rem solid transparent;
  border-right: 0.375rem solid transparent;
  border-top: 0.375rem solid currentColor;
}

.sub-tabs {
  display: flex;
  position: relative;
}

.tab-item {
  padding: 0.1875rem 0.5rem 0.5rem;
  font-weight: 400;
  font-size: 1rem;
  color: #222222;
  line-height: 1.1875rem;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.tab-item:hover {
  color: #C00000;
}

.tab-item.highlight {
  color: #606266;
}

.tab-item.active {
  font-size: 1rem;
  color: #CC1414;
}

.indicator {
  position: absolute;
  bottom: 0;
  height: 0.25rem;
  background-color: #CC1414;
  transition: all 0.3s ease;
}
</style>
