<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型" prop="mattersType">
        <el-select v-model="queryParams.mattersType" placeholder="请选择分类" clearable
                   @keyup.enter.native="handleQuery"
                   style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
                     :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="管理员" prop="leaderUserName">
        <el-input suffix-icon="el-icon-search" v-model="leaderNickName" placeholder="请选择管理员" clearable
                  @keyup.enter.native="handleQuery" @focus="selectUserFun('query')"
                  @change="changeresponsibilityer()" style="width: 200px;"/>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['rant:leader:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['rant:leader:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['rant:leader:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="leaderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="督办类型" align="center" prop="mattersType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rant_matters_type" :value="scope.row.mattersType"/>
        </template>
      </el-table-column>
      <el-table-column label="管理员" align="center" prop="leaderUserName"/>
      <el-table-column label="管理员名称" align="center" prop="leaderNickName"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 添加或修改督办类型管理员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="督办类型" prop="mattersType">

            <el-select class="batch-edit-input" v-model="form.mattersType" placeholder="请选择督办类型">
              <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
                         :value="Number(dict.value)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="管理员" prop="leaderUserName">
            <el-input v-model="form.leaderNickName" placeholder="请选择责任人" clearable
                      @keyup.enter.native="handleQuery"
                      @focus="selectUserFun('responsibilityer')">
              <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('responsibilityer')"
                 style="cursor: pointer;"></i></el-input>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData"/>
  </div>
</template>

<script>
import {getLeader, listLeader, addLeader, updateLeader, delLeader} from "@/api/rant/typeLeader.js";
import SelectUser from "@/views/rant/components/SelectUser/index.vue";

export default {
  name: "TypeLeader",
  components: {SelectUser},
  dicts: ['rant_matters_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督办类型管理员表格数据
      leaderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mattersType: null,
        leaderUserName: null,
        leaderNickName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mattersType: [
          {
            required: true,
            message: "类型不能为空",
            trigger: ["blur", "change"],
          },
        ],
        leaderUserName: [
          {
            required: true,
            message: "管理员不能为空",
            trigger: ["blur", "change"],
          },
        ],
      },
      leaderNickName: "",
      selectUserShow: true,
      isMultiple: false,
      roleName: ""
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询督办类型管理员列表 */
    getList() {
      this.loading = true;
      listLeader(this.queryParams).then(response => {
        this.leaderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mattersType: null,
        leaderUserName: null,
        leaderNickName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加督办类型管理员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLeader(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改督办类型管理员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          if (this.form.id != null) {
            updateLeader(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.submitLoading = false
              this.getList();
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            addLeader(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.submitLoading = false
              this.getList();
            }).finally(() => {
              this.submitLoading = false
            })
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除该数据项？').then(function () {
        return delLeader(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('rant/typeLeader/export', {
        ...this.queryParams
      }, `leader_${new Date().getTime()}.xlsx`)
    },
    changeresponsibilityer() {
      if (
        this.leaderNickName == "" ||
        this.leaderNickName == null
      ) {
        this.queryParams.leaderUserName = null;
      }
    },
    selectUserFun(type) {

      this.selectUserType = type;
      this.isMultiple = type == "responsibilityer";
      this.$refs.userRef.show();
    },
    selectUserData(data) {
      console.log(data, this.selectUserType);
      if (this.selectUserType == "query") {
        this.queryParams.leaderUserName = data.userName;
        this.leaderNickName = data.nickName;

      } else if (this.selectUserType == "responsibilityer") {
        this.form.leaderUserName = data.map((item) => item.userName).join(",");
        this.form.leaderNickName = data.map((item) => item.nickName).join(",");
      }
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);

    },
  }
};
</script>
