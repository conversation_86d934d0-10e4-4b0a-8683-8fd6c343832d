<template>
    <div class="custom-steps">
        <div class="step-item" :class="{ 'step-current': stepActive === 1, 'step-finished': stepActive > 1 }">
            <div class="step-icon">
                <img v-if="stepActive === 1" src="@/assets/rant/step-current.png" class="icon-img" alt="当前步骤" />
                <img v-else-if="stepActive > 1" src="@/assets/rant/step-finished.png" class="icon-img" alt="已完成" />
                <img v-else src="@/assets/rant/step-wait.png" class="icon-img" alt="等待中" />
            </div>
            <div class="step-title" :class="{ 'current': stepActive === 1, 'finished': stepActive > 1 }">新增</div>
            <div class="step-line" :class="{ 'finished-line': stepActive > 1 }"></div>
        </div>
        <div class="step-item" :class="{ 'step-current': stepActive === 2, 'step-finished': stepActive > 2 }">
            <div class="step-icon">
                <img v-if="stepActive === 2" src="@/assets/rant/step-current.png" class="icon-img" alt="当前步骤" />
                <img v-else-if="stepActive > 2" src="@/assets/rant/step-finished.png" class="icon-img" alt="已完成" />
                <img v-else src="@/assets/rant/step-wait.png" class="icon-img" alt="等待中" />
            </div>
            <div class="step-title" :class="{ 'current': stepActive === 2, 'finished': stepActive > 2 }">初审</div>
            <div class="step-line" :class="{ 'finished-line': stepActive > 2 }"></div>
        </div>
        <div class="step-item" :class="{ 'step-current': stepActive === 3, 'step-finished': stepActive > 3 }">
            <div class="step-icon">
                <img v-if="stepActive === 3" src="@/assets/rant/step-current.png" class="icon-img" alt="当前步骤" />
                <img v-else-if="stepActive > 3" src="@/assets/rant/step-finished.png" class="icon-img" alt="已完成" />
                <img v-else src="@/assets/rant/step-wait.png" class="icon-img" alt="等待中" />
            </div>
            <div class="step-title" :class="{ 'current': stepActive === 3, 'finished': stepActive > 3 }">责任人确认</div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'RantStep',
    props: {
        stepActive: {
            type: Number,
            required: true,
            default: 1
        }
    }
}
</script>

<style scoped lang="scss">
.custom-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    .step-item {
        flex: 1;
        text-align: center;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F5F7FA;
            margin-bottom: 8px;
            z-index: 2;

            .icon-img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .step-title {
            font-size: 14px;
            color: #909399;

            &.current {
                color: #409EFF;
                font-weight: 500;
            }

            &.finished {
                // color: #67C23A;
            }
        }

        .step-line {
            position: absolute;
            top: 16px;
            right: -50%;
            height: 2px;
            width: 100%;
            background-color: #EBEEF5;
            z-index: 1;

            &.finished-line {
                background-color: #3673FF;
            }
        }

        &:last-child .step-line {
            display: none;
        }

        &.step-current {
            .step-icon {
                background-color: #ECF5FF;
                // border: 2px solid #409EFF;
            }
        }

        &.step-finished {
            .step-icon {
                background-color: #F0F9EB;
                // border: 2px solid #3673FF;
            }
        }
    }
}
</style>
