import request from '@/utils/request'

// 获取城市公司，项目树
function getProjectTree (query) {
  return request({
    url: '/project/common/getTree',
    method: 'get',
  })
}

function getCity (query) { // 根据当前用户获取城市公司
  return request({
    url: '/project/common/getCity',
    method: 'get',
  })
}


function getProject (cityId) { // 根据城市公司id获取项目主数据
  return request({
    url: `/project/common/getProject/${cityId}`,
    method: 'get',
  })
}

export function uploadFile(file) { // 单文件上传
  return request({
    url: '/file/upload',
    method: 'post',
    data: file
  })
}

export function getProjectType(projectCode) {
  return request({
    url: `/project/common/getProjectType/${projectCode}`,
    method: 'get',
  })
}

export function getImage(query) {
  return request({
    url: `/project/common/getImage?imageUrl=${query.imageUrl}`,
    method: 'get'
  })
}
export default {
  getProjectTree,
  getCity,
  getProject,
  uploadFile,
  getProjectType,
  getImage
}
