{"remainingRequest": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue?vue&type=style&index=0&id=724887a0&scoped=true&lang=scss", "dependencies": [{"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue", "mtime": 1754312350764}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743920554310}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743920557792}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743920554762}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743920556210}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743920555710}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL3NhbGVzLnNjc3MnOw0K"}, {"version": 3, "sources": ["sales.vue"], "names": [], "mappings": ";AAo0DA", "file": "sales.vue", "sourceRoot": "src/views/projects/views", "sourcesContent": ["<template>\r\n  <section class=\"projects-content-container w-100\">\r\n    <!-- 无权限状态 -->\r\n    <div v-if=\"!hasPermi(['project:sales:manage'])\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-auth.png\" alt=\"无权限\">\r\n      <div class=\"desc\">暂无权限</div>\r\n      <div class=\"desc\">请联系管理员</div>\r\n    </div>\r\n    <!-- 无数据状态 -->\r\n    <div v-else-if=\"!hasData\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-data.png\" alt=\"无数据\">\r\n      <div class=\"desc\">暂无数据</div>\r\n    </div>\r\n    <section v-else class=\"w-100\">\r\n      <div class=\"sales-content\">\r\n        <!-- 全周期销售进度模块 -->\r\n        <div class=\"sales card mb-16 mt-16\" v-loading=\"loadingSales\">\r\n          <div class=\"card-title\">\r\n            全周期销售进度\r\n              <span class=\"total-target\" v-if=\"hasPermi(['sales:all:price'])\">全周期住宅均价 ¥ {{ allQyData.zzUnitPrice || '--' }}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">签约</div>\r\n                <Empty :no-authority=\"hasPermi(['sales:all:qy'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"qyProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': qyProgressOption.title.textStyle.color}\">\r\n                            {{ qyProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ qyProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.qyAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计签约</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计签约</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzQyAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDyAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"flex-item hk-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">回款</div>\r\n<!--                <div class=\"progress-item-title\">回款（权益）</div>-->\r\n                <Empty :no-authority=\"hasPermi(['sales:all:hk'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"hkProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': hkProgressOption.title.textStyle.color}\">\r\n                            {{ hkProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ hkProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">动态货值（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.hkAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计回款</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">累计回款（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">{{ allHkData.qyRate }}%</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">权益比例</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n<!--                          <td class=\"progress-label\">动态货值（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计回款</td>\r\n<!--                          <td class=\"progress-label\">累计回款（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzHkAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzHkAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 全周期进度偏差模块 -->\r\n        <div class=\"time-progress card mb-16 mt-16\">\r\n          <div class=\"card-title\">全周期进度偏差</div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetQYProgress\">\r\n              <div class=\"target-title mb-4rem\">签约</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:all:qyDeviation'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': signDataByTime.openDate ? `'${signDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': signDataByTime.liquidateDate ? `'${signDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue sign-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdRate), 'sign-tooltip-1')\">\r\n                          <div>时点完成{{$toFixed2(signDataByTime.qyAmount)}}亿（{{$toFixed2(signDataByTime.allSdRate)}}%）</div>\r\n                          <div>全盘动态货值{{$toFixed2(signDataByTime.dynamicAmount)}}亿</div>\r\n                          <div>偏差率{{$toFixed2(signDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow  sign-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdTargetRate),  'sign-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(signDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(signDataByTime.targetAmount)}}亿</div>\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(signDataByTime.zzQyAmount)}}亿（{{$toFixed2(signDataByTime.zzSdRate)}}%)</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(signDataByTime.zzDynamicAmount)}}亿</td>\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(signDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(signDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{ signDataByTime.zzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{ $formatNull(signDataByTime.zzLiquidateDate) }}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td  class=\"progress-label\">非住: 时点完成{{$toFixed2(signDataByTime.noZzQyAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdRate)}}%)</td>\r\n                      <td>| 动态货值{{$toFixed2(signDataByTime.noZzDynamicAmount)}}亿</td>\r\n                      <td>| 时点目标{{$toFixed2(signDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdTargetRate)}}%)</td>\r\n                      <td>| 目标货值{{$toFixed2(signDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td> | 清盘日期{{ signDataByTime.noZzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"pl-date\">清盘日期 {{ $formatNull(signDataByTime.noZzLiquidateDate) }}</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div></Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetHKProgress\">\r\n              <div class=\"target-title mb-4rem\">回款</div>\r\n<!--              <div class=\"target-title mb-4rem\">回款（权益）</div>-->\r\n              <Empty :no-authority=\"hasPermi(['sales:all:hkDeviation'])\"><div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': hkDataByTime.openDate ? `'${hkDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': hkDataByTime.liquidateDate ? `'${hkDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue  hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdRate), 'hk-tooltip-1')\">\r\n                      <div>时点完成{{$toFixed2(hkDataByTime.hkAmount)}}亿（{{$toFixed2(hkDataByTime.allSdRate)}}%）</div>\r\n                      <div>全盘动态货值{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>\r\n<!--                      <div>全盘动态货值（权益）{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>-->\r\n                      <div>偏差率{{$toFixed2(hkDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdTargetRate), 'hk-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(hkDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>\r\n<!--                      <div>全盘目标货值（权益）{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>-->\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(hkDataByTime.zzHkAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.zzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\">清盘日期 {{$formatNull(hkDataByTime.zzLiquidateDate)}}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 时点完成{{$toFixed2(hkDataByTime.noZzHkAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.noZzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{$formatNull(hkDataByTime.noZzLiquidateDate)}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <!-- <div>住宅: 时点完成{{hkDataByTime.zzHkAmount}}亿（{{hkDataByTime.allSdRate}}%） | 住宅动态货值{{hkDataByTime.zzDynamicAmount}}亿 | 时点目标{{hkDataByTime.zzSdTargetAmount}}亿（{{hkDataByTime.zzSdRate}}%） | 住宅目标货值{{hkDataByTime.targetAmount}}亿 | 清盘日期{{hkDataByTime.zzQpDate}}</div>\r\n                  <div>非住: 时点完成{{hkDataByTime.noZzHkAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住动态货值{{hkDataByTime.noZzDynamicAmount}}亿 | 时点目标{{hkDataByTime.noZzSdTargetAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住目标货值{{hkDataByTime.noZzTargetAmount}}亿 | 清盘日期{{hkDataByTime.noZzQpDate}}</div> -->\r\n                </div>\r\n              </div>\r\n            </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年度销售进度模块 -->\r\n        <div class=\"year-progress card mb-16 mt-16\" v-loading=\"loadingYearProgress\">\r\n          <div class=\"card-title\">\r\n            年度销售进度\r\n            <span class=\"total-target\" v-if=\"hasPermi(['sales:year:price'])\">年度住宅均价 ¥ {{ $toFixed2(yearQyData.zzUnitPrice)}}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度签约目标 ¥ {{ $toFixed2(yearQyData.targetAmount)}} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:qy'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <!-- 刻度线 -->\r\n                    <div\r\n                      class=\"marker blue\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.ratio)}%`}\"\r\n                    ></div>\r\n                    <!-- 提示框 -->\r\n                    <div class=\"tooltip-progress blue year-tooltip-1\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.ratio), 'year-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearQyData.totalAmount) }} 亿 ({{ $toFixed2(yearQyData.ratio) }}%)</span>\r\n                    </div>\r\n                    <!-- 三角形 -->\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearQyData.ratio))\"></div>\r\n                    <div\r\n                      class=\"marker yellow\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-tooltip-2\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.dayRatio), 'year-tooltip-2')\">\r\n                      <span>时间进度：{{ yearQyData.month }}月 ({{ $toFixed2(yearQyData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearQyData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度签约目标{{ $toFixed2(yearQyData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计签约金额{{ $toFixed2(yearQyData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearQyData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度签约目标{{ $toFixed2(yearQyData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计签约金额{{ $toFixed2(yearQyData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearQyData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度回款目标 ¥ {{ $toFixed2(yearHkData.targetAmount) || '--' }} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:hk'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <div class=\"marker blue\" :style=\"{ left: `${filterRatioMax(yearHkData.ratio)}%` }\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress blue year-hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.ratio), 'year-hk-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearHkData.totalAmount) }} 亿 ({{ $toFixed2(yearHkData.ratio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearHkData.ratio))\"></div>\r\n\r\n                    <div class=\"marker yellow\" :style=\"{ left: `${filterRatioMax(yearHkData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.dayRatio), 'year-hk-tooltip-2')\">\r\n                      <span>时间进度：{{ yearHkData.month }}月 ({{ $toFixed2(yearHkData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearHkData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度回款目标{{ $toFixed2(yearHkData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计回款金额{{ $toFixed2(yearHkData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearHkData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度回款目标 {{ $toFixed2(yearHkData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计回款金额 {{ $toFixed2(yearHkData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearHkData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 款齐模块 -->\r\n        <div class=\"receiving-payments mb-16\" v-loading=\"loadingPayments\">\r\n          <div class=\"card content-block\">\r\n            <div class=\"card-title-2\">款齐</div>\r\n            <Empty :no-authority=\"hasPermi(['sales:kq'])\">\r\n              <div class=\"basic-info-item flex-container\">\r\n              <div class=\"circle-chart chart-size\">\r\n                <section class=\"card-with-title\">\r\n                  <Chart :option=\"kqProgressOption\" class=\"chart-size\"></Chart>\r\n                  <section class=\"chart-title-block\">\r\n                    <div class=\"title-1\" :style=\"{'color': kqProgressOption.title.textStyle.color}\">\r\n                      {{ kqProgressOption.title.text }}\r\n                    </div>\r\n                    <div class=\"title-2\">{{ kqProgressOption.title.subtext }}</div>\r\n                  </section>\r\n                </section>\r\n              </div>\r\n              <div class=\"card-wrapper\">\r\n                <div class=\"data-card\" v-for=\"item in cardData\"\r\n                     :style=\"{ backgroundColor: item.color, boxShadow: `0px 8px 20px 0px ${item.color}` }\">\r\n                  <div class=\"card-label\">{{ item.label }}</div>\r\n                  <br/>\r\n                  <div class=\"card-value\">\r\n                    <span class=\"currency\">¥ </span>\r\n                    <span>{{ $toFixed2(item.value) }} 亿</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            </Empty>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格数据模块 -->\r\n        <div class=\"table-container mb-16\" v-loading=\"loadingTables\">\r\n          <div class=\"card-title mb-16\">销售分析</div>\r\n          <Empty :no-authority=\"hasPermi(['sales:data'])\">\r\n            <CardTabSales v-model=\"queryTypeSign\" :tabs=\"queryTypeSignList\" @click=\"handleTabSign\" class=\"mb-14\"/>\r\n          <el-table\r\n            :data=\"qyData\"\r\n            style=\"width: 100%\"\r\n            :border=\"true\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.5rem'\r\n                  }\"\r\n            :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                     lineHeight: '1.5rem'\r\n                  }\"\r\n            class=\"project-table mb-16\">\r\n            <!-- 签约table -->\r\n            <el-table-column align=\"center\" label=\"累计签约\">\r\n              <el-table-column label=\"金额\" prop=\"qyAmount\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.qyAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.qyAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"qyArea\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  <span>{{ $toFixed2(scope.row.qyArea)}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"套数\" prop=\"qyNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"12.5%\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"累计到访转化率\">\r\n              <el-table-column label=\"到访人数\" prop=\"dfNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"转化率\" prop=\"ratio\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{ $toFixed2(scope.row.ratio) }}%\r\n                </template>\r\n              </el-table-column>\r\n            </el-table-column>\r\n          </el-table>\r\n          <!-- 认购table -->\r\n          <el-table\r\n            :data=\"rgData\"\r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n                  :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n            class=\"mb-16 project-table\">\r\n            <el-table-column align=\"center\" label=\"累计认购\" :colspan=\"6\">\r\n              <el-table-column label=\"金额\" prop=\"rgAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.rgAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.rgAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"rgArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"tsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"截至当前认未签\" :colspan=\"5\">\r\n              <el-table-column label=\"金额\" prop=\"noQyTotalAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    (scope.row.noQyTotalAmount / 100000000).toFixed(2) + '亿'\r\n\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"noQyTotalArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"noQyTsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"noQyZzNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n          </el-table>\r\n          </Empty>\r\n\r\n\r\n          <div class=\"cost flex-container\" style=\"min-height: 26.5rem;\">\r\n            <div class=\"flex-item chart\">\r\n              <div class=\"card-title mb-10\">签约业态分布</div>\r\n              <!--              <div class=\"sign-chart-block\">-->\r\n              <Empty :no-authority=\"hasPermi(['sales:bussDist'])\">\r\n                <!-- <Chart class=\"\" :option=\"getQYYTOption(qyYTData)\" v-if=\"qyYTData.length > 0\"></Chart> -->\r\n                <Chart class=\"\" :option=\"qyYTOption\"></Chart>\r\n                <!-- <div v-else class=\"no-data\">暂无数据</div> -->\r\n              </Empty>\r\n              <!--              </div>-->\r\n            </div>\r\n            <div class=\"flex-item\" style=\"min-width:0;\">\r\n              <div class=\"card-title mb-10\">货值分布表</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:goodsValue'])\">\r\n                <!-- max-height: 30rem; -->\r\n                <el-table\r\n                class=\"project-table\"\r\n                :data=\"analysisData\"\r\n                style=\"width: 100%;max-height: 30rem; \"\r\n                :border=\"true\"\r\n                :span-method=\"handleSpanMethod\"\r\n                height=\"'30rem'\"\r\n                :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    fontSize: '0.875rem',\r\n                    height: '2rem',\r\n                  }\"\r\n                :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                  }\"\r\n              >\r\n                <el-table-column label=\"业态\" prop=\"projectType\" align=\"center\" min-width=\"8\"/>\r\n                <el-table-column label=\"户型\" prop=\"hxName\" align=\"center\" min-width=\"12\"/>\r\n                <el-table-column label=\"总套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"totalNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"allTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"allBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"总货值(万元)\" prop=\"allHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"已售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"ysNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"ysTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"ysBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                  <el-table-column label=\"已售货值(万元)\" prop=\"ysHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"未售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"wsNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"已供未售\" prop=\"ygwsNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"未供\" prop=\"wgNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"顶层\" prop=\"wsTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"wsBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"未售货值(万元)\" prop=\"wsHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n              </el-table>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n        <!-- 趋势图模块 -->\r\n        <div class=\"trend mb-16\" v-loading=\"loadingTrends\">\r\n          <div class=\"card-title mb-16\">趋势分析</div>\r\n          <CardTabSales v-model=\"queryTypeTrend\" :tabs=\"queryTypeTrendList\" @click=\"handleTrendTab\" class=\"mb-14\"/>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">到访趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:dfTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ dfOptionName }}</div>\r\n                <Chart :option=\"dfOption\" class=\"trend-chart\"></Chart>\r\n              </Empty>\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">认购趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:rgTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ rgOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabRGTrend === index }]\"\r\n                    @click=\"selectTabRGTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"rgOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"rgProjectType\" :tabs=\"projectTypeList\" @click=\"handleRgTab\" class=\"mb-14\"/>\r\n                <!--            <CardTabSales v-model=\"activeTabRGTrend\" :tabs=\"tabs\" @click=\"selectTabRGTrend\" class=\"mb-14\"/>-->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">签约趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:qyTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ qyOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabQYTrend === index }]\"\r\n                    @click=\"selectTabQYTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"qyOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"qyProjectType\" :tabs=\"projectTypeList\" @click=\"handleQyTab\" class=\"mb-14\"/>\r\n                <!-- <CardTabSales v-model=\"activeTabQYTrend\" :tabs=\"tabs\" @click=\"selectTabQYTrend\" class=\"mb-14\"/> -->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title  mb-25\">回款趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:hkTrend'])\">\r\n                <div class=\"trend-chart-container\">\r\n                <div class=\"trend-chart-title\">{{ hkOptionName }}</div>\r\n                <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart>\r\n              </div>\r\n              <!-- <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart> -->\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"hkProjectType\" :tabs=\"projectTypeList\" @click=\"handleHKTab\" class=\"mb-14\"/>\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </section>\r\n\r\n</template>\r\n<script>\r\nimport ProgressBar from '@/views/projects/components/ProgressBar.vue'\r\nimport CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'\r\nimport ProgressCircle from '@/views/projects/constants/ProgressCircle'\r\nimport Chart from '@/views/projects/components/Chart.vue'\r\nimport BarOption from '@/views/projects/constants/bar'\r\nimport PieOption from '@/views/projects/constants/Pie'\r\nimport API from '@/views/projects/api'\r\nimport CardTabSales from \"@/views/projects/components/CardTabSales.vue\";\r\nimport Empty from \"@/views/projects/components/empty.vue\";\r\n\r\nexport default {\r\n  name: 'Sales',\r\n  components: {\r\n    CumulativeIndicatorsCard,\r\n    ProgressBar,\r\n    Chart,\r\n    CardTabSales,\r\n    Empty\r\n  },\r\n  data() {\r\n    return {\r\n      yearQyData: {\r\n        /*ratio: 0,\r\n        dayRatio: 100,\r\n        totalAmount: 0,\r\n        month: 0,\r\n        zzTotalAmount: 0,\r\n        noZzTotalAmount: 0*/\r\n      }, // 年度销售进度-年度签约数据\r\n      yearHkData: {}, // 年度销售进度-年度回款数据\r\n      signDataByTime: { // 序时销售进度-签约\r\n      },\r\n      loadingAllTargetQYProgress: false,\r\n      loadingAllTargetHKProgress: false,\r\n      hkDataByTime: { // 序时销售进度-回款\r\n      },\r\n      queryTypeSignList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 4,\r\n          name: '年'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        },\r\n      ],\r\n      queryTypeSign: 2,\r\n      qyData: [],\r\n      rgData: [],\r\n      ////////////\r\n      queryTypeTrendList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        }\r\n      ],\r\n      queryTypeTrend: 2,\r\n      hkTrendData: {}, // 回款趋势图数据\r\n      qyTrendData: {}, // 签约趋势图数据\r\n      rgTrendData: {}, // 认购趋势图数据\r\n      dfTrendData: {}, // 到访趋势图数据\r\n      hkTrendRawData: {}, // 回款趋势图原始数据\r\n      qyTrendRawData: {}, // 签约趋势图原始数据\r\n      rgTrendRawData: {}, // 认购趋势图原始数据\r\n      dfTrendRawData: {}, // 到访趋势图原始数据\r\n      ProgressCircleOption: new ProgressCircle().getOption(),\r\n      cardData: [ // 款齐\r\n        {\r\n          label: '年度目标',\r\n          value: '--',\r\n          color: '#53BD88'\r\n        },\r\n        {\r\n          label: '年度款齐',\r\n          value: '--',\r\n          color: '#7B6FF2'\r\n        },\r\n        {\r\n          label: '可结利',\r\n          value: '--',\r\n          color: '#3EB6CF'\r\n        }\r\n      ],\r\n      projectCode: '',\r\n      projectTypeList: [], // 业态集合\r\n      hkProjectType: '',\r\n      rgProjectType: '',\r\n      qyProjectType: '',\r\n      queryType: 2, // （0-全盘 1-今日 2-本周 3-本月）\r\n      allQyData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, //全盘销售进度 - 签约\r\n      allHkData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, // 全盘销售进度 - 回款\r\n      kqData: {},\r\n      qyYTData: [], // 签约业态分布\r\n      tabs: [\r\n        '套数',\r\n        '金额',\r\n        '面积'\r\n      ],\r\n      activeTabRGTrend: 0, // 套数\r\n      activeTabQYTrend: 0, // 套数\r\n      tooltipStyles: {\r\n        blue: {left: '0px'},\r\n        yellow: {left: '0px'}\r\n      },\r\n      dfOption: {},\r\n      rgOption: {},\r\n      qyOption: {},\r\n      hkOption: {},\r\n      analysisData: [], // 货值分析数据\r\n      hasData: true,\r\n      isDeveloping: false, // 控制是否显示开发中状态\r\n\r\n      // 添加loading状态变量\r\n      loadingSales: false,\r\n      loadingTimeProgress: false,\r\n      loadingYearProgress: false,\r\n      loadingPayments: false,\r\n      loadingTables: false,\r\n      loadingTrends: false,\r\n      hkOptionName: '',\r\n      dfOptionName: '',\r\n      rgOptionName: '',\r\n      qyOptionName: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.projectCode = this.$route.query.projectCode\r\n  },\r\n  async mounted() {\r\n    await this.getProjectType(this.projectCode);\r\n    this.initTrend(); // 初始化趋势图\r\n    this.getAllQyData({ //全盘销售进度 - 签约\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getAllHkData({ //全盘销售进度 - 回款\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getkqData({ // 款齐\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQyDfData({ // 累���签约\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getRgData({ // 累计认购\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQYYTData({ // 签约业态分布\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.fetchAnalyse(this.projectCode) // 获取货值分析数据\r\n    // 年度销售进度-年度签约数据\r\n    this.getYearQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 年度销售进度-年度回款数据\r\n    this.getYearHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-签约数据\r\n    this.getAllTargetQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-回款数据\r\n    this.getAllTargetHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n\r\n  },\r\n  methods: {\r\n    hasPermi(permission) {\r\n      const permissions = this.$store.getters.permissions\r\n      // Check for all permissions wildcard first\r\n      if (permissions.includes('*:*:*')) {\r\n        return true\r\n      }\r\n      // Check specific permissions\r\n      return permissions.some(p => permission.includes(p))\r\n    },\r\n    filterRatioMax(ratio) {\r\n      if (ratio > 100) {\r\n        ratio = 100;\r\n      } else if (ratio < 0) {\r\n        ratio = 0;\r\n      }\r\n      return ratio;\r\n    },\r\n    getArrowStyle(ratio) {\r\n      return {left: `${ratio}%`};\r\n    },\r\n    getTooltipStyle(ratio, tooltipClass) {\r\n      const scale = window.innerWidth / 1680;\r\n      // const tooltipWidth = 170 * scale; // tooltip的宽度(px)\r\n      const tooltipWidth = document.querySelector(`.${tooltipClass}`)?.clientWidth || 0; // tooltip的宽度(px)\r\n      const containerWidth = document.querySelector('.timeline')?.clientWidth || 0;\r\n      const tooltipRatio = (tooltipWidth / containerWidth) * 100;\r\n      if (ratio == '--') { // raio为--时，tooltip靠边\r\n        return {\r\n          left: `0`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n\r\n      // 超出右边界\r\n      if ((Math.ceil(ratio) + Math.ceil(tooltipRatio) / 2) > 100) {\r\n        return {\r\n          // right: `0`,\r\n          right: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // bordrRadius: `0 0 0 0`\r\n\r\n      }\r\n      // 超出左边界\r\n      else if ((Math.ceil(ratio) - Math.floor(tooltipRatio) / 2) <= 0) {\r\n        return {\r\n          left: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // borderRadius: `0 0  0 0`\r\n\r\n      }\r\n\r\n      // console.log('ratio---', ratio);\r\n      // console.log('tooltipRatio---', tooltipRatio);\r\n      if (ratio >= 50) {\r\n        return {\r\n          // right: `${100 - ratio - 8}%`,\r\n          right: `${100 - ratio}%`,\r\n          transform: `translateX(50%)`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n      return {\r\n        left: `${ratio}%`,\r\n        transform: `translateX(-50%)`,\r\n        textAlign: 'left'\r\n      };\r\n    },\r\n    formatValue(value) {\r\n      const absValue = Math.abs(value);\r\n      let formattedValue;\r\n      let unit = '';\r\n\r\n      if (absValue >= 100000000) {\r\n        formattedValue = (absValue / 100000000).toFixed(2);\r\n        unit = '亿';\r\n      } else if (absValue >= 10000) {\r\n        formattedValue = (absValue / 10000).toFixed(2);\r\n        unit = '万';\r\n      } else {\r\n        formattedValue = absValue.toFixed(2);\r\n      }\r\n\r\n      return {\r\n        formattedValue: value < 0 ? `-${formattedValue}` : formattedValue,\r\n        unit\r\n      };\r\n    },\r\n    selectTabQYTrend(index) {\r\n      this.activeTabQYTrend = index;\r\n      // this.activeTabQYTrend = item.code;\r\n      this.updateQYTrendData();\r\n    },\r\n    getQyOption() {\r\n      this.qyOption = this.getBarOption(['#FF9B47'], this.qyTrendData);\r\n      if (this.activeTabQYTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.qyOption.yAxis[1].name = '单位：万元';\r\n          this.qyOptionName = '单位：万元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          //  this.qyOption.yAxis[1].name = '单位：亿元';\r\n          this.qyOptionName = '单位：亿元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabQYTrend === 0) {\r\n        // this.qyOption.yAxis[1].name = '单位：套';\r\n        this.qyOptionName = '单位：套';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      } else if (this.activeTabQYTrend === 2) {\r\n        //  this.qyOption.yAxis[1].name = '单位：m²';\r\n        this.qyOptionName = '单位：m²';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.qyOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    updateQYTrendData() {\r\n      const data = this.qyTrendRawData;\r\n      switch (this.activeTabQYTrend) {\r\n        case 0: // 套数\r\n          this.filteredQYTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredQYTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredQYTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredQYTrendData = data;\r\n      }\r\n      this.qyTrendData.series = this.filteredQYTrendData;\r\n      this.getQyOption();\r\n    },\r\n    selectTabRGTrend(index) {\r\n      this.activeTabRGTrend = index;\r\n      this.updateRGTrendData();\r\n    },\r\n    getRgOption() {\r\n      this.rgOption = this.getBarOption(['#376DF7'], this.rgTrendData)\r\n    },\r\n    updateRGTrendData() {\r\n      const data = this.rgTrendRawData;\r\n      switch (this.activeTabRGTrend) {\r\n        case 0: // 套数\r\n          this.filteredRGTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredRGTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredRGTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredRGTrendData = data;\r\n      }\r\n      this.rgTrendData.series = this.filteredRGTrendData;\r\n      this.getRgOption();\r\n      if (this.activeTabRGTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.rgOption.yAxis[1].name = '单位：万元';\r\n          this.rgOptionName = '单位：万元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          // this.rgOption.yAxis[1].name = '单位：亿元';\r\n          this.rgOptionName = '单位：亿元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabRGTrend === 0) {\r\n        // this.rgOption.yAxis[1].name = '单位：套';\r\n        this.rgOptionName = '单位：套';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n          ;\r\n        }\r\n      } else if (this.activeTabRGTrend === 2) {\r\n        // this.rgOption.yAxis[1].name = '单位：m²';\r\n        this.rgOptionName = '单位：m²';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.rgOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getQYYTData(data) { // 签约业态分布\r\n      const res = await API.Sales.bussDist(data);\r\n      this.qyYTData = res.data;\r\n    },\r\n    getQYYTOption(data) { // 签约业���分布chart option\r\n      const option = this.getPieOption();\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n      const scale = window.innerWidth / 1680;\r\n      const rich = {\r\n        yellow: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0],\r\n          align: 'center'\r\n        },\r\n        total: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 40 * scale,\r\n          align: 'center'\r\n        },\r\n        labelColor: {\r\n          color: \"#333333\",\r\n          align: 'center',\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0]\r\n        },\r\n        blue: {\r\n          color: '#49dff0',\r\n          fontSize: 16 * scale,\r\n          align: 'center'\r\n        },\r\n        hr: {\r\n          borderColor: '#0b5263',\r\n          width: '100%',\r\n          borderWidth: 1 * scale,\r\n          height: 0,\r\n          margin: [5, 0]\r\n        }\r\n      }\r\n      option.series[0].data = data.map(item => {\r\n        return {\r\n          value: this.$toFixed2(item.rate),\r\n          name: item.bussName,\r\n          ...item\r\n        }\r\n      });\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">名称: ${params.data.bussName}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">套数: ${params.data.num}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">面积: ${params.data.area}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n      // option.series[0].avoidLabelOverlap = true\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示连接线\r\n        minTurnAngle: 90, // 限制转折角度，防止错位\r\n        normal: {\r\n          length: 20 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: data.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle', // 设置图例为小圆点\r\n      };\r\n      option.series[0].label = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        // overflow: 'truncate', // 防止标签超出容器\r\n        distance: 5 * scale,\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n      return option;\r\n    },\r\n    async getAllHkData(data) { // 全盘销售进度 - 回款\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allHkData(data);\r\n        this.allHkData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    async getAllQyData(data) { // 全盘销售进度 - 签约\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allQyData(data);\r\n        this.allQyData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    handleTrendTab(item) { // 趋势图查询类型切换\r\n      this.queryTypeTrend = item.code;\r\n      this.initTrend(); // 初始化趋势图\r\n    },\r\n    updateCSSVariable(value) {\r\n      document.documentElement.style.setProperty('--timeline-before-content', value);\r\n    },\r\n    async getAllTargetQyData(data){ // 全周期进度偏差-签约数据\r\n      this.loadingAllTargetQYProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetQyData(data);\r\n        // 使用$formatNull对res.data中的所有数据字段格式化\r\n        this.signDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetQYProgress = false;\r\n      }\r\n    },\r\n    async getAllTargetHkData(data) { // 全周期进度偏差-回款数据\r\n      this.loadingAllTargetHKProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetHkData(data);\r\n        this.hkDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetHKProgress = false;\r\n      }\r\n    },\r\n    async getYearQyData(data) { // 年度销售进度-年度签约数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetQyData(data);\r\n        this.yearQyData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getYearHkData(data) { // 年度销售进度-年度回款数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetHkData(data);\r\n        this.yearHkData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n        // this.yearHkData = res.data;\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getRgData(data) { // 累计认购\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.rgData(data);\r\n        this.rgData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    async getQyDfData(data) { // 累计签约\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.qyDfData(data);\r\n        this.qyData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    sanitizeData(data) { // 数据为空时，显示--\r\n      if (!data) return {};\r\n      return Object.fromEntries(\r\n        Object.entries(data).map(([key, value]) => [key, value ?? '--'])\r\n      );\r\n    },\r\n    handleTabSign(item) {\r\n      this.queryTypeSign = item.code;\r\n      this.getQyDfData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getRgData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getQYYTData({ // 签约业态分布\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n    },\r\n    fetchAnalyse(projectCode) { // 货值分析-货值分布表\r\n      API.Value.analyse(projectCode).then(res => {\r\n        this.analysisData = res.data.map(item => ({\r\n          projectType: item.projectType || '--',\r\n          hxName: item.hxName || '--',\r\n          totalNum: item.totalNum === null ? '--' : item.totalNum,\r\n          ysNum: item.ysNum === null ? '--' : item.ysNum,\r\n          wsNum: item.wsNum === null ? '--' : item.wsNum,\r\n          ygwsNum: item.ygwsNum === null ? '--' : item.ygwsNum,\r\n          wgNum: item.wgNum === null ? '--' : item.wgNum,\r\n          allTopNum: item.allTopNum === null ? '--' : item.allTopNum,\r\n          allBottomNum: item.allBottomNum === null ? '--' : item.allBottomNum,\r\n          ysTopNum: item.ysTopNum === null ? '--' : item.ysTopNum,\r\n          ysBottomNum: item.ysBottomNum === null ? '--' : item.ysBottomNum,\r\n          wsTopNum: item.wsTopNum === null ? '--' : item.wsTopNum,\r\n          wsBottomNum: item.wsBottomNum === null ? '--' : item.wsBottomNum,\r\n          allHzAmount: item.allHzAmount === null ? '--' : item.allHzAmount,\r\n          ysHzAmount: item.ysHzAmount === null ? '--' : item.ysHzAmount,\r\n          wsHzAmount: item.wsHzAmount === null ? '--' : item.wsHzAmount,\r\n        }));\r\n      })\r\n    },\r\n    async getkqData(data) { // 款齐\r\n      this.loadingPayments = true;\r\n      try {\r\n        const res = await API.Sales.kqData(data);\r\n        this.kqData = res.data;\r\n        this.cardData[0].value = this.kqData.kqTargetAmount;\r\n        this.cardData[1].value = this.kqData.yearKqAmount;\r\n        this.cardData[2].value = this.kqData.kjlKqAmount;\r\n      } finally {\r\n        this.loadingPayments = false;\r\n      }\r\n    },\r\n\r\n    handleHKTab(item) {\r\n      this.hkProjectType = item.code;\r\n      this.getHkTrend();\r\n    },\r\n    handleRgTab(item) {\r\n      this.rgProjectType = item.code;\r\n      this.getRgTrend();\r\n    },\r\n    handleQyTab(item) {\r\n      this.qyProjectType = item.code;\r\n      this.getQyTrend();\r\n    },\r\n    async getProjectType(projectCode) { // 查询业态列表\r\n      const res = await API.Common.getProjectType(projectCode);\r\n      this.projectTypeList = res.data;\r\n      this.hasData = this.projectTypeList.length > 0;\r\n      this.qyProjectType = this.rgProjectType = this.hkProjectType\r\n        = this.projectTypeList[0].code;\r\n    },\r\n    async initTrend() {\r\n      this.loadingTrends = true;\r\n      try {\r\n        await Promise.all([\r\n          this.getQyTrend(),\r\n          this.getRgTrend(),\r\n          this.getDfTrend(),\r\n          this.getHkTrend()\r\n        ]);\r\n      } finally {\r\n        this.loadingTrends = false;\r\n      }\r\n    },\r\n    getDfOption() { // 到访趋势图数据\r\n      this.dfOption = this.getBarOption(['#53B997'], this.dfTrendData, 'df')\r\n      // this.dfOption.yAxis[1].name = '单位：人次';\r\n      this.dfOptionName = '单位：人次';\r\n      this.dfOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getDfTrend() { // 销售页签 - 到访趋势\r\n      const res = await API.Sales.dfTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.dfTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.dfTrendData.series = res.data.map(item => item.value);\r\n      this.getDfOption();\r\n    },\r\n    async getRgTrend() { // 销售页签 - 认购趋势\r\n      const res = await API.Sales.rgTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.rgProjectType\r\n      });\r\n      this.rgTrendRawData = res.data;\r\n      this.rgTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.rgTrendData.series = res.data.map(item => item.value);\r\n      this.updateRGTrendData();\r\n    },\r\n    async getQyTrend() { // 销售页签 - 签约趋势\r\n      const res = await API.Sales.qyTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.qyProjectType\r\n      });\r\n      this.qyTrendRawData = res.data;\r\n      this.qyTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.qyTrendData.series = res.data.map(item => item.value);\r\n      this.updateQYTrendData();\r\n    },\r\n    getHkOption() {\r\n      this.hkOption = this.getBarOption(['#9D7BFF'], this.hkTrendData);\r\n      if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n        // this.hkOption.yAxis[1].name = '单位：万元';\r\n        this.hkOptionName = '单位：万元'\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 10000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 10000).toFixed(2);\r\n        }\r\n      } else {\r\n        // this.hkOption.yAxis[1].name = '单位：亿元';\r\n        this.hkOptionName = '单位：万元';\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 100000000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 100000000).toFixed(2);\r\n        }\r\n\r\n      }\r\n      this.hkOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    async getHkTrend() { //销售页签 - 回款趋势\r\n      const res = await API.Sales.hkTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.hkProjectType\r\n      });\r\n\r\n      this.hkTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.hkTrendData.series = res.data.map(item => item.value);\r\n      this.getHkOption();\r\n    },\r\n    getPieOption(color = ['#376DF7', '#53B997', '#6750AA', '#F8C541']) {\r\n      const pieOption = new PieOption();\r\n      pieOption.setColor(color);\r\n      return pieOption.getOption();\r\n    },\r\n    getBarOption(color = ['#53B997'], data = {xAxis: [], series: []}, type = '') {\r\n      const barOption = new BarOption();\r\n      barOption.updateData(data.xAxis, data.series);\r\n      barOption.setColor(color);\r\n      const option = barOption.getOption();\r\n      if (!Array.isArray(option.yAxis)) {\r\n        option.yAxis = [{}];\r\n      }\r\n      return option;\r\n    },\r\n    handleSpanMethod({row, column, rowIndex, columnIndex}) {\r\n      // 处理最后一行\r\n      if (rowIndex === this.analysisData.length - 1) {\r\n        if (columnIndex === 0) {  // 第一列\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2  // 合并两列\r\n          };\r\n        }\r\n        if (columnIndex === 1) {  // 第二列\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0  // 隐藏第二列\r\n          };\r\n        }\r\n      }\r\n\r\n      // 处理其他行的第一列合并\r\n      if (columnIndex === 0) {\r\n        const projectType = row.projectType;\r\n\r\n        // 向上查找相同业态的起始位置\r\n        let startIndex = rowIndex;\r\n        while (startIndex > 0 && this.analysisData[startIndex - 1].projectType === projectType) {\r\n          startIndex--;\r\n        }\r\n\r\n        // 计算相同业态的行数\r\n        let spanCount = 0;\r\n        for (let i = startIndex; i < this.analysisData.length; i++) {\r\n          if (this.analysisData[i].projectType === projectType) {\r\n            spanCount++;\r\n          } else {\r\n            break;\r\n          }\r\n        }\r\n\r\n        // 只在每组的第一行显示，其他行隐藏\r\n        if (rowIndex === startIndex) {\r\n          return {\r\n            rowspan: spanCount,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    qyProgressOption() {\r\n      const data = this.allQyData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#1433CC');\r\n        progressCircleOption.setBackgroundStyle('#376DF7');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#376DF7');\r\n        progressCircleOption.setBackgroundStyle('#C2CEF8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${this.$toFixed2(data.rate)}%`,\r\n        textStyle: {\r\n          color: '#376DF7',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计签约: ${data.qyAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    hkProgressOption() {\r\n      const data = this.allHkData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#e56d16');\r\n        progressCircleOption.setBackgroundStyle('#FF974C');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#FF974C');\r\n        progressCircleOption.setBackgroundStyle('#F9DEC8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      // option.title[0].subtext = '进度';\r\n      // option.title[0].text = `${data.rate}%`;\r\n      // option.title[0].textStyle.color = '#FF974C';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${data.rate}%`,\r\n        textStyle: {\r\n          color: '#FF974C',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计回款: ${data.hkAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">权益比例: ${data.qyRate}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    kqProgressOption() {\r\n      const data = this.kqData.kqRatio;\r\n      let text = '--';\r\n      if (!(data === null || data === undefined)) {\r\n        text = data;\r\n      }\r\n\r\n      const progressCircleOption = new ProgressCircle();\r\n      progressCircleOption.setBarData([(data % 100).toFixed(2) || '--']);\r\n\r\n      if (data > 100) {\r\n        progressCircleOption.setBarItemStyle('#6A3DC4');\r\n        progressCircleOption.setBackgroundStyle('#7B6FF2');\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#7B6FF2');\r\n        progressCircleOption.setBackgroundStyle('#E2E0FB');\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      // option.title[0].subtext = '';\r\n      // option.title[0].text = `${text}%`;\r\n      // option.title[0].textStyle.color = '#7B6FF2';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '',\r\n        text: `${text}%`,\r\n        textStyle: {\r\n          color: '#7B6FF2',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(16 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: false,\r\n        position: 'right',\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐目标金额: ${this.kqData.kqTargetAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">可结利款齐金额: ${this.kqData.kjlKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">年度款齐: ${this.kqData.yearKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐百分比: ${this.$formatNull(this.kqData.kqRatio)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    qyYTOption() { // 签约业态分布chart option\r\n      return this.getQYYTOption(this.qyYTData);\r\n      /* const option = this.getPieOption();\r\n      const scale = window.innerWidth / 1680;\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        minTurnAngle: 90,\r\n        normal: {\r\n          length: 12 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.series[0].data = this.qyYTData.map(item => ({\r\n        value: item.rate,\r\n        name: item.bussName,\r\n        ...item\r\n      }));\r\n\r\n      option.tooltip = {\r\n        show: true,\r\n        padding: [0, 0],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: 0.875rem; line-height: 1.2;padding: 0.3rem 0.75rem;border-radius: 0.1rem;\">\r\n            <div>名称: ${params.data.bussName}</div>\r\n            <div>套数: ${params.data.num}</div>\r\n            <div>面积: ${params.data.area}</div>\r\n            <div>金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div>比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: this.qyYTData.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle',\r\n      };\r\n\r\n      option.series[0].label = {\r\n        show: true,\r\n        position: 'outside',\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n\r\n      return option; */\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import './sales.scss';\r\n</style>\r\n"]}]}