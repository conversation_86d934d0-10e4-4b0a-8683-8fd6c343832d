import request from '@/utils/request'

// 查询计划反馈列表
export function listFeedback(query) {
  return request({
    url: '/plan/feedback/feedbackNodeList',
    method: 'get',
    params: query
  })
}

// 查询计划反馈详细
export function getFeedback(planId, id) {
  return request({
    url: '/plan/feedback/' + planId + '/' + id,
    method: 'get'
  })
}

// 计划反馈-过程反馈提交接口
export function progressFeedback(data) {
  return request({
    url: '/plan/feedback/feedback',
    method: 'post',
    data: data
  })
}

// 计划反馈-完成反馈提交接口
export function finishFeedback(data) {
  return request({
    url: '/plan/feedback/finishFeedback',
    method: 'post',
    data: data
  })
}

// 计划反馈-流程发起
export function flowCreate(data) {
  return request({
    url: '/plan/feedback/flowCreate',
    method: 'post',
    data: data
  })
}


// 修改计划反馈
/*export function updateFeedback(data) {
  return request({
    url: '/plan/feedback',
    method: 'put',
    data: data
  })
}*/

// 删除计划反馈
export function delFeedback(id) {
  return request({
    url: '/plan/feedback/' + id,
    method: 'delete'
  })
}

// 查询最新版本计划编制列表
export function latestVersionList() {
  return request({
    url: '/plan/info/latestVersionList/',
    method: 'get'
  })
}

// 获取审批流
export function getApproveFlow (id) {
  return request({
    url: '/plan/feedback/approvalForm/' + id,
    method: 'get'
  })
}

// 刷新状态
export function getPlanStatus (id) {
  return request({
    url: '/plan/feedback/getPlanStatus/' + id,
    method: 'get'
  })
}

export function allPlanName(params){ // 查询计划名称
  return request({
    url: '/plan/feedback/allPlanName',
    method: 'get',
    params
  })
}
