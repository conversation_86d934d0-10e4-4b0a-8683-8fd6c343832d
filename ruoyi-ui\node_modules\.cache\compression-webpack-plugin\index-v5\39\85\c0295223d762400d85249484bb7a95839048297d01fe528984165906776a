
0137cb3339eafcc2d7f450c019ea89f36df9cb99	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"df15f4c3aa8a8850e3e75eba0b549090\"}","integrity":"sha512-G78f6O5Oq2A7dJafvOEl1c2+bHE7FCCvzLWzngJuccMVaNqcI/5AP+3xRY45GEPz0PLWkuDTxYbgXizFds5fAA==","time":1754311469340,"size":12045269}