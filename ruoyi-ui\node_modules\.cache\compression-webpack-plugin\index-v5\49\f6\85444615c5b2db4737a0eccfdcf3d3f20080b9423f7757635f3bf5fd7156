
87d4ff7033b2f8366b56039950fef16c86645a7d	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.bb964b55a4dc75b345bd.hot-update.js\",\"contentHash\":\"fd2cb2444aeba86dc2fb44e4077119f3\"}","integrity":"sha512-hMgMMgTD+3U1e/0dmj3zdFfj8HlwldmcslsBxcPz/fEDy+62HyARycm83gUfTUkOoVe1MzKp+oMuAo9P+iG1ZA==","time":1754311453768,"size":78298}