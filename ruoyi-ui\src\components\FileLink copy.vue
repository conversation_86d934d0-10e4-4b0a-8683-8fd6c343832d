<template>
  <div class="file-link-container">
    <!-- 文件名链接 -->
    <span class="file-name" @click="handlePreview">
      <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
    </span>
    
    <!-- 下载图标 -->
    <i class="el-icon-download download-icon" @click="handleDownload" title="下载文件"></i>

    <!-- 文件预览弹窗 -->
    <el-dialog 
      :title="file.name" 
      :visible.sync="previewVisible" 
      width="90%" 
      top="5vh"
      :before-close="handleClosePreview"
      class="file-preview-dialog"
    >
      <div class="preview-container" v-loading="previewLoading">
        <!-- PDF 预览 -->
        <div v-if="currentFileType === 'pdf'" class="pdf-container">
          <iframe 
            :src="pdfViewerUrl" 
            width="100%" 
            height="600px" 
            frameborder="0"
            @load="previewLoading = false"
          ></iframe>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="isImageFile(currentFileType)" class="image-container">
          <img 
            :src="file.url" 
            alt="图片预览" 
            style="max-width: 100%; max-height: 600px; object-fit: contain;"
            @load="previewLoading = false"
            @error="handlePreviewError"
          />
        </div>

        <!-- Office 文档预览 -->
        <div v-else-if="isOfficeFile(currentFileType)" class="office-container">
          <iframe 
            :src="officeViewerUrl" 
            width="100%" 
            height="600px" 
            frameborder="0"
            @load="previewLoading = false"
            @error="handlePreviewError"
          ></iframe>
        </div>

        <!-- 文本文件预览 -->
        <div v-else-if="isTextFile(currentFileType)" class="text-container">
          <pre v-if="textContent" class="text-content">{{ textContent }}</pre>
          <div v-else class="loading-text">正在加载文本内容...</div>
        </div>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-container">
          <div class="unsupported-content">
            <i class="el-icon-document" style="font-size: 64px; color: #ccc;"></i>
            <p>此文件类型暂不支持预览</p>
            <el-button type="primary" @click="handleDownload">下载文件</el-button>
          </div>
        </div>

        <!-- 预览错误提示 -->
        <div v-if="previewError" class="error-container">
          <div class="error-content">
            <i class="el-icon-warning" style="font-size: 64px; color: #f56c6c;"></i>
            <p>文件预览失败</p>
            <p class="error-message">{{ previewError }}</p>
            <el-button type="primary" @click="handleDownload">下载文件</el-button>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDownload">
          <i class="el-icon-download"></i> 下载文件
        </el-button>
        <el-button @click="previewVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FileLink',
  props: {
    /**
     * 文件对象
     * @type {Object}
     * @property {String} name - 文件名
     * @property {String} url - 文件URL
     */
    file: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.name === 'string' && typeof value.url === 'string'
      }
    }
  },

  data() {
    return {
      previewVisible: false,
      previewLoading: false,
      previewError: '',
      currentFileType: '',
      textContent: '',
      pdfViewerUrl: '',
      officeViewerUrl: ''
    }
  },

  methods: {
    /**
     * 处理文件预览
     */
    handlePreview() {
      const fileType = this.getFileType(this.file.name)
      this.currentFileType = fileType
      this.previewError = ''
      this.previewLoading = true
      this.textContent = ''

      if (this.isPreviewableFile(fileType)) {
        this.previewVisible = true
        this.setupPreview(fileType)
      } else {
        // 不支持预览的文件直接下载
        this.handleDownload()
      }
    },

    /**
     * 设置预览内容
     */
    setupPreview(fileType) {
      try {
        if (fileType === 'pdf') {
          this.setupPdfPreview()
        } else if (this.isImageFile(fileType)) {
          this.previewLoading = false
        } else if (this.isOfficeFile(fileType)) {
          this.setupOfficePreview()
        } else if (this.isTextFile(fileType)) {
          this.setupTextPreview()
        } else {
          this.previewLoading = false
        }
      } catch (error) {
        this.handlePreviewError(error.message)
      }
    },

    /**
     * 设置 PDF 预览
     */
    setupPdfPreview() {
      // 使用浏览器内置的 PDF 查看器
      this.pdfViewerUrl = this.file.url
      
      // 也可以使用 PDF.js 查看器（如果项目中有引入）
      // this.pdfViewerUrl = `/pdfjs/web/viewer.html?file=${encodeURIComponent(this.file.url)}`
    },

    /**
     * 设置 Office 文档预览
     */
    setupOfficePreview() {
      // 使用 Microsoft Office Online 预览服务
      const encodedUrl = encodeURIComponent(this.file.url)
      this.officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`
      
      // 备选方案：使用 Google Docs 预览服务
      // this.officeViewerUrl = `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`
    },

    /**
     * 设置文本预览
     */
    async setupTextPreview() {
      try {
        const response = await fetch(this.file.url)
        if (!response.ok) {
          throw new Error('文件加载失败')
        }
        this.textContent = await response.text()
        this.previewLoading = false
      } catch (error) {
        this.handlePreviewError('文本文件加载失败')
      }
    },

    /**
     * 处理预览错误
     */
    handlePreviewError(error) {
      this.previewLoading = false
      this.previewError = error || '预览失败，请尝试下载文件'
    },

    /**
     * 关闭预览弹窗
     */
    handleClosePreview() {
      this.previewVisible = false
      this.previewError = ''
      this.textContent = ''
      this.pdfViewerUrl = ''
      this.officeViewerUrl = ''
    },

    /**
     * 处理文件下载
     */
    handleDownload() {
      try {
        // 创建临时下载链接
        const link = document.createElement('a')
        link.href = this.file.url
        link.download = this.file.name
        // 移除 target='_blank'，确保文件直接下载而不是在新标签页打开
        
        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // 触发下载事件
        this.$emit('download', {
          fileUrl: this.file.url,
          fileName: this.file.name
        })
      } catch (error) {
        this.$message.error('下载失败: ' + error.message)
      }
    },

    /**
     * 获取文件类型
     */
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      return extension
    },

    /**
     * 判断是否为可预览文件
     */
    isPreviewableFile(fileType) {
      const previewableTypes = [
        // 图片
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
        // 文档
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        // 文本
        'txt', 'md', 'json', 'xml', 'csv', 'log', 'js', 'css', 'html'
      ]
      return previewableTypes.includes(fileType)
    },

    /**
     * 判断是否为图片文件
     */
    isImageFile(fileType) {
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
      return imageTypes.includes(fileType)
    },

    /**
     * 判断是否为 Office 文档
     */
    isOfficeFile(fileType) {
      const officeTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
      return officeTypes.includes(fileType)
    },

    /**
     * 判断是否为文本文件
     */
    isTextFile(fileType) {
      const textTypes = ['txt', 'md', 'json', 'xml', 'csv', 'log', 'js', 'css', 'html', 'yml', 'yaml']
      return textTypes.includes(fileType)
    },

    /**
     * 获取截断的文件名（不包含扩展名）
     */
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName)
      return fileName.replace(extension, '')
    },

    /**
     * 获取文件扩展名
     */
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.')
      if (lastDotIndex === -1) return ''
      return fileName.substring(lastDotIndex)
    }
  }
}
</script>

<style lang="scss" scoped>
.file-link-container {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;
}

.file-name {
  color: #409EFF;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  min-width: 0;
  flex: 1;
  
  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
  
  .filename-part {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .extension-part {
    flex-shrink: 0;
    white-space: nowrap;
  }
}

.download-icon {
  color: #909399;
  cursor: pointer;
  font-size: 16px;
  flex-shrink: 0;
  padding: 2px;
  
  &:hover {
    color: #409EFF;
  }
}

.preview-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-container,
.office-container {
  width: 100%;
  height: 600px;
}

.image-container {
  text-align: center;
  width: 100%;
}

.text-container {
  width: 100%;
  height: 600px;
  overflow: auto;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  
  .text-content {
    padding: 20px;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }
  
  .loading-text {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
  }
}

.unsupported-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  
  .unsupported-content,
  .error-content {
    text-align: center;
    
    p {
      margin: 16px 0;
      color: #666;
      font-size: 16px;
    }
    
    .error-message {
      font-size: 14px;
      color: #999;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>

<style>
/* 全局样式：文件预览弹窗 */
.file-preview-dialog {
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__header {
    padding: 20px 20px 10px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}
</style> 