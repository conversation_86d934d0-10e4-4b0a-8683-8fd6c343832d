<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="年份" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第几周" prop="week">
        <el-input
          v-model="queryParams.week"
          placeholder="请输入周数"
          clearable
          size="small"
          type="number"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="第几周" align="center" prop="week"  />
      <el-table-column
        label="填报周期"
        align="center"
        prop="startDate"
      >
        <template slot-scope="scope">
          <span
            >{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}~{{
              parseTime(scope.row.endDate, "{y}-{m}-{d}")
            }}</span
          >
        </template>
      </el-table-column>
      <!-- 1=待阅,2=已阅 -->
      <el-table-column
        label="审阅状态"
        align="center"
        prop="pushType"
      >
      <!-- 审阅状态（1-待阅 2-已阅 3-已驳回 ） -->
        <template slot-scope="scope">
          <span>{{ scope.row.pushType === 0 ? "待阅" : "已阅" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审阅日期"
        align="center"
        prop="approveTime"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.nodeApprovalDate
              ? parseTime(scope.row.nodeApprovalDate, "{y}-{m}-{d}")
              : "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            >详情</el-button>
          <el-button
            v-if="scope.row.pushType === 0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleApprove(scope.row)"
            >审阅</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      :page-sizes="[20, 30, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
import WeeklyForm from "./components/weeklyForm.vue";
import {
  getInfo,
  listInfo,
  updateInfo,
  delInfo,
  addInfo,
  addInit,
  callbackWeekly,
  approveList,
  approveDaiBanList
} from "@/api/weekly/reportInfo.js";

export default {
  name: "Info",
  components: {
    WeeklyForm,
  },
  data() {
    return {
      addLoading: false,
      // 遮罩层
      // loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 周报信息主表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        year: null,
        week: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      loading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleApprove(row){
      this.$router.push({
        path: `/weekly/approveDaiBan/${row.id}`,
        query: { from: this.$route.path }
      });
    },
    handleDetail(row) {
      this.$router.push({
        path: `/weekly/approveDaiBanDetail/${row.id}`,
        query: { from: this.$route.path }
      });
    },
    handleInit() {
      this.addLoading = true;
      addInit()
        .then((response) => {
          if (response.code === 200) {
            const data = response.data;
            // 存储数据到 Vuex
            this.$store.dispatch("weekly/setWeeklyFormData", data);
            // 然后跳转
            this.$router.push({ path: "/weekly/weeklyInput" });
          } else if (response.code === 500) {
            this.$message.error(response.msg);
          }
        })
        .finally(() => {
          this.addLoading = false;
        });
    },
    /** 查询周报信息主列表 */
    getList() {
      this.loading = true;
      approveDaiBanList(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        year: null,
        week: null,
        userCode: null,
        nickName: null,
        deptId: null,
        postName: null,
        startDate: null,
        endDate: null,
        status: null,
        approveUserCode: null,
        approveUserName: null,
        approveTime: null,
        approveStatus: null,
        approveDesc: null,
        circulateUserIds: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      const queryData = { ...this.queryParams };

      approveDaiBanList(queryData).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      /*  this.open = true;
      this.title = "添加周报信息主"; */
      this.handleInit();
    },
    handleCallback(row) {
      console.log("row", row);
      this.loading = true
      callbackWeekly({id: row.id}).then((response) => {
        this.$modal.msgSuccess("撤回成功");
        this.getList();
      }).finally(() => {
        this.loading = false
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      /* getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改周报信息主";
      }); */
      this.$router.push({ path: "/weekly/weeklyInput", query: { id: id, type: 'update' } });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除周报信息主编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "plan/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
