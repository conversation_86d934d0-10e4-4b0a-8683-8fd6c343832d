<template>
  <div class="weekly-index">
    <div class="content">
<!--      <div
        class="content-item"
        v-for="(item, index) in list"
        :key="index"
        @click="handleClick(item.path, item.type)"
      >
        <img :src="item.image" :alt="item.title" class="content-item-image" />
        &lt;!&ndash; <div class="content-item-title">{{ item.title }}</div> &ndash;&gt;
      </div>-->

      <div class="portal">
        <div
          class="portal-item"
          v-for="(item, index) in portal"
          :key="index"
          @click="handleClick(item.path, item.type)"
        >
          <van-badge :content="approveTotal" class="portal-item-badge" v-if="item.type === 'approve' && approveTotal > 0">
            <img  :src="item.image" :alt="item.title" class="portal-item-image" />
          </van-badge>
            <img v-else :src="item.image" :alt="item.title" class="portal-item-image" />
        </div>
        <div
          v-hasPermi="['weekly:statictis']"
          class="portal-item"
          @click="handleClick(statictisItem.path, statictisItem.type)"
        >
          <img :src="statictisItem.image" :alt="statictisItem.title" class="portal-item-image" />
        </div>

      </div>

      <div class="list-container">
        <MyWeekly />
      </div>
      </div>
  </div>
</template>
<script>
//周报首页 http://localhost/wechatE/mobile/weekly
// 待办中心填报：http://localhost/wechatE/mobile/todoCenterweeklyInput?todoNoticeId=655
// 移动端待办中心审阅：http://localhost/wechatE/mobile/todoCenterweeklyApprove?username=yjchenxumin&year=2025&week=8
/* import weekly1 from "@/assets/weeklyMobile/weekly1.png";
import weekly2 from "@/assets/weeklyMobile/weekly2.png";
import weekly3 from "@/assets/weeklyMobile/weekly3.png"; */
import portalInput from "@/assets/weeklyMobile/protal-input.png";
import portalApprove from "@/assets/weeklyMobile/portal-approve.png";
import portalStatistics from "@/assets/weeklyMobile/portal-statistics.png";
import {addDraft, addInfo, addInit, getInfo, updateInfo, waitReviewNum} from "@/api/weekly/mobile-reportInfo";
import MyWeekly from "@/views/weeklyMobile/myWeekly";
import {Badge } from 'vant'
export default {
  name: "WeeklyIndex",
  components: {
    MyWeekly,
    [Badge.name]: Badge
  },
  data() {
    return {
      portal: [
        {
          title: "填报",
          image: portalInput,
          path: "/wechatE/mobile/weeklyInput",
          type: "add",
        },
        {
          title: "待审",
          image: portalApprove,
          path: "/wechatE/mobile/approveList",
          type: "approve",
        },
        /* {
          title: "统计",
          image: portalStatistics,
          path: "/wechatE/mobile/myWeeklyStatistics",
          type: "my",
        }, */
      ],
      statictisItem: {
        title: "统计",
        image: portalStatistics,
        path: "/wechatE/mobile/myWeeklyStatistics",
        type: "my",
      },
      /* list: [
        {
          title: "填写周报",
          image: weekly3,
          path: "/wechatE/mobile/weeklyInput",
          type: "add",
        },
        {
          title: "我的审阅",
          image: weekly2,
          path: "/wechatE/mobile/approveList",
          type: "approve",
        },
        {
          title: "我的周报",
          image: weekly1,
          path: "/wechatE/mobile/myWeekly",
          type: "my",
        },
      ], */
      approveTotal: 0,
    };
  },
  created() {
    this.fetchWaitReviewNum();
  },
  methods: {
    fetchWaitReviewNum() {
      waitReviewNum().then(res => {
        if(res.code === 200) {
          this.approveTotal = res.data;
        }
        else {
          this.approveTotal = 0;
        }
      })
    },
    handleClick(path, type) {
      if (type === "add") {
        this.handleInit();
      } else {
        this.$router.push(path);
      }
    },
    async handleInit() {
      try {
        const { code, msg } = await addInit();
        if (code === 200) {
          this.$router.push("/wechatE/mobile/weeklyInput");
        } else {
          this.$modal.msgError(msg);
        }
      } catch (error) {
      }
    },
  },
};
</script>
<style scoped lang="scss">
:deep(.my-weekly){
  background-image: unset;
  min-height: unset;
}
.weekly-index {
  width: 100%;
  height: 100vh;
  background-image: url("~@/assets/weeklyMobile/weeklybac.png");
  background-size: cover;
  background-repeat: no-repeat;

  /*.header {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 11px 0;
    margin-bottom: 60px;
  }*/
  .content {
    //padding: 71px 47px 0;
    .portal{
      display: flex;
      justify-content: space-evenly;
      padding: 16px 10px;
      //gap: 12px;
      .portal-item{
        //flex: 1;
        width: 104px;
        height: 88px;
        .portal-item-badge{
          //width: 100%;
          //height: 100%;
        }
        .portal-item-image{
          width: 100%;
          //height: 100%;
        }
      }
    }
    /*.content-item {
      .content-item-image {
        width: 100%;
        margin-bottom: 42px;
      }
    }*/
  }
}
.list-container{
  height: calc(100vh - 140px);
}
</style>
