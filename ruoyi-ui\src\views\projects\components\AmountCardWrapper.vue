<template>
  <div class="amount-card-wrapper" @click.stop="evtHandler">
    <div class="amount-card-line">
      <div class="amount-card-title">{{ cardData.projectName }}</div>
      <section class="amount-card-text">
        <img src="@/views/projects/assets/images/Success.png" alt="info" class="info-icon">
        <div class="">{{ $formatNull(cardData.takeLandDate) }}</div>
        <div class="">{{ '拿地' }}</div>
      </section>
    </div>
    <div class="amount-card-line">
      <div class="amount-card-text">
        <img src="@/views/projects/assets/images/Shopping-mall.png" alt="info" class="info-icon">
        {{ cardData.companyRegisterName }}
      </div>
      <section class="amount-card-text">
        <img src="@/views/projects/assets/images/Calendar.png" alt="info" class="info-icon">
        <div class="">{{ $formatNull(cardData.firstOpenDate) }}</div>
        <div class="">{{ '开盘' }}</div>
      </section>
    </div>
    <div class="amount-card-slot">
      <slot/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AmountCardWrapper',
  props: {
    cardData: {
      type: Object,
      required: true,
      /*validator: (value) => {
        return ['title', 'title2', 'time1', 'time2', 'operation1', 'operation2']
          .every(key => typeof value[key] === 'string' && value[key] !== '')
      }*/
    }
  },
  methods: {
    evtHandler(evt) {
      this.$emit('clickHandler', this.cardData)
    }
  }
}
</script>
<style scoped lang="scss">
.amount-card-wrapper {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  font-family: PingFang SC, PingFang SC;
  height: 100%;
  padding: 2rem; // 32px -> 2rem
  background: rgba(204, 234, 255, 0.4);
  border-radius: 0.3125rem; // 5px -> 0.3125rem
  border: 0.125rem solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(207, 222, 229, 1)) 1 1;

  .amount-card-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.6875rem; // 11px -> 0.6875rem
  }

  .amount-card-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 1rem; // 16px -> 1rem
    color: #333333;
    line-height: 1.1875rem; // 19px -> 1.1875rem
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .amount-card-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 0.875rem; // 14px -> 0.875rem
    color: #666666;
    line-height: 1rem; // 16px -> 1rem
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    gap: 0.5rem; // 8px -> 0.5rem
  }

  .amount-card-slot {
    display: flex;
    gap: 1rem; // 16px -> 1rem
  }

  .info-icon {
    width: 1rem; // 16px -> 1rem
    height: 1rem; // 16px -> 1rem
  }
}
</style>
</style>
