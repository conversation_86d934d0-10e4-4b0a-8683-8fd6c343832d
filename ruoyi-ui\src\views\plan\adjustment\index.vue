<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="城市公司" prop="deptNum">
        <el-select
          v-model="queryParams.deptNum"
          placeholder="请选择城市公司"
          clearable
          @change="handleQueryProject"
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict.deptNum"
            :label="dict.deptName"
            :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in storeProjectList"
            :key="dict.name"
            :label="dict.name"
            :value="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分期" prop="stageId" v-show="showAllSearch">
        <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                   clearable
                   filterable>
          <el-option
            v-for="dict in dict.type.stages"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标段" prop="lot" v-show="showAllSearch">
        <el-select v-model="queryParams.lot" placeholder="请选择分期"
                   clearable
                   filterable>
          <el-option
            v-for="dict in dict.type.project_lot"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划状态" prop="status" v-show="showAllSearch">
        <el-select v-model="queryParams.status" placeholder="请选择计划状态"
                   clearable
                   filterable>
          <el-option
            v-for="dict in dict.type.plan_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          size="mini"
          @click="toggleAllSearch"
        >更多筛选</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      class="table-fixed"
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
      ref="table"
      @row-click="showPlanAndNodeDialogFun"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" prop="id" width="30" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.plan_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="城市公司" align="center" prop="deptName" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
<!--          @click="$copyToClipboard(scope.row.deptName)"-->
          <span>{{ scope.row.deptName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
<!--          @click="$copyToClipboard(scope.row.projectName)"-->
          <span class="wrap-line">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分期" align="center" prop="stageId" width="55" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stages" :value="scope.row.stageId" />
        </template>
      </el-table-column>
      <el-table-column label="标段" align="center" prop="lot" width="90">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.project_lot" :value="scope.row.lot" />
        </template>
      </el-table-column>
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
      >
        <template slot-scope="scope">
<!--          @click="$copyToClipboard(scope.row.planName)"-->
          <el-link class="wrap-line" v-if="scope.row.status != 0" type="primary" @click.stop="fetchApprovalUser(scope.row)">{{ scope.row.planName || '无' }}</el-link>
          <span class="wrap-line" v-else>{{ scope.row.planName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="版本号"
        align="center"
        prop="version"
        width="80"
        show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.smallVersion == 0">V{{scope.row.version}}</span>
          <span v-if="scope.row.smallVersion != 0">V{{scope.row.version}}-{{scope.row.smallVersion}}</span>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(scope.row)"
            v-show="scope.row.status == 0 || scope.row.status == 5"
            >删除
          </el-button>
<!--          编辑详情，点击后要判断一下， 如果smallVersion 大于0 则进入人员调整页面，如果smallVersion = 0 则进入节点调整页面-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click.stop="showDialogFun(scope.row, scope.row.smallVersion > 0 ? 2 : 1)"
            v-if="scope.row.status === 0 || scope.row.status === 4 || scope.row.status === 5"
            >编辑
          </el-button>
            <el-button
              slot="reference"
              size="mini"
              type="text"
              icon="el-icon-video-play"
              v-if="scope.row.status === 0 || scope.row.status === 4  || scope.row.status === 5"
              @click.stop="openSortDialog(scope.row)"
              >提交
            </el-button>
<!--          调整类型 （1-节点调整  2-人员调整-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click.stop="showDialogFun(scope.row, 1)"
            v-if="scope.row.status === 2"
            >节点调整
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click.stop="showDialogFun(scope.row, 2)"
            v-if="scope.row.status === 2"
          >人员调整
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click.stop="showHistoryFun(scope.row)"
          >
            历史版本
          </el-button>
          <el-button
            v-if="scope.row.status == 1"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click.stop="refreshApproval(scope.row, scope.$index)"
            >刷新
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计划编制对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      v-if="open"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!--<el-form-item label="主责专业" prop="professionId">-->
          <!--<el-select v-model="form.professionId" placeholder="请选择主责专业">-->
            <!--<el-option-->
              <!--v-for="dict in dict.type.departs"-->
              <!--:key="dict.value"-->
              <!--:label="dict.label"-->
              <!--:value="dict.value"-->
            <!--&gt;</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <el-form-item label="城市公司" prop="deptNum">
          <el-select v-model="form.deptNum" placeholder="请选择城市公司">
            <el-option
              v-for="dict in cityCompanys"
              :key="dict.deptNum"
              :label="dict.deptName"
              :value="dict.deptNum"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择项目">
            <el-option
              v-for="dict in projectList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分期" prop="stageId">
          <el-select v-model="form.stageId" placeholder="请选择分期">
            <el-option
              v-for="dict in dict.type.stages"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划模板" prop="templateId">
          <el-select v-model="form.templateId" placeholder="请选择计划模板">
            <el-option
              v-for="template in allTemplateList"
              :key="template.id"
              :value="template.id"
              :label="template.templateName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="form.planName" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="计划说明" prop="planExplain">
          <el-input
            type="textarea"
            autosize
            v-model="form.planExplain"
            placeholder="请输入计划说明"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="计划明细编制"
      :visible.sync="showDialog"
      width="1200px"
      append-to-body
      v-if="showDialog"
    >
      <NodeInfo :planId="planId" @cancel="cancelDialog" />
    </el-dialog>
    <node-sort ref="nodeSortRef" @submit="submitApprovalHandle"></node-sort>
  </div>
</template>

<script>
import {
  addInfo,
  adjustmentListInfo,
  delInfo,
  getInfo,
  updateInfo,
  getPlanStatus,
  getApproveFlow
} from "@/api/plan/info"
import { listProject } from "@/api/plan/project"
import { adjustList, adjustSubmit } from "@/api/plan/node"
import HistoryVersion from '@/views/plan/components/HistoryVersion/index.vue'
import PlanAndNodeInfo from '@/views/plan/components/PlanAndNodeInfo/index.vue'
import NodeSort from "./nodeSort.vue"
import { mapState } from 'vuex';
import Template from "@/views/plan/template/index.vue";
import mixin from "@/mixins";
export default {
  mixins: [mixin],
  name: "Adjustment",
  dicts: ['departs', 'stages', 'plan_status', 'project_lot'],
  components: {
    Template,
    HistoryVersion,
    PlanAndNodeInfo,
    NodeSort
  },
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPlanNode: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划编制表格数据
      infoList: [],
      // 计划-节点表格数据
      planNodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        deptNum: null,
        projectId: null,
        stageId: null,
        status: null,
        planName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // professionId: [{
        //   required: true,
        //   message: "专业不能为空",
        //   trigger: "change"
        // }],
        deptNum: [{
          required: true,
          message: "城市公司不能为空",
          trigger: "change"
        }],
        projectId: [{
          required: true,
          message: "项目不能为空",
          trigger: "change"
        }],
        stageId: [{
          required: true,
          message: "分期不能为空",
          trigger: "change"
        }],
        planName: [{
          required: true,
          message: "计划名称不能为空",
          trigger: "blur"
        }],
        templateId: [{
          required: true,
          message: "计划模板不能为空",
          trigger: "change"
        }],
        version: [{
          required: true,
          message: "版本号不能为空",
          trigger: "blur"
        }],
      },
      projectList: [],
      showDialog: false,
      planId: null,
      showHistoryDialog: false,
      rowData: null,
      showPlanAndNodeDialog: false
    }
  },
  mounted () {
    this.getList()
    //获取部门列表
    this.$store.dispatch('plan/fetchDepartList')
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList')
    //事件总成监听
    this.$bus.$on('refreshAdjustList', () => {
      this.getList()
    })
  },
  beforeDestroy () {
    //收尾操作，销毁
    this.$bus.$off('refreshAdjustList') //$off解绑当前组件所用到的事件
  },
  watch: {
    'form.deptNum': {
      handler (newVal, oldVal,) {
        if (newVal && newVal != oldVal) {
          this.fetchProjectByCity(newVal)
        }
      }
    },
    'queryParams.deptNum': {
      handler (newVal, oldVal,) {
        if (newVal && newVal != oldVal) {
          this.fetchProjectByCity(newVal)
        }
      }
    }
  },
  computed: {
    cityCompanys () {
      return this.$store.state.plan.deptList
    },
    ...mapState('plan', {storeProjectList: 'projectList'})
  },
  methods: {
    handleQueryProject(value){
      this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
    },
    openSortDialog(row) {
      this.$refs.nodeSortRef.show(row.id)
    },
    submitApprovalHandle(palnId, nodeList){
      this.submitApproval(palnId, nodeList)
    },
    /*submitApprovalHandle(row){
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitApproval(row)
      }).catch(() => {
      });
    },*/
    //获取审批流
    fetchApprovalUser (row) {
      this.loading = true
      getApproveFlow(row.id).then(res => {
        this.loading = false
        window.open(res.data)
      }).catch(err => {
        this.loading = false
      })
    },
    // 刷新审批状态
    refreshApproval (row, index) {
      this.loading = true
      getPlanStatus(row.id).then(res => {
        this.loading = false
        this.infoList[index].status = res.data
        this.infoList = [...this.infoList]
      }).catch(err => {
        this.loading = false
      })
    },
    //提交审批
    async submitApproval (planId, nodeList) {
      this.loading = true
      adjustSubmit({
        id: planId,
        planNodeList: nodeList || []
      }).then(res => {
        this.$refs.nodeSortRef.setSubmitLoading(false)
        this.$refs.nodeSortRef.closeShowNode()

        this.loading = false
        this.$message.success("提交成功")
        this.getList()
        window.open(res.data)
      }).catch(err => {
        this.loading = false
        this.$refs.nodeSortRef.setSubmitLoading(false)
      })

    },
    showPlanAndNodeDialogFun (row) { // 计划查看
        this.$router.push({
          path: `/plan/planNodeInfo`,
          query: {
            planId: row.id
          }
        })
    },
    cancelDialog () {
      this.showDialog = false
      this.getList()
    },
    cancelHistoryDialog () {
      this.showHistoryDialog = false
    },
    showHistoryFun (row) {
      // this.showHistoryDialog = true
      // this.rowData = row
      this.$router.push({
        path: `/plan/historyVersion`,
        query: {
          planId: row.id,
          planCode: row.planCode,
          version: row.version
        }
      })
    },
    showDialogFun (row, type) {
      this.$router.push({
        path: `/plan/adjustNodeInfo`,
        query: {
          planId: row.id,
          planStatus: row.status,
          planName: row.planName,
          version: row.version,
          adjustType: type
        }
      })
    },

    // 获取城市公司下面的项目
    fetchProjectByCity (params) {
      // this.loading = true
      listProject({
        name: '',
        company: params
      }).then(res => {
        this.projectList = res.rows
        this.queryParams.projectId = null
        this.loading = false
      }).catch(err => {
        this.loading = false

      })
    },
    /** 查询计划编制列表 */
    getList () {
      this.loading = true
      adjustmentListInfo(this.queryParams).then(response => {
        this.infoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        deptNum: null,
        professionId: null, // 专业
        projectId: null, //项目
        stageId: null, //分期
        templateId: null,
        planName: null, //项目名称
        planExplain: ''
      }
      this.planNodeList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset()
      this.open = true
      this.title = "添加计划编制"
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset()
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data
        this.planNodeList = response.data.planNodeList
        this.open = true
        this.title = "修改计划编制"
      })
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          //this.form.planNodeList = this.planNodeList
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      this.$modal.confirm('是否确认删除计划名称为"' + row.planName + '"的数据项？').then(function () {
        return delInfo(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 复选框选中数据 */
    handlePlanNodeSelectionChange (selection) {
      this.checkedPlanNode = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('plan/info/adjustmentLatestExport', {
        ...this.queryParams
      }, `计划列表（调整）_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped lang="scss">
::v-deep .el-table .cell {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
</style>
