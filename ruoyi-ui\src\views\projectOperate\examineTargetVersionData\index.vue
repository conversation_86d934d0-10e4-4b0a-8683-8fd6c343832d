<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="城市公司" prop="cityId">
        <el-select
          v-model="queryParams.cityId"
          placeholder="请选择城市公司"
          @change="handleCitySearch"
          clearable
          class="width-100">
          <el-option
            v-for="item in cites"
            :key="item.cityId"
            :label="item.cityName"
            :value="item.cityId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目" prop="projectCode">
        <el-select
          v-model="queryParams.projectCode"
          placeholder="请选择项目"
          @change="handleProjectSearch"
          clearable
          class="width-100"
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.displayName"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:examineTargetVersionData:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['project:examineTargetVersionData:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:examineTargetVersionData:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['project:examineTargetVersionData:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="60">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="城市公司名称" align="center" prop="cityName" />
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="可研版版本" align="center" prop="kyVersion">
        <template slot-scope="scope">
          <!-- 版本类型(1-可研版,2-投决版,3-目标版,4-动态版,5-后评价版) -->
          <el-button size="mini" type="text" @click="openFillExamineData(scope.row.projectCode, 1, scope.row, scope.row.kyVersion)">
            V{{ scope.row.kyVersion}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="投决版版本" align="center" prop="tjVersion">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openFillExamineData(scope.row.projectCode, 2, scope.row, scope.row.tjVersion)">
            V{{ scope.row.tjVersion}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="目标版版本" align="center" prop="mbVersion">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openFillExamineData(scope.row.projectCode, 3, scope.row, scope.row.mbVersion)">
            V{{ scope.row.mbVersion}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="动态版版本" align="center" prop="dtVersion">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openFillExamineData(scope.row.projectCode, 4, scope.row, scope.row.dtVersion)">
            V{{ scope.row.dtVersion}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="局后评价版版本" align="center" prop="hpgVersion">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openFillExamineData(scope.row.projectCode, 5, scope.row, scope.row.hpgVersion)">
            V{{ scope.row.hpgVersion}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['project:examineTargetVersionData:edit']"-->
<!--          >修改</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:examineTargetVersionData:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改考核指标版本数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="城市公司" prop="cityId">
          <el-select
            v-model="form.cityId"
            placeholder="请选择城市公司"
            @change="handleCity"
            clearable
            class="width-100">
            <el-option
              v-for="item in cites"
              :key="item.cityId"
              :label="item.cityName"
              :value="item.cityId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目" prop="projectCode">
          <el-select
            v-model="form.projectCode"
            placeholder="请选择项目"
            @change="handleProject"
            clearable
            class="width-100"
          >
            <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.displayName"
              :value="item.projectCode"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 考核指标填报 -->
    <el-dialog :title="title" :visible.sync="fillExamineDataVisible" width="800px" append-to-body>
      <fill-examine-data
        ref="fillExamineDataRef"
        @close="fillExamineDataVisible = false"
      />
    </el-dialog>

  </div>
</template>

<script>
  import {getData, listData, addData, updateData, delData} from "@/api/projectOperate/examineTargetVersionData";
  import API from '@/views/projects/api';
  import FillExamineData from './components/FillExamineData.vue'

  export default {
  name: "ExamineTargetVersionData",
  components: {
    FillExamineData
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考核指标版本数据表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        cityId: null,
        cityName: null,
        projectCode: null,
        projectName: null,
        kyVersion: null,
        tjVersion: null,
        mbVersion: null,
        dtVersion: null,
        hpgVersion: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cityId: [
          { required: true, trigger: "change", message: "请选择城市公司" }
        ],
        projectCode: [
          { required: true, trigger: "change", message: "请选择项目" }
        ]
      },
      // 城市公司列表
      cites: [],
      // 项目列表
      projects: [],
      fillExamineDataVisible: false,
    };
  },
  created() {
    this.getList();
    this.getCities();
  },
  mounted() {
    // 添加事件总线监听
    this.$bus.$on('refresh-examine-target-list', this.getList)
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听，防止内存泄漏
    this.$bus.$off('refresh-examine-target-list', this.getList)
  },
  methods: {
    handleCity(val) {
      this.projects = null;
      this.form.projectCode = null;
      this.form.projectName = null;
      this.form.cityName = this.cites.find(item => item.cityId === this.form.cityId).cityName;
      !!this.form.cityId ? this.getProjects(this.form.cityId) : null;
    },
    handleCitySearch(val) {
      this.projects = null;
      this.queryParams.projectCode = null;
      !!this.queryParams.cityId ? this.getProjects(this.queryParams.cityId) : null;
      this.handleQuery();
    },
    handleProjectSearch(val) {
      this.handleQuery()
    },
    handleProject(val) {
      this.form.projectName = this.projects.find(item => item.projectCode === this.form.projectCode).displayName;
    },
    /** 查询考核指标版本数据列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getCities() {
      API.Common.getCity().then(res => {
        if (res.code === 200) {
          this.cites = res.data
        }
        else {
          this.$message.error(res.message || '获取城市公司信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取城市公司信息失败')
      })
    },
    getProjects(cityId) {
      API.Common.getProject(cityId).then(res => {
        if (res.code === 200) {
          this.projects = res.data
        }
        else {
          this.$message.error(res.message || '获取项目信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取项目信息失败')
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        cityId: null,
        cityName: null,
        projectCode: null,
        projectName: null,
        kyVersion: null,
        tjVersion: null,
        mbVersion: null,
        dtVersion: null,
        hpgVersion: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考核指标版本数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getData(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考核指标版本数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除考核指标版本数据编号为"' + ids + '"的数据项？').then(function() {
        return delData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('plan/data/export', {
        ...this.queryParams
      }, `data_${new Date().getTime()}.xlsx`)
    },
    openFillExamineData(projectCode, versionType, row, version) {
      this.title = '考核指标填报';
      this.fillExamineDataVisible = true;
      this.$nextTick(() => {
        this.$refs.fillExamineDataRef.openHandle(projectCode, versionType, row, version);
      });
    }
  }
};
</script>
