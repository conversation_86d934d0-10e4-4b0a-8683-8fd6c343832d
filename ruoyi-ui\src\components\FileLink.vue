<template>
  <div class="file-link-container">
    <!-- 文件名链接 -->
    <span class="file-name" @click="handlePreview">
      <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
    </span>
    
    <!-- 下载图标 -->
    <i class="el-icon-download download-icon" @click="handleDownload" title="下载文件"></i>

    <!-- 文件预览弹窗 -->
    <el-dialog 
      :title="file.name" 
      :visible.sync="previewVisible" 
      width="95%" 
      top="2vh"
      :before-close="handleClosePreview"
      class="file-preview-dialog"
      custom-class="officetohtml-dialog"
    >
      <div class="preview-container" v-loading="previewLoading">
        <!-- 图片预览 - 使用el-image -->
        <div v-if="isImageFile(getFileExtension(file.name).toLowerCase().replace('.', ''))" class="image-container">
          <el-image 
            :src="file.url" 
            :preview-src-list="[file.url]"
            fit="contain"
            style="max-width: 100%; max-height: 70vh;"
            @load="previewLoading = false"
            @error="handlePreviewError"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
              <p>图片加载失败</p>
            </div>
          </el-image>
        </div>
        
        <!-- PDF预览 - 使用iframe -->
        <div v-else-if="isPdfFile(file.name)" class="pdf-container">
          <iframe 
            :src="file.url" 
            width="100%" 
            height="700px"
            frameborder="0"
            @load="previewLoading = false"
            @error="handlePreviewError"
          ></iframe>
        </div>
        
        <!-- TXT文件预览 - 直接显示文本内容 -->
        <div v-else-if="isTxtFile(file.name)" class="txt-container">
          <pre class="txt-content" v-if="txtContent">{{ txtContent }}</pre>
          <div v-if="!txtContent && !previewLoading" class="txt-error">
            <i class="el-icon-document"></i>
            <p>文本内容加载失败</p>
          </div>
        </div>
        
        <!-- 其他文件预览 - 使用officetohtml.js -->
        <div v-else>
          <!-- officetohtml.js 预览容器 -->
          <div 
            :id="previewContainerId" 
            class="office-preview-container"
            v-show="!previewLoading"
          ></div>
          
          <!-- 加载提示 -->
          <div v-if="previewLoading" class="loading-container">
            <i class="el-icon-loading"></i>
            <p>正在加载文件预览，请稍候...</p>
          </div>
          
          <!-- 错误提示 -->
          <div v-if="previewError" class="error-container">
            <i class="el-icon-warning"></i>
            <p>{{ previewError }}</p>
            <el-button @click="handleRetryPreview" type="primary" size="small">重试</el-button>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDownload" type="primary" icon="el-icon-download">下载文件</el-button>
        <el-button @click="previewVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFile } from '@/api/rant/common'

export default {
  name: 'FileLink',
  props: {
    /**
     * 文件对象
     * @type {Object}
     * @property {String} name - 文件名
     * @property {String} url - 文件URL
     */
    file: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.name === 'string' && typeof value.url === 'string'
      }
    }
  },
  data() {
    return {
      previewVisible: false,
      previewLoading: false,
      previewError: null,
      txtContent: null,
      currentBlobUrl: null,
      previewContainerId: `office-preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  methods: {
    /**
     * 处理文件预览
     */
    handlePreview() {
      this.previewVisible = true
      this.previewLoading = true
      this.previewError = null
      this.txtContent = null // 清空之前的txt内容
      
      const fileExtension = this.getFileExtension(this.file.name).toLowerCase().replace('.', '')
      
      console.log('Preview Debug Info:', {
        fileName: this.file.name,
        fileExtension: fileExtension,
        isImage: this.isImageFile(fileExtension),
        isPdf: this.isPdfFile(this.file.name),
        isTxt: this.isTxtFile(this.file.name)
      })
      
      // 图片和PDF文件直接显示，不需要额外处理
      if (this.isImageFile(fileExtension) || this.isPdfFile(this.file.name)) {
        // 加载状态由相应的组件处理
        return
      }
      
      // TXT文件通过API获取内容并显示
      if (this.isTxtFile(this.file.name)) {
        this.loadTxtContent()
        return
      }
      
      // 其他文件使用officetohtml.js预览
      this.$nextTick(() => {
        this.initializeOfficePreview()
      })
    },

    /**
     * 初始化officetohtml.js预览（仅用于非图片、非PDF文件）
     */
    initializeOfficePreview() {
      console.log('开始初始化office文件预览')
      this.tryBlobFilePreview()
    },

    /**
     * 尝试直接使用文件URL预览
     */
    async tryDirectFilePreview() {
      try {
        // 确保jQuery和officeToHtml已加载
        if (typeof window.$ === 'undefined') {
          throw new Error('jQuery未加载')
        }
        
        if (typeof window.$.fn.officeToHtml === 'undefined') {
          throw new Error('officeToHtml插件未加载')
        }

        const fileType = this.getFileType(this.file.name)
        const $container = window.$(`#${this.previewContainerId}`)
        
        // 清空容器
        $container.empty()
        
        // 根据文件类型配置预览选项
        const previewOptions = this.getPreviewOptions(fileType)
        
        console.log('尝试直接预览文件:', this.file.name, '类型:', fileType)
        console.log('原始文件URL:', this.file.url)
        
        // 先测试文件是否可以直接访问
        const response = await fetch(this.file.url, { method: 'HEAD' })
        if (!response.ok) {
          throw new Error('文件不可直接访问')
        }
        
        // 直接使用原始文件URL进行预览
        $container.officeToHtml({
          url: this.file.url,
          ...previewOptions
        })
        
        this.previewLoading = false
        
        // 触发预览事件
        this.$emit('preview', {
          fileUrl: this.file.url,
          fileName: this.file.name,
          fileType: fileType
        })
        
      } catch (error) {
        console.error('直接文件预览失败:', error)
        throw error // 重新抛出错误，让调用者处理
      }
    },

    /**
     * 通过API获取文件流创建blob URL预览
     */
    async tryBlobFilePreview() {
      try {
        // 确保jQuery和officeToHtml已加载
        if (typeof window.$ === 'undefined') {
          throw new Error('jQuery未加载')
        }
        
        if (typeof window.$.fn.officeToHtml === 'undefined') {
          throw new Error('officeToHtml插件未加载')
        }

        console.log('通过API获取文件流进行预览:', this.file.name)
        
        // 通过 API 获取文件流
        const response = await getFile(this.file.url)
        console.log('文件流获取成功，大小:', response.size || response.length || 'unknown')
        
        // 获取文件扩展名
        const fileExtension = this.file.name.split('.').pop().toLowerCase()
        console.log('文件扩展名:', fileExtension)
        
        // 创建 Blob 对象
        const mimeType = this.getMimeType(this.file.name)
        const blob = new Blob([response], { type: mimeType })
        
        // 创建临时 URL
        const blobUrl = window.URL.createObjectURL(blob)
        console.log('创建的blob URL:', blobUrl)
        
        const $container = window.$(`#${this.previewContainerId}`)
        
        // 清空容器
        $container.empty()
        
        // 关键修复：直接调用officetohtml.js的内部方法，手动传递文件信息
        // 创建文件对象，确保扩展名正确传递
        const fileObject = {
          Obj: blobUrl,
          ext: fileExtension
        }
        
        console.log('传递给officetohtml.js的文件对象:', fileObject)
        
        // 根据文件类型配置预览选项
        const fileType = this.getFileType(this.file.name)
        const previewOptions = this.getPreviewOptions(fileType)
        
        // 关键修复：临时修改URL处理，让officetohtml.js正确识别blob URL的文件类型
        
        // 保存原始的字符串split方法
        const originalSplit = String.prototype.split
        
        // 临时重写split方法，当officetohtml.js尝试从URL提取扩展名时返回正确的值
        String.prototype.split = function(separator, limit) {
          // 如果是blob URL并且正在尝试提取扩展名
          if (this.startsWith('blob:') && separator === '.') {
            console.log('拦截blob URL扩展名提取，返回正确的文件扩展名:', fileExtension)
            // 返回一个模拟的数组，最后一个元素是正确的文件扩展名
            return ['blob://fake', fileExtension]
          }
          // 否则使用原始的split方法
          return originalSplit.call(this, separator, limit)
        }
        
        try {
          console.log('开始预览，文件扩展名:', fileExtension)
          
          // 使用officeToHtml进行预览
          $container.officeToHtml({
            url: blobUrl,
            ...previewOptions
          })
          
        } catch (error) {
          console.error('预览初始化失败:', error)
          this.previewError = '预览初始化失败: ' + error.message
        } finally {
          // 恢复原始的split方法
          setTimeout(() => {
            String.prototype.split = originalSplit
            console.log('已恢复原始的split方法')
          }, 1000)
        }
        
        this.previewLoading = false
        
        // 存储blob URL以便后续清理
        this.currentBlobUrl = blobUrl
        
        // 检查预览是否成功
        setTimeout(() => {
          const containerContent = $container.html()
          if (containerContent.includes('The file is not supported!') || containerContent.includes('unknown_files')) {
            this.previewError = `文件类型 "${fileExtension}" 预览失败。这可能是因为blob URL无法被正确识别。您仍可以下载文件。`
            console.warn('officetohtml.js无法识别blob URL文件类型:', fileExtension)
          } else if (!containerContent || containerContent.trim() === '') {
            this.previewError = '预览加载失败，可能是文件格式不支持或文件损坏'
            console.warn('预览容器为空')
          } else {
            console.log('预览加载成功')
          }
        }, 3000)
        
        // 触发预览事件
        this.$emit('preview', {
          fileUrl: blobUrl,
          fileName: this.file.name,
          fileType: fileType,
          fileExtension: fileExtension
        })
        
      } catch (error) {
        console.error('blob文件预览失败:', error)
        this.previewLoading = false
        this.previewError = '预览失败: ' + (error.message || '网络错误或文件无法访问')
      }
    },

    /**
     * 获取预览选项配置
     */
    getPreviewOptions(fileType) {
      const baseOptions = {}
      
      switch (fileType) {
        case 'pdf':
          return {
            ...baseOptions,
            pdfSetting: {
              thumbnailViewBtn: true,
              searchBtn: true,
              nextPreviousBtn: true,
              pageNumberTxt: true,
              totalPagesLabel: true,
              zoomBtns: true,
              scaleSelector: true,
              printBtn: true,
              downloadBtn: true
            }
          }
        
        case 'document':
          return {
            ...baseOptions,
            docxSetting: {
              includeEmbeddedStyleMap: true,
              includeDefaultStyleMap: true,
              ignoreEmptyParagraphs: false,
              isRtl: "auto"
            }
          }
        
        case 'presentation':
          return {
            ...baseOptions,
            pptxSetting: {
              slidesScale: "80%",
              slideMode: true,
              slideType: "divs2slidesjs",
              keyBoardShortCut: true,
              mediaProcess: true,
              slideModeConfig: {
                first: 1,
                nav: true,
                navTxtColor: "black",
                keyBoardShortCut: true,
                showSlideNum: true,
                showTotalSlideNum: true,
                autoSlide: false,
                loop: false,
                background: false,
                transition: "default",
                transitionTime: 1
              }
            }
          }
        
        case 'spreadsheet':
          return {
            ...baseOptions,
            sheetSetting: {
              allowEmpty: true,
              autoColumnSize: true,
              columnSorting: true,
              contextMenu: true,
              copyable: true,
              readOnly: true,
              rowHeaders: true,
              colHeaders: true,
              width: '100%',
              height: 600
            }
          }
        
        case 'image':
          return {
            ...baseOptions,
            imageSetting: {
              frame: ['100%', '100%', false],
              maxZoom: '900%',
              zoomFactor: '10%',
              mouse: true,
              keyboard: true,
              toolbar: true,
              rotateToolbar: false
            }
          }
        
        default:
          return baseOptions
      }
    },

    /**
     * 重试预览
     */
    handleRetryPreview() {
      this.previewError = null
      this.previewLoading = true
      this.initializeOfficePreview()
    },

    /**
     * 处理文件下载
     */
    handleDownload() {
      // 显示下载加载状态
      const loading = this.$loading({
        text: '正在下载文件，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 通过 API 获取文件流
      getFile(this.file.url).then(response => {
        console.log('File response received for download')
        
        // 创建 Blob 对象
        const blob = new Blob([response], {
          type: 'application/octet-stream'
        })
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = this.file.name
        
        // 触发下载
        document.body.appendChild(link)
        link.click()
        
        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        // 触发下载事件
        this.$emit('download', {
          fileUrl: this.file.url,
          fileName: this.file.name
        })
        
        loading.close()
        this.$message.success('文件下载成功')
      }).catch(error => {
        console.error('下载失败:', error)
        loading.close()
        this.$message.error('下载失败: ' + (error.message || '网络错误'))
      })
    },

    /**
     * 关闭预览弹窗
     */
    handleClosePreview() {
      this.previewVisible = false
      this.previewError = null
      this.txtContent = null // 清空txt内容
      
      // 清空预览容器
      if (typeof window.$ !== 'undefined') {
        window.$(`#${this.previewContainerId}`).empty()
      }
      
      // 清理blob URL
      if (this.currentBlobUrl) {
        window.URL.revokeObjectURL(this.currentBlobUrl)
        this.currentBlobUrl = null
      }
    },

    /**
     * 获取文件类型
     */
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const typeMap = {
        // 图片
        jpg: 'image', jpeg: 'image', png: 'image', gif: 'image', bmp: 'image', webp: 'image', svg: 'image',
        // 文档
        pdf: 'pdf',
        doc: 'document', docx: 'document',
        xls: 'spreadsheet', xlsx: 'spreadsheet',
        ppt: 'presentation', pptx: 'presentation',
        // 文本
        txt: 'text', md: 'text', json: 'text', xml: 'text', csv: 'text'
      }
      return typeMap[extension] || 'unknown'
    },

    /**
     * 获取截断的文件名（不包含扩展名）
     */
    getTruncatedFileName(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
    },

    /**
     * 获取文件扩展名（包含点号）
     */
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ''
    },

    /**
     * 获取文件的 MIME 类型
     */
    getMimeType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const mimeTypeMap = {
        // 图片
        jpg: 'image/jpeg', jpeg: 'image/jpeg', png: 'image/png', gif: 'image/gif', bmp: 'image/bmp', webp: 'image/webp', svg: 'image/svg+xml',
        // 文档
        pdf: 'application/pdf',
        doc: 'application/msword', docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xls: 'application/vnd.ms-excel', xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ppt: 'application/vnd.ms-powerpoint', pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        // 文本
        txt: 'text/plain', md: 'text/markdown', json: 'application/json', xml: 'application/xml', csv: 'text/csv'
      }
      return mimeTypeMap[extension] || 'application/octet-stream'
    },

    /**
     * 判断是否为图片文件
     */
    isImageFile(extension) {
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
      return imageTypes.includes(extension)
    },

    /**
     * 判断是否为PDF文件
     */
    isPdfFile(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      return extension === 'pdf'
    },

    /**
     * 判断是否为TXT文件
     */
    isTxtFile(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      return extension === 'txt'
    },

    /**
     * 处理预览错误
     */
    handlePreviewError(error) {
      this.previewLoading = false
      this.previewError = error || '预览失败，请尝试下载文件'
    },

    /**
     * 加载TXT文件内容
     */
    async loadTxtContent() {
      try {
        console.log('开始加载TXT文件内容:', this.file.name)
        
        // 通过API获取文件流
        const response = await getFile(this.file.url)
        console.log('TXT文件流获取成功')
        
        // 将响应转换为文本
        let textContent = ''
        
        if (response instanceof ArrayBuffer) {
          // 如果是ArrayBuffer，使用TextDecoder解码
          const decoder = new TextDecoder('utf-8')
          textContent = decoder.decode(response)
        } else if (response instanceof Blob) {
          // 如果是Blob，使用text()方法
          textContent = await response.text()
        } else if (typeof response === 'string') {
          // 如果已经是字符串
          textContent = response
        } else {
          // 尝试将其他类型转换为字符串
          textContent = String(response)
        }
        
        console.log('TXT文件内容解析成功，长度:', textContent.length)
        
        // 检查文件是否为空
        if (!textContent || textContent.trim() === '') {
          this.txtContent = '(文件为空)'
        } else {
          // 限制显示长度，避免过大文件影响性能
          const maxLength = 50000 // 最大显示5万字符
          if (textContent.length > maxLength) {
            this.txtContent = textContent.substring(0, maxLength) + '\n\n... (文件内容过长，仅显示前' + maxLength + '个字符，请下载完整文件查看)'
          } else {
            this.txtContent = textContent
          }
        }
        
        this.previewLoading = false
        
        // 触发预览事件
        this.$emit('preview', {
          fileUrl: this.file.url,
          fileName: this.file.name,
          fileType: 'txt',
          contentLength: textContent.length
        })
        
      } catch (error) {
        console.error('TXT文件内容加载失败:', error)
        this.previewLoading = false
        this.previewError = 'TXT文件加载失败: ' + (error.message || '网络错误或文件无法访问')
        this.txtContent = null
      }
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    // 清空预览容器
    if (typeof window.$ !== 'undefined') {
      window.$(`#${this.previewContainerId}`).empty()
    }
    
    // 清理blob URL
    if (this.currentBlobUrl) {
      window.URL.revokeObjectURL(this.currentBlobUrl)
      this.currentBlobUrl = null
    }
    
    // 清理txt内容
    this.txtContent = null
  }
}
</script>

<style scoped>
.file-link-container {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
  display: flex;
  align-items: center;
  max-width: 200px;
}

.file-name:hover {
  text-decoration: underline;
}

.filename-part {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.extension-part {
  flex-shrink: 0;
}

.download-icon {
  color: #909399;
  cursor: pointer;
  font-size: 16px;
  transition: color 0.3s;
}

.download-icon:hover {
  color: #409eff;
}

.preview-container {
  min-height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  position: relative;
}

.office-preview-container {
  width: 100%;
  min-height: 500px;
  background: #fff;
}

.image-container {
  text-align: center;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
}

.pdf-container {
  text-align: center;
  width: 100%;
  min-height: 700px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.pdf-container iframe {
  width: 100%;
  height: 700px;
  border: none;
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  font-size: 14px;
}

.image-slot i {
  font-size: 48px;
  margin-bottom: 12px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.loading-container i {
  font-size: 32px;
  margin-bottom: 16px;
  color: #409eff;
}

.error-container i {
  font-size: 32px;
  margin-bottom: 16px;
  color: #f56c6c;
}

.loading-container p, .error-container p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.txt-container {
  width: 100%;
  min-height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.txt-content {
  width: 100%;
  height: 700px;
  padding: 20px;
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #fff;
  border: none;
  border-radius: 4px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  resize: none;
}

.txt-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
  font-size: 14px;
}

.txt-error i {
  font-size: 48px;
  margin-bottom: 12px;
}
</style>

<style>
/* 全局样式：针对 officetohtml.js 生成的内容 */
.officetohtml-dialog .el-dialog__body {
  padding: 20px;
  max-height: 80vh;
  overflow: auto;
}

/* PDF 预览样式优化 */
.officetohtml-dialog .pdfViewer {
  max-height: 70vh;
  overflow: auto;
}

/* DOCX 预览样式优化 */
.officetohtml-dialog .docx-preview {
  max-height: 70vh;
  overflow: auto;
  padding: 20px;
  background: #fff;
}

/* PPTX 预览样式优化 */
.officetohtml-dialog .pptx-preview {
  max-height: 70vh;
  overflow: auto;
}

/* Excel 预览样式优化 */
.officetohtml-dialog .handsontable {
  max-height: 60vh;
}

/* 图片预览样式优化 */
.officetohtml-dialog .image-preview {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

.officetohtml-dialog .image-preview img {
  max-width: 100%;
  height: auto;
}
</style> 