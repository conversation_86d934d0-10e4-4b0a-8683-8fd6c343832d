import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const MobileTokenKey = 'Admin-Token-Mobile'
const RantMobileTokenKey = 'Admin-Token-Mobile-Rant'

const ExpiresInKey = 'Admin-Expires-In'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function getMobileToken() { // 周报移动端token
  return Cookies.get(MobileTokenKey)
}

export function getRantMobileToken() { // 督办移动端token
  return Cookies.get(RantMobileTokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function setMobileToken(token) {
  return Cookies.set(MobileTokenKey, token)
}

export function setRantMobileToken(token) {
  return Cookies.set(RantMobileTokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}

// 通过oaut2链接获取code
export function redirectToOauth2Url(){
  const corpId = process.env.VUE_APP_WX_CORP_ID;
  const redirectUri = window.location.href;
  let authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=SCOPE&state=STATE#wechat_redirect`;
  window.location.href = authUrl;
}

export function getAuthType(to){
  var code = to.query.code;
  // 1-企业微信应用  2-企业微信待办中小程序  3-中建通应用 4-中建通待办中心小程序
  var type = 1;
  if (to.query.qy_code) {
    code = to.query.qy_code
    type = 2
  }
  if (to.query.type != null && to.query.type === "3") {
    type = 3
  }
  var openSource = to.query.open_source;
  if (openSource && openSource === "zjtapp") { // 中建通
    type = 4
  }
  return {type, code}
}

export function getAuthTypeOfMobile(){
  const urlParams = new URLSearchParams(window.location.search)
  const codeParam = urlParams.get('code')
  let type = 1;
  let code = codeParam;
  const qy_code = urlParams.get('qy_code')
  if (qy_code) {
    code = qy_code
    type = 2
  }
  const typeParam = urlParams.get('type')
  if (typeParam != null && typeParam === "3") {
    type = 3
  }
  const openSource = urlParams.get('open_source')
  if (openSource && openSource === "zjtapp") {
    type = 4
  }
  return {type, code}
}
