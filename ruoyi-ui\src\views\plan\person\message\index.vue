<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称"></el-input>
      </el-form-item>
      <el-form-item label="城市公司" prop="deptNum">
        <el-select
          v-model="queryParams.deptNum"
          placeholder="请选择城市公司"
          clearable
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict.deptNum"
            :label="dict.deptName"
            :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleSet"
          v-hasPermi="['plan:message:query']"
        >设置</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="messageList">
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="消息内容" align="center" prop="content" />
      <el-table-column label="是否已读" align="center"  width="100" prop="isRead">
        <template slot-scope="scope">
          <el-tag :type="'info'"  :effect="scope.row.isRead === 0 ? 'dark' : 'light'">{{scope.row.isRead=== 0 ? '未读' : '已读'}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 展示节点详情 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body @close="detailClose">
      <div class="app-container">
        <div class="feedback-container" v-loading="loading">
          <div class="feedback-detail">
            <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
            <el-form class="feedback-detail">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="项目名称：" prop="projectName">
                    {{feedbackDetail.projectName || '--'}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="项目分期：" prop="stageId">
                    <dict-tag :options="dict.type.stages" :value="feedbackDetail.stageId"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="节点顺序：" prop="nodeIndex">
                    {{feedbackDetail.nodeIndex || '--'}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="节点名称：" prop="nodeName">
                    {{feedbackDetail.nodeName || '--'}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="计划开始时间：" prop="startTime">
                    {{feedbackDetail.startTime || '--'}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="计划完成时间：" prop="endTime">
                    {{feedbackDetail.endTime || '--'}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="完成时间：" prop="actualCompletionDate">
                    {{feedbackDetail.actualCompletionDate || '--'}}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="实际偏差：" prop="deviationNum">
                    {{feedbackDetail.deviationNum || '--'}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="完成标准：" prop="completeCriteria">
                    {{feedbackDetail.completeCriteria || '--'}}
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="节点描述：" prop="nodeDesc">
                    {{feedbackDetail.nodeDesc || '--'}}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--  设置  -->
    <el-dialog :title="'消息设置'" :visible.sync="messageSettingOpen" width="800px" append-to-body>
      <el-form ref="settingForm" :model="messageSettingForm" :rules="settingRules" label-width="150px" v-loading="messageFormLoading">
        <el-form-item label="消息提醒开关" prop="isOpen">
          <el-switch
            v-model="messageSettingForm.isOpen"
            :active-value="0"
            :inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="选择接收项目提醒" prop="selectedProjectId">
          <el-select
            v-model="messageSettingForm.selectedProjectId"
            placeholder="请选择项目"
            clearable
            multiple
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="dict in messageSettingForm.projectList"
              :key="dict.projectId"
              :label="dict.projectName"
              :value="dict.projectId"
            ></el-option>

          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSettingForm">确 定</el-button>
        <el-button @click="cancelSetting">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getMessage, listMessage, getMessageConfig, updateMessageConfig} from "@/api/plan/message";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import Template from "@/views/plan/template/index.vue";

export default {
  name: "Message",
  dicts: ['stages'],
  components: {Template, StatusTag},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的消息表格数据
      messageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        nodeId: null,
        content: null,
        isRead: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        nodeId: [
          { required: true, message: "节点id不能为空", trigger: "blur" }
        ],
      },
      projectList: [],
      isReadOption: [ //是否已读(0-未读 1-已读)
        {
          value: 0,
          label: '未读',
          type: 'danger'
        },
        {
          value: 1,
          label: '已读',
          type: 'info'
        }
      ],
      messageSettingOpen: false,
      messageSettingForm: {
        selectedProjectId: null,
      },
      settingRules: {
        isOpen: [
          { required: true, message: "消息提醒开关不能为空", trigger: "blur" }
        ],
        projectList: [
          { required: true, message: "选择接收项目提醒不能为空", trigger: "blur" }
        ],
      },
      nodeOutcomeDocumentList: [], // 反馈成果文件
      feedbackDetail: {}, // 反馈详情
      messageFormLoading: false,
    };
  },
  computed: {
    cityCompanys () {
      return this.$store.state.plan.deptList
    }
  },
  created() {
    this.getList();
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList')
  },
  methods: {
    detailClose() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    submitSettingForm(){
      this.$refs["settingForm"].validate(valid => {
        if (valid) {
          let form = {
            id: this.messageSettingForm.id,
            isOpen: this.messageSettingForm.isOpen,
            selectedProjectId: this.messageSettingForm.selectedProjectId.join(',')
          }
          this.messageFormLoading = true;
          updateMessageConfig(form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.messageSettingOpen = false;
            this.messageFormLoading = false;

          });
        }
      });
    },
    cancelSetting(){
      this.messageSettingOpen = false
    },
    handleSet(){
      this.messageSettingOpen = true
      getMessageConfig().then(response => {
        /*{
          id: null,
          isOpen: 0, // 0=打开,1=关闭
          projectId: null, // 项目id集合
          projectList: null, // 所有项目信息
          selectedProjectId: null, // 已选项目id集合
          userId: 73578
        }*/
        this.messageSettingForm = response.data
        this.messageSettingForm.selectedProjectId = response.data.selectedProjectId.split(',') || [];
      })
    },
    /** 查询我的消息列表 */
    getList() {
      this.loading = true;
      listMessage(this.queryParams).then(response => {
        this.messageList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
      ;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nodeId: null,
        content: null,
        isRead: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加我的消息";
    },
    handleView(row){
      this.reset();
      const id = row.id || this.ids
      getMessage(id).then(response => {
        this.open = true;
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList;
        this.loading = false;
        this.title = "查看节点详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMessage(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改我的消息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMessage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMessage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除我的消息编号为"' + ids + '"的数据项？').then(function() {
        return delMessage(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('plan/message/export', {
        ...this.queryParams
      }, `message_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
.feedback-container {
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;
  .feedback-form-title {
    margin-bottom: 20px;
    background-color: #f8f8f9;
    padding: 10px;
    width: 100%;
  }
  .icon-primary {
    color: #409eff;
  }
  .cursor {
    cursor: pointer;
  }
  .display-none {
    display: none !important;
  }
  .file-wrapper {
    .el-icon-circle-close {
      margin-left: 10px;
    }
  }
  .link {
    color: #409eff;
  }
}

.transfer-item {
  display: flex;
  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}

</style>
