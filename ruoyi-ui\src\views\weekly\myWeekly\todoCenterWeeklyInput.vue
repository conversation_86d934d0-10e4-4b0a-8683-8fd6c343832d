<template>
  <div v-loading="loading" class="weekly-input">
    <WeeklyForm ref="weeklyFormRef" class="weekly-input-form" />
    <div class="bottom-button" v-if="!isRead">
      <el-button type="primary" @click="saveDraft">保存草稿</el-button>
      <el-button type="primary" v-if="type === 'update'" @click="updateWeekly"
        >提交</el-button
      >
      <el-button type="primary" v-else @click="submitWeekly">提交</el-button>
      <el-button @click="cancelHandle">取消</el-button>
    </div>
  </div>
</template>
<script>
// http://localhost/weekly/todoCenterWeeklyInput?todoNoticeId=655
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}

import {
  addInfo,
  addInit,
  getInfo,
  getDaiBanInfo,
  updateInfo,
  addDraft,
  addInitNew
} from "@/api/weekly/reportInfo";
import WeeklyForm from "./components/weeklyForm.vue";

export default {
  components: {
    WeeklyForm,
  },
  data() {
    return {
      form: {},
      loading: false,
      type: "", // 新增还是编辑模式
      id: "", // 周报ID
      todoNoticeId: "",
      updateAction: false,
      isRead: false
    };
  },
  created() {
    // 获取路由参数
    const { type, id, todoNoticeId } = this.$route.query;
    this.type = type;
    this.id = id;
    this.todoNoticeId = todoNoticeId;
  },
  mounted() {
      if (this.type === "update" && this.id) { // 待办中心编辑周报，暂时没有使用到。路由中只配置了todoNoticeId参数
      this.getWeeklyDetail();
    } else {
        this.handleInit();
    }
  },
  methods: {
    async saveDraft() {
      if(this.loading) return
      this.loading = true;
      try {
        const formData = await this.$refs.weeklyFormRef.draftForm();
        await addDraft(formData);
        this.$modal.msgSuccess("草稿保存成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("草稿保存失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async getWeeklyDetail() {
      this.loading = true;
      try {
        const { data } = await getDaiBanInfo(this.id);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        this.isRead = data.isRead;
        this.$refs.weeklyFormRef.updateFormData(data);
      } catch (error) {
        // this.$modal.msgError("获取详情失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async addWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        await addInfo(data);
        this.$modal.msgSuccess("新增成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("新增失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },

    async handleInit() {
      try {
        // const { data } = await addInit();
        // 如果返回的周报信息中id存在，提交的时候就调用修改方法，不存在则调用新增方法
        const { data } = await addInitNew(this.todoNoticeId);
        this.updateAction = !!data.id;
        this.isRead = !!data.isRead;
        if(this.updateAction) {
          this.type = "update";
        }
        else {
          this.type = "add";
        }
        console.log("handleInit data:", data);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }

        const formFields = [
          "year",
          "week",
          "nickName",
          "deptId",
          "deptName",
          "postName",
          "startDate",
          "endDate",
          "workSummaryList",
          "workPlanList",
          "workSupportList",
          "userCode",
          "reportReviewDetails",
          "id",
          "reflectInfo",
        ];
        const formData = {};
        formFields.forEach((field) => (formData[field] = data[field]));
        if(!formData.reflectInfo){
          formData.reflectInfo = {type: 4, progressScheme: "" };
        }
        if(this.type === "update"){
          this.$refs.weeklyFormRef.setFormData(data);
        }
        else{
          this.$refs.weeklyFormRef.setFormData(formData);
        }
      } catch (error) {
        // this.$modal.msgError("初始化失败：" + error.message);
      }
    },
    async updateWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        const formData = {
          ...data,
          /*id: this.id,
          year: data.year,
          week: data.week,
          userCode: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          // approveUserName: data.approveUserName,
          // approveUserCode: data.approveUserCode,
          workSummaryList: data.workSummaryList,
          workPlanList: data.workPlanList,
          workSupportList: data.workSupportList,
          reportReviewDetails: data.reportReviewDetails,*/
        };
        await updateInfo(formData);
        this.$modal.msgSuccess("修改成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("修改失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 编辑周报
    async updateWeekly() {
      if(this.loading) return
      this.loading = true;
      try{
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await this.updateWeeklyHandle(formData);
      }
      finally {
        this.loading = false;
      }
    },
    // 新增周报
    async submitWeekly() {
      if(this.loading) return
      this.loading = true;
      try{
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await this.addWeeklyHandle(formData);
        close();
      }
      finally {
        this.loading = false;
      }
    },

    closeCurrentPage() {
      close();
      // this.$tab.closeOpenPage({ path: "/weekly/myWeekly" });
    },
    cancelHandle() {
      this.closeCurrentPage();
    },
  },
};
</script>
<style scoped lang="scss">
.weekly-input {
  min-height: 0;
  display: flex;
  flex-direction: column;
  .weekly-input-form {
    flex: 1;
    overflow: auto;
  }
}
.bottom-button {
  margin-top: 20px;
  text-align: center;
}
</style>
