<template>
  <div class="projects-content-container w-100">
    <div v-if="!hasPermi(['project:operate:index'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限" />
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else class="w-100">
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中" />
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据" />
        <div class="desc">暂无数据</div>
      </div>

      <section
        v-else-if="showContent"
        class="w-100"
        style="min-height: 60vh"
        v-loading="isLoading"
      >
        <div class="cost-content">
          <div class="card-content mb-12">
            <div class="card-title mb-16">
              <span class="text">全周期指标</span>
            </div>
            <div class="cost-card-item">
              <div
                class="cost-card-item-content cost"
                v-loading="loadings.value"
              >
                <div class="title">货值</div>
                <Empty :no-authority="hasPermi(['operate:all:goodsValue'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart class="chart-size-220" :option="valueOption" />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: valueOption.title.textStyle.color,
                            }"
                          >
                            {{ valueOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ valueOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(valueData.targetAmount) }}亿
                        </div>
                        <div class="label">目标货值</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(valueData.dynamicAmount) }}亿
                        </div>
                        <div class="label">动态货值</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(valueData.changeAmount) }}亿
                        </div>
                        <div class="label">变动</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content change"
                v-loading="loadings.stock"
              >
                <div class="title">成本</div>
                <Empty :no-authority="hasPermi(['operate:all:cost'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart class="chart-size-220" :option="stockOption" />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: stockOption.title.textStyle.color,
                            }"
                          >
                            {{ stockOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ stockOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(stockData.targetAmount) }}亿
                        </div>
                        <div class="label">目标成本</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(stockData.dynamicAmount) }}亿
                        </div>
                        <div class="label">动态成本</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(stockData.changeAmount) }}亿
                        </div>
                        <div class="label">变动</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content marketing"
                v-loading="loadings.marketing"
              >
                <div class="title">营销费</div>
                <Empty :no-authority="hasPermi(['operate:all:market'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="marketingOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: marketingOption.title.textStyle.color,
                            }"
                          >
                            {{ marketingOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ marketingOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(marketingData.targetAmount) }}亿
                        </div>
                        <div class="label">目标费用</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(marketingData.totalAmount) }}亿
                        </div>
                        <div class="label">累计费用</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content sign"
                v-loading="loadings.sign"
              >
                <div class="title">签约</div>
                <Empty :no-authority="hasPermi(['operate:all:qy'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart class="chart-size-220" :option="signOption" />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{ color: signOption.title.textStyle.color }"
                          >
                            {{ signOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ signOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(signData.targetAmount) }}亿
                        </div>
                        <div class="label">全周期目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(signData.totalAmount) }}亿
                        </div>
                        <div class="label">累计签约</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content payment"
                v-loading="loadings.payment"
              >
                <div class="title">回款</div>
                <Empty :no-authority="hasPermi(['operate:all:hk'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart class="chart-size-220" :option="paymentOption" />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: paymentOption.title.textStyle.color,
                            }"
                          >
                            {{ paymentOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ paymentOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(paymentData.targetAmount) }}亿
                        </div>
                        <div class="label">全周期目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(paymentData.totalAmount) }}亿
                        </div>
                        <div class="label">累计回款</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
            </div>
          </div>

          <div class="card-content mb-12">
            <div class="card-title mb-16">年度指标</div>
            <div class="cost-card-item">
              <div
                class="cost-card-item-content cost"
                v-loading="loadings.yearlySign"
              >
                <div class="title">签约</div>
                <Empty :no-authority="hasPermi(['operate:year:qy'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="yearlySignOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: yearlySignOption.title.textStyle.color,
                            }"
                          >
                            {{ yearlySignOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ yearlySignOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlySignData.targetAmount) }}亿
                        </div>
                        <div class="label">年度目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlySignData.totalAmount) }}亿
                        </div>
                        <div class="label">累计签约</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content change"
                v-loading="loadings.yearlyPayment"
              >
                <div class="title">回款</div>
                <Empty :no-authority="hasPermi(['operate:year:hk'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="yearlyPaymentOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: yearlyPaymentOption.title.textStyle.color,
                            }"
                          >
                            {{ yearlyPaymentOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ yearlyPaymentOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyPaymentData.targetAmount) }}亿
                        </div>
                        <div class="label">年度目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyPaymentData.totalAmount) }}亿
                        </div>
                        <div class="label">累计回款</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content marketing"
                v-loading="loadings.yearlyFullPayment"
              >
                <div class="title">款齐</div>
                <Empty :no-authority="hasPermi(['operate:year:kq'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="yearlyFullPaymentOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color:
                                yearlyFullPaymentOption.title.textStyle.color,
                            }"
                          >
                            {{ yearlyFullPaymentOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ yearlyFullPaymentOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyFullPaymentData.targetAmount) }}亿
                        </div>
                        <div class="label">年度目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyFullPaymentData.totalAmount) }}亿
                        </div>
                        <div class="label">累计款齐</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content sign"
                v-loading="loadings.yearlyProfit"
              >
                <div class="title">利润</div>
                <Empty :no-authority="hasPermi(['operate:year:lr'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="yearlyProfitOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: yearlyProfitOption.title.textStyle.color,
                            }"
                          >
                            {{ yearlyProfitOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ yearlyProfitOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyProfitData.targetAmount) }}亿
                        </div>
                        <div class="label">年度目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyProfitData.totalAmount) }}亿
                        </div>
                        <div class="label">累计利润</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
              <div
                class="cost-card-item-content payment"
                v-loading="loadings.yearlyCashFlow"
              >
                <div class="title">现金流</div>
                <Empty :no-authority="hasPermi(['operate:year:xjl'])">
                  <section>
                    <div v-if="showChart">
                      <section class="card-with-title">
                        <Chart
                          class="chart-size-220"
                          :option="yearlyCashFlowOption"
                        />
                        <section class="chart-title-block">
                          <div
                            class="title-1"
                            :style="{
                              color: yearlyCashFlowOption.title.textStyle.color,
                            }"
                          >
                            {{ yearlyCashFlowOption.title.text }}
                          </div>
                          <div class="title-2">
                            {{ yearlyCashFlowOption.title.subtext }}
                          </div>
                        </section>
                      </section>
                    </div>
                    <div class="data-content">
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyCashFlowData.targetAmount) }}亿
                        </div>
                        <div class="label">年度目标</div>
                      </div>
                      <div class="value-item">
                        <div class="value mb-8">
                          {{ $toFixed2(yearlyCashFlowData.totalAmount) }}亿
                        </div>
                        <div class="label">累计现金流</div>
                      </div>
                    </div>
                  </section>
                </Empty>
              </div>
            </div>
          </div>

          <div class="card-content analysis mb-12">
            <section class="analysis-item">
              <div class="card-title mb-16 flex-space">

                <div class="title">指标数据（考核口径）</div>
                <div class="info">单位：万元</div>
              </div>
              <Empty :no-authority="hasPermi(['operate:examine:data'])">
                <div class="cost-card-item w-100" v-loading="loadings.analysis">
                  <div class="table-wrapper w-100" style="min-width: 0">
                    <el-table
                      :data="analysisData"
                      :tree-props="{
                        children: 'children',
                        hasChildren: 'hasChildren',
                      }"
                      row-key="id"
                      class="w-100 project-table"
                      :border="true"
                      :header-cell-style="{
                        background: '#E1EFFD',
                        color: '#666666',
                        fontWeight: '400',
                        padding: '0.5rem 0',
                        height: '2rem',
                        fontSize: '0.875rem',
                        lineHeight: '1.5rem',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }"
                      :cell-style="{
                        borderColor: 'rgba(0,106,255,0.2);',
                        fontWeight: '400',
                        padding: '0.5rem 0',
                        height: '2rem',
                        fontSize: '0.875rem',
                        lineHeight: '1.5rem',
                      }"
                    >
                      <el-table-column
                        prop="projectType"
                        label="科目（含税金额）"
                        min-width="16%"
                      >
                      </el-table-column>

                      <el-table-column
                        prop="kyAmount"
                        label="可研版"
                        align="center"
                        min-width="16%"
                      >
                        <template #header>
                          <div
                            class="header-with-icon"
                            @click="
                              fetchExamineHistoryData(projectCode, 1, '可研版')
                            "
                          >
                            <span>可研版</span>
                            <img :src="clock" alt="clock" class="header-icon" />
                          </div>
                        </template>
                        <template #default="scope">
                          {{
                            scope.row.kyAmount
                              ? $toThousands($toFixed2(scope.row.kyAmount))
                              : "-"
                          }}
                          <span
                            v-if="
                              scope.row.kyAmount &&
                              ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                                scope.row.projectType.replace(/[\d\s]/g, '')
                              )
                            "
                            class="percent-sign"
                            >%</span
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="tjAmount"
                        label="投决版"
                        align="center"
                        min-width="16%"
                      >
                        <template #header>
                          <div
                            class="header-with-icon"
                            @click="
                              fetchExamineHistoryData(projectCode, 2, '投决版')
                            "
                          >
                            <span>投决版</span>
                            <img :src="clock" alt="clock" class="header-icon" />
                          </div>
                        </template>
                        <template #default="scope">
                          {{
                            scope.row.tjAmount
                              ? $toThousands($toFixed2(scope.row.tjAmount))
                              : "-"
                          }}
                          <span
                            v-if="
                              scope.row.tjAmount &&
                              ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                                scope.row.projectType.replace(/[\d\s]/g, '')
                              )
                            "
                            class="percent-sign"
                            >%</span
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="mbAmount"
                        label="目标版"
                        align="center"
                        min-width="16%"
                      >
                        <template #header>
                          <div
                            class="header-with-icon"
                            @click="
                              fetchExamineHistoryData(projectCode, 3, '目标版')
                            "
                          >
                            <span>目标版</span>
                            <img :src="clock" alt="clock" class="header-icon" />
                          </div>
                        </template>
                        <template #default="scope">
                          {{
                            scope.row.mbAmount
                              ? $toThousands($toFixed2(scope.row.mbAmount))
                              : "-"
                          }}
                          <span
                            v-if="
                              scope.row.mbAmount &&
                              ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                                scope.row.projectType.replace(/[\d\s]/g, '')
                              )
                            "
                            class="percent-sign"
                            >%</span
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="dtAmount"
                        label="动态版"
                        align="center"
                        min-width="16%"
                      >
                        <template #header>
                          <div
                            class="header-with-icon"
                            @click="
                              fetchExamineHistoryData(projectCode, 4, '动态版')
                            "
                          >
                            <span>动态版</span>
                            <img :src="clock" alt="clock" class="header-icon" />
                          </div>
                        </template>
                        <template #default="scope">
                          {{
                            scope.row.dtAmount
                              ? $toThousands($toFixed2(scope.row.dtAmount))
                              : "-"
                          }}
                          <span
                            v-if="
                              scope.row.dtAmount &&
                              ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                                scope.row.projectType.replace(/[\d\s]/g, '')
                              )
                            "
                            class="percent-sign"
                            >%</span
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="hpjAmount"
                        label="局后评价版"
                        align="center"
                        min-width="16%"
                      >
                        <template #header>
                          <div
                            class="header-with-icon"
                            @click="
                              fetchExamineHistoryData(
                                projectCode,
                                5,
                                '局后评价版'
                              )
                            "
                          >
                            <span>局后评价版</span>
                            <img :src="clock" alt="clock" class="header-icon" />
                          </div>
                        </template>
                        <template #default="scope">
                          {{
                            scope.row.hpjAmount
                              ? $toThousands($toFixed2(scope.row.hpjAmount))
                              : "-"
                          }}
                          <span
                            v-if="
                              scope.row.hpjAmount &&
                              ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                                scope.row.projectType.replace(/[\d\s]/g, '')
                              )
                            "
                            class="percent-sign"
                            >%</span
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </Empty>
            </section>
          </div>
        </div>
      </section>
    </div>

    <!-- 添加弹窗组件 -->
    <el-dialog :visible.sync="dialogVisible" class="history-dialog" width="80%">
      <div class="card-title mb-16">{{ dialogTitle }}</div>
      <el-table
        v-if="historyData && historyData.length > 0"
        :data="historyData"
        row-key="id"
        border
        default-expand-all
        style="width: 100%"
        height="calc(70vh - 80px)"
        :header-cell-style="{
          background: '#E1EFFD',
          color: '#666666',
          fontWeight: '400',
          padding: '0.5rem 0',
          height: '2rem',
          fontSize: '0.875rem',
          lineHeight: '1.5rem',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }"
        :cell-style="{
          borderColor: 'rgba(0,106,255,0.2);',
          fontWeight: '400',
          padding: '0.5rem 0',
          height: '2rem',
          fontSize: '0.875rem',
          lineHeight: '1.5rem',
        }"
        :tree-props="{
          children: 'children',
          hasChildren: 'hasChildren',
        }"
        show-summary
        :summary-method="getSummary"
      >
        <el-table-column
          prop="targetName"
          label="科目（含税金额）"
          width="200"
        />
        <el-table-column
          v-for="version in versions || []"
          :key="version.versionName"
          :prop="version.versionName"
          :label="version.versionName"
          min-width="150"
        >
          <template #default="scope">
            {{
              scope.row[version.versionName]
                ? $toThousands($toFixed2(scope.row[version.versionName]))
                : "-"
            }}
            <span
              v-if="
                ['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(
                  scope.row.targetName.replace(/[\d\s]/g, '')
                )
              "
              class="percent-sign"
              >%</span
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 当没有数据时显示提示 -->
      <div
        v-else
        style="
          text-align: center;
          color: #909399;
          padding: 30px 0;
          font-size: 14px;
        "
      >
        暂无数据
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Chart from "@/views/projects/components/Chart.vue";
import ProgressCircleOption from "@/views/projects/constants/ProgressCircle";
import API from "@/views/projects/api";
import Empty from "@/views/projects/components/empty.vue";
import clock from "@/views/projects/assets/images/clock.png";
export default {
  name: "Operation",
  components: {
    Chart,
    Empty,
  },
  data() {
    return {
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      projectCode: this.$route.query.projectCode,
      valueData: {}, // 货值
      stockData: {}, // 库存
      marketingData: {
        targetAmount: null,
        totalAmount: null,
        rate: 0,
      }, // 营销费
      signData: {}, // 签约
      paymentData: {}, // 回款
      yearlySignData: {}, // 年度签约
      yearlyPaymentData: {}, // 年度回款
      yearlyFullPaymentData: {}, // 年度款齐
      yearlyProfitData: {}, // 年度利润
      yearlyCashFlowData: {}, // 年度现金流
      // analysisData: [], // 考核口径指标数据
      windowWidth: window.innerWidth, // Add this new property
      loadings: {
        value: false,
        stock: false,
        marketing: false,
        sign: false,
        payment: false,
        yearlySign: false,
        yearlyPayment: false,
        yearlyFullPayment: false,
        yearlyProfit: false,
        yearlyCashFlow: false,
        analysis: false,
      },
      chartKey: 0, // Add a key to force chart update
      showChart: true,
      showContent: true,
      isLoading: false,
      defineList: [], // 添加定义列表
      kyList: [], // 可研版数据
      tjList: [], // 投决版数据
      mbList: [], // 目标版数据
      dtList: [], // 动态版数据
      hpjList: [], // 局后评价版数据
      dialogVisible: false,
      dialogTitle: "",
      historyData: [],
      versions: [],
    };
  },
  mounted() {
    this.fetchData(this.projectCode);
    this.fetchExamineTarget(this.projectCode);
    // // 使用防抖仅用于显示内容的操作
    // const debouncedShow = this.debounce(this.showContentWithDelay, 500);

    // // resize事件处理：立即隐藏 + 延迟显示
    // window.addEventListener('resize', () => {
    //   this.handleResize(); // 立即执行隐藏
    //   debouncedShow(); // 延迟执行显示
    // });

    // // 保存cleanup函数的引用
    // this._resizeHandler = debouncedShow;
  },
  computed: {
    clock() {
      return clock;
    },
    valueOption() {
      return this.getValueOption(this.windowWidth);
    },
    stockOption() {
      return this.getStockOption(this.windowWidth);
    },
    marketingOption() {
      return this.getMarketingOption(this.windowWidth);
    },
    signOption() {
      return this.getSignOption(this.windowWidth);
    },
    paymentOption() {
      return this.getPaymentOption(this.windowWidth);
    },
    yearlySignOption() {
      return this.getYearlySignOption(this.windowWidth);
    },
    yearlyPaymentOption() {
      return this.getYearlyPaymentOption(this.windowWidth);
    },
    yearlyFullPaymentOption() {
      return this.getYearlyFullPaymentOption(this.windowWidth);
    },
    yearlyProfitOption() {
      return this.getYearlyProfitOption(this.windowWidth);
    },
    yearlyCashFlowOption() {
      return this.getYearlyCashFlowOption(this.windowWidth);
    },
    analysisData() {
      // 处理每个版本的数据
      const getVersionAmount = (id, versionList) => {
        if (!versionList) return null;

        // 递归查找函数
        const findInList = (list) => {
          for (const item of list) {
            // 检查当前项
            if (item.defineId === id) {
              return item.amount;
            }
            // 如果有子列表，递归查找
            if (item.childList && item.childList.length) {
              const found = findInList(item.childList);
              if (found !== null) {
                return found;
              }
            }
          }
          return null;
        };

        return findInList(versionList);
      };

      // 递归处理树形结构
      const mapItemWithChildren = (item) => ({
        id: item.id,
        projectType: item.targetName,
        kyAmount: getVersionAmount(item.id, this.kyList),
        tjAmount: getVersionAmount(item.id, this.tjList),
        mbAmount: getVersionAmount(item.id, this.mbList),
        dtAmount: getVersionAmount(item.id, this.dtList),
        hpjAmount: getVersionAmount(item.id, this.hpjList),
        children: item.childList?.map((child) => mapItemWithChildren(child)),
      });

      // 转换defineList为树形结构
      return this.defineList.map((item) => mapItemWithChildren(item));
    },
  },
  methods: {
    getSummary({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "版本说明"; // 第一列显示"版本说明"
          return;
        }

        // 从 versions 数组中找到对应的版本说明
        const version = this.versions.find(
          (v) => v.versionName === column.property
        );
        sums[index] = version?.versionDesc || "-"; // 如果没有找到版本说明则显示'-'
      });
      return sums;
    },
    // 查询考核指标版本数据列表
    async fetchExamineHistoryData(projectCode, versionType, label) {
      try {
        this.loading = true;
        this.dialogTitle = `全周期指标(考核口径)-${label}`;
        const response = await API.Operation.getExamineDetail({
          projectCode,
          versionType,
        });

        if (response.data) {
          // 使用传入的 label 设置弹窗标题
          // this.dialogTitle = label || this.getDialogTitle(versionType);

          // 处理版本信息，添加 versionDesc
          this.versions = response.data.map((v) => ({
            version: v.version,
            versionName: v.versionName,
            versionDesc: v.versionDesc,
          }));

          console.log("Processed versions:", this.versions); // 添加这行来检查处理后的版本数据

          // 处理数据为扁平化结构同时保持树形关系
          this.historyData = this.flattenData(response.data);

          // 显示弹窗
          this.dialogVisible = true;
        }
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        this.loading = false;
      }
    },
    // 经营指标 - 指标数据（考核口径）
    async fetchExamineTarget(projectCode) {
      this.loadings.analysis = true;
      try {
        const res = await API.Operation.examineTarget(projectCode);
        if (res.data) {
          // 假设返回的数据结构包含这些字段，根据实际API返回调整
          this.defineList = res.data.defineList || [];
          this.kyList = res.data.kyList || [];
          this.tjList = res.data.tjList || [];
          this.mbList = res.data.mbList || [];
          this.dtList = res.data.dtList || [];
          this.hpjList = res.data.hpjList || [];
        }
      } catch (error) {
        console.error("获取考核指标数据失败:", error);
      } finally {
        this.loadings.analysis = false;
      }
    },
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions;
      // Check for all permissions wildcard first
      if (permissions.includes("*:*:*")) {
        return true;
      }
      // Check specific permissions
      return permissions.some((p) => permission.includes(p));
    },
    formatAmount(value) {
      return value ? Number(value).toLocaleString() : "0";
    },
    fetchData(projectCode) {
      this.fetchValueData(projectCode); // 货值数据
      this.fetchStockData(projectCode); // 成本数据
      this.fetchMarketingData(projectCode); // 营销费数据
      this.fetchSignData(projectCode); // 签约数据
      this.fetchPaymentData(projectCode); // 回款数据
      this.fetchYearlySignData(projectCode); // 年度签约数据
      this.fetchYearlyPaymentData(projectCode); // 年度回款数据
      this.fetchYearlyFullPaymentData(projectCode); // 年度款齐数据
      this.fetchYearlyProfitData(projectCode); // 年度利润数据
      this.fetchYearlyCashFlowData(projectCode); // 年度现金流数据
    },

    async fetchValueData(projectCode) {
      this.loadings.value = true;
      try {
        const res = await API.Operation.allGoodsValue(projectCode);
        this.valueData = res.data;
      } catch (error) {
        console.error("获取全周期货值数据失败:", error);
      } finally {
        this.loadings.value = false;
      }
    },

    async fetchStockData(projectCode) {
      this.loadings.stock = true;
      try {
        const res = await API.Operation.allCost(projectCode);
        this.stockData = res.data;
      } catch (error) {
        console.error("获取全周期成本数据失败:", error);
      } finally {
        this.loadings.stock = false;
      }
    },

    async fetchMarketingData(projectCode) {
      this.loadings.marketing = true;
      try {
        const res = await API.Operation.allMarketing(projectCode);
        this.marketingData = res.data;
      } catch (error) {
        console.error("获取全周期营销费数据失败:", error);
      } finally {
        this.loadings.marketing = false;
      }
    },

    async fetchSignData(projectCode) {
      this.loadings.sign = true;
      try {
        const res = await API.Operation.allQy(projectCode);
        this.signData = res.data;
      } catch (error) {
        console.error("获取全周期签约数据失败:", error);
      } finally {
        this.loadings.sign = false;
      }
    },

    async fetchPaymentData(projectCode) {
      this.loadings.payment = true;
      try {
        const res = await API.Operation.allHk(projectCode);
        this.paymentData = res.data;
      } catch (error) {
        console.error("获取全周期回款数据失败:", error);
      } finally {
        this.loadings.payment = false;
      }
    },

    async fetchYearlySignData(projectCode) {
      this.loadings.yearlySign = true;
      try {
        const res = await API.Operation.yearQyData(projectCode);
        this.yearlySignData = res.data;
      } catch (error) {
        console.error("获取年度签数据失败:", error);
      } finally {
        this.loadings.yearlySign = false;
      }
    },

    async fetchYearlyPaymentData(projectCode) {
      this.loadings.yearlyPayment = true;
      try {
        const res = await API.Operation.yearHkData(projectCode);
        this.yearlyPaymentData = res.data;
      } catch (error) {
        console.error("获取年度回款数据失败:", error);
      } finally {
        this.loadings.yearlyPayment = false;
      }
    },

    async fetchYearlyFullPaymentData(projectCode) {
      this.loadings.yearlyFullPayment = true;
      try {
        const res = await API.Operation.yearKqData(projectCode);
        this.yearlyFullPaymentData = res.data;
      } catch (error) {
        console.error("获取年度款齐数据失败:", error);
      } finally {
        this.loadings.yearlyFullPayment = false;
      }
    },

    async fetchYearlyProfitData(projectCode) {
      this.loadings.yearlyProfit = true;
      try {
        const res = await API.Operation.yearLrData(projectCode);
        this.yearlyProfitData = res.data;
      } catch (error) {
        console.error("获取年度利润数据失败:", error);
      } finally {
        this.loadings.yearlyProfit = false;
      }
    },

    async fetchYearlyCashFlowData(projectCode) {
      this.loadings.yearlyCashFlow = true;
      try {
        const res = await API.Operation.yearXhlData(projectCode);
        this.yearlyCashFlowData = res.data;
      } catch (error) {
        console.error("获取年度现金流数据失败:", error);
      } finally {
        this.loadings.yearlyCashFlow = false;
      }
    },

    getProgressCircleOption(
      finishNum = 0,
      total = 100,
      text = "",
      subText = "",
      bgColor = "#3DCC85",
      color = "376DF7",
      color2 = "#1433CC"
    ) {
      const instance = new ProgressCircleOption();

      // Calculate font sizes
      const calculateFontSize = (scale = 1) => {
        const baseSize = 21;
        const width = window.innerWidth;
        return baseSize * (width / 1680) * scale;
      };

      // 先设置样式
      if (finishNum > total) {
        instance.setBackgroundStyle(color);
        instance.setBarItemStyle(color2);
      } else {
        instance.setBackgroundStyle(bgColor);
        instance.setBarItemStyle(color);
      }

      // 获取配置后再修改其他属性
      const option = instance.getOption();

      if (finishNum > total) {
        option.series[0].data = [parseFloat((finishNum % total).toFixed(2))];
        option.color = [color2];
      } else {
        option.series[0].data = [finishNum];
        option.color = [color];
      }
      option.series[0].roundCap = option.series[0].data[0] > 3;

      // 设置其他配置，包括响应式字体大小
      option.title = {};
      option.title.left = "center";

      option.title.itemGap = 10 * (window.innerWidth / 1680);
      option.title.textStyle = {
        ...option.title.textStyle,
        color: color,
        fontSize: calculateFontSize(0.8),
        height: calculateFontSize(1) * 1,
        lineHeight: calculateFontSize(1),
        verticalAlign: "middle",
      };
      option.title.subtextStyle = {
        ...option.title.subtextStyle,
        fontSize: calculateFontSize(0.6),
        height: calculateFontSize(1) * 1,
        lineHeight: calculateFontSize(1),
        verticalAlign: "middle",
      };
      option.title.subtext = subText;
      option.title.text = text;
      option.title.show = false;
      option.angleAxis.max = total;
      option.polar.radius = ["45%", "60%"];
      option.polar.center = ["50%", "50%"];

      return option;
    },

    getValueOption(windowWidth) {
      // this.updateChart();
      const option = this.getProgressCircleOption(
        this.$formatNull(this.valueData.rate),
        100,
        this.$formatNull(this.valueData.deviationRate) + "%",
        "偏差率",
        "#B9C9F7",
        "#376DF7",
        "#1433CC"
      );

      // 计算响应式字体大小
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
          <div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">目标货值: ${this.$formatNull(
          this.valueData.targetAmount
        )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">动态货值: ${this.$formatNull(
          this.valueData.dynamicAmount
        )}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">比例: ${this.$formatNull(this.valueData.rate)}%</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">变动: ${this.$formatNull(this.valueData.changeAmount)}亿</div>
            <div style="line-height: ${Math.floor(
              fontSize * 1.5
            )}px">偏差率: ${this.$formatNull(
          this.valueData.deviationRate
        )}%</div>
          </div>
        `,
      };
      return option;
    },

    getStockOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.stockData.rate),
        100,
        this.$formatNull(this.stockData.deviationRate) + "%",
        "偏差率",
        "#BDE8D1",
        "#3DCC85",
        "#0F9954"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">目标成本: ${this.$formatNull(
          this.stockData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">动态成本: ${this.$formatNull(
          this.stockData.dynamicAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">比例: ${this.$formatNull(this.stockData.rate)}%</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">变动: ${this.$formatNull(
          this.stockData.changeAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">偏差率: ${this.$formatNull(
          this.stockData.deviationRate
        )}%</div>
            </div>
          `,
      };
      return option;
    },

    getMarketingOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.marketingData.rate),
        100,
        this.$formatNull(this.marketingData.rate) + "%",
        "进度",
        "#FFE4CC",
        "#FF974D",
        "#e56d16"
      );

      // 计算响应式字体大小
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">目标费用: ${this.$formatNull(
          this.marketingData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计费用: ${this.$formatNull(
          this.marketingData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(this.marketingData.rate)}%</div>
            </div>
          `,
      };
      return option;
    },

    getSignOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.signData.rate),
        100,
        this.$formatNull(this.signData.rate) + "%",
        "进度",
        "#E4CCFF",
        "#6D3DCC",
        "#6A3DC4"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">全周期目标: ${this.$formatNull(
          this.signData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计签约: ${this.$formatNull(
          this.signData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(this.signData.rate)}%</div>
            </div>
          `,
      };
      return option;
    },

    getPaymentOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.paymentData.rate),
        100,
        this.$formatNull(this.paymentData.rate) + "%",
        "进度",
        "#CCF6F6",
        "#3DCCCC",
        "#0D8080"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">全周期目标: ${this.$formatNull(
          this.paymentData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计回款: ${this.$formatNull(
          this.paymentData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(this.paymentData.rate)}%</div>
            </div>
          `,
      };
      return option;
    },

    getYearlySignOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.yearlySignData.rate),
        100,
        this.$formatNull(this.yearlySignData.rate) + "%",
        "完成率",
        "#B9C9F7",
        "#376DF7",
        "#1433CC"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">年度目标: ${this.$formatNull(
          this.yearlySignData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计签约: ${this.$formatNull(
          this.yearlySignData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(this.yearlySignData.rate)}%</div>
            </div>
          `,
      };
      return option;
    },

    getYearlyPaymentOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.yearlyPaymentData.rate),
        100,
        this.$formatNull(this.yearlyPaymentData.rate) + "%",
        "完成率",
        "#BDE8D1",
        "#3DCC85",
        "#0F9954"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">年度目标: ${this.$formatNull(
          this.yearlyPaymentData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计回款: ${this.$formatNull(
          this.yearlyPaymentData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(
          this.yearlyPaymentData.rate
        )}%</div>
            </div>
          `,
      };
      return option;
    },

    getYearlyFullPaymentOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.yearlyFullPaymentData.rate),
        100,
        this.$formatNull(this.yearlyFullPaymentData.rate) + "%",
        "完成率",
        "#FFE4CC",
        "#FF974D",
        "#e56d16"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">年度目标: ${this.$formatNull(
          this.yearlyFullPaymentData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计款齐: ${this.$formatNull(
          this.yearlyFullPaymentData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(
          this.yearlyFullPaymentData.rate
        )}%</div>
            </div>
          `,
      };
      return option;
    },

    getYearlyProfitOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.yearlyProfitData.rate),
        100,
        this.$formatNull(this.yearlyProfitData.rate) + "%",
        "完成率",
        "#E4CCFF",
        "#6D3DCC",
        "#6A3DC4"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">年度目标: ${this.$formatNull(
          this.yearlyProfitData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计利润: ${this.$formatNull(
          this.yearlyProfitData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(
          this.yearlyProfitData.rate
        )}%</div>
            </div>
          `,
      };
      return option;
    },

    getYearlyCashFlowOption(windowWidth) {
      const option = this.getProgressCircleOption(
        this.$formatNull(this.yearlyCashFlowData.rate),
        100,
        this.$formatNull(this.yearlyCashFlowData.rate) + "%",
        "完成率",
        "#CCF6F6",
        "#3DCCCC",
        "#0D8080"
      );
      const fontSize = Math.floor(18 * (windowWidth / 1680));

      option.tooltip = {
        confine: true,
        show: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + "px",
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => `
            <div style="font-size: ${fontSize}px;">
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">年度目标: ${this.$formatNull(
          this.yearlyCashFlowData.targetAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">累计现金流: ${this.$formatNull(
          this.yearlyCashFlowData.totalAmount
        )}亿</div>
              <div style="line-height: ${Math.floor(
                fontSize * 1.5
              )}px">完成率: ${this.$formatNull(
          this.yearlyCashFlowData.rate
        )}%</div>
            </div>
          `,
      };
      return option;
    },

    // 分离立即执行和延迟执行的操作
    handleResize() {
      // 立即执行的操作
      this.isLoading = true;
      this.showContent = false;
      this.windowWidth = window.innerWidth;
    },

    // 新增显示内容的方法
    showContentWithDelay() {
      this.showContent = true;
      this.isLoading = false;
    },

    // 添加防抖函数
    debounce(fn, delay) {
      let timer = null;
      return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, delay);
      };
    },

    getDialogTitle(versionType) {
      const titles = {
        1: "可研版",
        2: "投决版",
        3: "目标版",
        4: "动态版",
        5: "局后评价版",
      };
      return titles[versionType] || "版本历史";
    },

    // 处理数据为扁平化结构同时保持树形关系
    flattenData(data) {
      // 如果没有数据返回空数组
      if (!data || data.length === 0) return [];

      // 递归处理树形结构
      const processNode = (node) => {
        const result = {
          id: node.defineId,
          targetName: node.targetName,
          level: node.pid === 0, // true为父节点，false为子节点
        };

        // 为每个版本添加金额
        data.forEach((version) => {
          const versionData = version.list.find((item) => item.defineId === node.defineId);
          const nestedData = findNestedData(version.list, node.defineId);
          result[version.versionName] = nestedData?.amount || null;
        });

        // 如果有子节点，递归处理
        if (node.childList && node.childList.length > 0) {
          result.children = node.childList.map((child) => processNode(child));
        }

        return result;
      };

      // 辅助函数：在嵌套结构中查找数据
      const findNestedData = (list, defineId) => {
        for (const item of list) {
          if (item.defineId === defineId) return item;
          if (item.childList) {
            const found = item.childList.find((child) => child.defineId === defineId);
            if (found) return found;
          }
        }
        return null;
      };

      // 处理每个顶级节点
      return data[0].list.map((node) => processNode(node));
    },

    findItemInVersion(versionList, defineId) {
      return versionList.find((item) => item.defineId === defineId);
    },
  },
  beforeDestroy() {
    if (this._resizeHandler) {
      window.removeEventListener("resize", this._resizeHandler);
    }
  },
};
</script>

<style scoped lang="scss">
@import "./operation.scss";
.flex-space{
  display: flex;
  justify-content: space-between;
  margin-right: 10px;
}
</style>
