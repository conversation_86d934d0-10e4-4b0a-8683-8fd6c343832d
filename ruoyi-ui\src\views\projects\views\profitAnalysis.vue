<template>

</template>
<script>

</script>
<style scoped lang="scss">

</style>
<template>
    <div class="projects-content-container">
      <!-- 无权限状态 -->
      <div v-if="!hasPermi(['project:profit:manage'])" class="empty-status">
        <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
        <div class="desc">暂无权限</div>
        <div class="desc">请联系管理员</div>
      </div>

      <!-- 有权限时显示的内容 -->
      <div v-else>
        <!-- 开发中状态 -->
        <div v-if="isDeveloping" class="empty-status">
          <img src="@/views/projects/assets/images/developing.png" alt="开发中">
          <div class="desc">正在开发中</div>
          <div class="desc">敬请期待</div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="!hasData" class="empty-status">
          <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
          <div class="desc">暂无数据</div>
        </div>
      </div>
    </div>
  </template>

  <script>
  export default {
    name: 'QualityManage',
    data() {
      return {
        isDeveloping: true, // 控制是否显示开发中状态
        hasData: false // 控制是否有数据
      }
    },
    methods: {
      hasPermi(permission) {
        const permissions = this.$store.getters.permissions
        // Check for all permissions wildcard first
        if (permissions.includes('*:*:*')) {
          return true
        }
        // Check specific permissions
        return permissions.some(p => permission.includes(p))
      }
    }
  }
  </script>

  <style scoped lang="scss">
   @import '~@/views/projects/styles/projects.scss';
  </style>
