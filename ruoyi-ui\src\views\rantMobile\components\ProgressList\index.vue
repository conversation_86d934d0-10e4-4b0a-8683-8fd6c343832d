<template>
  <div class="progress-list-container">
    <div class="rant-form-title">
      <img src="@/assets/rant/rant-progress.png" class="icon">进度情况
    </div>
    <div v-for="(record, recordIndex) in progressList" :key="recordIndex" class="progress-item">
      <div class="progress-header">
        <div class="progress-number">{{ recordIndex + 1 }}</div>
        <div class="progress-date">{{ parseTime(record.feedbackTime, "{y}-{m}-{d}") }}</div>
      </div>
      <div class="progress-content" v-html="record.actualProgress"></div>
      <div v-if="record.fileList && record.fileList.length > 0" class="progress-files">
        <!-- <div class="files-title">成果文件：</div> -->
        <div v-for="(file, fileIdx) in record.fileList" :key="fileIdx" class="file-link">
          <div class="file-link" @click.stop="openFile(file.url)">
            <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
            <span class="extension-part">{{ getFileExtension(file.name) }}</span>
            <!-- 下载文件 -->
            <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
          </div>
        </div>
      </div>
    </div>
    <div v-if="!progressList || progressList.length === 0" class="no-data">
      暂无进度记录
    </div>
  </div>
</template>

<script>
import mixin from '@/views/rantMobile/mixins'
export default {
  name: 'ProgressList',
  mixins: [mixin],
  props: {
    progressList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    parseTime(time, pattern) {
      if (!time) return '';
      let date = new Date(time);
      let formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      };
      const timeStr = pattern.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        if (key === 'a') {
          return ['日', '一', '二', '三', '四', '五', '六'][value];
        }
        if (result.length > 0 && value < 10) {
          value = '0' + value;
        }
        return value || 0;
      });
      return timeStr;
    },
    openFile(url) {
      window.open(url);
    },
  }
}
</script>

<style scoped lang="scss">
.progress-list-container {
  // background: #F8FAFC;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 20px;

  .progress-item {
    padding: 0 12px 12px;
    border-bottom: 1px solid #EBEEF5;
    margin-bottom: 15px;

    &:last-child {
      border-bottom: none;
    }
  }
  .rant-form-title{
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #F0F0F0;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    margin-bottom: 12px;
;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    .icon{
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .progress-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .progress-number {
      width: 20px;
      height: 20px;
      background-color: #4080FF;
      color: #FFFFFF;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      margin-right: 10px;
    }

    .progress-date {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }
  }

  .progress-content {
    font-size: 14px;
    color: #333333;
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 30px;
  }

  .progress-files {
    // background: #F2F6FC;
    padding: 8px;
    border-radius: 4px;
    padding-left: 30px;

    .files-title {
      margin-bottom: 8px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }

    .file-link {
      margin-bottom: 12px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #3673FF;
      line-height: 14px;
      color: #4080FF;
      display: flex;
      align-items: center;
      width: 100%;
      min-width: 0;
      gap: 0;
      
      .filename-part {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 0;
        flex: 1;
      }
      
      .extension-part {
        flex-shrink: 0;
        white-space: nowrap;
      }
      
      .download-icon {
        flex-shrink: 0;
        margin-left: 8px;
        font-size: 14px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .no-data {
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    padding: 20px 0;
  }
}
</style>
