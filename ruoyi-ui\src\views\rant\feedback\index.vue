<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container" v-loading="loading">
      <div v-for="(rant, index) in rantList" :key="index" class="rant-content">
        <div class="section-title" v-if="rantList.length > 1">
          <span>
            <img src="@/assets/rant/rant-item.png" class="icon" alt="">
            督办事项{{ index + 1 }}
          </span>
          <section class="expand" @click="toggleCollapse(index)">
            <img src="@/assets/rant/rant-collapse.png" class="icon" :class="{ 'is-active': collapseStates[index] }"
                 alt="">
            {{ !collapseStates[index] ? '收起' : '展开' }}
          </section>
        </div>
        <template v-if="!collapseStates[index]">
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-info.png" class="icon">基本信息</div>
            <el-form class="rant-detail-form" :model="rant" label-position="right" label-width="120px" :rules="rules">
              <el-row>
                <el-col :span="6">
                  <el-form-item label="来源" prop="ranter">
                    {{ rant.ranterName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="类型" prop="mattersType">
                    <section class="custom-matters-type">
                      <dict-tag v-for="(type, index) in rant.mattersType?.split(',')" :key="index"
                                :options="dict.type.rant_matters_type" :value="type" style="height: 29px"/>
                    </section>

                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分类" prop="rantClassify">
                    <dict-tag :options="dict.type.rant_classify" :value="rant.rantClassify"/>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任人" prop="responsiblePerson">
                    {{ rant.responsiblePersonName }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6">
                  <el-form-item label="责任部门" prop="deptName">
                    {{ rant.deptName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任部门负责人" prop="respDeptResponsiblerName">
                    {{ rant.respDeptResponsiblerName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分管领导" prop="respDeptLeaderName">
                    {{ rant.respDeptLeaderName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="计划完成时间" prop="planTime">
                    {{ rant.planTime }}
                  </el-form-item>
                </el-col>
                <el-col v-if="type == 'matters'" :span="6">
                  <el-form-item label="是否私密" prop="isPrivate">
                    {{ rant.isPrivate == 1 ? "是" : "否" }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="内容" prop="rantContent">
                    {{ rant.rantContent }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="措施" prop="solution">
                    {{ rant.solution }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col v-if="rant.progressFeedbackStatus == 3" :span="24">
                  <el-form-item label="驳回原因" prop="approvalDesc">
                    {{ rant.approvalDesc }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

              </el-row>
              <div class="rant-form-title">
                <img src="@/assets/rant/rant-this-progress.png" class="icon">
                本次进展
                <span class="required-icon">*</span>
              </div>
              <editor v-model="rant.thisProgress" :height="192" class="width-100 mb-16"/>
              <el-row>
                <el-col :span="6">
                  <el-form-item label="是否结项" prop="isCompletion" class="custom-form-item-bac"
                                :rules="[{ required: true, message: '请选择是否结项', trigger: 'blur' }]">
                    <el-select v-model="isCompletion" placeholder="请选择" @change="handleIsCompletionChange"
                               class="width-100">
                      <el-option label="是" :value="true"></el-option>
                      <el-option label="否" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="isCompletion">
                  <el-form-item label="结项时间" prop="closingTime" class="custom-form-item-bac">
                    <el-date-picker clearable v-model="rant.closingTime" type="date"
                        class="width-100 custom-date-picker"
                        value-format="yyyy-MM-dd" placeholder="选择结项时间" :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="成果" prop="achievement" class="custom-form-item-bac"
                                :rules="[{ required: isCompletion, message: '请上传成果文件', trigger: 'blur' }]">
                    <section class="file-wrapper">
                      <label v-if="!fileList || fileList.length === 0" :for="'uploadFile_0'"
                             class="cursor"
                             :class="{'custom-warning': !!rantAchievementFiles.fileListError,}">
                        <i class="el-icon-folder-add" style="color: #3673FF;font-size: 16px;"></i>
                        <span>{{ rantAchievementFiles.fileListError || " 请上传文件" }}</span>
                      </label>
                      <section v-else>
                        <div v-for="(file, index) in fileList" :key="index" class="file-show"
                             @click="triggerFileUpload(index)">
                          <i class="el-icon-document cursor" style="color: #3673FF;font-size: 16px;"></i>
                          <a :href="file.url" class="link" style="margin-bottom: 0;" target="_blank" :title="file.name">{{
                              file.name || "--"
                            }}</a>
                          <i class="el-icon-circle-close cursor link"
                             @click.stop="handleDeleteAnnexUrl($event, index)"></i>
                        </div>
                      </section>
                    </section>
                    <input type="file" class="display-none" multiple :id="'uploadFile_0'" @change="uploadFileHandle"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-progress.png" class="icon">进度情况</div>
            <el-table :data="rant.recordDtoList" style="width: 100%">
              <el-table-column align="center" label="序号" prop="id" width="80" :header-row-class-name="'header-row'">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="实际进展" align="center" prop="actualProgress">
                <template slot-scope="scope">
                  <div v-html="scope.row.actualProgress"></div>
                </template>
              </el-table-column>
              <el-table-column label="汇报时间" align="center" width="100" prop="feedbackTime">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.feedbackTime, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column label="成果" align="center" width="300" prop="achievementFileUrl">
                <template slot-scope="scope">
                  <section style="display: flex; flex-direction: column">
                    <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" class="link"
                       target="_blank">
                      {{ file.name || "--" }}</a>
                  </section>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-buttons">
      <el-button class="custom-btn-save" type="primary" @click="submitForm(0)">保 存</el-button>
      <el-button class="custom-btn-submit" type="primary" @click="submitForm(1)">提交</el-button>
    </div>
  </div>
</template>
<script>
import {getMatters, feedbackRankMatter} from "@/api/rant/matters";
import {submitFeedback, deptLeaderApprove} from "@/api/rant/record";
import {uploadFileMultiple} from "@/api/rant/common";
import mixin from '../mixins/mixin'
export default {
  name: "feedback",
  mixins: [mixin],
  dicts: ["rant_classify", "rant_completion_status", "rant_matters_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      rantDetail: {}, // 吐槽详情
      recordList: [], // 进度记录
      fileList: [],
      rantAchievementFiles: {}, // 附件信息
      uploadFileLoading: false,
      isCompletion: false,
      rantList: [],
      collapseStates: [], // 控制每个事项的折叠状态
      submitStatus: 0,
      rules: {
        closingTime: [
          {required: true, message: "请选择结项时间", trigger: ["blur", "change"]}
        ],
        /*  achievement: [
           {
             required: true,
             message: "请上传成果文件",
             trigger: "blur"
           }
         ], */
        /*  isCompletion: [{
           validator: (rule, value, callback) => {
             if (value === true || value === false) {
               callback();
             } else {
               callback(new window.Error('请选择是否结项'));
             }
           },
           trigger: ['blur', 'change']
         },
         {
           required: true,
           message: "请选择是否结项",
           trigger: "blur"
         }
       ] */
      },
      type: '',
      // isEnable: null,
      // limitDay: null,
    };
  },

  mounted() {
    const id = this.$route.query.id;
    this.handleGetRantDetail(id);

  },
 /* async created() {
    await this.getLimitDayIsEnable();
  },*/
  methods: {
   /* async getLimitDayIsEnable() {
      let isEnable = await this.getConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
      let limitDay = await this.getConfigKey("rant.finish.date.limit.day"); // 设置限制天数
      this.isEnable = parseInt(isEnable.msg);
      this.limitDay = parseInt(limitDay.msg);
    },*/
    handleIsCompletionChange(value) {
      this.isCompletion = value;
    },
    handleGetRantDetail(id) {
      feedbackRankMatter(id).then((response) => {
        this.rantList = [response.data];
        this.rantDetail = response.data;
        this.recordList = response.data.recordDtoList;
        this.fileList = response.data.fileList || [];
        this.isCompletion = response.data.isCompletion;
        this.loading = false;
      });
    },

    uploadFileHandle(event) {
      const files = event.target.files;
      if (!files) {
        // 没有选择文件
        return;
      }
      this.loading = true;
      this.uploadFileLoading = true;
      const formData = new FormData();
      for (let item of files) {
        formData.append("files", item);
      }
      uploadFileMultiple(formData)
        .then((response) => {
          this.fileList = [...this.fileList, ...response.data]

          this.$forceUpdate();
          this.uploadFileLoading = false;
        })
        .catch((error) => {
          this.uploadFileLoading = false;
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDeleteAnnexUrl(event, index) {
      event.stopPropagation();
      this.fileList.splice(index, 1);
    },
    triggerFileUpload(index) {
      document.getElementById('uploadFile_0').click();
    },
    /** 提交按钮 */
    submitForm(submitStatus) {
      console.log(this.rantDetail.closingTime);

      this.loading = true;
      let fileList = this.fileList;
      if (!this.rantDetail.thisProgress) {
        this.$modal.msgError("请输入本次进展");
        this.loading = false;
        return;
      }

      // 当"是否结项"为"是"时，结项时间始终是必填的
      if (this.isCompletion) {
        // 确保将选择的结项时间从rant对象同步到rantDetail对象
        if (this.rant && this.rant.closingTime) {
          this.rantDetail.closingTime = this.rant.closingTime;
        }

        if (!this.rantDetail.closingTime) {
          this.$modal.msgError("请输入结项时间");
          this.loading = false;
          return;
        }

        // 当"是否结项"为"是"且是最终提交时，成果文件是必填的
        if (submitStatus == 1 && (!this.fileList || this.fileList.length == 0)) {
          this.$modal.msgError("请上传成果文件");
          this.loading = false;
          return;
        }
      }

      this.rantDetail.isCompletion = this.isCompletion;
      let rantDetail = this.rantDetail;
      rantDetail.fileList = fileList;
      rantDetail.rantMattersId = this.rantDetail.id;
      rantDetail.submitStatus = submitStatus;
      submitFeedback(rantDetail).then((response) => {
        if (submitStatus === 1) {
          this.$modal.msgSuccess("提交成功");
        } else {
          this.$modal.msgSuccess("保存成功");
        }

        this.$tab.closeOpenPage({
          path: "/rant/myTask",
        });
        this.$bus.$emit("refreshRantTaskList"); // 触发计划反馈页面刷新
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  position: relative;
  overflow: hidden;
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  overflow-y: hidden;
  height: calc(100vh - 86px);
}

.rant-container {
  /*  height: calc(100vh - 86px);
  overflow-y: auto;
  padding-bottom: 60px; !* 为了防止底部按钮遮挡内容 *!*/
  margin-bottom: 100px;
  overflow-y: auto;
  height: calc(100vh - 86px);
}

.expand {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #3673FF;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    transition: transform 0.3s ease;

    &.is-active {
      transform: rotate(180deg);
    }
  }
}

.rant-content {
  transition: all 0.3s ease-out;
  background: rgba(255, 255, 255, 0.7);
  margin-bottom: 100px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 13px 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.bottom-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  text-align: center;
  z-index: 1000;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  background-color: #EDF7F9;
}

:deep(.custom-form-item-bac input) {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(55, 109, 247, 0.3);
  background: #F1F7FE;
}

:deep(.custom-form-item-bac .el-form-item__label) {
  line-height: 36px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

:deep(.el-form-item__content) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__header .el-table__cell) {
  background: rgba(54, 115, 255, 0.1);
}

:deep(.el-table__header .el-table__cell .cell) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__body .el-table__row .el-table__cell) {
  background-color: #F3F8FC;
}

:deep(.el-table__empty-block) {
  background-color: #F3F8FC;
}

.custom-btn-save {
  width: 160px;
  background: #C8DDFA;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  color: #3673FF;
}

.custom-btn-submit {
  width: 160px;
  background: #3673FF;
  border-radius: 4px 4px 4px 4px;
  color: #FFFFFF;
}

.rant-detail-content {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0px 0px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
}

.rant-detail {
  margin-bottom: 44px;
  padding: 19px 16px 0 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.rant-form-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 18px;
  display: flex;
  align-items: center;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.icon-primary {
  color: #409eff;
}

.cursor {
  cursor: pointer;
}

.display-none {
  display: none !important;
}

.file-show {
  padding: 2px 0px;
}

.link {
  max-width: calc(100% - 60px);
}

.file-wrapper {
  width: 400px;
  min-height: 32px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(55, 109, 247, 0.3);

  > label {
    display: inline-block;
    width: 100%;
    height: 100%;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 12px;
  }

  .el-icon-circle-close {
    margin-left: 10px;
  }
}

/*.file-show{
  display: flex;
  min-width: 0;
}
.link {
  color: #409eff;
  margin-bottom: 8px;
  margin-left: 8px;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
}*/

.transfer-item {
  display: flex;

  .item {
    text-align: center;
    margin-right: 5px;
  }
}

.bottom-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  text-align: center;
  z-index: 1000;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  background-color: #EDF7F9;
}

</style>
