<template>
  <el-dialog title="选择节点" :visible.sync="showAllNode" width="1200px" append-to-body v-if="showAllNode">
    <div class="app-container" :style="{ '--color': theme }">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="节点名称" prop="nodeName">
          <el-input style="display: none;"></el-input>
          <el-input v-model="queryParams.nodeName" @keyup.enter.native="handleQuery" placeholder="请输入节点名称" clearable size="small"/>
<!--          @keyup.enter.native="handleQuery"-->
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="nodeList" @selection-change="handleSelectionChange" height="50vh" ref="table">
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column label="序号" align="center" prop="id" width="60">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="节点名称" align="center" prop="nodeName" width="150" show-overflow-tooltip />
        <el-table-column label="计划开始时间" align="center" prop="startTime" width="160">
          <template slot-scope="scope">
            <div class="time">
              <el-date-picker width="150" clearable size="small" v-model="scope.row.startTime" type="date"
                value-format="yyyy-MM-dd" placeholder="计划开始时间" :disabled="true">
              </el-date-picker>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="计划完成时间" align="center" prop="endTime" width="160">
          <template slot-scope="scope">
            <div class="time">
              <el-date-picker width="150" clearable size="small" v-model="scope.row.endTime" type="date"
                value-format="yyyy-MM-dd" placeholder="计划完成时间" :disabled="true">
              </el-date-picker>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
        label="实际完成时间"
        align="center"
        prop="completTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
        <el-table-column label="责任部门" align="center" prop="departmentArr" width="250" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-select :disabled="true" v-model="scope.row.departmentArr" multiple placeholder="请选择" size="small"  @change="selectChange($event,scope.$index)">
              <el-option
                v-for="item in departmentsData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="反馈人" align="center" prop="feedbackUserName" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>
              {{ scope.row.feedbackUserName || "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否可用" align="center" prop="isValid" width="140">
          <template slot-scope="scope">
            <el-select v-model="scope.row.isValid" clearable :disabled="true">
              <el-option v-for="option in isValidOption" :key="option.value" :label="option.label"
                :value="option.value"></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      /> -->
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmFun">确 定</el-button>
      <el-button @click="cancelDailog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {
    listNode,
    save,
    submit,
    importNodeFile
  } from "@/api/plan/node"
  import SelectTree from '@/components/SelectTree.vue'
  import {
    isValidOption
  } from '@/views/plan/constant'
  import variables from '@/assets/styles/element-variables.scss'
  import SelectUser from "../components/SelectUser/index.vue"
  import SelectNodeInfo from "./selectNodeInfo.vue"
  import AddNodeForm from "./addNodeForm.vue"

  export default {
    name: "Node",
    components: {
      SelectTree,
      SelectUser,
      SelectNodeInfo,
      AddNodeForm
    },
    props: ['defaultSelect','departmentsData', 'adjustId'],
    // dicts: ['levels', 'node_stage'],
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 计划-节点表格数据
        nodeList: [],
        // 弹出层标题
        // 是否显示弹出层
        // 查询参数
        queryParams: {
          // pageNum: 1,
          // pageSize: 20,
          nodeLevel: null,
          nodeName: null,
          planId: null,
        },
        editIndex: null,
        clickIndex: null,
        /*专业成果*/
        resultList: [],
        showResultDialog: false,
        ids2: [],
        //责任人
        respectPeople: [],
        resultConfigIds: [],
        theme: variables.theme,
        showDepart: false,
        planId: null,
        selectData: [],
        selectNodeCodes: [],
        showAllNode: false
      }
    },
    created() {},
    computed: {
      isValidOption: () => isValidOption,
    },
    watch: {},
    methods: {
      getList() {
        this.loading = true
        this.queryParams.planId = this.adjustId;
        listNode(this.queryParams).then(response => {
          let da=response.rows
          da.map(item=>{
            item.departmentArr=item.department.length?item.department.split(','):[]
          })
          // console.log(da, this.selectNodeCodes)
          this.nodeList = da.filter(item => !this.selectNodeCodes.includes(item.nodeCode))
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.editIndex = null
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm")
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectData = selection
      },
      show() {
        this.clickIndex = null
        this.showAllNode = true
        this.queryParams.planId = this.$route.query.planId
        this.selectNodeCodes = this.defaultSelect
        this.getList()
      },
      confirmFun() {
        if (!this.selectData.length) {
          return this.$message('请选择节点')
        }
        this.selectData.map(item => {
          item.adjustType = item.adjustType === 0 ? 1 : item.adjustType || 1
        })
        this.$emit('handleData', this.selectData)
        this.showAllNode = false
      },
      cancelDailog() {
        this.handleQuery()
        this.showAllNode = false
      }
    },
  };
</script>
<style lang="scss" scoped>
  @import "@/assets/styles/index.scss";

  .time ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 150px;
  }

  .iconStyle {
    color: var(--color);
    font-weight: 600;
    font-size: 20px;
    cursor: pointer;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
</style>
