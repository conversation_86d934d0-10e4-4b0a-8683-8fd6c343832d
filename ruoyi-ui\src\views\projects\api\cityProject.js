import request from '@/utils/request'

// 获取城市公司下所有项目信息
function allProjectData(cityId) {
  return request({
    url: `/project/cityProject/allProject/data/${cityId}`,
    method: 'get',
  })
}

// 根据项目编码获取项目基本信息
function getProjectInfo(projectCode) {
  return request({
    url: `/project/cityProject/getProject/${projectCode}`,
    method: 'get',
  })
}

// 根据项目编码获取项目全盘指标
function getProjectIndex(projectCode) {
  return request({
    url: `/project/cityProject/getProjectIndex/${projectCode}`,
    method: 'get',
  })
}

function getAllProjects () { // 根据当前用户获取所有城市公司和项目信息
  return request({
    url: '/project/cityProject/allProject/data',
    method: 'get',
  })
}

export default {
  allProjectData,
  getProjectInfo,
  getProjectIndex,
  getAllProjects
}
