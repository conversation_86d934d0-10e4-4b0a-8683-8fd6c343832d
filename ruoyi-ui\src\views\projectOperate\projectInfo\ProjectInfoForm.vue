<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="200px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="城市公司" prop="cityId">
            <!--              <el-input v-model="form.cityId" placeholder="请输入城市公司uuid"/>-->
            <el-select v-model="form.cityId" placeholder="请选择城市公司" :disabled="disabled"
                       @change="handleCityChange" class="width-100">
              <el-option
                v-for="item in cites"
                :key="item.cityId"
                :label="item.cityName"
                :value="item.cityId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目" prop="projectCode">
            <el-select v-model="form.projectCode" placeholder="请选择项目" :disabled="disabled"
                       @change="handleProjectChange" class="width-100">
              <el-option
                v-for="item in projects"
                :key="item.projectCode"
                :label="item.displayName"
                :value="item.projectCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案名" prop="name">
            <el-input v-model="form.name" :disabled="disabled" placeholder="请输入案名"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地块名" prop="plotName">
            <el-input v-model="form.plotName" :disabled="disabled" placeholder="请输入地块名"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目公司注册名" prop="companyRegisterName">
            <el-input v-model="form.companyRegisterName" :disabled="disabled" placeholder="请输入项目公司注册名"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人、总经理及归属公司" prop="belongCompanyName">
            <el-input v-model="form.belongCompanyName" type="textarea" :disabled="disabled" placeholder="请输入法人、总经理及归属公司"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="股权占比" prop="shareholdRate">
            <el-input v-model="form.shareholdRate" type="textarea" :disabled="disabled" placeholder="请输入股权占比"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操盘条线" prop="operateStripline">
            <el-input v-model="form.operateStripline" :disabled="disabled" type="textarea" placeholder="请输入内容"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="并表方">
            <el-input
              :disabled="disabled"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="form.combineTableContent"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="拿地时间" prop="takeLandDate">
            <el-date-picker clearable size="small"
                            class="width-100"
                            :disabled="disabled"
                            v-model="form.takeLandDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择拿地时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首次开盘时间" prop="firstOpenDate">
            <el-date-picker clearable size="small"
                            class="width-100"
                            :disabled="disabled"
                            v-model="form.firstOpenDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择首次开盘时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首次竣备时间关联标识" prop="firstCompletedDateFlag">
            <el-select v-model="form.firstCompletedDateFlag" placeholder="请选择首次竣备时间关联标识" :disabled="disabled" class="width-100">
              <el-option
                v-for="item in firstCompletedDateFlagOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首次竣备时间" prop="firstCompletedDate">
            <el-date-picker clearable size="small"
                            class="width-100"
                            :disabled="disabled"
                            v-model="form.firstCompletedDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择首次竣备时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首批合同交付时间关联标识" prop="firstCompletedDateFlag">
            <el-select v-model="form.firstDeliverDateFlag" placeholder="请选择首批合同交付时间关联标识" :disabled="disabled" class="width-100">
              <el-option
                v-for="item in firstDeliverDateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首批合同交付时间" prop="firstDeliverDate">
            <el-date-picker clearable size="small"
                            class="width-100"
                            :disabled="disabled"
                            v-model="form.firstDeliverDate"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择首批合同交付时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="土地费用(亿，不含契税)" prop="landCost">
            <el-input v-model="form.landCost" :disabled="disabled" placeholder="请输入土地费用(亿，不含契税)"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计容面积㎡" prop="capacityArea">
            <el-input v-model="form.capacityArea" :disabled="disabled" placeholder="请输入计容面积㎡"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="可售面积㎡" prop="canSalesArea">
            <el-input v-model="form.canSalesArea" :disabled="disabled" placeholder="请输入可售面积㎡"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="限价政策" prop="constraintPricePolicy">
            <el-input v-model="form.constraintPricePolicy" :disabled="disabled" type="textarea"
                      placeholder="请输入内容"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计容楼面价(元)" prop="capacityPrice">
            <el-input v-model="form.capacityPrice" :disabled="disabled" placeholder="请输入计容楼面价"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="可售楼面价(元)" prop="canSalesPrice">
            <el-input v-model="form.canSalesPrice" :disabled="disabled" placeholder="请输入可售楼面价"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用地面积" prop="useLandArea">
            <el-input v-model="form.useLandArea" :disabled="disabled" placeholder="请输入用地面积"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="容积率/限高(米)" prop="plotRatio">
            <el-input v-model="form.plotRatio" type="textarea" :disabled="disabled" :rows="5" placeholder="请输入容积率/限高(米)"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="配套情况" prop="supportSituation">
            <el-input v-model="form.supportSituation" :disabled="disabled" type="textarea" :rows="5" placeholder="请输入内容"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="竣备交付形式" prop="completedDeliverModality">
            <el-input v-model="form.completedDeliverModality" :disabled="disabled" type="textarea" :rows="5"
                      placeholder="请输入内容"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼栋图" prop="buildingPic">
            <!--              <el-input v-model="form.buildingPic" placeholder="请输入楼栋图"/>-->
            <label class="el-icon-plus cursor icon-primary upload" v-if="!form.buildingPic">
              <input type="file" class="display-none" style="display: none"
                     @change="(event) => uploadFileHandle(event, 'buildingPic')"/>
            </label>
            <el-image
              v-else
              style="width: 100px; height: 100px"
              :src="form.buildingPic"
              :preview-src-list="[form.buildingPic]">
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="鸟瞰图" prop="birdPic">
            <!--              <el-input v-model="form.birdPic" placeholder="请输入鸟瞰图"/>-->
            <label class="el-icon-plus cursor icon-primary upload" v-if="!form.birdPic">
              <input type="file" class="display-none" style="display: none"
                     @change="(event) => uploadFileHandle(event, 'birdPic')"/>
            </label>
            <el-image
              v-else
              style="width: 100px; height: 100px"
              :src="form.birdPic"
              :preview-src-list="[form.birdPic]">
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区位图" prop="locationPic">
            <!--              <el-input v-model="form.locationPic" placeholder="请输入区位图"/>-->
            <label class="el-icon-plus cursor icon-primary upload" v-if="!form.locationPic">
              <input type="file" class="display-none" style="display: none"
                     @change="(event) => uploadFileHandle(event, 'locationPic')"/>
            </label>
            <el-image
              v-else
              style="width: 100px; height: 100px"
              :src="form.locationPic"
              :preview-src-list="[form.locationPic]">
            </el-image>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="bottom-buttons" v-if="!disabled">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import {getInfo} from "@/api/projectOperate/projectInfo";
import API from '@/views/projects/api'

export default {
  name: "ProjectInfoForm",
  props: {
    mode: {
      type: String,
      default: 'add',
      required: true
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      form: {
        //   projectId: null,
        //   projectCode: null,
        //   projectName: null,
        //   cityId: null,
        //   cityName: null,
        //   projectType: null,
        //   projectStatus: null,
        //   projectAddress: null,
        //   startDate: null,
        //   endDate: null,
        //   projectManager: null,
        //   contactPhone: null,
        //   remark: null
      },
      rules: {
        //   cityId: [
        //     { required: true, message: "请选择城市公司", trigger: "change" }
        //   ],
        //   projectId: [
        //     { required: true, message: "请选择项目", trigger: "change" }
        //   ],
        //   projectType: [
        //     { required: true, message: "请选择项目类型", trigger: "change" }
        //   ],
        //   projectStatus: [
        //     { required: true, message: "请选择项目状态", trigger: "change" }
        //   ],
        //   projectAddress: [
        //     { required: true, message: "请输入项目地址", trigger: "blur" }
        //   ],
        //   startDate: [
        //     { required: true, message: "请选择开工日期", trigger: "change" }
        //   ],
        //   endDate: [
        //     { required: true, message: "请选择竣工日期", trigger: "change" }
        //   ],
        //   projectManager: [
        //     { required: true, message: "请输入项目负责人", trigger: "blur" }
        //   ],
        //   contactPhone: [
        //     { required: true, message: "请输入联系电话", trigger: "blur" },
        //     { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        //   ]
      },
      cites: [],
      projects: [],
      // 项目类型选项
      projectTypeOptions: [],
      // 项目状态选项
      projectStatusOptions: [],
      firstCompletedDateFlagOptions: [{"label" : "是", "value" : 1}, {"label" : "否", "value" : 0}],
      firstDeliverDateOptions: [{"label" : "是", "value" : 1}, {"label" : "否", "value" : 0}]
    }
  },
  computed: {
    disabled() {
      return this.mode === 'detail'
    }
  },
  created() {
    this.getCities()
    /*this.getDicts("project_type").then(response => {
      this.projectTypeOptions = response.data
    })
    this.getDicts("project_status").then(response => {
      this.projectStatusOptions = response.data
    })*/
    if (this.mode !== 'add') {
      this.getDetail()
    }
  },
  methods: {
    getCities() {
      API.Common.getCity().then(res => {
        if (res.code === 200) {
          this.cites = res.data
        } else {
          this.$message.error(res.message || '获取城市公司信息失败')
        }
      })
    },
    getProjects(cityId) {
      API.Common.getProject(cityId).then(res => {
        if (res.code === 200) {
          this.projects = res.data
        } else {
          this.$message.error(res.message || '获取项目信息失败')
        }
      })
    },
    getDetail() {
      if (!this.id) return
      getInfo(this.id).then(response => {
        this.form = response.data
        this.getProjects(response.data.cityId)
      })
    },
    handleCityChange() {
      this.form.cityName = this.cites.find(item => item.cityId === this.form.cityId)?.cityName
      this.projects = []
      // this.form.projectId = null
      this.form.projectName = null
      this.form.projectCode = null
      this.getProjects(this.form.cityId)
    },
    handleProjectChange() {
    /*  const selectedProject = this.projects.find(item => item.projectCode === this.form.projectCode)
      if (selectedProject) {
        this.form.projectName = selectedProject.projectName
        this.form.projectCode = selectedProject.projectCode
      }*/
      const project = this.projects.find(item => item.projectCode === this.form.projectCode)
      this.form.projectName = project.displayName
      this.$forceUpdate()
    },
    uploadFileHandle(event, type) {
      const files = event.target.files
      if (!files) { //
        // 没有选择文件
        return
      }
      const formData = new FormData()
      for (let item of files) {
        formData.append('file', item)
        console.log('item----', item)
      }
      console.log(formData)
      API.Common.uploadFile(formData).then(response => {
        this.form[type] = response?.data?.url
        this.$forceUpdate()
      })
        .catch(error => {
        })
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', this.form)
        }
      })
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.bottom-buttons {
  margin-top: 20px;
  text-align: center;
}

.width-100 {
  width: 100% !important;
}

.upload {
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-align: center;
  line-height: 100px;
}
</style>
