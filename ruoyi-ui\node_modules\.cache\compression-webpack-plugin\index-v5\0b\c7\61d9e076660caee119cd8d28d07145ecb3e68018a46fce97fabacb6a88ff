
c3256e03315d0e28c809cfcf2175fe7ecadb6abe	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.dba5734cbc02c9c79bc6.hot-update.js\",\"contentHash\":\"70be71b79b2bfa89c19bbfe6c5729f54\"}","integrity":"sha512-SNytcson6q2+T/u/S3EQkdoWd2ZiQsn9uvaXzKw0A3DjRrvZ/32RF8patug4XUu2Z/d0KuVMt8qzUNamu3M0yA==","time":1754312351579,"size":78156}