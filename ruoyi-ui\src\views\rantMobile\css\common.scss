.empty-data{
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  padding: 50px 0;
}
.file-name {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #3673FF;
    line-height: 14px;
    text-align: right;
    font-style: normal;
    white-space: nowrap;      /* 禁止换行 */
    overflow: hidden;        /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    width: 180px;
  }

  /* Vant 样式覆盖 */
  :deep(.van-field__control) {
    background-color: #f5f7fa;
    min-height: 30px;
    font-size: 14px;
    color: #333;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
  }

  :deep(.van-button) {
    border-radius: 4px;
  }

  :deep(.van-dropdown-menu) {
    width: 100%;
    background-color: transparent;
    box-shadow: none;
  }

  :deep(.van-dropdown-item__option) {
    padding: 10px 12px;
  }

  .app-container {
    background: #D6E1F1;
    padding: 12px 16px 66px;
    min-height: 100vh;
    box-sizing: border-box;
  }

  .rant-content {
    margin-bottom: 10px;
    background: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;
  }

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #FFFFFF;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }

  .expand {
    font-weight: 400;
    font-size: 14px;
    color: #3673FF;
    cursor: pointer;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      transition: transform 0.3s ease;

      &.is-active {
        transform: rotate(180deg);
      }
    }
  }

  .rant-detail-content {
    padding: 12px;
    background: #FFFFFF;
  }

  .rant-form-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .form-group {
    // margin-bottom: 16px;
    // background: #F8FAFC;
    border-radius: 4px;
    padding: 8px;

    &.text-area {
      display: flex;
      flex-direction: column;

      .form-label {
        margin-bottom: 8px;
        font-weight: 500;
      }

      .form-value {
        line-height: 1.6;
      }
    }
  }

  .progress-textarea {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(55, 109, 247, 0.3);
  }

  :deep(.van-field__control) {
    background-color: unset;
  }

  .form-item {
    display: flex;
    // min-height: 40px;
    padding: 11px 0;
    align-items: center;
    // margin-bottom: 12px;

    .form-label {
      width: 100px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
      text-align: left;
      flex-shrink: 0;
    }

    .form-value {
      flex: 1;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 16px;
      text-align: left;
    }
  }

  .file-wrapper {
    width: 100%;
    text-align: right;

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .file-item {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      gap: 8px;
    }

 /*   .file-name {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #3673FF;
      line-height: 14px;
      text-align: left;
      font-style: normal;

      white-space: nowrap;      !* 禁止换行 *!
      overflow: hidden;        !* 隐藏超出部分 *!
      text-overflow: ellipsis; !* 显示省略号 *!
      width: 180px;
    }*/

    .delete-icon {
      color: #ee0a24;
      font-size: 18px;
    }

    .file-error {
      color: #ee0a24;
      border-color: #ee0a24;
    }
  }

  .progress-list {
    background: #F8FAFC;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 20px;
  }

  .progress-item {
    padding: 12px;
    border-bottom: 1px solid #EBEEF5;

    &:last-child {
      border-bottom: none;
    }
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    .progress-number {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 16px;
    }

    .progress-date {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }
  }

  .progress-content {
    line-height: 1.6;
    margin-bottom: 8px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
  }

  .progress-files {
    background: #F2F6FC;
    padding: 8px;
    border-radius: 4px;

    .files-title {
      margin-bottom: 8px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 16px;
    }

    .file-link {
      margin-bottom: 12px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #3673FF;
      line-height: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .no-data {
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    padding: 20px 0;
  }

  .bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px;
    background: #FFFFFF;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    gap: 12px;
    z-index: 10;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
    }

    .custom-btn-save {
      background: #C8DDFA;
      color: #3673FF;
    }

    .custom-btn-submit {
      background: #3673FF;
      color: #FFFFFF;
    }
  }

  .display-none {
    display: none !important;
  }

  .completion-switch {
    display: flex;
    justify-content: flex-end;

    .switch-btn {
      width: 60px;
      height: 28px;
      background: #E8EFF8;
      // border-radius: 4px 4px 4px 4px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 26px;
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        background: #376DF7;
        color: #F2F7FD;
      }
    }
  }

  .align-right {
    text-align: right !important;
  }

  .margin-top-0 {
    margin-top: 0 !important;
  }

  .padding-top-10 {
    padding-top: 10px !important;
  }

  .mb-10 {
    margin-bottom: 10px !important;
  }
  :deep(.rant-container .el-loading-mask) {
      background-color: unset;
      height: 20vh;
  }
