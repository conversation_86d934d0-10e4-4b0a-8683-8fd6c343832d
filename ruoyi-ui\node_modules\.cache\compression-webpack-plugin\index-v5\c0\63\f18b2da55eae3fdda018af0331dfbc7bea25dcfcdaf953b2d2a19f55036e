
ac93cdd7502c54d8089b94cb043fa056ea746c06	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"cba0823556dc76ae0ece498690d935cc\"}","integrity":"sha512-qQrT0mSK9F+FgOiHc7A0pyvJu2T225aktWyarQdqQarkXOOv3vc898nfi2n+Ru3L5qPp3HdXwZfCIKARCjfBZw==","time":1754311560456,"size":12044795}