import request from '@/utils/request'
// 获取全景计划项目分期集合
function qjPlanStages(projectCode) {
  return request({
    url: `/project/plan/qjStages/${projectCode}`,
    method: 'get',
  })
}

// 获取对应分期的关键节点
function keyNodes(projectDataPlan) {
  return request({
    url: '/project/plan/keyNodes',
    method: 'post',
    data: projectDataPlan
  })
}

// 获取对应分期的节点轨道图
function nodeTrackMap(projectDataPlan) {
  return request({
    url: '/project/plan/nodeTrackMap',
    method: 'post',
    data: projectDataPlan
  })
}

// 节点统计信息
function nodeCount(projectDataPlan) {
  return request({
    url: '/project/plan/nodeCount',
    method: 'post',
    data: projectDataPlan
  })
}

// 全景计划延期未完成节点
function qjDelayNoFinish(projectDataPlan) {
  return request({
    url: '/project/plan/qjDelayNoFinish',
    method: 'post',
    data: projectDataPlan
  })
}

export default {
  qjPlanStages,
  keyNodes,
  nodeTrackMap,
  nodeCount,
  qjDelayNoFinish
}
