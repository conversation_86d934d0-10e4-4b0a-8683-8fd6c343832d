<!--责任部门负责人审批完结项的事项，需要给类型管理员发送审批待办-->
<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container padding-top-10" v-loading="loading">
      <!-- 页面头部 -->
      <!--  <div class="page-header">
        <div class="back-icon" @click="cancel">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="page-title">{{ rantTitle || '督办审批' }}</div>
      </div> -->

      <!-- 审批列表 -->
      <div v-for="(rant, index) in rantList" :key="index" class="rant-content margin-top-0">
        <div class="section-title" v-if="rantList.length > 1">
          <span>
            <van-checkbox v-model="rant.isSelected" @change="handleItemSelect" class="rant-checkbox"></van-checkbox>
            <img src="@/assets/rant/rant-item.png" class="icon">
            督办事项{{ index + 1 }}
          </span>
<!--          <section>
            <van-button type="primary" size="small" @click="handleSingleApprove(index)">同意</van-button>
            <van-button type="danger" size="small" plain @click="handleSingleReject(index)">驳回</van-button>
          </section>-->
          <section class="expand" @click="toggleCollapse(index)">
            <img src="@/assets/rant/rant-collapse.png" class="icon" :class="{'is-active': collapseStates[index]}">
            {{ !collapseStates[index] ? '收起' : '展开' }}
          </section>
        </div>

        <template v-if="!collapseStates[index]">
          <div class="rant-detail-content">
            <!-- 基本信息 -->
            <div class="rant-form-title">
              <img src="@/assets/rant/rant-info.png" class="icon">
              基本信息
            </div>

            <van-form class="rant-detail">
              <div class="form-group">
<!--                <div class="form-item">-->
<!--                  <div class="form-label">来源</div>-->
<!--                  <div class="form-value">{{ rant.ranterName }}</div>-->
<!--                </div>-->

                <div class="form-item">
                  <div class="form-label">类型</div>
                  <div class="form-value">
                    <dict-tag v-for="(type, typeIndex) in rant.mattersType?.split(',')"
                             :key="typeIndex"
                             :options="mattersTypeOptionsLabel"
                             :value="type" />
                  </div>
                </div>

                <div class="form-item">
                  <div class="form-label">分类</div>
                  <div class="form-value">
                    <dict-tag :options="classifyOptionsLabel" :value="rant.rantClassify" />
                  </div>
                </div>

                <div class="form-item">
                  <div class="form-label">计划完成时间</div>
                  <div class="form-value">{{ rant.planTime }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任人</div>
                  <div class="form-value">{{ rant.responsiblePersonName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任部门</div>
                  <div class="form-value">{{ rant.deptName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任部门负责人</div>
                  <div class="form-value">{{ rant.respDeptResponsiblerName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">分管领导</div>
                  <div class="form-value">{{ rant.respDeptLeaderName }}</div>
                </div>


                <div class="form-item" v-if="type == 'matters'">
                  <div class="form-label">是否私密</div>
                  <div class="form-value">{{ rant.isPrivate == 1 ? "是" : "否" }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">内容</div>
                  <div class="form-value content-text">{{ rant.rantContent }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">措施</div>
                  <div class="form-value content-text">{{ rant.solution }}</div>
                </div>
              </div>

              <!-- 本次进展 -->
              <div class="rant-form-title" style="margin-top:16px;">
                <img src="@/assets/rant/rant-this-progress.png" class="icon">
                本次进展
              </div>

              <div class="form-group text-area">
                <textarea
                  class="progress-textarea"
                  v-model="rant.thisProgress"
                  rows="6"
                  placeholder="请输入本次进展"
                  :readOnly="true"
                ></textarea>
              </div>

              <div class="form-group">
                <div class="form-item">
                  <div class="form-label">是否结项</div>
                  <div class="form-value">{{ rant.isCompletion ? '是' : '否' }}</div>
                </div>

                <div class="form-item" v-if="rant.isCompletion">
                  <div class="form-label">结项时间</div>
                  <div class="form-value">{{ rant.closingTime }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">成果</div>
                  <div class="form-value">
                    <div v-if="rant.fileList && rant.fileList.length > 0" class="file-list">
                      <div v-for="(file, fileIndex) in rant.fileList" :key="fileIndex" class="file-item">
                        <a :href="file.url" target="_blank" class="file-link">
                          <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
                          <span class="extension-part">{{ getFileExtension(file.name) }}</span>
                          <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
                        </a>
                      </div>
                    </div>
                    <div v-else class="form-empty">暂无文件</div>
                  </div>
                </div>
              </div>
            </van-form>

            <!-- 进度情况 -->
            <ProgressList :progressList="rant.recordDtoList" />
           <!--  <div class="rant-form-title" style="margin-top:16px;">
              <img src="@/assets/rant/rant-progress.png" class="icon">
              进度情况
            </div>

            <div class="progress-list mb-100px" v-if="rant.recordDtoList && rant.recordDtoList.length > 0">
              <div v-for="(record, recordIndex) in rant.recordDtoList" :key="recordIndex" class="progress-item">
                <div class="progress-header">
                  <div class="progress-number">进度 {{ recordIndex + 1 }}</div>
                  <div class="progress-date">{{ parseTime(record.feedbackTime, "{y}-{m}-{d}") }}</div>
                </div>
                <div class="progress-content" v-html="record.actualProgress"></div>
                <div class="progress-files" v-if="record.fileList && record.fileList.length > 0">
                  <div class="files-title">附件：</div>
                  <div v-for="(file, fileIndex) in record.fileList" :key="fileIndex" class="file-link">
                    <a :href="file.url" target="_blank" style="display: inline-flex;max-width: 100%;">
                      <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
                      <span class="extension-part">{{ getFileExtension(file.name) }}</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-data  mb-100px">暂无进度记录</div> -->

            <!-- 单项操作按钮 (仅在多项时显示) -->
            <div class="item-actions" v-if="rantList.length > 1 && !isRead">
              <van-button type="primary" size="small" @click="handleSingleApprove(index)">同意</van-button>
              <van-button type="danger" size="small" plain @click="handleSingleReject(index)">驳回</van-button>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="empty-data" v-if="!loading && rantList.length === 0">
      暂无反馈事项
    </div>
    <!-- 底部按钮 - 单项审批 -->
    <div class="bottom-buttons" v-if="rantList.length === 1 && !isRead" >
      <van-button class="custom-btn-submit" @click="handleSingleApprove(0)">同 意</van-button>
      <van-button class="custom-btn-danger" @click="handleSingleReject(0)">驳 回</van-button>
    </div>

    <!-- 底部按钮 - 批量审批 -->
    <div class="bottom-buttons" v-if="rantList.length > 1 && !isRead">
      <div class="select-all">
        <van-checkbox v-model="isAllSelected" @change="handleSelectAll(isAllSelected)">全选</van-checkbox>
      </div>
      <van-button class="custom-btn-submit" type="primary" @click="handleBatchApprove">批量同意</van-button>
      <van-button class="custom-btn-danger" type="danger" plain @click="handleBatchReject">批量驳回</van-button>
    </div>

    <!-- 审批弹窗 -->
    <van-popup
      v-model="approveDialog.visible"
      position="center"
      round
      closeable
      @close="cancelApprove"
      class="approve-popup"
    >
      <div class="popup-title">{{ approveDialog.type === 'reject' ? '驳回意见' : '审批意见' }}</div>

      <div class="popup-content">
        <van-field
          v-model="approveDialog.form.approveDesc"
          type="textarea"
          rows="4"
          :placeholder="approveDialog.type === 'reject' ? '请输入驳回意见' : '请输入审批意见'"
          :rules="[{ required: approveDialog.type === 'reject', message: '请输入驳回意见' }]"
        />
      </div>

      <div class="popup-footer">
        <van-button plain @click="cancelApprove">取 消</van-button>
        <van-button type="primary" @click="submitApprove" class="custom-btn-submit" :loading="approveDialog.loading">确 定</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
// http://localhost/wechatE/mobile/rant/todoCenterManagerApprove?todoNoticeId=8
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/

import { close } from '@/utils';
import { getMatters, approveTodoInfo, submitFeedback } from "@/api/rantMobile/matters";
import { adminApprove } from "@/api/rantMobile/record";
import { uploadFileMultiple } from "@/api/rantMobile/common";
import mixin from '../mixins'
import ProgressList from '../components/ProgressList/index.vue'
// 导入Vant组件
import {
  Form,
  Field,
  Button,
  Checkbox,
  CheckboxGroup,
  Dialog,
  Toast,
  Popup,
  Icon
} from 'vant';

export default {
  name: "Feedback",
  mixins: [mixin],
  // dicts: ["rant_classify", "rant_completion_status", "rant_matters_type"],
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
    ProgressList
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      rantId: null,
      type: "matters",
      // 列表数据
      rantList: [],
      // 是否折叠
      collapseStates: [],
      // 是否全选
      isAllSelected: false,
      // 督办标题
      rantTitle: "",
      // 当前的附件
      currentFiles: [],
      // 上传的附件
      rantAchievementFiles: {
        uploadLoading: false,
        fileList: [],
        fileListError: "",
      },
      // 折叠状态 collapsed - 折叠, expanded - 展开
      expandState: 'expanded',
      // 审批弹窗
      approveDialog: {
        visible: false,
        loading: false,
        type: 'approve', // approve - 同意, reject - 驳回
        form: {
          approveDesc: '',
          selectedIds: []
        },
        rules: {
          approveDesc: [
            { required: true, message: '请输入审批意见', trigger: 'blur' }
          ]
        }
      },
      todoNoticeId: this.$route.query.todoNoticeId,
      isRead: false
    };
  },
  async created() {
    await this.fetchMattersType();
    await this.fetchRantClassify();

    console.log('类型选项:', this.mattersTypeOptionsLabel);
    console.log('分类选项:', this.classifyOptionsLabel);

    this.handleGetRantList(this.todoNoticeId);
  },
  mounted() {
    // 添加自动滚动到顶部
    const container = document.querySelector('html');
    if (container) {
      container.scrollTop = 0;
    }
  },
  computed: {
    // 选中的项目ID
    selectedIds() {
      return this.rantList
        .filter(item => item.isSelected)
        .map(item => ({
          mattersId: item?.id,
          progressFeedbackId: item?.thisProgressFeedbackId
        }));
    },
    // 是否有选中项
    hasSelected() {
      return this.selectedIds.length > 0;
    }
  },
  methods: {
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    // 取消并关闭页面
    cancel() {
      close();
    },

    // 切换展开/折叠状态
    toggleCollapse(index) {
      this.$set(this.collapseStates, index, !this.collapseStates[index]);
    },

    // 全选/取消全选
    handleSelectAll(value) {
      this.rantList.forEach(item => {
        this.$set(item, 'isSelected', value);
      });
      this.isAllSelected = value;
    },

    // 单项选择变更
    handleItemSelect() {
      const allSelected = this.rantList.every(item => item.isSelected);
      const anySelected = this.rantList.some(item => item.isSelected);

      this.isAllSelected = allSelected;

      // 如果没有任何选中项，禁用批量操作按钮
      if (!anySelected) {
        // 这里可以添加禁用批量操作按钮的逻辑
      }
    },

    /**
     * 处理审批操作
     * @param {Object} options 选项对象
     * @param {string} options.type 操作类型 approve-同意 reject-驳回
     * @param {boolean} options.isBatch 是否批量操作
     * @param {number} options.index 单项操作的索引
     */
    handleApprove(options) {
      const { type, isBatch, index } = options;

      // 重置表单
      this.approveDialog.form.approveDesc = '';
      this.approveDialog.type = type;

      // 设置选中的事项ID
      if (isBatch) {
        // 批量操作：获取所有选中的事项ID
        if (!this.hasSelected) {
          Toast('请至少选择一项');
          return;
        }
        this.approveDialog.form.selectedIds = this.selectedIds;
      } else {
        // 单个操作：获取指定索引的事项ID
        this.approveDialog.form.selectedIds = [{
          mattersId: this.rantList[index]?.id,
          progressFeedbackId: this.rantList[index]?.thisProgressFeedbackId
        }];
      }

      // 驳回时需要填写意见，同意时直接提交
      if (type === 'reject') {
        this.approveDialog.visible = true;
      } else {
        this.submitApprove();
      }
    },

    /**
     * 提交审批
     */
    submitApprove() {
      const { type, form } = this.approveDialog;

      // 如果是驳回操作，需要验证是否填写了意见
      if (type === 'reject' && !form.approveDesc) {
        Toast('请输入驳回意见');
        return;
      }

      this.approveDialog.loading = true;
      this.loading = true;

      adminApprove({
        todoNoticeId: this.todoNoticeId,
        approveStatus: type === 'approve' ? 1 : 2,
        feedbackApproveVoList: form.selectedIds,
        approveDesc: type === 'reject' ? form.approveDesc : undefined
      }).then(async(response) => {
        Toast.success("提交成功");
        this.approveDialog.visible = false;

        // 刷新列表
        await this.handleGetRantList(this.todoNoticeId);
       /*  if(this.rantList.length === 0) {
          Toast('暂无待审批事项');
          setTimeout(() => {
            close();
          }, 1500);
        } */
        if(this.isRead) {
          Toast('待办已完结，即将关闭页面');
          setTimeout(() => {
            close();
          }, 1500);
        }
      }).catch(() => {
        Toast.fail("提交失败");
      }).finally(() => {
        this.approveDialog.loading = false;
        this.loading = false;
      });
    },

    /**
     * 取消审批
     */
    cancelApprove() {
      this.approveDialog.visible = false;
      this.approveDialog.form.approveDesc = '';
      this.approveDialog.form.selectedIds = [];
      this.loading = false;
    },

    // 快捷方法
    handleSingleApprove(index) {
      this.handleApprove({ type: 'approve', isBatch: false, index });
    },

    handleBatchApprove() {
      this.handleApprove({ type: 'approve', isBatch: true });
    },

    handleSingleReject(index) {
      this.handleApprove({ type: 'reject', isBatch: false, index });
    },

    handleBatchReject() {
      this.handleApprove({ type: 'reject', isBatch: true });
    },

    async handleGetRantList(todoNoticeId) {
      this.loading = true;
      try {
        const response = await approveTodoInfo(todoNoticeId);
        this.rantList = response.data?.rankMattersFeedbackDtoList || [];
        this.rantTitle = response.data?.title;
        this.isRead = response.data?.isRead;
        // 初始化折叠状态，默认折叠
        this.collapseStates = new Array(this.rantList.length).fill(true);
        if (this.collapseStates.length > 0) {
          this.collapseStates[0] = false; // 设置第一个元素为 false
        }
        this.$nextTick(() => {
          const container = document.querySelector('html');
          if (container) {
            container.scrollTop = 0;
          }
        });
        // 如果列表为空，关闭页面
        /* if (this.rantList.length === 0) {
          Toast('暂无待审批事项');
          setTimeout(() => {
            close();
          }, 1500);
        } */
      } catch (error) {
        Toast.fail('获取列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 文件操作相关方法
    uploadFileHandle(event, index) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      this.rantAchievementFiles.uploadLoading = true;
      this.rantAchievementFiles.fileListError = "";

      // 构建 FormData
      const formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        formData.append("files", files[i]);
      }

      // 上传文件
      uploadFileMultiple(formData)
        .then((res) => {
          if (res.code === 200) {
            // 将上传成功的文件添加到列表
            const fileList = res.data.map(item => ({
              name: item.fileName,
              url: item.filePath
            }));

            // 更新当前审批项的文件列表
            if (!this.rantList[index].fileList) {
              this.rantList[index].fileList = [];
            }
            this.rantList[index].fileList.push(...fileList);

            // 清空文件输入框
            event.target.value = "";
          } else {
            this.rantAchievementFiles.fileListError = res.msg || "上传失败";
          }
        })
        .catch(() => {
          this.rantAchievementFiles.fileListError = "上传失败";
        })
        .finally(() => {
          this.rantAchievementFiles.uploadLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../css/common.scss';
.mb-100px{
  margin-bottom: 100px;
}
.cu-flex {
  display: flex;
  align-items: center;
  gap: 2px;
}
.app-container-feedback {
  padding: 0;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
}
.rant-container {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
}
.page-header {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #FFFFFF;
  padding: 0 16px;
  position: relative;

  .back-icon {
    font-size: 18px;
    color: #333;
    padding: 8px;
    margin-left: -8px;
  }

  .page-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 500;
    color: #333;
  }
}

.rant-content {
  margin: 12px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.margin-top-0 {
  margin-top: 0;
}

.padding-top-10 {
  padding-top: 10px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid #EBEEF5;

  span {
    display: flex;
    align-items: center;
  }

  .rant-checkbox {
    margin-right: 8px;
  }
}

.rant-detail-content {
  padding: 16px;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.form-group {
  margin-bottom: 16px;
}

.form-empty {
  color: #999;
  font-size: 14px;
}

.empty-data {
  text-align: center;
  color: #999;
  padding: 20px 0;
  font-size: 14px;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.progress-textarea {
  width: 100%;
  border: 1px solid rgba(55, 109, 247, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #F1F7FE;
  min-height: 100px;
  font-size: 14px;
  color: #333333;
  outline: none;
  resize: vertical;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;

  .select-all {
    margin-right: auto;
  }
}
.custom-btn-submit {
    width: 160px;
    height: 40px;
    background: #3673FF;
    color: #FFFFFF;
    margin-right: 16px;
    border-radius: 20px;
  }

  .custom-btn-danger {
    width: 160px;
    height: 40px;
    background: #FFFFFF;
    color: #ee0a24;
    border: 1px solid #ee0a24;
    border-radius: 20px;
  }

  .custom-btn-small {
    height: 36px;
    margin-left: 12px;
    border-radius: 18px;
  }

.approve-popup {
  width: 90%;
  max-width: 500px;
  border-radius: 8px;

  .popup-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
  }

  .popup-content {
    padding: 16px;
  }

  .popup-footer {
    display: flex;
    border-top: 1px solid #ebedf0;

    .van-button {
      flex: 1;
      height: 40px;
      margin: 0;
      border: none;
      border-radius: 0;

      &:first-child {
        border-right: 1px solid #ebedf0;
      }
    }
  }
}
.form-item{
  display: flex;
  .form-value{
    flex: 1;
    min-width: 0;
  }
}
.file-link {
  color: #376DF7;
  font-size: 12px;
  font-weight: 400;
  max-width: 100%;
  display: flex;
  align-items: center;
  min-width: 0;
  gap: 0;

  .filename-part {
    min-width: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .extension-part {
    flex-shrink: 0;
    white-space: nowrap;
  }

  .download-icon {
    flex-shrink: 0;
    margin-left: 8px;
    font-size: 14px;
    color: #376DF7;
  }
}
</style>
