import request from '@/utils/request'

// 查询可见范围详情列表
export function listScopedetail(query) {
  return request({
    url: '/rant/scopedetail/list',
    method: 'get',
    params: query
  })
}

// 查询可见范围详情详细
export function getScopedetail(id) {
  return request({
    url: '/rant/scopedetail/' + id,
    method: 'get'
  })
}

// 新增可见范围详情
export function addScopedetail(data) {
  return request({
    url: '/rant/scopedetail',
    method: 'post',
    data: data
  })
}

// 修改可见范围详情
export function updateScopedetail(data) {
  return request({
    url: '/rant/scopedetail',
    method: 'put',
    data: data
  })
}

// 删除可见范围详情
export function delScopedetail(id) {
  return request({
    url: '/rant/scopedetail/' + id,
    method: 'delete'
  })
}
