<template>
  <div class="app-container">
    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" prop="id" width="55">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column-->
        <!--label="主责专业"-->
        <!--align="center"-->
        <!--prop="professionId"-->
        <!--width="80"-->
      <!--&gt;-->
        <!--<template slot-scope="scope">-->
          <!--<dict-tag-->
            <!--:options="dict.type.departs"-->
            <!--:value="scope.row.professionId"-->
          <!--/>-->
        <!--</template>-->
      <!--</el-table-column>-->
      <el-table-column label="城市公司" align="center" prop="deptName">
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" width="160" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="分期" align="center" prop="stageId" width="55">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stages" :value="scope.row.stageId" />
        </template>
      </el-table-column>
      <el-table-column
        label="计划编码"
        align="center"
        prop="templateId"
        show-overflow-tooltip
        width="160"
      />
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
        width="160" show-overflow-tooltip/>
      <el-table-column
        label="版本号"
        align="center"
        prop="version"
        width="100"
      />
      <el-table-column label="状态" align="center" prop="status" >
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="showDialogFun(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.status != 0"
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="fetchApprovalUser(scope.row)"
            >查看审批流</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="计划明细编制"
      :visible.sync="showDialog"
      width="1200px"
      append-to-body
      v-if="showDialog"
    >
      <PlanAndNodeInfo @cancel="cancelDialog" :data="rowData" />
    </el-dialog>
  </div>
</template>

<script>
import { getApproveFlow, listhistory } from "@/api/plan/info"
import PlanAndNodeInfo from '@/views/plan/components/PlanAndNodeInfo/index.vue'
import {  nodeStatusOption } from '@/constant'
import StatusTag from "@/views/plan/components/StatusTag/index.vue"

export default {
  name: "Info",
  dicts: ['sys_second_institution', 'departs', 'stages',],
  components: {
    PlanAndNodeInfo,StatusTag
  },
  props: ['data'],
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPlanNode: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划编制表格数据
      infoList: [],
      // 计划-节点表格数据
      planNodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        planCode: null,
        version: null
      },
      // 表单参数
      showDialog: false,
      rowData: null
    }
  },
  mounted () {
    this.queryParams.planCode = this.data.planCode
    this.queryParams.version = this.data.version
    this.queryParams.id = this.data.id
    this.getList()
    // //获取部门列表
    // this.$store.dispatch('plan/fetchDepartList')
    // //获取城市公司
    // this.$store.dispatch('plan/fetchDeptList')
  },
  watch: {
  },
  computed: {
    nodeStatusOption: function () {
      return nodeStatusOption
    },
    allTemplateList () {
      console.log(this.$store.state.plan)
      return this.$store.state.plan.allTemplateList
    },
    cityCompanys () {
      return this.$store.state.plan.deptList
    }
  },
  methods: {
    fetchApprovalUser (row) {
      this.loading = true
      getApproveFlow(row.id).then(res => {
        // this.dialogVisible = true
        // this.approveUserInfo = res.data
        this.loading = false
        if (res.data) {
          window.open(res.data)
        }
      }).catch(err => {
        this.loading = false

      })
    },
    cancelDialog () {
      this.getList()
      this.showDialog = false
    },
    showDialogFun (row) {
      this.showDialog = true
      this.rowData = row
    },

    /** 查询计划编制列表 */
    getList () {
      this.loading = true
      listhistory(this.queryParams).then(response => {
        console.log(JSON.parse(JSON.stringify(response)))
        this.infoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        deptNum: null,
        professionId: null, // 专业
        projectId: null, //项目
        stageId: null, //分期
        templateId: null,
        planName: null, //项目名称
        planExplain: ''
      }
      this.planNodeList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 计划-节点序号 */
    rowPlanNodeIndex ({
      row,
      rowIndex
    }) {
      row.index = rowIndex + 1
    },
    /** 计划-节点添加按钮操作 */
  }
};
</script>
<style scoped>
.infoStyle {
  height: 34px;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
</style>
