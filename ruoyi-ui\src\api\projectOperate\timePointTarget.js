import request from '@/utils/request'

// 查询项目时点目标列表
export function listTimePointTarget(query) {
  return request({
    url: '/project/timePointTarget/list',
    method: 'get',
    params: query
  })
}

// 查询项目时点目标详细
export function getTimePointTarget(query) {
  return request({
    url: '/project/timePointTarget/getInfo',
    method: 'get',
    params: query
  })
}

// 新增项目时点目标
export function addTimePointTarget(data) {
  return request({
    url: '/project/timePointTarget',
    method: 'post',
    data: data
  })
}

// 修改项目时点目标
export function updateTimePointTarget(data) {
  return request({
    url: '/project/timePointTarget',
    method: 'put',
    data: data
  })
}

// 删除项目时点目标
export function delTimePointTarget(id) {
  return request({
    url: '/project/timePointTarget/' + id,
    method: 'delete'
  })
}

// 签约时点目标数据导入
export function importQyData(data) {
  return request({
    url: '/project/timePointTarget/importQyData',
    method: 'post',
    data: data
  })
}

// 回款时点目标数据导入
export function importHyData(data) {
  return request({
    url: '/project/timePointTarget/importHyData',
    method: 'post',
    data: data
  })
}

// 删除工程项目信息
export function deleteData(data) {
  return request({
    url: '/project/timePointTarget/deleteData',
    method: 'delete',
    data: data
  })
}


// 现金流时点目标数据导入
export function importXjlData(data) {
  return request({
    url: '/project/timePointTarget/importXjlData',
    method: 'post',
    data: data
  })
}

// 查询项目时点目标详细
export function getXjlTimePointTarget(query) {
  return request({
    url: '/project/timePointTarget/getXjlInfo',
    method: 'get',
    params: query
  })
}
