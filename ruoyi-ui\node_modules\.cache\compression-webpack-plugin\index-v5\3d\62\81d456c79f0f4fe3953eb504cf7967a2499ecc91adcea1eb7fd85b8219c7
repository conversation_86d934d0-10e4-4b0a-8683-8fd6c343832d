
b77a7dcf29288de619818dd901025d5c7aaf429c	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"1a5673d94ebb71e1e916aec0d13a8e75\"}","integrity":"sha512-z8+BVvfs8hnLkltzVDEXwn3BlJQKnULv2kw0D3Uxp7VMxP0e0+Exh53ClUyxUSI7xvNpCnf3chx2/dda5HXMlw==","time":1754311457240,"size":12045114}