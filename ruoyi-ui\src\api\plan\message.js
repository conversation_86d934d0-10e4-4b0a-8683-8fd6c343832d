import request from '@/utils/request'

// 查询我的消息列表
export function listMessage(query) {
  return request({
    url: '/plan/message/list',
    method: 'get',
    params: query
  })
}

// 查询我的消息详细
export function getMessage(id) {
  return request({
    url: '/plan/message/' + id,
    method: 'get'
  })
}

// 获取我的消息配置
export function getMessageConfig(id) {
  return request({
    url: '/plan/message/config',
    method: 'get'
  })
}

// 修改我的消息配置
export function updateMessageConfig(data) {
  return request({
    url: '/plan/message',
    data: data,
    method: 'put'
  })
}
// 新增我的消息
/*export function addMessage(data) {
  return request({
    url: '/plan/message',
    method: 'post',
    data: data
  })
}*/

// 修改我的消息
/*export function updateMessage(data) {
  return request({
    url: '/plan/message',
    method: 'put',
    data: data
  })
}*/

// 删除我的消息
/*export function delMessage(id) {
  return request({
    url: '/plan/message/' + id,
    method: 'delete'
  })
}*/

// 致远OA是否登录过
export function oaIsLogin() {
  return request({
    url: '/plan/message/oaIsLogin',
    method: 'get'
  })
}

// 致远OA登录成功缓存15分钟
export function oaLogin() {
  return request({
    url: '/plan/message/oaLogin',
    method: 'get'
  })
}
