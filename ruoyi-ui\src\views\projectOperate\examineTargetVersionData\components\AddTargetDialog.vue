<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="visible"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div class="mb-20">
        <el-form ref="form" :model="form" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="版本日期" prop="versionDate">
                <el-date-picker
                  v-model="form.versionDate"
                  type="month"
                  placeholder="请选择版本日期"
                  value-format="yyyyMM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本说明" prop="versionDesc">
                <el-input
                  v-model="form.versionDesc"
                  placeholder="请输入版本说明"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-table
        :data="examineTargets"
        border
        style="width: 100%"
        height="500"
        row-key="id"
        :tree-props="{children: 'childList'}"
        default-expand-all
      >
        <el-table-column
          prop="targetName"
          label="指标名称"
        />
        <el-table-column
          label="金额"
        >
          <template slot-scope="scope">
            <div class="amount-input-wrapper">
              <el-input
                v-model.number="scope.row.amount"
                type="number"
                placeholder="请输入金额"
              />
              <span
                v-if="['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(scope.row.targetName.replace(/[\d\s]/g, ''))"
                class="percent-sign">%</span>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button v-if="type === 'add'" type="primary" @click="handleSubmit">确 定</el-button>
        <el-button v-if="type === 'edit'" type="primary" @click="handleEdit">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
import {updateVersion, editVersion} from "@/api/projectOperate/examineTargetVersionData";

export default {
  name: 'AddTargetDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '考核指标新增'
    },
    type: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      examineTargets: [],
      form: {
        versionDate: '',
        versionType: '',
        versionDesc: '', // 版本说明
        version: '',
        examineTargetVersionDataId: '',
      },
      rules: {
        versionDate: [
          {required: true, message: '请选择版本日期', trigger: 'change'}
        ],
      },
      loading: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      if (this.loading) return;
      this.loading = true;
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        const params = {
          examineTargetVersionDataId: this.form.examineTargetVersionDataId, // 考核指标版本数据id
          versionType: this.form.versionType, // 版本类型(1-可研版、2-投决版、3-目标版、4-动态版、5-后评价版)
          versionDate: this.form.versionDate,
          versionDesc: this.form.versionDesc,
          childList: this.examineTargets // 指标数据集合
        }
        try {
          const res = await updateVersion(params)
          console.log(res)
          if (res.code === 200) {
            this.$emit('success', res)
          } else {
            this.$message.error(res.msg)
          }
        } finally {
          this.loading = true;
        }

      })
    },
    async handleEdit() {
      if (this.loading) return;
      this.loading = true;
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        const params = {
          examineTargetVersionDataId: this.form.examineTargetVersionDataId, // 考核指标版本数据id
          versionType: this.form.versionType, // 版本类型(1-可研版、2-投决版、3-目标版、4-动态版、5-后评价版)
          versionDate: this.form.versionDate,
          versionDesc: this.form.versionDesc,
          version: this.form.version,
          childList: this.examineTargets // 指标数据集合
        }
        editVersion(params).then(res => {
          if (res.code === 200) {
            this.$emit('success', res)
          } else {
            this.$message.error(res.msg)
          }
        })
          .finally(() => {
            this.loading = false;
          })
      })

    }
  }
}
</script>

<style scoped>
.mb-20 {
  margin-bottom: 20px;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
}

.percent-sign {
  margin-left: 4px;
}

/* 新增样式：调整 footer 样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end; /* 按钮组靠右 */
  align-items: center; /* 垂直居中 */
  padding: 10px; /* 添加内边距 */
}
</style>
