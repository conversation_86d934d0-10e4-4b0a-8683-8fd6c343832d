import request from '@/utils/request'

// 查询周报部门领导信息列表
export function listLeader(query) {
  return request({
    url: '/weekly/leader/list',
    method: 'get',
    params: query
  })
}

// 查询周报部门领导信息详细
export function getLeader(id) {
  return request({
    url: '/weekly/leader/' + id,
    method: 'get'
  })
}

// 新增周报部门领导信息
export function addLeader(data) {
  return request({
    url: '/weekly/leader',
    method: 'post',
    data: data
  })
}

// 修改周报部门领导信息
export function updateLeader(data) {
  return request({
    url: '/weekly/leader',
    method: 'put',
    data: data
  })
}

// 删除周报部门领导信息
export function delLeader(id) {
  return request({
    url: '/weekly/leader/' + id,
    method: 'delete'
  })
}
