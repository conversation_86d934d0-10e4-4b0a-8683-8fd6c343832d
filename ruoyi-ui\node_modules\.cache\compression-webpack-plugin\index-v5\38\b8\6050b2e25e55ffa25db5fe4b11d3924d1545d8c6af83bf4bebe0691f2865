
fb4db22631033bb0366bed1d6bfba937b51ece6f	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"895add0ed8459f78be91ac34d93cf9aa\"}","integrity":"sha512-7YH4xvROLbTYGvmFrorzduPOAgiaEWRG7FDmfzNfvaVuONY8YatIAqhNluxsEom2dDtNVfOcubR1ekFn7c5vdQ==","time":1754311451767,"size":12045137}