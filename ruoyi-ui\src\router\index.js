import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: 'index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/index'),
  //       name: 'Index',
  //       meta: { title: '首页', icon: 'dashboard', affix: true }
  //     }
  //   ]
  // },
  {
    path: '',
    component: Layout,
    redirect: () => {
      const targetRoute = sessionStorage.getItem('targetRoute');
      return targetRoute || 'index';  // 如果有保存的目标路由则使用它，否则默认到index
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  // {
  //   path: '/plan',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'dashboard',
  //       component: () => import('@/views/plan/planDashboard/index'),
  //       name: 'dashboard',
  //       meta: { title: '计划查看', noCache: true}
  //     }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  // 企业微信周报--start
  {
    path: '/wechatE/mobile/combineApprove', // 移动端周报合并审阅
    component: () => import('@/views/weeklyMobile/approve'),
    name: 'mobileWeeklyApprove',
    hidden: true,
    meta: { title: '周报审阅' }
  },
  {
    path: '/wechatE/mobile/todoCenterWeeklyUpdate', // 待办中心周报修改
    component: () => import('@/views/weeklyMobile/todoCenterWeeklyUpdate'),
    name: 'mobileTodoCenterWeeklyUpdate',
    hidden: true,
    meta: { title: '周报修改' }
  },
  {
    path: '/wechatE/mobile/weekly', // 移动端周报主页入口
    component: () => import('@/views/weeklyMobile/index'),
    name: 'mobileWeekly',
    hidden: true,
    meta: { title: '周报' }
  },
  {
    path: '/wechatE/mobile/myWeekly', // 移动端我的周报列表
    component: () => import('@/views/weeklyMobile/myWeekly'),
    name:'mobileMyWeekly',
    hidden: true,
    meta: { title: '我的周报' }
  },
  {
    path: '/wechatE/mobile/weeklyApprove/:id', // 移动端周报审批
    component: () => import('@/views/weeklyMobile/weeklyApprove.vue'),
    name:'weeklyApprove',
    hidden: true,
    meta: { title: '周报审阅' }
  },
  {
    path: '/wechatE/mobile/approveList', // 移动端周报审批列表
    component: () => import('@/views/weeklyMobile/approveList'),
    name:'mobileApproveList',
    hidden: true,
    meta: { title: '我的审阅' }
  },
  {
    path: '/wechatE/mobile/weeklyInput', // 移动端周报填报
    component: () => import('@/views/weeklyMobile/weeklyInput'),
    name:'mobileWeeklyInput',
    hidden: true,
    meta: { title: '周报填写' }
  },
  {
    path: '/wechatE/mobile/weeklyDetail/:id', // 移动端周报详情
    component: () => import('@/views/weeklyMobile/weeklyDetail'),
    name:'mobileWeeklyDetail',
    hidden: true,
    meta: { title: '周报详情' }
  },
  {
    path: '/wechatE/mobile/approveDetail/:id', // 移动端审批详情
    component: () => import('@/views/weeklyMobile/approveDetail'),
    name:'mobileApproveDetail',
    hidden: true,
    meta: { title: '审批详情' }
  },
  {
    path: '/wechatE/mobile/todoCenterweeklyInput', // 移动端待办中心周报填报
    component: () => import('@/views/weeklyMobile/todoCenterWeeklyInput'),
    name:'mobileTodoCenterWeeklyInput',
    hidden: true,
    meta: { title: '周报填写' }
  },
  {
    path: '/wechatE/mobile/myWeeklyStatistics', // 移动端周报统计
    component: () => import('@/views/weeklyMobile/myStatistics'),
    name:'mobileMyStatistics',
    hidden: true,
    meta: { title: '周报统计' }
  },
  // 企业微信周报--end
  {
    path: '/IAMFeedback', // 从IAM系统跳转的节点反馈
    component: () => import('@/views/plan/IAMFeedback/feedback.vue'),
    name: 'IAMFeedback',
    hidden: true,
    meta: { title: '计划节点反馈维护' }
  },
  {
    path: '/weekly/todoCenterWeeklyInput', // 从IAM系统跳转的节点反馈
    component: () => import('@/views/weekly/myWeekly/todoCenterWeeklyInput.vue'),
    name: 'todoCenterWeeklyInput',
    hidden: true,
    meta: { title: '周报填写' }
  },
  {
    path: '/weekly/weeklyCombineApprove', // 待办中心批量审阅
    component: () => import('@/views/weekly/myWeekly/weeklyCombineApprove.vue'),
    name: 'weeklyCombineApprove',
    hidden: true,
    meta: { title: '周报审阅' }
  },
  {
    path: '/rant/todoDetail', // 待办中心督办事项详情查看
    component: () => import('@/views/rant/look/todoDetail.vue'),
    name: 'todoDetail',
    hidden: true,
    meta: { title: '督办事项详情' }
  },
  {
    path: '/rant/rantFinishDetail',
    component: () => import('@/views/rant/look/todoFinishDetail.vue'),
    name: 'rantFinishDetail',
    hidden: true,
    meta: { title: '结项详情' }
  },
  {
    path: '/plan',
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'adjustNodeInfo',
        component: () => import('@/views/plan/adjustNodeInfo/index.vue'),
        name: 'adjustNodeInfo',
        meta: { title: '调整节点信息' }
      },
      {
        path: 'feedbackDetail',
        component: () => import('@/views/plan/feedback/detail.vue'),
        name: 'feedbackDetail',
        meta: { title: '计划反馈详情' }
      },
      {
        path: 'interveneDetail',
        component: () => import('@/views/plan/intervene/detail.vue'),
        name: 'interveneDetail',
        meta: { title: '干预详情' }
      },
      {
        path: 'planDetail',
        component: () => import('@/views/plan/planDashboard/detail.vue'),
        name: 'planDetail',
        meta: { title: '计划详情' }
      },
      {
        path: 'feedbackUpdate',
        component: () => import('@/views/plan/feedback/feedback.vue'),
        name: 'feedbackUpdate',
        meta: { title: '节点反馈维护' }
      },
    ]
  },
  {
    path: '/planDetailBlank',
    hidden: true,
    component: () => import('@/views/plan/planDashboard/detail.vue'),
    name: 'planDetailBlank',
    meta: { title: '计划详情' }
  },
  {
    path: '/plan',
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'nodeInfo',
        component: () => import('@/views/plan/nodeInfo/index.vue'),
        name: 'nodeInfo',
        meta: { title: '计划节点信息' }
      }
    ]
  },
  {
    path: '/plan',
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'planNodeInfo',
        component: () => import('@/views/plan/planNodeInfo/index.vue'),
        name: 'planNodeInfo',
        meta: { title: '计划节点明细' }
      }
    ]
  },
  {
    path: '/plan',
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'historyVersion',
        component: () => import('@/views/plan/historyVersion/index.vue'),
        name: 'historyVersion',
        meta: { title: '历史版本' }
      }
    ]
  },
  {
    path: '/asset',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'assetList',
        component: () => import('@/views/asset/assetList/index'),
        name: 'assetList',
        meta: { title: '资产列表', noCache: true}
      }
    ]
  },
  // 项目经营看板
  {
    path: '/projects',
    hidden: true,
    component: () => import('@/views/projects/views/dashboard'),
    name: 'Projects',
    meta: { title: '项目经营看板', icon: 'el-icon-data-board'}
  },
  {
    path: '/projects/baseInfo',
    hidden: true,
    component: () => import('@/views/projects/views/baseInfo'),
    name: 'ProjectsBaseInfo',
    meta: { title: '基本信息', icon: ''}
  },
  {
    path: '/projects/example',
    hidden: true,
    component: () => import('@/views/projects/views/example'),
    name: 'ProjectsExample',
    meta: { title: '项目经营看板组件示例', icon: ''}
  },
  {
    path: '/projectOperate',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'add',
        component: () => import('@/views/projectOperate/projectInfo/add'),
        name: 'AddProjectInfo',
        meta: { title: '新增项目信息' }
      },
      {
        path: 'edit/:id',
        component: () => import('@/views/projectOperate/projectInfo/edit'),
        name: 'EditProjectInfo',
        meta: { title: '编辑项目信息' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/projectOperate/projectInfo/detail'),
        name: 'ProjectInfoDetail',
        meta: { title: '项目信息详情' }
      },
      {
        path: 'configBuilding/:id',
        component: () => import('@/views/projectOperate/projectInfo/configBuilding'),
        name: 'ConfigBuilding',
        meta: { title: '楼栋配置'}
      }
    ]
  },
  // 周报管理 ---周报管理：我的周报，我的审阅菜单在src/store/modules/permission.js中通过静态路由配置
 //  我的周报，我的审阅不受权限控制
  {
    "name": "Weekly",
    // 周报管理:不受权限控制的周报主菜单，通过静态路由配置。
    // 菜单管理中配置的周报管理的其他接受权限控制的菜单，会在src/store/modules/permission.js合并到这个菜单下
    "path": "/weekly",
    "hidden": false,
    "redirect": "noRedirect",
    "component": Layout,
    "alwaysShow": false,
    "meta": {
      "title": "周报管理",
      "icon": "log",
      "noCache": false,
      "link": null
    },
    children: [
      {
        path: 'myWeekly',
        component: () => import('@/views/weekly/myWeekly/index'),
        name: 'MyWeekly',
        meta: { title: '我的周报', "icon": "list"}
      },
      //  {
      //   path: 'approveList',
      //   hidden: false,
      //   component: () => import('@/views/weekly/myWeekly/approveList'),
      //   name: 'ApproveList',
      //   meta: { title: '我的审阅', "icon": "documentation" }
      // },
      {
        path: 'approveDaiBanList',
        // hidden: false,
        component: () => import('@/views/weekly/myWeekly/approveDaiBanList'),
        name: 'ApproveDaiBanList',
        meta: { title: '我的审阅', "icon": "documentation" }
      },
      { // -菜单配置
        path: 'weeklyDetail/:id',
        hidden: true,
        component: () => import('@/views/weekly/myWeekly/weeklyDetail'),
        name: 'WeeklyDetail',
        meta: { title: '周报详情' }
      },
      { // -菜单配置
        path: 'approveDaiBanDetail/:id',
        hidden: true,
        component: () => import('@/views/weekly/myWeekly/approveDaiBanDetail'),
        name: 'ApproveDaiBanDetail',
        meta: { title: '审批详情' }
      },
      { // -菜单配置
        path: 'approveDaiBan/:id',
        hidden: true,
        component: () => import('@/views/weekly/myWeekly/approveDaiBan'),
        name: 'ApproveDaiBan',
        meta: { title: '周报审批' }
      },
      { // -菜单配置
        path: 'weeklyInput',
        hidden: true,
        component: () => import('@/views/weekly/myWeekly/weeklyInput'),
        name: 'WeeklyInput',
        meta: { title: '周报填写', noCache: false  }
      },

      {
        path: 'weeklyApprove/:id',
        hidden: true,
        component: () => import('@/views/weekly/myWeekly/weeklyApprove'),
        name: 'WeeklyApprove',
        meta: { title: '周报审批' }
      },
    ]
  },
  //吐槽管理
  {
    path: '/rant',
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    meta: {noCache: true },
    children: [
      {
        path: 'rantDetail',
        component: () => import('@/views/rant/look/detail.vue'),
        name: 'rantDetail',
        meta: { title: '查看详情' }
      },
      {
        path: 'feedback',
        component: () => import('@/views/rant/feedback/index.vue'),
        name: 'feedback',
        meta: { title: '进度反馈' }
      },
      {
        path: 'addRant',
        component: () => import('@/views/rant/myRant/add.vue'),
        name: 'addRant',
        meta: { title: '新增吐槽'}
      },
     /*  {
        path: 'look',
        component: () => import('@/views/rant/look/index.vue'),
        name: 'look',
        meta: { title: '督办查看' }
      }, */
      {
        path: 'addMatter',
        component: () => import('@/views/rant/matters/add.vue'),
        name: 'addMatter',
        meta: { title: '添加督办事项' }
      },
      {
        path: 'updateVisibleScope',
        component: () => import('@/views/rant/matters/updateVisibleScope.vue'),
        name: 'updateVisibleScope',
        meta: { title: '督办事项编辑' }
      },
      {
        path: 'addFeedback',
        component: () => import('@/views/rant/myFeedback/add.vue'),
        name: 'addFeedback',
        meta: { title: '添加反馈' }
      },
      {
        path: 'myFeedbackDetail',
        component: () => import('@/views/rant/myFeedback/detail.vue'),
        name: 'myFeedbackDetail',
        meta: { title: '督办反馈详情', noCache: true }
      },
      {
        path: 'myFeedbackApproval',
        component: () => import('@/views/rant/myFeedback/approval.vue'),
        name: 'myFeedbackApproval',
        meta: { title: '审批详情' }
      },
      {
        path: 'myLookApproval',
        component: () => import('@/views/rant/look/approval.vue'),
        name: 'myLookApproval',
        meta: { title: '审批详情' }
      }

    ]
  },
  // 待办中心吐槽+反馈
  {
    path: '/rant/todoCenterAddFeedback',
    component: () => import('@/views/rant/myRant/todoCenterAddFeedback.vue'),
    name: 'todoCenterAddFeedback',
    hidden: true,
    meta: { title: '吐槽事项新增' }
  },
  {
    path: '/rant/todoCenterConfirmFeedback',
    component: () => import('@/views/rant/myRant/todoCenterConfirmFeedback.vue'),
    name: 'todoCenterConfirmMatter',
    hidden: true,
    meta: { title: '吐槽事项初审' }
  },
  {
    path: '/rant/todoCenterRejectFeedback',
    component: () => import('@/views/rant/myRant/todoCenterRejectFeedback.vue'),
    name: 'todoCenterRejectMatter',
    hidden: true,
    meta: { title: '吐槽事项确认' }
  },
  {
    path: '/rant/todoCenterAddMatter', // 待办中心督办事项添加
    /* query: { // 传递的参数
      type: "edit",
      id: row.id,
      isHavaSave: isHavaSave,
      deptId: row.deptId,
    }, */
    component: () => import('@/views/rant/matters/todoCenterAdd.vue'),
    name: 'todoCenterAddMatter',
    hidden: true,
    meta: { title: '修改督办事项' }
  },
  {
    path: '/rant/todoCenterFeedback25Combine',
    component: () => import('@/views/rant/feedback/todoCenterFeedback25Combine.vue'),
    name: 'todoCenterFeedback25Combine',
    hidden: true,
    meta: { title: '进度反馈' }
  },
  {
    path: '/rant/todoCenterFeedbackOverDateSingle',
    component: () => import('@/views/rant/feedback/todoCenterFeedbackOverDateSingle.vue'),
    name: 'todoCenterFeedbackOverDateSingle',
    hidden: true,
    meta: { title: '进度反馈' }
  },
  {
    path: '/rant/todoCenterFeedbackRejectSingle',
    component: () => import('@/views/rant/feedback/todoCenterFeedbackRejectSingle.vue'),
    name: 'todoCenterFeedbackRejectSingle',
    hidden: true,
    meta: { title: '进度反馈' }
  },
  {
    path: '/rant/todoCenterDeptApprove',
    component: () => import('@/views/rant/matters/todoCenterDeptApprove.vue'),
    name: 'todoCenterDeptApprove',
    hidden: true,
    meta: { title: '负责人审批' }
  },
  {
    path: '/rant/todoCenterManagerApprove',
    component: () => import('@/views/rant/matters/todoCenterManagerApprove.vue'),
    name: 'todoCenterManagerApprove',
    hidden: true,
    meta: { title: '管理员审批' }
  },
  {
    path: '/rant/todoCenterMattersNotice',
    component: () => import('@/views/rant/matters/todoCenterMattersNotice.vue'),
    name: 'todoCenterMattersNotice',
    hidden: true,
    meta: { title: '督办提醒' }
  },
  {
    //督办提醒事项详情
    path: '/rant/todoCenterMattersDetail',
    component: () => import('@/views/rant/look/todoCenterMattersDetail.vue'),
    name: 'todoCenterMattersDetail',
    hidden: true,
    meta: { title: '督办详情' }
  },
  // 企业微信待办中心督办移动端H5页面
  {
    path: '/wechatE/mobile/rant/todoCenterFeedback25Combine', // 25号推送进度反馈，一个合并的页面
    component: () => import('@/views/rantMobile/feedback/todoCenterFeedback25Combine.vue'),
    name: 'todoCenterFeedback25CombineMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterFeedbackOverDateSingle', // 督办事项进度填报
    component: () => import('@/views/rantMobile/feedback/todoCenterFeedbackOverDateSingle.vue'),
    name: 'todoCenterFeedbackOverDateSingleMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterFeedbackRejectSingle', // 督办事项进度填报
    component: () => import('@/views/rantMobile/feedback/todoCenterFeedbackRejectSingle.vue'),
    name: 'todoCenterFeedbackRejectSingleMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoDetail', // 督办事项详情
    component: () => import('@/views/rantMobile/look/todoDetail.vue'),
    name: 'todoDetailMobile',
    hidden: true,
    meta: { title: '督办事项详情' }
  },

  {
    path: '/wechatE/mobile/rant/todoCenterAddMatter', // 督办事项进度填报
    component: () => import('@/views/rantMobile/matters/todoCenterAdd.vue'),
    name: 'todoCenterAddMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterDeptApprove', // 督办事项进度填报
    component: () => import('@/views/rantMobile/matters/todoCenterDeptApprove.vue'),
    name: 'todoCenterDeptApproveMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterManagerApprove', // 督办事项进度填报
    component: () => import('@/views/rantMobile/matters/todoCenterManagerApprove.vue'),
    name: 'todoCenterManagerApproveMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterAddFeedback', // 督办事项进度填报
    component: () => import('@/views/rantMobile/myRant/todoCenterAddFeedback.vue'),
    name: 'todoCenterAddFeedbackMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterConfirmFeedback', // 督办事项进度填报
    component: () => import('@/views/rantMobile/myRant/todoCenterConfirmFeedback.vue'),
    name: 'todoCenterConfirmFeedbackMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterRejectFeedback', // 督办事项进度填报
    component: () => import('@/views/rantMobile/myRant/todoCenterRejectFeedback.vue'),
    name: 'todoCenterRejectFeedbackMobile',
    hidden: true,
    meta: { title: '督办事项进度填报' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterMattersNotice',
    component: () => import('@/views/rantMobile/matters/todoCenterMattersNotice.vue'),
    name: 'todoCenterMattersNoticeMobile',
    hidden: true,
    meta: { title: '督办提醒' }
  },
  {
    path: '/wechatE/mobile/rant/todoCenterMattersDetail', // 待办中心督办事项详情查看
    component: () => import('@/views/rantMobile/look/todoCenterMattersDetail.vue'),
    name: 'todoCenterMattersDetailMobile',
    hidden: true,
    meta: { title: '督办详情' }
  },
  {
    path: '/wechatE/mobile/rant/rantFinishDetail', // 待办中心督办事项详情查看
    component: () => import('@/views/rantMobile/look/todoFinishDetail.vue'),
    name: 'rantFinishDetailMobile',
    hidden: true,
    meta: { title: '结项详情' }
  },


]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/sales/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        // path: 'user/:roleId(\\d+)',
        path: 'user',
        component: () => import('@/views/sales/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/sales/role' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        // path: 'user/:roleId(\\d+)',
        path: 'user',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/project/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['project:dictType:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/projectOperate/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/projectOperate/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
]

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
