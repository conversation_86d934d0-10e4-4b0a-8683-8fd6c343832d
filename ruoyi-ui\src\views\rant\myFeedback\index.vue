<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" v-hasPermi="['rant:record:AllList']" type="card">
      <el-tab-pane label="我的" name="1"></el-tab-pane>
      <el-tab-pane label="所有" name="2"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型" prop="mattersType">
        <el-select v-model="queryParams.mattersType" multiple placeholder="请选择分类" clearable
          @keyup.enter.native="handleQuery" style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>

      </el-form-item>
      <el-form-item label="分类" prop="rantClassify">
        <el-select v-model="queryParams.rantClassify" placeholder="请选择分类" clearable @keyup.enter.native="handleQuery"
          style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
            :value="dict.label"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="事项状态" prop="mattersStatus">
        <el-select v-model="queryParams.mattersStatus" placeholder="事项状态" multiple clearable size="small" style="width: 200px;">
          <el-option v-for="dict in rantStatusOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈来源" prop="feedbackSource">
        <el-select v-model="queryParams.feedbackSource" placeholder="反馈来源" clearable size="small" style="width: 200px;">
          <el-option v-for="dict in dict.type.rant_feedback_source" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>

      </el-form-item>
      <el-form-item label="反馈状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="反馈状态" multiple clearable size="small" style="width: 200px;">
          <el-option v-for="dict in feedbackStatusOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="rantContent">
        <el-input v-model="queryParams.rantContent" placeholder="请输入内容" clearable size="small"
                  @keyup.enter.native="handleQuery" style="width: 200px;" maxlength="100"/>
      </el-form-item>

      <el-form-item v-if="activeTab === '2'" label="责任部门" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :normalizer="normalizer"
                    @input="handleSelectDept"
                    placeholder="选择责任部门" style="width: 200px;"/>
      </el-form-item>

      <el-form-item v-if="activeTab === '2'" label="责任人" prop="responsiblePerson">
        <el-input suffix-icon="el-icon-search" v-model="responsiblePersonNameQuery" placeholder="请选择责任人" clearable
                  @keyup.enter.native="handleQuery" @focus="selectUserFun()"
                  @change="changeresponsibilityer(responsiblePersonNameQuery)" style="width: 200px;"/>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" >
      <el-col v-if="activeTab == '1'" :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col v-if="activeTab == '2'" :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column align="center" label="序号" prop="id" width="50">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="事项状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <status-tag class="no-transition" :status="scope.row.mattersStatus" :options="rantStatusOption" />
        </template>
      </el-table-column>
      <el-table-column label="来源" align="center" prop="ranterName">
        <template slot-scope="scope">
          <el-tooltip class="ellipsis" effect="dark" :content="scope.row.ranterName" placement="top-start"
            popper-class="custom-tooltip">
            <span>{{ scope.row.ranterName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="类型" width="200" align="center" prop="mattersType">
        <template slot-scope="scope">
          <section class="matters-type-box">
            <dict-tag v-for="(type, index) in scope.row.mattersType.split(',')" :key="index"
              :options="dict.type.rant_matters_type" :value="type" />
          </section>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="rantClassify" width="80" />
      <el-table-column label="内容" align="center" prop="rantContent">
        <template slot-scope="scope">
          <el-tooltip class="ellipsis" effect="dark" :content="scope.row.rantContent" placement="top-start"
            popper-class="custom-tooltip">
            <span>{{ scope.row.rantContent }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="措施" align="center" prop="solution">
        <template slot-scope="scope">
          <el-tooltip class="ellipsis" effect="dark" :content="scope.row.solution" placement="top-start"
            popper-class="custom-tooltip">
            <span>{{ scope.row.solution }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column v-if="activeTab === '2'" label="责任部门" align="center" prop="deptName"  />
      <el-table-column v-if="activeTab === '2'" label="责任人" align="center" prop="responsiblePersonName" />
      <el-table-column label="反馈来源" align="center" prop="feedbackSource" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rant_feedback_source" :value="scope.row.feedbackSource"/>
        </template>
      </el-table-column>
      <el-table-column label="汇报时间" align="center" prop="feedbackTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.feedbackTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="本次进展" align="center" prop="actualProgress">
        <template slot-scope="scope">
          <el-tooltip class="ellipsis" effect="dark" :content="scope.row.actualProgress" placement="top-start"
            popper-class="custom-tooltip">
            <span
              :class="{ 'clickable-progress': scope.row.approveFlag == 1 }"
              @click="scope.row.approveFlag == 1 ? handleViewApproval(scope.row) : null">
              {{ scope.row.actualProgress }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="结项时间" align="center" prop="closingTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.closingTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="反馈状态" align="center" prop="status" >
        <template slot-scope="scope">
          <status-tag class="no-transition" :status="scope.row.status" :options="feedbackStatusOption" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="110">
        <template slot-scope="scope">
          <el-button  v-if="scope.row.status == 0 || scope.row.status == 3" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status == 0 || scope.row.status == 3" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row, scope.$index + 1)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
      @pagination="getList" />

    <!-- 添加或修改进度反馈记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="吐槽事项id" prop="rantMattersId">
          <el-input v-model="form.rantMattersId" placeholder="请输入吐槽事项id" />
        </el-form-item>
        <el-form-item label="反馈来源" prop="feedbackSource">
          <el-input v-model="form.feedbackSource" placeholder="请输入反馈来源" />
        </el-form-item>
        <el-form-item label="汇报时间" prop="feedbackTime">
          <el-date-picker clearable size="small" v-model="form.feedbackTime" type="date" value-format="yyyy-MM-dd"
            placeholder="选择汇报时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际进展" prop="actualProgress">
          <el-input v-model="form.actualProgress" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="成果文件路径" prop="achievementFileUrl">
          <el-input v-model="form.achievementFileUrl" placeholder="请输入成果文件路径" />
        </el-form-item>
        <el-form-item label="待办年" prop="toDoYear">
          <el-input v-model="form.toDoYear" placeholder="请输入待办年" />
        </el-form-item>
        <el-form-item label="待办月" prop="toDoMonth">
          <el-input v-model="form.toDoMonth" placeholder="请输入待办月" />
        </el-form-item>
        <el-form-item label="结项时间" prop="closingTime">
          <el-date-picker clearable size="small" v-model="form.closingTime" type="date" value-format="yyyy-MM-dd"
            placeholder="选择结项时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见" prop="approvalDesc">
          <el-input v-model="form.approvalDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <select-user ref="userRef" :roleId="roleName" @feedbackEmit="selectUserData"/>
  </div>
</template>

<script>
import { getRecord, listRecord, addRecord, delRecord } from "@/api/rant/record";
import { rantStatusOption,feedbackStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import { getInfo } from '@/api/login'
import Treeselect from "@riophae/vue-treeselect";
import {listRespdet} from "@/api/rant/respdet";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SelectUser from "@/views/plan/components/SelectUser/index.vue";

export default {
  name: "MyFeedback",
  components: {SelectUser, Treeselect, StatusTag },
  dicts: ['rant_classify', 'rant_matters_type', 'rant_feedback_source'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 进度反馈记录表格数据
      recordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 责任部门列表
      deptOptions: [],
      responsiblePersonNameQuery: '',
      roleName: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        responsiblePerson: null,
        mattersType: null,
        rantClassify: null,
        mattersStatus: null,
        feedbackSource: null,
        status: null,
        rantContent: null,
        deptId: null,

      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        rantMattersId: [
          { required: true, message: "吐槽事项id不能为空", trigger: "blur" }
        ],
        actualProgress: [
          { required: true, message: "实际进展不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ]
      },
      activeTab: '1'
    };
  },

  computed: {
    rantStatusOption() {
      return rantStatusOption
    },
    feedbackStatusOption() {
      return feedbackStatusOption
    }
  },
  created() {
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, "id");
    });
    this.queryParams.isPerson  = this.activeTab;
    this.getList()
  },
  methods: {
    handleTabClick(tab, event) {
      this.activeTab = tab.name;
      this.queryParams.isPerson  = this.activeTab;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleDetail(row) {
      this.$router.push({
        path: '/rant/myFeedbackDetail',
        query: {
          id: row.id
        }
      })
    },
    /** 查看审批详情 */
    handleViewApproval(row) {
      this.$router.push({
        path: '/rant/myFeedbackApproval',
        query: {
          id: row.id
        }
      })
    },
    /** 查询进度反馈记录列表 */
    getList() {
      this.loading = true;
      if (this.activeTab === '1') {
        this.queryParams.deptId = null
        this.queryParams.responsiblePerson = null
        this.responsiblePersonNameQuery = null
      }
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        rantMattersId: null,
        feedbackSource: null,
        feedbackTime: null,
        actualProgress: null,
        achievementFileUrl: null,
        toDoYear: null,
        toDoMonth: null,
        closingTime: null,
        status: 0,
        approvalDesc: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.responsiblePersonNameQuery = '';
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({
        path: '/rant/addFeedback',
        query: {
          type: 'add'
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id
      this.$router.push({
        path: '/rant/addFeedback',
        query: {
          id: id,
          type: 'edit'
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, index) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除进度反馈记录序号为"' + index + '"的数据项？').then(function () {
        return delRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('rant/record/export', {
        ...this.queryParams
      }, `反馈记录_${new Date().getTime()}.xlsx`)
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    handleSelectDept(value) {
      this.form.deptId = null
    },
    selectUserFun() {
      this.$refs.userRef.show()
    },
    selectUserData(data) {
      this.queryParams.responsiblePerson = data.userId
      this.responsiblePersonNameQuery = data.nickName
    },
    changeresponsibilityer() {
      if (this.responsiblePersonNameQuery == '' || this.responsiblePersonNameQuery == null) {
        this.queryParams.responsiblePerson = null
      }

    },
  }
};
</script>
<style lang="scss" scoped>
.matters-type-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
}

.clickable-progress {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
}

.clickable-progress:hover {
    color: #66b1ff;
}
</style>
