<template>
  <div class="example-container">
    <div class="example-content">
      <h1>头部Header</h1>
      <Header />
    </div>
    <h1>城市公司card</h1>
    <div class="example-card-content">
      <AmountCardWrapper :card-data="cardData" class="flex-1">
        <AmountCard :card-data="cardData2" type="orange" icon="file" class="item-card flex-1" />
        <AmountCard :card-data="cardData2" type="blue" icon="amount" class="item-card flex-1" />
        <AmountCard :card-data="cardData2" type="purple" icon="price" class="item-card flex-1" />
      </AmountCardWrapper>
      <AmountCardWrapper :card-data="cardData" class="flex-1">
        <AmountCard :card-data="cardData2" type="orange" icon="file" class="item-card flex-1" />
        <AmountCard :card-data="cardData2" type="blue" icon="amount" class="item-card flex-1" />
        <AmountCard :card-data="cardData2" type="purple" icon="price" class="item-card flex-1" />
      </AmountCardWrapper>
    </div>
    <div class="example-company-tree">
      <h1>公司项目选择树</h1>
      <CompanyTree />
    </div>
    <div class="example-tab">
      <h1>信息切换tab</h1>
      <Tab v-model="activeTab" :tabs="tabs" />
    </div>
    <div class="example-cumulative-indicators-card">
      <h1>累计指标</h1>
      <CumulativeIndicatorsCard
        value="2508.82"
        currency="¥"
        unit="亿"
        type="pink"
        label="累计签约金额"
      />
    </div>
    <div class="example-base-info-table">
      <h1>基本信息展示表格</h1>
      <BaseInfoTable />
    </div>
    <div class="example-node-orbit-diagram">
      <h1>节点轨道图</h1>
      <NodeOrbitDiagram :milestones="milestones"/>
    </div>
    <div class="example-line-trend">
      <h1>折线图</h1>
      <div id="lineTrend" />
    </div>
    <div class="example-progress-bar">
      <h1>进度条</h1>
      <progress-bar
        :value="259"
        :target="558.82"
        :show-scale="true"
        :show-bubble="true"
        :show-target="true"
      />
    <!--不带刻度的进度条 -->
    <progress-bar
        :value="8"
        :target="12"
        :show-scale="false"
        :show-bubble="true"
        :show-target="false"
      />
    </div>
  </div>
</template>
<script>
import Header from '@/views/projects/components/Header.vue';
import AmountCardWrapper from '@/views/projects/components/AmountCardWrapper.vue'
import AmountCard from '@/views/projects/components/AmountCard.vue'
import CompanyTree from '@/views/projects/components/CompanyTree.vue'
import Tab from '@/views/projects/components/Tab.vue'
import CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'
import BaseInfoTable from '@/views/projects/components/BaseInfoTable.vue'
import NodeOrbitDiagram from '@/views/projects/components/NodeOrbitDiagram.vue'
import LineTrendOption from '@/views/projects/constants/LineTrend'
import ProgressBar from '@/views/projects/components/ProgressBar.vue'
import echarts from 'echarts'
export default {
  name: 'Example',
  components: {
    Header,
    AmountCardWrapper,
    AmountCard,
    CompanyTree,
    Tab,
    CumulativeIndicatorsCard,
    BaseInfoTable,
    NodeOrbitDiagram,
    ProgressBar
  },
  data() {
    return {
      cardData: {
        title: '中建·宸庐',
        title2: '北京智地顺景房地产开发有限公司',
        time1: '2023-10-8',
        time2: '2024-5-10',
        operation1: '拿地',
        operation2: '开盘'
      },
      cardData2: {
        title: '销售额',
        amount: 10000,
        unit: '元'
      },
      tabs: [
        { label: '基本信息', value: 'basic' },
        { label: '计划', value: 'plan' },
        { label: '销售', value: 'sales' },
        { label: '货值', value: 'values' },
        { label: '成本', value: 'cost' },
        { label: '客关', value: 'relation' },
        { label: '营销费', value: 'marketingFees' },
        { label: '财务', value: 'finance' }
      ],
      activeTab: 'basic',
      milestones: [
        {
          "name": "获取项目",
          "date": "2024-01-10",
          "status": "complete",
          "details": "计划完成日期：2024-01-10，实际完成日期：2024-01-10，责任人：张三"
        },
        {
          "name": "方案批复",
          "date": "2024-02-12",
          "status": "complete",
          "details": "计划完成日期：2024-02-12，实际完成日期：2024-02-12，责任人：李四"
        },
        {
          "name": "工程规划许可证",
          "date": "2024-05-12",
          "status": "delayed",
          "details": "计划完成日期：2024-02-12，实际完成日期：2024-05-12，延迟原因：手续复杂"
        },
        {
          "name": "工程施工许可证",
          "date": "2024-01-25",
          "status": "complete",
          "details": "计划完成日期：2024-01-25，实际完成日期：2024-01-25，责任人：王五"
        },
        {
          "name": "国有土地使用证",
          "date": "2024-02-12",
          "status": "in-progress",
          "details": "正在进行中，预计完成日期：2024-02-28"
        },
        {
          "name": "展示区开放",
          "date": "2024-02-12",
          "status": "delayed",
          "details": "计划完成日期：2024-02-12，未完成，延迟原因：施工进度延误"
        },
        {
          "name": "室外综合管线施工",
          "date": "2024-02-12",
          "status": "in-progress",
          "details": "预计完成日期：2024-03-15，当前进度：50%"
        },
        {
          "name": "主体结构封顶（首批）",
          "date": "2024-02-12",
          "status": "complete",
          "details": "计划完成日期：2024-02-12，实际完成日期：2024-02-12，责任人：赵六"
        },
        {
          "name": "设备后改造",
          "date": "2024-02-12",
          "status": "not-reached",
          "details": "尚未开始，预计启动日期：2024-04-01"
        }
      ]


    }
  },
  mounted() {
    this.drawLineTrend()
  },
  methods: {
    drawLineTrend() {
      const xAxisData = ['2023-01', '2023-02', '2023-03', '2023-04', '2023-05', '2023-06']
      const seriesData = [23, 60, 20, 36, 23, 85]
      const option = new LineTrendOption(xAxisData, seriesData).getOption()
      const lineTrend = echarts.init(document.getElementById('lineTrend'))
      lineTrend.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/views/projects/styles/projects.scss';
.example-container{
    min-height: 100vh;
    padding: 12px;
    box-sizing: border-box;
    background: #F5F5F5;
    .example-content{
      margin-bottom: 16px;
    }
    .example-card-content{
      display: flex;
      gap: 21px;
      .item{
        flex: 1;
        display: flex;
        .item-card{
          flex: 1;
        }
      }
    }
    .example-company-tree{
      margin-top: 21px;
    }
    .example-tab{
      padding-top: 20px;
    }
    .example-cumulative-indicators-card{
      margin-top: 21px;
      padding: 16px;
    }
    #lineTrend{
      width: 50%;
      height: 300px;
    }
}
</style>

