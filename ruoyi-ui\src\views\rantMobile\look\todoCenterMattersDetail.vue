<template>
  <div class="rant-detail-container">
    <!-- 基本信息卡片 -->
    <div class="detail-card">
      <div class="card-header">
        <image class="header-icon" src="@/assets/rant/rant-info.png" mode="aspectFit"></image>
        <div class="header-title">基本信息</div>
      </div>

      <div class="info-item">
        <div class="info-label">来源</div>
        <div class="info-value">{{ rantDetail.ranterName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">类型</div>
        <div class="info-value">
          <div v-for="(type, index) in rantDetail.mattersType ? rantDetail.mattersType.split(',') : []" :key="index" class="type-tag">
            {{ getMattersTypeLabel(type) }}
          </div>
        </div>
      </div>

      <div class="info-item">
        <div class="info-label">分类</div>
        <div class="info-value">{{ getClassifyLabel(rantDetail.rantClassify) || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">状态</div>
        <div class="info-value">
          <div :class="[getStatusClass(rantDetail.status)]">{{ getStatusLabel(rantDetail.status) }}</div>
        </div>
      </div>

      <div class="info-item">
        <div class="info-label">责任部门</div>
        <div class="info-value">{{ rantDetail.deptName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">责任人</div>
        <div class="info-value">{{ rantDetail.responsiblePersonName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">责任部门负责人</div>
        <div class="info-value">{{ rantDetail.respDeptResponsiblerName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">分管领导</div>
        <div class="info-value">{{ rantDetail.respDeptLeaderName || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">计划完成时间</div>
        <div class="info-value">{{ rantDetail.planTime || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">是否私密</div>
        <div class="info-value">{{ rantDetail.isPrivate == 1 ? '是' : '否' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">可见范围</div>
        <div class="info-value">{{ rantDetail.visibleScope || '所有人' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">是否结项</div>
        <div class="info-value">{{ rantDetail.isCompletion ? '是' : '否' }}</div>
      </div>

      <div class="info-item content-item">
        <div class="info-label">内容</div>
        <div class="info-value content-value">{{ rantDetail.rantContent || '--' }}</div>
      </div>

      <div class="info-item content-item">
        <div class="info-label">措施</div>
        <div class="info-value content-value">{{ rantDetail.solution || '--' }}</div>
      </div>

      <div class="info-item" v-if="rantDetail.isCompletion">
        <div class="info-label">结项时间</div>
        <div class="info-value">{{ rantDetail.closingTime || '--' }}</div>
      </div>

      <div class="info-item">
        <div class="info-label">成果</div>
        <div class="info-value">
          <div v-for="(file, index) in rantDetail.fileList" :key="index" class="file-link" @click="openFile(file.url)">
            <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
            <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
          </div>
          <div v-if="!rantDetail.fileList || rantDetail.fileList.length === 0">--</div>
        </div>
      </div>

      <div class="info-item content-item">
        <div class="info-label">最新进展</div>
        <div class="info-value content-value" :nodes="rantDetail.thisProgress || '--'"></div>
      </div>
    </div>

    <!-- 进度情况卡片 -->
    <div class="detail-card">
      <div class="card-header">
        <image class="header-icon" src="@/assets/rant/rant-progress.png" mode="aspectFit"></image>
        <div class="header-title">进度情况</div>
      </div>

      <div class="progress-list">
        <div class="progress-item" v-for="(item, index) in recordDtoList" :key="index">
          <div class="progress-header">
            <div class="progress-index">{{ index + 1 }}</div>
            <div class="progress-date">{{ formatDate(item.feedbackTime) }}</div>
          </div>
          <div class="progress-content">
            <div class="progress-text" :nodes="item.actualProgress"></div>
            <div class="progress-files">
              <div v-for="(file, fileIndex) in item.fileList" :key="fileIndex" class="file-link" @click="openFile(file)">
                <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span><span class="extension-part">{{ getFileExtension(file.name) }}</span>
                <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />
              </div>
            </div>
          </div>
        </div>

        <div class="empty-state" v-if="!recordDtoList || recordDtoList.length === 0">
          <div>暂无进度记录</div>
        </div>
      </div>
    </div>

    <!-- 评价信息卡片 -->
    <div class="detail-card">
      <div class="card-header cu-flex">
        <div>
          <image class="header-icon" src="@/assets/rant/rant-rate.png" mode="aspectFit"></image>
          <div class="header-title">评价信息</div>
        </div>

        <div>
          <div class="">
            <div class="rating-stars cu-flex">
              <van-rate :value="evaluateAvgScore / 2" :allow-half="true" readonly color="#ffd21e"  void-icon="star"
                        void-color="#eee"/>
<!--              <div v-for="star in generateStars(evaluateAvgScore/2)" :key="star.id" :class="star.class">★</div>-->
              <div class="rating-score">{{ evaluateAvgScore }}分</div>
            </div>
          </div>
        </div>
      </div>

      <div class="rating-list">
        <div class="rating-item" v-for="(item, index) in rantEvaluativeInfoDtoList" :key="index">
          <div class="rating-header cu-flex">
            <div>
              <div class="rating-stars small cu-flex">
                <van-rate :value="item.score / 2" :allow-half="true" readonly color="#ffd21e"  void-icon="star"
                          void-color="#eee"/>
<!--                <div v-for="star in generateStars(item.score/2)" :key="star.id" :class="star.class">★</div>-->
                <div class="rating-score">{{ item.score }}分</div>
              </div>
            </div>
            <div>
              <div class="rating-info cu-flex">
                <div class="rating-user">评价人：{{ item.evaluatorName || '--' }}</div>
                <div class="rating-date">{{ item.createTime}}</div>
              </div>
            </div>
          </div>
          <div class="rating-content">
            <div class="rating-text">{{ item.evaluationContent || '--' }}</div>

          </div>
        </div>

        <div class="empty-state" v-if="!rantEvaluativeInfoDtoList || rantEvaluativeInfoDtoList.length === 0">
          <div>暂无评价信息</div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div class="loading-overlay" v-if="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
  </div>
</template>

<script>
import { lookRankMatter } from '@/api/rantMobile/matters.js';
import { evaluativeList, avgScore } from '@/api/rantMobile/evaluative.js';
import { getDicts } from '@/api/rantMobile/common.js';
import mixin from '../mixins'
// 状态选项定义 - 与PC端保持一致
const rantStatusOption = [
  // 0草稿 -蓝色 ，1进行中 -黄色，2按时完成 -绿色，3延期完成-浅红，4延期未完成-深红，5终止-灰色, 6审批中，7驳回
  {label: '草稿', value: 0, type: null},
  {label: '进行中', value: 1, type: 'info', color: 'yellow'},
  {label: '按时完成', value: 2, type: 'success'},
  {label: '延期完成', value: 3, type: 'danger'},
  {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
  {label: '终止', value: 5, type: 'info'},
  {label: '审批中', value: 6, type: 'warning'},
  {label: '驳回', value: 7, type: 'success', color: 'red', fontColor: 'white!important'},
];
import {Rate } from 'vant';
export default {
  mixins: [mixin],
  components: {
    [Rate.name]: Rate,
  },
  data() {
    return {
      id: this.$route.query.id,
      loading: true,
      rantDetail: {},
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      evaluateAvgScore: 0,
      type: 'matters',
      // 字典数据
      dictData: {
        rant_classify: [],
        rant_matters_type: []
      },
      rantStatusOption: rantStatusOption, // 添加状态选项到data中
      pageSize: 10,
      pageNum: 1,
      total: 0,
      hasMore: true,
      isRefreshing: false
    }
  },
  created() {
    this.initDictData();
  },
  methods: {
    fetchAvgScore() {
      avgScore(this.id).then(res => {
        if (res.code === 200) {
          this.evaluateAvgScore = res.data;
        }
      });
    },
    // 初始化字典数据
    initDictData() {
      this.fetchAvgScore();
      // 获取吐槽分类字典
      getDicts('rant_classify').then(res => {
        if (res.code === 200) {
          this.dictData.rant_classify = res.data;
          // 加载督办事项类型字典
          return getDicts('rant_matters_type');
        }
      }).then(res => {
        if (res && res.code === 200) {
          this.dictData.rant_matters_type = res.data;
          // 加载详情数据
          this.loadDetailData();
        }
      }).catch(err => {
      });
    },

    // 加载详情数据
    loadDetailData() {
      this.loading = true;

      // 获取督办事项详情
      lookRankMatter(this.id).then(res => {
        if (res.code === 200) {
          this.rantDetail = res.data;
          this.recordDtoList = res.data.recordDtoList || [];
        } else {
          /*uni.showToast({
            icon: 'none',
            title: res.msg || '加载详情失败'
          });*/
        }

      }).catch(err => {
       /* uni.showToast({
          icon: 'none',
          title: '加载数据失败，请稍后重试'
        });*/
      }).finally(() => {
        this.loading = false;
      });
      // 获取评价列表
      this.fetchEvaluativeList();
    },

    // 获取事项类型字典标签
    getMattersTypeLabel(value) {
      const item = this.dictData.rant_matters_type.find(item => item.dictValue === value);
      return item ? item.dictLabel : value;
    },

    // 获取分类字典标签
    getClassifyLabel(value) {
      const item = this.dictData.rant_classify.find(item => item.dictValue === value);
      return item ? item.dictLabel : value;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--';
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 生成评分星星
    generateStars(rating) {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        let starClass = 'star';
        if (rating >= i) {
          starClass = 'star filled';
        } else if (rating > i - 0.5) {
          starClass = 'star half-filled';
        }
        stars.push({
          id: `star-${i}`,
          class: starClass
        });
      }
      return stars;
    },

    // 打开文件
    openFile(url) {
      console.log('openFile', url);
      if (!url) return;
      window.open(url);
    },

    // 获取文件类型
    getFileType(url) {
      const extension = url.split('.').pop().toLowerCase();
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const documentTypes = {
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'docx',
        'xls': 'xls',
        'xlsx': 'xlsx',
        'ppt': 'ppt',
        'pptx': 'pptx',
        'txt': 'txt'
      };

      if (imageTypes.includes(extension)) {
        return 'image';
      }

      return documentTypes[extension] || 'unknown';
    },

    // Add new method to get status label from rantStatusOption
    getStatusLabel(value) {
      const statusItem = this.rantStatusOption.find(item => item.value === Number(value));
      return statusItem ? statusItem.label : '未知状态';
    },

    // Add new method to get status class for styling
    getStatusClass(value) {
      const statusItem = this.rantStatusOption.find(item => item.value === Number(value));
      if (!statusItem) return '';

      let className = 'status-tag';
      if (statusItem.type === 'success') className += ' status-success';
      else if (statusItem.type === 'info') className += ' status-info';
      else if (statusItem.type === 'danger') className += ' status-danger';
      else if (statusItem.type === 'warning') className += ' status-warning';

      // Add custom colors if specified
      if (statusItem.color) className += ` status-custom-${statusItem.color}`;

      return className;
    },
    async fetchEvaluativeList() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      try {
        const res = await evaluativeList({
          rantMattersId: this.id,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        });

        if (res.code === 200) {
          const newList = res.rows || [];
          if (this.pageNum === 1) {
            this.rantEvaluativeInfoDtoList = newList;
          } else {
            this.rantEvaluativeInfoDtoList = [...this.rantEvaluativeInfoDtoList, ...newList];
          }

          // 判断是否还有更多数据
          this.hasMore = newList.length === this.pageSize;
        } else {
          uni.showToast({
            title: res.msg || '获取评价列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取评价列表失败', error);
        uni.showToast({
          title: '获取评价列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取截断的文件名（不包含扩展名）
    getTruncatedFileName(fileName) {
      if (!fileName) return '--';
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },

    // 获取文件扩展名（包含点号）
    getFileExtension(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    }
  }
}
</script>

<style lang="scss" scoped>
.cu-flex{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rant-detail-container {
  min-height: 100vh;
  background-color: #CCDDFF;
  padding: 15px;
}

/* 卡片通用样式 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F0F0F0;
}

.header-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

/* 基本信息样式 */
.info-item {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-label {
  width: 100px;
  color: #666666;
  font-weight: 400;
  word-wrap: keep-all;
  white-space: nowrap;
}

.info-value {
  flex: 1;
  color: #333333;
  font-weight: 600;
}

.content-item {
  align-items: flex-start;
}

.content-value {
  line-height: 1.6;
}

.type-tag {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: #E8F4FF;
  color: #4080FF;
  border-radius: 4px;
  font-size: 12px;
}

.file-link {
  max-width: 215px;
  color: #4080FF;
  line-height: 1.8;
  display: flex;
  align-items: center;
  min-width: 0;
  gap: 0; /* 精确控制间距 */
  
  .filename-part {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    flex: 1; /* 可伸缩部分 */
  }
  
  .extension-part {
    flex-shrink: 0; /* 不收缩 */
    white-space: nowrap;
  }
  
  .download-icon {
    flex-shrink: 0; /* 不收缩 */
    margin-left: 8px; /* 与文件名保持间距 */
    font-size: 14px;
  }
}

/* 进度情况样式 */
.progress-list {
  padding: 5px 0;
}

.progress-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.progress-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-index {
  width: 20px;
  height: 20px;
  background-color: #4080FF;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  margin-right: 10px;
}

.progress-date {
  font-size: 12px;
  color: #999999;
}

.progress-content {
  padding-left: 30px;
}

.progress-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.progress-files {
  margin-top: 5px;
}

/* 评价信息样式 */
.rating-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.rating-list {
  padding: 5px 0;
}

.rating-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.rating-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.rating-stars {
  display: flex;
  margin-right: 10px;
  justify-content: flex-start!important;
  align-items: center;
}

.rating-stars.small {
  transform: scale(0.8);
  transform-origin: left;
}

.star {
  font-size: 18px;
  color: #DDDDDD;
  margin-right: 2px;
}

.star.filled {
  color: #FFCC00;
}

.star.half-filled {
  position: relative;
  color: #DDDDDD;
}

.star.half-filled:after {
  content: '★';
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  overflow: hidden;
  color: #FFCC00;
}

.rating-score {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.rating-content {
  // padding-left: 30px;
}

.rating-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.rating-info {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.rating-user, .rating-date {
  font-size: 12px;
  color: #999999;
  margin-right: 15px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}

/* 加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid #f3f3f3;
  border-top-color: #4080FF;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 15px;
  font-size: 14px;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Add status tag styles */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.status-success {
  background-color: #52c41a;
}

.status-info {
  background-color: #1890ff;
}

.status-warning {
  background-color: #faad14;
}

.status-danger {
  background-color: #f5222d;
}

/* Custom status colors */
.status-custom-yellow {
  background-color: #ffcb2f;
}

.status-custom-red {
  background-color: #ff4d4f;
}

.status-custom-blue {
  background-color: #1890ff;
}

.status-custom-green {
  background-color: #52c41a;
}

.status-custom-gray {
  background-color: #999999;
}
</style>
