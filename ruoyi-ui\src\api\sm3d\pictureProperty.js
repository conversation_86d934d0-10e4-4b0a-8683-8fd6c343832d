import request from '@/utils/request'


export function getPic() {
  return request({
    url: 'pic/picdown',
    method: 'get',
  })
}

export function uploadfile(pic) {
  return request({
    url: 'file/upload',
    method: 'post',
    data: pic
  })
}

export function uploadPic(longitude,latitude,description,url) {

  return request({
    url: 'pic/uploadPic',
    method: 'post',
    data: {longitude,latitude,description,url}
  })
}
export function updatePic(id,longitude,latitude,description,url) {

  return request({
    url: 'pic/updPic',
    method: 'post',
    data: {id,longitude,latitude,description,url}
  })
}
export function delPic(id) {

  return request({
    url: 'pic/delPic',
    method: 'delete',
    data: id
  })
}
/*export function uploadPic(query) {
  console.log("query",query)
  return request({
    url: 'auth/uploadPic',
    method: 'get',
    params:query
  })
}*/
