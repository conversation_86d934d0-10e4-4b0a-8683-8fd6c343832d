<template>
  <div class="parent-container">
    <h3 style="margin-left: 10px;">【{{this.detailData.projectName}}】楼栋图配置</h3>
    <div class="content-area">
      <div class="image-area">
        <ImageMarker
          :imgSrc="imgUrl"
          :annotations="annotations"
          @annotation-change="handleAnnotationChange"
        />
      </div>
      <div class="form-area">
        <h3>标注表单</h3>
        <div class="annotations-container">
          <div
            v-for="(ann, index) in annotations"
            :key="ann.id"
            class="annotation-form"
          >
            <div class="annotation-header">
              <div class="annotation-title">标注#{{index + 1}}</div>
              <el-input class="annotation-input" type="text" v-model="ann.mappingValue" placeholder="请输入映射值" />
              <div class="delete-btn" @click="deleteAnnotation(index)">删除</div>
            </div>
            <!-- <label>X (%):
              <input type="number" step="0.1" v-model.number="ann.x" />
            </label>
            <label>Y (%):
              <input type="number" step="0.1" v-model.number="ann.y" />
            </label>
            <label>宽度 (%):
              <input type="number" step="0.1" v-model.number="ann.width" />
            </label>
            <label>高度 (%):
              <input type="number" step="0.1" v-model.number="ann.height" />
            </label> -->
          </div>
        </div>
        <div class="save-button-container">
          <el-button type="primary" @click="saveAnnotations" style="width: 100%;">保存</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageMarker from './ImageMarker.vue'
import {getInfo, buildingConfig, getBuildConfig} from "@/api/projectOperate/projectInfo";
export default {
  components: {
    ImageMarker
  },
  data() {
    return {
      imgUrl: '', // 替换为实际图片地址
      annotations: [],
      id: null,
      detailData: {}
    }
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getDetail();
    this.getBuildConfigData(this.detailData.projectCode);
  },
  methods: {
    async getBuildConfigData(projectCode) {
      try {
        const response = await getBuildConfig(projectCode);
        this.annotations = response?.data?.map(item => {
          return {
            id: item.id,
            x: item.axCoordinate,
            y: item.ayCoordinate,
            width: item.axLength,
            height: item.ayLength,
            mappingValue: item.mappingValue
          }
        });
      } catch (error) {
        console.error('获取配置数据失败:', error);
      }
    },

    async saveAnnotations() {
      // 检查是否有空的 mappingValue
      const emptyMapping = this.annotations.find(ann => !ann.mappingValue?.trim());
      if (emptyMapping) {
        this.$message.error('请为所有标注填写映射值');
        return;
      }

      const data = {
        "projectCode": this.detailData.projectCode,
        "buildingConfigPic": this.imgUrl,
        "buildingConfigList": this.annotations.map(ann => ({
          "projectCode": this.detailData.projectCode,
          "axCoordinate": ann.x.toFixed(5),
          "ayCoordinate": ann.y.toFixed(5),
          "axLength": ann.width.toFixed(5),
          "ayLength": ann.height.toFixed(5),
          "mappingValue": ann.mappingValue
        }))
      };

      try {
        await buildingConfig(data);
        this.$message.success('保存成功');
      } catch (error) {
        console.error('保存配置失败:', error);
      }
    },

    async getDetail() {
      if (!this.id) return;
      try {
        const response = await getInfo(this.id);
        this.detailData = response.data;
        this.imgUrl = this.detailData?.buildingPic ?? null;
      } catch (error) {
        console.error('获取详情失败:', error);
      }
    },
    handleAnnotationChange(newAnnotations) {
      this.annotations = newAnnotations
      // console.log(this.annotations);
    },
    deleteAnnotation(index) {
      this.annotations.splice(index, 1);
    }
  }
}
</script>

<style scoped>
.content-area {
  display: flex;
  flex-direction: row;
  /* 父容器自动宽度适应 */
  width: 100%;
  box-sizing: border-box;
}

.image-area {
  /* 将图片区域设置为可伸展的flex子项 */
  flex: 1;
  position: relative;
  margin-right: 20px;
  box-sizing: border-box;
}

.form-area {
  width: 300px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px); /* 调整高度，减去顶部标题等元素的高度 */
}

.image-container {
  position: relative;
  display: block;
  /* 不再使用固定500px宽度，让其自动填满父级的flex空间 */
  width: 100%;
  border: 1px solid #ccc;
  user-select: none;
  box-sizing: border-box;
}

.annotator-image {
  display: block;
  width: 100%;
  height: auto;
  box-sizing: border-box;
}

.annotation-header {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .annotation-title {
    flex-shrink: 0;
    word-break: keep-all;
  }
  .annotation-input {
    flex:1;
  }
  .delete-btn {
    height: 100%;
    word-break: keep-all;
  }
}

.delete-btn {
  padding: 4px 8px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.delete-btn:hover {
  background-color: #ff7875;
}

.annotations-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px; /* 为滚动条留出空间 */
  margin-bottom: 10px;
}

.save-button-container {
  padding: 10px 0;
  background-color: #fff;
  border-top: 1px solid #eee;
}

</style>
