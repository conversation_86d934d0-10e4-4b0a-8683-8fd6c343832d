
4889d48da46cd8697c437c9770b42a5b6a0bbda3	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.5e0ec961c13680103a24.hot-update.js\",\"contentHash\":\"105bf09c93a73525452aecae768a497a\"}","integrity":"sha512-0NsQ1nH21hqC61zyi8vPkdhAMF4R/zeDvrFHJypHuTiOgzbXLDcZDYpwjYqRxxLL/zO1N0VJLVmlT0Ih0fEPMw==","time":1754311728596,"size":77965}