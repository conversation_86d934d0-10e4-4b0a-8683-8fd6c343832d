<script>
export default {
  name: 'QJCard',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({})
    },
    bgColor: {
      type: String,
      default: '#A579D4'
    }
  },
  computed: {
    statusClass() {
      const statusMap = {
        '按期完成': 'completed',
        '延期完成': 'completed-late',
        '延期未完成': 'uncompleted-late',
        '未到期': 'unexpired'
      }
      return statusMap[this.data.keyNodeStatus] || ''
    }
  }
}
</script>

<template>
  <div class="qj-card" :style="{ backgroundColor: bgColor }">
    <div class="card-header">
      <span class="node-name">{{ $formatNull(data.keyNodeName) }}</span>
      <span class="cycle-days">周期{{ $formatNull(data.cycleDays) }}天</span>
    </div>
    <div class="card-content">
      <div class="date">{{ $formatNull(data.date) }}</div>
      <div class="status" :class="statusClass">
        {{ $formatNull(data.keyNodeStatus) }}
      </div>
    </div>
    <div class="date-desc">{{ $formatNull(data.dateDesc) }}</div>
  </div>
</template>

<style scoped lang="scss">
.qj-card {
  background: #F8F2FF;
  border-radius: 0.25rem;
  padding: 0.8125rem 1rem;
  width: fit-content;

  // 定义通用文本样式
  %text-base {
    font-family: PingFang SC, sans-serif;
    font-style: normal;
    text-transform: none;
    color: #FFFFFF;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .node-name,
    .cycle-days {
      @extend %text-base;
      font-weight: 600;
      font-size: 1.125rem;
      line-height: 1.3125rem;
      text-align: center;
      word-break: keep-all;
    }
  }

  .card-content {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;

    .date {
      font-family: Roboto, sans-serif;
      font-weight: bold;
      font-size: 1.375rem;
      color: #FFFFFF;
      line-height: 1.625rem;
      text-align: left;
    }

    .status {
      display: flex;
      align-items: center;
      width: fit-content;
      padding: 0.25rem 0.5rem;
      border-radius: 0.5rem;
      background: #F6F2FB;
      font-family: PingFang SC, sans-serif;
      font-weight: 600;
      font-size: 0.875rem;
      color: #FF8000;
      line-height: 0.875rem;
      text-align: center;
      &.completed {
        color: #3DCC55;
      }
      &.completed-late {
        color: #FF8000;
      }
      &.uncompleted-late {
        color: #FF4040;
      }
      &.unexpired {
        color: #666666;
      }
    }
  }

  .date-desc {
    @extend %text-base;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1rem;
    text-align: left;
  }
}
</style>
