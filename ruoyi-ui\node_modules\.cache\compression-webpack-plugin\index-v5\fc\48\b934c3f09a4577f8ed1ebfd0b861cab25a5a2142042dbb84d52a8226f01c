
c278a84a65ae88bd13cefbefd4e0d9c028e5d9fa	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.bc1a4d3692cb206aeada.hot-update.js\",\"contentHash\":\"e996f3ffa376771ffaade7dd2a5b3ea6\"}","integrity":"sha512-VEESEm0UMQRCuJdJKsh+CU9FlwtpJuMZcwguojzGN6u3Ahq1bxOVYmxm8hGnUwJLUqVCe3hNdyWXM7LWAQNzPQ==","time":1754311450961,"size":78045}