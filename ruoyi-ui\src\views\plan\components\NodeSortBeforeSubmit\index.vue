<template>
<!-- 计划调整中，节点在提交前的排序 -->
  <el-dialog title="节点排序" v-loading="submitLoading" :visible.sync="showAllNode" width="1250px"  append-to-body v-if="showAllNode">
    <div class="app-container" :style="{ '--color': theme }">
<!--      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="节点名称" prop="nodeName">
          <el-input v-model="queryParams.nodeName" placeholder="请输入节点名称" clearable size="small"
                    @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>-->
<!--      <el-table v-loading="loading" :data="nodeList" @selection-change="handleSelectionChange" height="65vh" ref="table">-->
        <el-table v-loading="loading" :data="nodeList"  height="65vh" ref="table" stripe>
<!--        <el-table-column type="selection" width="45" align="center" />-->
        <el-table-column label="序号" align="center" prop="id" width="60" fixed>
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="节点名称" align="center" prop="nodeName" width="150" show-overflow-tooltip fixed />
        <el-table-column label="计划开始时间" align="center" prop="startTime" width="160">
<!--          <template slot-scope="scope">
            <div class="time">
              <el-date-picker width="150" clearable size="small" v-model="scope.row.startTime" type="date"
                              value-format="yyyy-MM-dd" placeholder="计划开始时间" :disabled="true">
              </el-date-picker>
            </div>
          </template>-->
        </el-table-column>
        <el-table-column label="计划完成时间" align="center" prop="endTime" width="160">
<!--          <template slot-scope="scope">
            <div class="time">
              <el-date-picker width="150" clearable size="small" v-model="scope.row.endTime" type="date"
                              value-format="yyyy-MM-dd" placeholder="计划完成时间" :disabled="true">
              </el-date-picker>
            </div>
          </template>-->
        </el-table-column>
          <el-table-column label="操作状态" align="center" prop="adjustType">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.adjustType == 1"
              >修改</el-tag
              >
              <el-tag v-if="scope.row.adjustType == 2">新增</el-tag>
              <el-tag type="info" v-if="scope.row.adjustType == 0"
              >未调整</el-tag
              >
            </template>
          </el-table-column>
        <!-- <el-table-column
        label="实际完成时间"
        align="center"
        prop="completTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
        <el-table-column label="责任部门" align="center" prop="departmentArr" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{scope.row.departmentArr.join(',')}}
<!--            <el-select :disabled="true" v-model="scope.row.departmentArr" multiple placeholder="请选择" size="small"  @change="selectChange($event,scope.$index)">
              <el-option
                v-for="item in departmentsData"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>-->
          </template>
        </el-table-column>
        <el-table-column label="反馈人" align="center" prop="feedbackUserName" width="70" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>
              {{ scope.row.feedbackUserName || "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否可用" align="center" prop="isValid" width="140">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isValid == 1">是</el-tag>
            <el-tag v-if="scope.row.isValid == 0">否</el-tag>
<!--            <el-select v-model="scope.row.isValid" clearable :disabled="true">
              <el-option v-for="option in isValidOption" :key="option.value" :label="option.label"
                         :value="option.value"></el-option>
            </el-select>-->
          </template>
        </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.rowIndex"  size="small"
                        @change="updateIndex(scope.row, scope.row.rowIndex, scope.$index)"
                        placeholder="请输入内容"
                        style="width: 50px; margin-right: 10px; text-align: center;"></el-input>
              <el-button @click="moveUp(scope.$index)" style="font-weight: bold;" type="text" size="small" icon="el-icon-top custom-bold" v-if="scope.$index !== 0"></el-button>
              <el-button @click="moveDown(scope.$index)" type="text" size="small" icon="el-icon-bottom custom-bold" v-if="scope.$index < (nodeList.length - 1)"></el-button>
            </template>
          </el-table-column>
      </el-table>
      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      /> -->
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmFun">确 定</el-button>
      <el-button @click="cancelDailog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  listAllNode as listNode
} from "@/api/plan/node"
import SelectTree from '@/components/SelectTree.vue'
import {
  isValidOption
} from '@/views/plan/constant'
import variables from '@/assets/styles/element-variables.scss'
import Template from "@/views/plan/template/index.vue";
export default {
  name: "Node",
  components: {
    Template,
    SelectTree,
  },
  props: ['defaultSelect','departmentsData'],
  data() {
    return {
      input: '',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      // 是否显示弹出层
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 20,
        nodeLevel: null,
        nodeName: null,
        planId: null,
      },
      editIndex: null,
      clickIndex: null,
      /*专业成果*/
      resultList: [],
      showResultDialog: false,
      ids2: [],
      //责任人
      respectPeople: [],
      resultConfigIds: [],
      theme: variables.theme,
      showDepart: false,
      planId: null,
      selectData: [],
      selectNodeCodes: [],
      showAllNode: false,
      submitLoading: false,
    }
  },
  created() {},
  computed: {
    isValidOption: () => isValidOption,
  },
  watch: {},
  methods: {
    updateIndex(row, newIndex, originalIndex) {
      if (newIndex > 0 && newIndex <= this.nodeList.length) {
        const rowToMove = this.nodeList.splice(originalIndex, 1)[0];
        this.nodeList.splice(newIndex - 1, 0, rowToMove);
        this.updateRowIndices();
        this.$forceUpdate();
      }
      else{
        this.$message.error('请输入正确的序号,序号范围为1~'+this.nodeList.length)
      }
    },
    moveUp(index) {
      if (index > 0) {
        const row = this.nodeList.splice(index, 1)[0];
        this.nodeList.splice(index - 1, 0, row);
        this.updateRowIndices();
      }
    },
    moveDown(index) {
      if (index < this.nodeList.length - 1) {
        const row = this.nodeList.splice(index, 1)[0];
        this.nodeList.splice(index + 1, 0, row);
        this.updateRowIndices();
      }
    },
    updateRowIndices() {
      this.nodeList.forEach((row, index) => {
        // row.rowIndex = index + 1;
        this.$set(row, 'rowIndex', index + 1);
      });
    },
    async getList() {
      this.loading = true
      const response = await listNode(this.queryParams)
      let da=response.data
      da.map(item=>{
        item.departmentArr=item.department.length?item.department.split(','):[]
      })
      // this.nodeList = da.filter(item => !this.selectNodeCodes.includes(item.nodeCode))
      this.nodeList = da
      this.total = response.total
      this.loading = false
    },
    // 取消按钮
    cancel() {
      this.editIndex = null
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectData = selection
    },

    async show(planId, updateData = [], addData = []) {
      this.clickIndex = null
      this.showAllNode = true
      // this.queryParams.planId = this.$route.query.planId
      this.queryParams.planId = planId
      this.selectNodeCodes = this.defaultSelect
      await this.getList()
      if(updateData.length){ // 如果有调整数据，则需要根据updateData中的状态更新nodeList中的状态
        this.nodeList = this.nodeList.map(item=>{
          updateData.map(updateItem=>{
            if(item.id===updateItem.id){
              // item.adjustType=updateItem.adjustType
              item = updateItem;
            }
          })
          return item
        })
      }
      this.updateRowIndices();

      this.nodeList = this.nodeList.concat(addData) // 拼接新增节点
      this.$forceUpdate()
    },
    confirmFun() {
      if (!this.nodeList.length) {
        return this.$message('节点数据为空')
      }
      /*this.selectData.map(item => {
        item.adjustType = item.adjustType === 0 ? 1 : item.adjustType || 1
      })
      this.$emit('handleData', this.selectData)
      this.showAllNode = false*/
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 设置节点顺序字段
        for(let i = 0; i < this.nodeList.length; i++){
          this.nodeList[i].nodeIndex  = i + 1
        }
        this.submitLoading = true
        this.$emit('submit', this.nodeList)
      }).catch(() => {
      });

    },
    setSubmitLoading(flag){
      this.submitLoading = flag
    },
    cancelDailog() {
      this.handleQuery()
      this.showAllNode = false
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";
.sort-input{
  width: 50px; margin-right: 10px;text-align: center;
}
.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 150px;
}

.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
