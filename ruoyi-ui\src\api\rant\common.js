import request from '@/utils/request'

// 文件上传
export function uploadFile(file) { // 单文件上传
  return request({
    url: '/file/upload',
    method: 'post',
    data: file
  })
}

// 多文件上传
export function uploadFileMultiple(files) {

  return request({
    url: '/file/rantUploads',
    method: 'post',
    data: files
  })
}

// 获取文件
export function getFile(fileUrl) {
  return request({
    url: 'rant/common/file/stream',
    params: {
      url: fileUrl
    },
    method: 'get',
    responseType: 'blob'
  })
}