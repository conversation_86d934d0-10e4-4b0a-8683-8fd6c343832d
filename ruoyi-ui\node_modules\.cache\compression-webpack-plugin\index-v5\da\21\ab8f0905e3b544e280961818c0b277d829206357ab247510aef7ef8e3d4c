
838c86a8d303ec3635a26caa7f33db27b43de8cf	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"391396dcc0e2fa155f596e58b220c652\"}","integrity":"sha512-hXk1vhL1ectE+sWLSNC+QRPXz7/uBghd9ea2Y9Hi6sSYjWfagE+LAqS4Z3vRC9Yte6YRYW80LuRRXw/A0QfP0g==","time":1754312413577,"size":12045796}