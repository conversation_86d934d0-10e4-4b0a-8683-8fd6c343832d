// cover some element-ui styles
.el-transfer.custom-transfer{
  display: flex;
}
.custom-transfer .el-transfer-panel {
  width: 500px; /* ??????? */
  flex: 1;
}
.custom-transfer .el-transfer-panel__item{
  width: 100%;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
.wrap-line{
  white-space: normal!important;

}
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: rgb(16, 119, 222)!important;
  color: #fff!important;
}
.el-message-box__message{
  max-height: 60vh!important;
  overflow-y: auto;
}
:deep(.custom-date-picker input::placeholder) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}
:deep(.custom-date-picker input) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}