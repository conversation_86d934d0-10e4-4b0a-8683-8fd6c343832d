<template>
<div style="padding: 10px">
  <el-table
    ref="multipleTable"
    :data="tableData"
    tooltip-effect="dark"
    style="width: 100%"
    @selection-change="handleSelectionChange">
    <el-table-column
      type="selection"
      width="55"
    >
    </el-table-column>
    <el-table-column prop="id" label="ID"/>
    <el-table-column prop="supermap" label="supermap功能"/>
  </el-table>

</div>
</template>

<script>
import {begincom, getcom, setcom} from "@/api/sm3d/componentssetting";

export default {
  name: "con-iearth",
  data()
  {
    return{
      tableData:[
        {
          id:1,
          supermap:'图层管理'
        },
        {
          id:2,
          supermap:'添加图层'
        },
        {
          id:3,
          supermap:'底层设置'
        },
        {
          id:4,
          supermap:'场景属性'
        },
        {
          id:5,
          supermap:'裁剪'
        },
        {
          id:6,
          supermap:'地形'
        },
        {
          id:7,
          supermap:'三维分析'
        },
        {
          id:8,
          supermap:'量算'
        },
        {
          id:9,
          supermap:'在线编辑'
        },
        {
          id:10,
          supermap:'重置'
        },
        {
          id:11,
          supermap:'放大'
        },
        {
          id:12,
          supermap:'缩小'
        },
        {
          id:13,
          supermap:'全屏'
        },
        {
          id:14,
          supermap:'矿区图片'
        }
      ],
      multipleSelection: [],

    }
  },

  mounted() {
    getcom().then((res)=>{
      this.$nextTick(()=>{
        this.tableData.forEach(row => {
          if(res.indexOf(row.id) >= 0){
            this.$refs.multipleTable.toggleRowSelection(row,true);
          }
        })
      })

    })
  },
  methods:{
    handleSelectionChange(val) {
      this.multipleSelection = val;
      if(val.length==0)
      {
        begincom()
      }else {
        var i = "";
        val.forEach(item=>{
          i=i+","+item.id;

        })
        setcom(i)
      }
    }
  }
}
</script>

<style scoped>

</style>
