## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## 项目看板
```
项目看板工程文件在
views/projects目录下
router配置和计划项目耦合
```

## 新增功能组件

### FileLink 文件链接组件 (v5.0) 🎉

基于强大的 [officetohtml.js](https://officetohtml.js.org/) 库重构，提供专业的文件预览体验：

#### 主要特性
- **强大预览引擎**: 基于 officetohtml.js，业界领先的Office文档预览解决方案
- **完整功能支持**: 
  - **PDF**: 专业阅读器界面，支持缩放、搜索、导航、打印
  - **DOCX**: Word文档完整预览，保持原始格式
  - **PPTX**: PowerPoint幻灯片模式，支持演示播放
  - **XLSX**: Excel交互式表格，支持排序、筛选
  - **图片**: 高质量图片查看器，支持缩放、旋转
- **文件流安全**: 通过后端API获取文件流，保障文件安全
- **专业界面**: 95%宽度对话框，提供沉浸式预览体验
- **智能错误处理**: 完善的错误提示和重试机制
- **性能优化**: 自动资源管理，防止内存泄漏

#### 使用方法
```vue
<!-- 基础用法 -->
<FileLink 
  :file="{ url: 'file.pdf', name: '文档.pdf' }" 
  @preview="handlePreview"
  @download="handleDownload"
/>

<!-- 在表格中使用 -->
<el-table-column prop="name" label="文件名">
  <template slot-scope="scope">
    <FileLink :file="scope.row" />
  </template>
</el-table-column>
```

#### 技术依赖
- `officetohtml.js`: 强大的Office文档预览库 (本地部署)
- `jQuery 3.3.1`: 必需的JavaScript库 (本地部署)
- `PDF.js`: PDF文档预览支持 (本地部署)
- `mammoth.js`: DOCX文档解析 (本地部署)
- `pptxjs + d3.js`: PowerPoint演示文稿支持 (本地部署)
- `handsontable + xlsx.js`: Excel表格支持 (本地部署)
- `verySimpleImageViewer`: 图片查看器 (本地部署)
- 后端文件流API: 安全的文件获取接口

**🎯 本地部署优势:**
- ✅ 离线访问，无需依赖外网CDN
- ✅ 加载速度快，避免网络延迟
- ✅ 版本稳定，不受CDN更新影响
- ✅ 企业内网友好

详细文档请参考：[FileLink组件说明](components/FileLink.md)

#### 已更新的页面
- 督办反馈详情页面 (`src/views/rant/myFeedback/detail.vue`)

可以在其他文件显示的地方按照相同方式替换使用。