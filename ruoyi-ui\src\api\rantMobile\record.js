import request from '@/utils/request'
// import request from '@/utils/request-rant-mobile'
// 查询进度反馈记录列表
export function listRecord(query) {
  return request({
    url: '/rant/record/list',
    method: 'get',
    params: query
  })
}

// 查询进度反馈记录详细
export function getRecord(id) {
  return request({
    url: '/rant/record/' + id,
    method: 'get'
  })
}

// 新增进度反馈记录
export function addRecord(data) {
  return request({
    url: '/rant/record',
    method: 'post',
    data: data
  })
}

// 修改进度反馈记录
export function updateRecord(data) {
  return request({
    url: '/rant/record',
    method: 'put',
    data: data
  })
}

// 删除进度反馈记录
export function delRecord(id) {
  return request({
    url: '/rant/record/' + id,
    method: 'delete'
  })
}

// 进度反馈-过程反馈提交接口
export function submitFeedback(data) {
  return request({
    url: '/rant/record/submitFeedback',
    method: 'post',
    data: data
  })
}

// 进度反馈-过程反馈提交接口
export function submitTodoFeedback(data) {
  return request({
    url: '/rant/record/submitTodoFeedback',
    method: 'post',
    data: data
  })
}

// 过期待办提交反馈记录
export function submitExpireTodoFeedback(data={}) {
  return request({
    url: '/rant/record/submitExpireTodoFeedback',
    method: 'post',
    data
  })
}

// 过期待办提交反馈记录
export function submitRejectTodoFeedback(data={}) {
  return request({
    url: '/rant/record/submitRejectTodoFeedback',
    method: 'post',
    data
  })
}

// 进度反馈责任部门负责人审批
export function deptLeaderApprove(data={}) {
  return request({
    url: '/rant/record/deptLeaderApprove',
    method: 'post',
    data
  })
}

// 事项结项进度反馈管理员审批
export function adminApprove(data={}) {
  return request({
    url: '/rant/record/adminApprove',
    method: 'post',
    data
  })
}


