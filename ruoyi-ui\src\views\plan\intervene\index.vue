<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="城市公司" prop="deptNum">
        <el-select v-model="queryParams.deptNum" placeholder="请选择城市公司" clearable @change="handleQueryProject">
          <el-option v-for="dict in deptList"
                     :key="dict.deptNum"
                     :label="dict.deptName"
                     :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in storeProjectList"
            :key="dict.name"
            :label="dict.name"
            :value="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" fixed="left"/>
      <el-table-column label="顺序" align="center" type="index" fixed="left"/>
      <el-table-column label="计划名称" align="center" prop="planName" width="100" fixed="left">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.planName)">{{ scope.row.planName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" width="100">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.projectName)">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点状态" align="center" prop="status">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column label="标准节点名称" align="center" prop="standardNodeName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.standardNodeName)">{{ scope.row.standardNodeName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="节点名称" align="center" prop="nodeName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.nodeName)">{{ scope.row.nodeName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成标准" align="center" prop="completeCriteria"/>
      <el-table-column label="标准工期(天)" align="center" prop="durationNum"/>
      <el-table-column label="计划开始时间" width="100" align="center" prop="startTime"/>
      <el-table-column label="计划截至时间" width="100" align="center" prop="endTime"/>
      <el-table-column label="实际完成时间" width="100" align="center" prop="actualCompletionDate"/>
      <el-table-column label="实际偏差" width="100" align="center" prop="deviationNum"/>
      <el-table-column label="责任部门" align="center" prop="department">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.departmentName)">{{ scope.row.departmentName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="反馈人" align="center" prop="feedbackPeopleName"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleIntervene(scope.row)"
            v-hasPermi="['plan:nodeIntervene:query']"
          >干预
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!--干预弹窗-->
    <!--节点状态：status-->
    <!--    {label: '按期完成', value: 2, type: 'success'},-->
    <!--    {label: '延期完成', value: 3, type: 'warning'},-->
<!--    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <div class="feedback-container">
          <div class="feedback-detail">
            <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="项目名称：" prop="projectName">
                    {{ form.projectName || '&#45;&#45;' }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="项目分期：" prop="stageId">
                    <dict-tag :options="dict.type.stages" :value="form.stageId"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="节点名称：" prop="nodeName">
                    {{ form.nodeName || '&#45;&#45;' }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="完成标准：" prop="completeCriteria">
                    <el-input v-model="form.completeCriteria"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              &lt;!&ndash;责任部门、责任人&ndash;&gt;
              <el-row>
                <el-col :span="12">
                  <el-form-item label="责任部门：" prop="department">
                  &lt;!&ndash;<select-tree
                      v-model="form.department"
                      placeholder="请选择责任部门"
                      :selectValue="form.department"
                      @changeEmit="(data) => receiveData(data, form)"
                    />&ndash;&gt;
                    <section class="flex-align-center">
                      <span class="dialog-select" @click="selectDepartFun(form)">
                        {{departNameShow(form) ? form.departmentName : '请选择'}}
                        <i class="el-icon-search"></i>
                      </span>
                    </section>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="责任人：" prop="respPeopleName">
&lt;!&ndash;                      <el-input v-model="form.respPeopleName" placeholder="&#45;&#45;" disabled="true"></el-input>&ndash;&gt;
                    {{ form.responsibilityPeopleName || '&#45;&#45;'}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="计划开始时间：" prop="startTime">
                    <el-date-picker v-model="form.startTime" type="date" format="yyyy-MM-dd" :style="{width: '100%'}"
                                    value-format="yyyy-MM-dd" placeholder="请选择计划开始时间" clearable>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="计划完成时间：" prop="endTime">
                    <el-date-picker v-model="form.endTime" type="date" format="yyyy-MM-dd" :style="{width: '100%'}"
                                    value-format="yyyy-MM-dd" placeholder="请选择计划完成时间" clearable>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
          </div>
          <div class="feedback-form">
            <div class="el-icon-s-comment feedback-form-title icon-primary">反馈信息</div>
            &lt;!&ndash;根据节点状态，已完成的默认选择完成反馈，显示实际完成时间&ndash;&gt;
            &lt;!&ndash;未完成的显示完成百分比和预计完成时间&ndash;&gt;
  &lt;!&ndash;          <el-form ref="form" :model="form" :rules="rules" label-width="120px">&ndash;&gt;
              <el-row>
                <el-form-item label="反馈内容" prop="feedbackType">
                  <el-radio-group v-model="form.feedbackType" size="medium" @input="handleFYRadioChange">
                    <el-radio v-for="(item, index) in feedbackTypeOptions" :key="index" :label="item.value">{{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-row>
              <el-row>
                <el-col :span="12"  v-if="form.feedbackType !== 2">
                  <el-form-item label="完成百分比" prop="feedbackProgress" required>
                    <el-slider :max='100' :step='1' show-input v-model="form.feedbackProgress"></el-slider>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="form.feedbackType == 1">
                  <el-form-item label="预计完成时间" prop="expectedCompletionDate">
                    <el-date-picker v-model="form.expectedCompletionDate" type="date" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    :style="{width: '100%'}" placeholder="请选择预计完成时间" clearable>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-else>
                  <el-form-item label="实际完成时间" prop="actualCompletionDate">
                    <el-date-picker v-model="form.actualCompletionDate" type="date" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    :style="{width: '100%'}" placeholder="请选择实际完成时间" clearable>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="进度说明" prop="notes">
                  <el-input v-model="form.notes" type="textarea" placeholder="请输入进度说明"
                            :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-row>
  &lt;!&ndash;          </el-form>&ndash;&gt;
          </div>
          <div class="feedback-file">
            <div class="el-icon-files feedback-form-title icon-primary">成果文件</div>
            <el-row :gutter="10" class="mb8" v-if="dialogType === 'update'">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-setting"
                  size="mini"
                  @click="handleResultOpen"
                >节点列表选择
                </el-button>
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleNodeDocument"
                >新增
                </el-button>
              </el-col>
            </el-row>
            <el-table :data="nodeOutcomeDocumentList" @selection-change="handleNodeDocumentChange" style="width: 100%;">
              <el-table-column type="index" width="55" align="center"/>
              <el-table-column label="成果类型" align="center" prop="type" width="150">
                <template slot-scope="scope">
                  <span v-if="dialogType === 'view'">{{ scope.row.type || '&#45;&#45;' }}</span>
                  <el-input v-else-if="dialogType === 'update'" v-model="scope.row.type"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="成果文件" align="center" prop="annexUrl" width="150">
                <template slot-scope="scope">
                  <section class="file-wrapper" v-if="dialogType === 'update'">
                    <label v-if="!scope.row.annexUrl" :for="'uploadFile_'+scope.$index"
                           class="el-icon-upload cursor icon-primary">
                      {{ !scope.row.annexUrl ? '请选择文件上传' : scope.row.annexName }}
                    </label>
                    <a :href="scope.row.annexUrl" class="link" :download="scope.row.annexName"
                       v-else>{{ scope.row.annexName || '&#45;&#45;' }}</a>
                    <i class="el-icon-circle-close cursor link" :class="{'display-none': !scope.row.annexUrl}"
                       @click="handleDeleteAnnexUrl(scope.row)"></i>
                  </section>
                  <section v-else-if="dialogType === 'view'">
                    &lt;!&ndash;                  <a @click="downloadFile(scope.row.annexUrl, scope.row.annexName)" class="link">下载</a>&ndash;&gt;
                    <a :href="scope.row.annexUrl" :download="scope.row.annexName" class="link"
                       target="_blank">{{ scope.row.annexName || '&#45;&#45;' }}</a>
                  </section>
                  <input type="file" class="display-none" :id="'uploadFile_'+scope.$index"
                         @change="(event) => uploadFileHandle(event, scope.row, scope.$index)"/>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" align="center" prop="createTime">
                <template slot-scope="scope">
                  <span v-if="dialogType === 'view'">{{ scope.row.createTime || '&#45;&#45;' }}</span>
                  <el-input v-else-if="dialogType === 'update'" v-model="scope.row.createTime" disabled=""></el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注说明" align="center" prop="outcomeDocumentsDesc">
                <template slot-scope="scope">
                  <span v-if="dialogType === 'view'">{{ scope.row.outcomeDocumentsDesc || '&#45;&#45;' }}</span>
                  <el-input v-else-if="dialogType === 'update'" v-model="scope.row.outcomeDocumentsDesc"></el-input>
                </template>
              </el-table-column>
              <el-table-column v-if="dialogType === 'update'" label="操作" align="center"
                               class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleNodeDocumentDelete(scope.row, scope.$index)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="dialogType !== 'view'">确 认</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="成果文件选择" :visible.sync="resultOpen" append-to-body>
      <el-table v-loading="resultFileLoading" :data="resultFileList" @selection-change="handleFileSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" align="center" type="index"/>
        <el-table-column label="成果类别" align="center" prop="type"/>
        <el-table-column label="成果名称" align="center" prop="name"/>
        <el-table-column label="是否必填" align="center" prop="fillFlag">
          <template slot-scope="scope">
            <status-tag :status="scope.row.fillFlag" :options="fillFlagOption"/>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <status-tag :status="scope.row.status" :options="statusOption"/>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        class="mt20"
        v-show="resultFileTotal>0"
        :total="resultFileTotal"
        :page.sync="queryResultFileParams.pageNum"
        :limit.sync="queryResultFileParams.pageSize"
        @pagination="getResultFileNodeList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResultFile" v-if="dialogType !== 'view'">确 认</el-button>
        <el-button @click="cancelFile">取 消</el-button>
      </div>
    </el-dialog>
    <select-depart
      ref="departRef"
      :selectDepartIds="strToArray(form.department)"
      @departEmit="selectDepartData"
    />-->
  </div>
</template>

<script>
import {
  listPlan,
  getNodeDetail,
  intervene,
} from "@/api/plan/intervene";
import { uploadFile } from "@/api/plan/common";
import Template from "@/views/plan/template/index.vue";
import {nodeStatusOption} from '@/constant';
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import {statusOption, fillFlagOption} from '@/constant';
import {listConfig} from "@/api/plan/config";
import SelectTree from "@/components/SelectTree.vue";
import { mapState } from 'vuex';
export default {
  name: "Intervene",
  components: {SelectTree, Template, StatusTag},
  dicts: ['stages'/*分期*/],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划反馈表格数据
      feedbackList: [],
      // 弹出层标题
      title: "节点反馈维护",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        nodeCode: null,
        feedbackPeople: null,
        projectId: null, // 项目id
        stageId: null,
        completeTime: null,
        notes: null,
        annexUrl: null,
        tenantId: null,
        creator: null,
        updater: null,
        deleted: null,
        deptNum: null, //城市公司
        planId: null, // 计划id
        projectName: null, // 项目名称
        nodeName: null,
      },
      // 表单参数
      form: {},
      nodeOutcomeDocumentList: [], // 反馈成果文件
      // 表单校验
      rules: {
        feedbackType: [{
          required: true,
          message: '反馈内容不能为空',
          trigger: 'blur'
        }],
        feedbackProgress: [{
          required: true,
          message: '完成百分比不能为空',
          trigger: 'change'
        }],
        startTime: [{
          required: true,
          message: '计划开始时间不能为空',
          trigger: 'change'
        }],
        endTime: [{
          required: true,
          message: '计划完成时间不能为空',
          trigger: 'change'
        }],
        department: [{
          required: true,
          message: '责任部门不能为空',
          trigger: 'blur'
        }],
        /*responsibilityPeople: [{
          required: true,
          message: '责任人不能为空',
          trigger: 'blur'
        }],*/
        completeCriteria: [{
          required: true,
          message: '完成标准不能为空',
          trigger: 'blur'
        }]
      },
      feedbackTypeOptions: [{
        "label": "过程反馈",
        "value": 1
      }, {
        "label": "完成反馈",
        "value": 2
      }],
      feedbackDetail: {}, // 反馈详情
      dialogType: 'update', // 新增、修改'update'，查看'view'
      resultFileList: [
        /*{
          "searchValue": null,
          "createBy": null,
          "createTime": "2023-12-29 06:53:47",
          "updateBy": null,
          "updateTime": "2023-12-29 06:53:57",
          "remark": null,
          "params": {},
          "id": "72c0fbce7a1a447db3a8664508c59304",
          "type": "会议报告",
          "name": "会议报告",
          "fillFlag": 0,
          "status": 0,
          "delFlag": "0"
        },
        {
          "searchValue": null,
          "createBy": null,
          "createTime": "2023-12-31 09:55:29",
          "updateBy": null,
          "updateTime": null,
          "remark": null,
          "params": {},
          "id": "72c0fbce7a1a447db3agdf86558c59304",
          "type": "验收报告",
          "name": "验收报告",
          "fillFlag": 0,
          "status": 1,
          "delFlag": "0"
        }*/
      ], // 成果文件列表
      selectedFileList: [], // 已选成果文件列表
      resultOpen: false, // 成果文件选择弹出层
      resultFileTotal: 0, // 成果文件总条数
      resultFileLoading: false, // 成果文件加载
      queryResultFileParams: {
        pageNum: 1,
        pageSize: 200,
        status: 0
      },
      selectDepartIds: '', // 选择的部门id
    };
  },
  computed: {
    deptList() {
      return this.$store.state.plan.deptList
    },
    latestVersionList() {
      return this.$store.state.plan.latestVersionList
    },
    nodeStatusOption() {
      return nodeStatusOption;
    },
    statusOption() {
      return statusOption
    },
    fillFlagOption() {
      return fillFlagOption
    },
    ...mapState('plan', {storeProjectList: 'projectList'})
  },
  created() {
    this.getList();
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList');
  },
  methods: {
    handleQueryProject(value){
      this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
    },
    strToArray(val){
      if(typeof val === 'string'){
        return val.split(',')
      }
      return [];
    },
    departNameShow(form){
      return !!form.departmentName;
    },
    selectDepartFun (row, index) {
      this.$refs.departRef.show()
    },
    selectDepartData (data) {
      console.log('data---------',data);
      this.form.department = data.map(item => item.department).join(',');
      this.form.departmentName = data.map(item => item.departName).join(',');
      this.$forceUpdate();
    },
    receiveData (data, row) {
      this.form.department = data;
    },
    /** 查询成果设置列表 */
    getResultFileNodeList() {
      this.resultFileLoading = true;
      listConfig(this.queryResultFileParams).then(response => {
        this.resultFileList = response.rows;
        this.resultFileTotal = response.total;
        this.resultFileLoading = false;
        this.resultFileTotal = response.total;
      });
    },
    handleResultOpen() {
      this.resultOpen = true;
      this.getResultFileNodeList();
    },
    submitResultFile() {
      this.resultOpen = false;
      this.nodeOutcomeDocumentList = this.nodeOutcomeDocumentList.concat(
        this.selectedFileList.map(item => {
          return {
            ...item,
            createTime: null
          };
        }));
    },
    cancelFile() {
      this.resultOpen = false;
    },
    handleFileSelectionChange(selection) {
      this.selectedFileList = selection;
      for (let item of this.selectedFileList) {
        delete item.id;
      }

    },
    filterFileMethod(query, item) { // 成果文件搜索
      // return item.pinyin.indexOf(query) > -1;
      return true;
    },
    async downloadFile(url, fileName) {
      const response = await fetch(url);
      const blob = await response.blob(); // 将响应转换为 Blob
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.click();
      window.URL.revokeObjectURL(downloadUrl);
    },
    handleFYRadioChange(val) {
      // this.rules.expectedCompletionDate[0].required = val !== 2;
      this.form.expectedCompletionDate =  val === 2 ? 100 : 0;
      this.form.feedbackType = val;
      this.$forceUpdate();


    },
    handleDeleteAnnexUrl(row) {
      row.annexUrl = null;
      row.createTime = null;
    },
    uploadFileHandle(event, row, index) {
      const file = event.target.files[0];
      if (!file) {
        // 没有选择文件
        return;
      }
      //  模拟数据
      // {
      //   type: null,
      //   createTime: null,
      //   annexUrl: null,
      //   outcomeDocumentsDesc: null,
      //   annexName: null
      // }
      this.nodeOutcomeDocumentList[index].annexName = file.name;
      this.nodeOutcomeDocumentList[index].createTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
      const formData = new FormData();
      formData.append('file', file);
      uploadFile(formData).then(response => {
        this.nodeOutcomeDocumentList[index].annexUrl = response.data.url;
        this.nodeOutcomeDocumentList[index].ossFileName = response.data.name;
        this.$forceUpdate();
      })
        .catch(error => {
          console.error(error);
        });
    },
    /** 查询计划反馈列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then(response => {
        this.feedbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        nodeCode: null,
        feedbackPeople: null,
        projectId: null,
        stageId: null,
        completeTime: null,
        notes: null,
        annexUrl: null,
        tenantId: null,
        creator: null,
        createTime: null,
        updater: null,
        updateTime: null,
        deleted: null,
        planId: null,
        feedbackType: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleNodeDocumentChange(selection) { // 成果文件选中

    },
    handleNodeDocument() { // 成果文件新增
      /*成果类型String type;
      成果名称String name;
      创建时间"yyyy-MM-dd HH:mm:ss" Date createTime;
      专业成果文件和证明材料描述 String outcomeDocumentsDesc;
      附件 String annexUrl;
      是否必填（0-是 1-否）Integer fillFlag;
      文件名称 annexName;
      文件上传后的附件名称 ossFileName
      */
      this.nodeOutcomeDocumentList.push({
        type: null,
        createTime: null,
        annexUrl: null,
        outcomeDocumentsDesc: null,
        annexName: null,
        ossFileName: null,
      });
    },
    handleNodeDocumentDelete(row, index) { // 成果文件删除
      this.nodeOutcomeDocumentList.splice(index, 1);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "节点反馈维护";
    },
    /*handleView(row) {
      this.dialogType = 'view';
      this.reset();
      const id = row.id || this.ids
      getFeedback(id).then(response => {
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList;
        this.form.nodeId = response.data.id;
        this.open = true;
        this.title = "节点反馈查看";
      });
    },*/
    /** 节点干预 */
    handleIntervene(row) {
      this.$router.push({
        path: '/plan/interveneDetail',
        query: {
          id: row.id,
          planId: row.planId,
          status: row.status
        }
      });
      /*return;
      this.dialogType = 'update';
      this.reset();
      const id = row.id || this.ids
      getNodeDetail(id).then(response => {
        this.form = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList;
        this.form.nodeId = response.data.id;
        if(row.status === 2 || row.status === 3) {
          // 根据节点状态，已完成的默认选择完成反馈，显示实际完成时间
          // 未完成的显示完成百分比和预计完成时间
          this.form.feedbackType = 2;
        }
        else{
          this.form.feedbackType = 1;
        }
        this.open = true;
        this.title = "节点干预";
      });*/
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          intervene({
            ...this.form,
            nodeOutcomeDocumentList: this.nodeOutcomeDocumentList
          }).then(response => {
            this.$modal.msgSuccess("节点干预成功");
            this.open = false;
            this.queryParams.pageNum = 1;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    /*handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除计划反馈编号为"' + ids + '"的数据项？').then(function () {
        return delFeedback(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },*/
    /** 导出按钮操作 */
    handleExport() {
      /*this.download('plan/feedback/export', {
        ...this.queryParams
      }, `feedback_${new Date().getTime()}.xlsx`)*/
    }
  }
};
</script>
<style lang="scss" scoped>
.feedback-container {
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;

  .feedback-form-title {
    margin-bottom: 20px;
    background-color: #f8f8f9;
    padding: 10px;
    width: 100%;
  }

  .icon-primary {
    color: #409eff;
  }

  .cursor {
    cursor: pointer;
  }

  .display-none {
    display: none !important;
  }

  .file-wrapper {
    .el-icon-circle-close {
      margin-left: 10px;
    }
  }

  .link {
    color: #409eff;
  }
}

.transfer-item {
  display: flex;

  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}

</style>
