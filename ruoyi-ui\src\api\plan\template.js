/*import request from '@/utils/request'

// 查询计划模版列表
export function listTemplate(query) {
  return request({
    url: '/plan/template/list',
    method: 'get',
    params: query
  })
}

// 查询计划模版详细
export function getTemplate(id) {
  return request({
    url: '/plan/template/' + id,
    method: 'get'
  })
}

// 新增计划模版
export function addTemplate(data) {
  return request({
    url: '/plan/template',
    method: 'post',
    data: data
  })
}

// 修改计划模版
export function updateTemplate(data) {
  return request({
    url: '/plan/template',
    method: 'put',
    data: data
  })
}

// 删除计划模版
export function delTemplate(id) {
  return request({
    url: '/plan/template/' + id,
    method: 'delete'
  })
}*/

import request from '@/utils/request'

// 查询计划模版列表
export function listTemplate(query) {
  return request({
    url: '/plan/planTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询计划模版详细
export function getTemplate(id) {
  return request({
    url: '/plan/planTemplate/' + id,
    method: 'get'
  })
}

// 新增计划模版
export function addTemplate(data) {
  return request({
    url: '/plan/planTemplate',
    method: 'post',
    data: data
  })
}

// 修改计划模版
export function updateTemplate(data) {
  return request({
    url: '/plan/planTemplate',
    method: 'put',
    data: data
  })
}

// 删除计划模版
export function delTemplate(id) {
  return request({
    url: '/plan/planTemplate/' + id,
    method: 'delete'
  })
}

export function saveTemplate(data) { // 保存模板
  return request({
    url: '/plan/planTemplate/save',
    method: 'post',
    data: data
  })
}

export function copyTemplate(data) { // 保存模板
  return request({
    url: '/plan/planTemplate/copy',
    method: 'post',
    data: data
  })
}


export function submitTemplate(data) { // 提交模板
  return request({
    url: '/plan/planTemplate/submit',
    method: 'post',
    data: data
  })
}



export function importNodeTemplateFile(data) { // 导入节点模板文件
  return request({
    url: '/plan/nodeTemplate/importData',
    method: 'post',
    data: data
  })
}

export function listNode(params) { // 获取模板下的节点
  return request({
    url: '/plan/planTemplate/nodeList',
    method: 'get',
    params
  })
}

export function changeStatus(data) { // 模板状态变更 0:禁用，1：启用
  return request({
    url: '/plan/planTemplate/changeStatus',
    method: 'post',
    data
  })
}

export function allTemplate(params) { // 查询所有计划模板
  return request({
    url: '/plan/planTemplate/allTemplate',
    method: 'get',
    params
  })
}





