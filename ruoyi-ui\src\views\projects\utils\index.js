function formatNull(value) {
  if (value === null || value === undefined) {
    return '--';
  }
  return value;
}

function rgbToHsl(r, g, b) {
  r /= 255, g /= 255, b /= 255;
  const max = Math.max(r, g, b), min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  return [h * 360, s, l];
}

function hslToRgb(h, s, l) {
  let r, g, b;

  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}

function getDarkerColor(hex) {
  // 将hex转换为RGB
  hex = hex.replace('#', '');
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  // 转换为HSL
  let [h, s, l] = rgbToHsl(r, g, b);

  // 调整HSL值以获得更深、更亮的颜色
  l = Math.min(1, l + 0.1); // 增加亮度
  s = Math.min(1, s + 0.2); // 增加饱和度

  // 转换回RGB
  const [newR, newG, newB] = hslToRgb(h, s, l);

  // 转换回hex格式
  return '#' + [newR, newG, newB].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
}

/**
 * 简易哈希函数，将字符串转化为相对短的 hash 值
 * @param {string} str
 * @returns {string}
 */
function generateHashKey(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = (hash << 5) - hash + str.charCodeAt(i);
    // JavaScript 位运算会自动转换为32位整数
    hash |= 0;
  }
  // 转成16进制并带上一个前缀
  return 'img_' + Math.abs(hash).toString(16);
}

/**
 * 保留两位小数
 * @param {number} num
 * @returns {string|'--'}
 */
function toFixed2(num) {
  if (num === null || num === undefined || isNaN(num) || num === '') {
    return '--';
  }
  const number = Number(num);
  if (isNaN(number)) {
    return '--';
  }
  return number.toFixed(2);
}

// 对数字的非小数部分进行千分位处理
function toThousands(num) {
  if (num === null || num === undefined || isNaN(num) || num === '') {
    return '--';
  }
  // return Number(num).toLocaleString();
  // 先保留两位小数
  const fixedNum = toFixed2(num);
  if (fixedNum === '--') {
    return '--';
  }
  // 分割整数和小数部分
  const [integerPart, decimalPart] = fixedNum.split('.');
  // 整数部分添加千分位
  const formattedInteger = Number(integerPart).toLocaleString();
  // 拼接
  return `${formattedInteger}.${decimalPart}`;
}

export default {
  formatNull,
  getDarkerColor,
  generateHashKey,
  toFixed2,
  toThousands
};

