<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="工程项目" prop="gcProjectName">
        <el-input
          v-model="queryParams.gcProjectName"
          placeholder="请输入工程项目"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="季度" prop="quarter">
        <el-select
          clearable
          v-model="queryParams.quarter"
          placeholder="请选择季度"
        >
          <el-option
            v-for="dict in dict.type.quarter"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:data:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:data:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:data:remove']"
          >删除</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--          v-hasPermi="['project:data:export']"-->
      <!--        >导出</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate"
          >导入模板下载</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工程项目" align="center" prop="gcProjectName" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="季度" align="center" prop="quarter">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.quarter" :value="scope.row.quarter" />
        </template>
      </el-table-column>
      <el-table-column
        label="综合得分"
        align="center"
        prop="comprehensiveScore"
      />
      <el-table-column label="塔吊" align="center" prop="towerCraneScore" />
      <el-table-column
        label="施工升降机"
        align="center"
        prop="constructionHoistScore"
      />
      <el-table-column
        label="电动吊篮"
        align="center"
        prop="electricHangBasketScore"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createdTime"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:data:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:data:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改大型机械检查对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工程项目" prop="gcProjectId">
          <el-select
            clearable
            v-model="form.gcProjectId"
            placeholder="请选择工程项目"
            class="w-100"
          >
            <el-option
              v-for="item in gcProjectList"
              :key="item.gcProjectId"
              :label="item.gcProjectName"
              :value="item.gcProjectId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年份" prop="year">
          <el-date-picker
            clearable
            size="small"
            v-model="form.year"
            type="year"
            value-format="yyyy"
            placeholder="选择年份"
            class="w-100"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="季度" prop="quarter">
          <el-select clearable v-model="form.quarter" placeholder="请选择季度" class="w-100">
            <el-option
              v-for="dict in dict.type.quarter"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="综合得分" prop="comprehensiveScore">
          <el-input
            v-model="form.comprehensiveScore"
            placeholder="请输入综合得分"
          />
        </el-form-item>
        <el-form-item label="塔吊" prop="towerCraneScore">
          <el-input v-model="form.towerCraneScore" placeholder="请输入塔吊" />
        </el-form-item>
        <el-form-item label="施工升降机" prop="constructionHoistScore">
          <el-input
            v-model="form.constructionHoistScore"
            placeholder="请输入施工升降机"
          />
        </el-form-item>
        <el-form-item label="电动吊篮" prop="electricHangBasketScore">
          <el-input
            v-model="form.electricHangBasketScore"
            placeholder="请输入电动吊篮"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 导入文件对话框 -->
    <el-dialog
      title="导入文件"
      :visible.sync="inputFileDialog"
      v-loading="inputFileLoading"
      width="1000px"
    >
      <div class="import-dialog">
        <importTable :dataList="importDataList" ref="importTable">
          <template v-slot:default="{ dataList, handleDelete }">
            <el-table :data="dataList">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" width="50">
                <template slot-scope="scope">
                  <span>{{ scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="工程项目"
                align="center"
                prop="gcProjectName"
              />
              <el-table-column label="年份" align="center" prop="year" />
              <el-table-column label="季度" align="center" prop="quarter">
                <template slot-scope="scope">
                  <dict-tag
                    :options="dict.type.quarter"
                    :value="scope.row.quarter"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="综合得分"
                align="center"
                prop="comprehensiveScore"
              />
              <el-table-column
                label="塔吊"
                align="center"
                prop="towerCraneScore"
              />
              <el-table-column
                label="施工升降机"
                align="center"
                prop="constructionHoistScore"
              />
              <el-table-column
                label="电动吊篮"
                align="center"
                prop="electricHangBasketScore"
              />
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row, scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </template>
        </importTable>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleImportDataSubmit"
          >确 定</el-button
        >
        <el-button @click="inputFileDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script>
import {
  getData,
  listData,
  addData,
  updateData,
  delData,
  importDataSubmit,
  importFile,
} from "@/api/projectOperate/gc/gcLargeMachineryCheckData";
import { listAll } from "@/api/projectOperate/gc/gcProejctData";
import importTable from "./components/importTable.vue";

export default {
  name: "Data",
  dicts: ["quarter"],
  components: {
    importTable,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大型机械检查表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        gcProjectId: null,
        year: null,
        quarter: null,
        comprehensiveScore: null,
        towerCraneScore: null,
        constructionHoistScore: null,
        electricHangBasketScore: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      inputFileLoading: false,
      gcProjectList: [],
      importDataList: [],
      inputFileDialog: false,
    };
  },
  created() {
    listAll().then((response) => {
      this.gcProjectList = response.data;
    });
    this.getList();
  },
  methods: {
    /** 查询大型机械检查列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        gcProjectId: null,
        year: null,
        quarter: null,
        comprehensiveScore: null,
        towerCraneScore: null,
        constructionHoistScore: null,
        electricHangBasketScore: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加大型机械检查";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getData(id).then((response) => {
        this.form = response.data;
        this.form.year = String(this.form.year);
        this.form.quarter = String(this.form.quarter);
        this.open = true;
        this.title = "修改大型机械检查";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateData(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除大型机械检查编号为"' + ids + '"的数据项？')
        .then(function () {
          return delData(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "project/gcLargeMachineryCheckData/export",
        {
          ...this.queryParams,
        },
        `大型机械检查_${new Date().getTime()}.xlsx`
      );
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      this.download(
        "project/gcLargeMachineryCheckData/exportTemplate",
        {
          ...this.queryParams,
        },
        `大型机械检查数据导入模板_${new Date().getTime()}.xlsx`
      );
    },
    // 导入文件
    importFile() {
      this.$refs.fileInput.click(); // 触发文件输入的点击事件
    },
    handleFileChange(event) {
      this.inputFileLoading = true;
      const files = event.target.files;
      const formData = new FormData();
      formData.append("file", files[0]);
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null;
      importFile(formData)
        .then((response) => {
          if (response.msg) {
            this.$modal
              .confirm(response.msg)
              .then(function () {
              })
          } else {
            this.inputFileDialog = true;
            this.importDataList = response.data;
            this.inputFileDialog = true;
          }
          this.inputFileLoading = false;
        })
        .catch(() => {
          this.inputFileLoading = false;
        });
    },
    handleImportDataSubmit() {
      this.inputFileLoading = true;
      this.importDataList = this.$refs.importTable.getDataList();
      const data = {
        gcLargeMachineryCheckDataDtoList: this.importDataList,
      };
      importDataSubmit(data)
        .then((response) => {
          this.$modal.msgSuccess("导入成功");
          this.inputFileDialog = false;
          this.getList();
        })
        .finally(() => {
          this.inputFileLoading = false;
        });
    },
  },
};
</script>
