<!-- 考核指标填报 -->
<template>
  <div v-loading="loading">
    <!-- Add operation buttons -->
    <div class="operation-bar">
      <el-button type="primary"  size="mini" @click="handleAdd">考核指标新增</el-button>
      <el-button type="primary"  size="mini" @click="handleDirectImport">考核指标导入</el-button>
      <el-button type="primary"  size="mini" @click="handleDownloadTemplate">导入模板下载</el-button>
      <!-- Add hidden file input -->
      <input
        type="file"
        ref="fileInput"
        style="display: none"
        accept=".xlsx,.xls"
        @change="onFileSelected"
      />
    </div>

    <div v-if="versionList && versionList.length">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane
          v-for="version in versionList"
          :key="version.version"
          :label="version.versionName"
          :name="version.version.toString()"
        >
          <el-row style="margin-bottom:10px;">
            <el-button type="primary" size="mini" :disabled="version?.list?.length === 0" @click="handleUpdate(version)">编辑</el-button>
          </el-row>
          <el-table
            :data="version.list"
            row-key="id"
            border
            :tree-props="{children: 'childList'}"
            max-height="500"
            :header-cell-style="{ background: '#f5f7fa' }"
            :show-summary="true"
            :summary-method="getSummary"
            default-expand-all
          >
            <el-table-column
              prop="targetName"
              label="指标名称"
              min-width="180"
              fixed
            />
            <el-table-column
              prop="amount"
              label="金额"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ scope.row.amount ? scope.row.amount.toFixed(2) : '-' }}
                <span v-if="['成本净利润率', '自有资金内部收益率', '考核成本净利润率（目标版）'].includes(scope.row.targetName.replace(/[\d\s]/g, ''))" class="percent-sign">%</span>
              </template>
            </el-table-column>
            <!-- Add empty data template -->
            <template slot="empty">
              <div class="el-table__empty-text">暂无数据</div>
            </template>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-else class="empty-block">
      <span class="empty-text">暂无数据</span>
    </div>

    <add-target-dialog
      ref="addTargetDialogRef"
      v-if="showAddDialog"
      :visible.sync="showAddDialog"
      :examine-target-define-data="examineTargetDefineData"
      @success="handleAddSuccess"
      :title="title"
      :type="type"
    />
  </div>
</template>
<script>
import {getDetail, examineTargetDefine, importExamineTarget, updateVersion} from "@/api/projectOperate/examineTargetVersionData";
import AddTargetDialog from './AddTargetDialog.vue'
import {formatDateWithTime} from "@/utils/index";

export default {
  name: 'FillExamineData',
  components: {
    AddTargetDialog
  },
  data() {
    return {
      loading: false,
      versionList: [],
      activeTab: '',
      examineTargetDefineData: [],
      showImportDialog: false,
      showAddDialog: false,
      versionType: '',
      projectCode: '',
      examineTargetVersionDataId: '',
      title: "考核指标新增",
      type: 'add',
    }
  },
  methods: {
    handleDownloadTemplate() {
      this.download('/project/examineTargetVersionData/detail/exportTemplate', {}, '考核指标导入模板.xlsx')
    },
    openHandle(projectCode, versionType, row, version) {
      // this.fetchExamineTargetDefine()
      this.versionType = versionType
      this.projectCode = projectCode
      this.version = version
      this.examineTargetVersionDataId = row.id
      this.fetchData(projectCode, versionType)
    },
    async fetchData(projectCode, versionType, version) {
      try {
        this.loading = true
        const response = await getDetail({ projectCode, versionType, version })
        if (response.code === 200 && response.data) {
          this.versionList = response.data
          // 设置第一个版本为默认active的tab
          if (this.versionList.length > 0) {
            this.activeTab = this.versionList[0].version.toString()
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleTabClick(tab) {
      // tab切换的处理逻辑，如果需要的话
      console.log('切换到版本:', tab.name)
    },
    // 考核科目集合
    async fetchExamineTargetDefine() {
      try {
        const res = await examineTargetDefine()
        this.examineTargetDefineData = res.rows
        this.showAddDialog = true
        this.$nextTick(() => {
          this.$refs.addTargetDialogRef.form.examineTargetVersionDataId = this.examineTargetVersionDataId
          this.$refs.addTargetDialogRef.form.versionType = this.versionType
          this.$refs.addTargetDialogRef.form.projectCode = this.projectCode
          this.$refs.addTargetDialogRef.examineTargets = this.examineTargetDefineData
        })
      } catch (error) {
        console.error('获取考核科目失败:', error)
        this.$message.error('获取考核科目失败')
      }
    },
    handleImport() {
      this.showImportDialog = true
    },
    handleAdd() {
      this.type = 'add'
      this.fetchExamineTargetDefine()
    },
    async fillExamineData(versionData) {
      try {
        const {version, versionName, versionDesc, list} = versionData;
        this.title = "考核指标编辑";
        // const res = await examineTargetDefine()
        this.examineTargetDefineData = list
        this.showAddDialog = true
        this.$nextTick(() => {
          this.$refs.addTargetDialogRef.form.examineTargetVersionDataId = this.examineTargetVersionDataId
          this.$refs.addTargetDialogRef.form.versionType = this.versionType
          this.$refs.addTargetDialogRef.form.projectCode = this.projectCode
          this.$refs.addTargetDialogRef.examineTargets = this.examineTargetDefineData;
          this.$refs.addTargetDialogRef.form.versionDate = this.examineTargetDefineData[0]?.createTime;
          this.$refs.addTargetDialogRef.form.versionDesc = this.examineTargetDefineData[0]?.versionDesc
          this.$refs.addTargetDialogRef.form.version = this.version
        })
      } catch (error) {
        console.error('获取考核科目失败:', error)
        this.$message.error('获取考核科目失败')
      }
    },
    handleUpdate(version) {
      this.type = 'edit'
      this.fillExamineData(version);
    },
    handleImportSuccess() {
      this.showImportDialog = false
      this.fetchData(this.projectCode, this.versionType)
    },
    handleAddSuccess() {
      this.showAddDialog = false
      this.fetchData(this.projectCode, this.versionType)
      // 触发事件总线，通知父组件刷新列表
      this.$bus.$emit('refresh-examine-target-list')
    },
    handleDirectImport() {
      this.$refs.fileInput.click()
    },
    updateVersionHandle(){ // 新增考核指标数据

    },
    async onFileSelected(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        this.loading = true
        const formData = new FormData()
        formData.append('file', file)

        const response = await importExamineTarget(formData)
        if (response.code === 200) {
          this.$message.success('导入成功')
          // this.fetchData(this.$route.params.projectCode, this.$route.params.versionType)
          // const params = {
          //   examineTargetVersionDataId: this.examineTargetVersionDataId, // 考核指标版本数据id
          //   versionType: this.versionType, // 版本类型(1-可研版、2-投决版、3-目标版、4-动态版、5-后评价版)
          //   versionDate: formatDateWithTime(new Date()),
          //   childList: this.examineTargets // 指标数据集合
          // }
          // const res = await updateVersion(params)

          this.showAddDialog = true
          this.$nextTick(() => {
            this.$refs.addTargetDialogRef.form.examineTargetVersionDataId = this.examineTargetVersionDataId
            this.$refs.addTargetDialogRef.form.versionType = this.versionType
            this.$refs.addTargetDialogRef.form.projectCode = this.projectCode
            this.$refs.addTargetDialogRef.examineTargets = response.data
          })

          // if (res.code === 200) {
          //   this.fetchData(this.projectCode, this.versionType, this.version)
          // } else {
          //   this.$message.error(res.msg)
          // }
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.loading = false
        // Reset file input
        event.target.value = ''
      }
    },
    getSummary(param) {
      console.log(param)
      const { columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '版本说明';
          return;
        }
        // For amount column, show version description
        if (column.property === 'amount') {
          sums[index] = (data[0] && data[0].versionDesc) || '-';
          return;
        }
        sums[index] = '';
      });
      return sums;
    }
  }
}
</script>
<style scoped lang="scss">
.operation-bar {
  margin-bottom: 16px;

  .el-button {
    margin-right: 6px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.empty-block {
  text-align: center;
  padding: 32px 0;

  .empty-text {
    font-size: 14px;
    color: #909399;
  }
}
</style>
