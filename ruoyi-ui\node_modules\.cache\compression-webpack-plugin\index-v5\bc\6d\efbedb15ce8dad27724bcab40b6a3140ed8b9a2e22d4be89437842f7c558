
582d10e39e0203cbb44834293ed144a2ebb54f18	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"8a07cff50f7ab0f41cf0fe8cb1cc5a84\"}","integrity":"sha512-bLmz2vVHzKUiZftTVKZUYou0V1QGb8O+o+/WQ1xiUcUpx9XO/qDB7PXKJ74HitlvX3H8+Uw1D7CoH32W7Oaj/w==","time":1754311732393,"size":12044138}