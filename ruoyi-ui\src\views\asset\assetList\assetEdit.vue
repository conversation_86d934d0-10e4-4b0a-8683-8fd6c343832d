<template>
  <div>
    <el-dialog
      :title="assetTitle"
      :visible.sync="isVisible"
      v-if="isVisible"
      width="80%"
      append-to-body
      @close="closeHandle"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="公司代码" prop="companyCode">
              <el-input v-model="form.companyCode" placeholder="请输入公司代码" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="利润中心" prop="profitCenter">
              <el-input v-model="form.profitCenter" placeholder="请输入利润中心" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产分类" prop="assetClassification">
              <el-select
                v-model="form.assetClassification"
                placeholder="请选择资产分类"
                clearable
                class="assetClassification"
                filterable
              >
                <el-option
                    v-for="dict in assetClassificationList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
         <el-col :span="6">
            <el-form-item label="资产名称" prop="assetName">
              <el-input v-model="form.assetName" placeholder="请输入资产名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="规格型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="明细类别" prop="detailedCategories">
              <el-select
              v-model="form.detailedCategories"
              placeholder="请选择明细类别"
              clearable
              filterable
              >
                <el-option
                    v-for="dict in detailedCategoriesList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务范围" prop="businessScope">
              <el-input v-model="form.businessScope" placeholder="请输入业务范围" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="使用状态" prop="usageStatus">
              <el-select
              v-model="form.usageStatus"
              placeholder="请选择使用状态"
              clearable
              filterable
              >
                <el-option
                    v-for="dict in usageStatusList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="管理部门" prop="managementDepartment">
              <el-select
              v-model="form.managementDepartment"
              placeholder="请选择管理部门"
              clearable
              filterable
              >
                <el-option
                    v-for="dict in managementDepartmentList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用项目" prop="usingProjects">
              <el-input v-model="form.usingProjects" placeholder="请输入使用项目" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计提部门" prop="accrualDepartment">
              <el-input v-model="form.accrualDepartment" placeholder="请输入计提部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="数量" prop="number">
              <el-input v-model="form.number" placeholder="请输入数量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计量单位" prop="measurementUnit">
              <el-select
              v-model="form.measurementUnit"
              placeholder="请选择计量单位"
              clearable
              filterable
              >
                <el-option
                    v-for="dict in measurementUnitList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="存放地点" prop="storageLocation">
              <el-input v-model="form.storageLocation" placeholder="请输入存放地点" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用人" prop="user">
              <el-input v-model="form.user" placeholder="请输入使用人" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="closeHandle">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AssetEdit",
  props:{
    assetDetail: {
      type: Object,
      default: {}
    },
    assetEditVisible: Boolean,
    assetTitle: String,
  },
  data () {
    return {
      // 表单参数
      form: {},
      // 表单校验`
      rules: {
        companyCode: [
          { required: true, message: "公司代码不能为空", trigger: "blur" }
        ],
        profitCenter: [
          { required: true, message: "利润中心不能为空", trigger: "blur" }
        ],
        assetClassification: [
          { required: true, message: "资产分类不能为空", trigger: "change" }
        ],
        assetName: [
          { required: true, message: "资产名称不能为空", trigger: "blur" }
        ],
        model: [
          { required: true, message: "规格型号不能为空", trigger: "blur" }
        ],
        detailedCategories: [
          { required: true, message: "明细类别不能为空", trigger: "change" }
        ],
        usageStatus: [
          { required: true, message: "使用状态不能为空", trigger: "change" }
        ],
        managementDepartment: [
          { required: true, message: "管理部门不能为空", trigger: "change" }
        ],
        number: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
        measurementUnit: [
          { required: true, message: "计量单位不能为空", trigger: "change" }
        ],
        storageLocation: [
          { required: true, message: "存放地点不能为空", trigger: "blur" }
        ],
        user: [
          { required: true, message: "使用人不能为空", trigger: "blur" }
        ],
      },
      isVisible: false,
      assetClassificationList: [],
      detailedCategoriesList: [],
      usageStatusList: [],
      managementDepartmentList: [],
      measurementUnit: [],
    }
  },
  computed: {
  },
  watch: {
    assetEditVisible(val){
      if(val){
        this.isVisible = val
        // 资产类型
        this.getCommonList('assetCategory')
        // 明细类别
        this.getCommonList('detailCategory')
        // 使用状态
        this.getCommonList('useStatus')
        // 管理部门

        // 使用部门

        // 计量单位

      }
    }
  },
  methods: {
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))
          this.$emit('handleFormFun', data)
          this.isVisible = false
        }
      })
    },
    closeHandle() {
      this.$emit('closeEditHandle')
      this.isVisible = false
    },
    getCommonList(type){ // 公共类
      this.getDicts(type).then(res => {
        let data = res.data
        let list =  data.map(item => {
          return {
              label: item.dictLabel,
              value: item.dictValue,
            }
          }
        )
        if(type === 'assetCategory'){ // 资产类型
          this.assetClassificationList = list
        }
        if(type === 'detailCategory'){ // 明细类别
          this.detailedCategoriesList = list
        }
         if(type === 'useStatus'){ // 使用状态
          this.usageStatusList = list
        }

        // 管理部门

        // 使用部门

        // 计量单位

        
      })
    },
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/index.scss";
.time ::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 150px;
}
.iconStyle {
  color: var(--color);
  font-weight: 600;
  font-size: 20px;
  cursor: pointer;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.el-form-item {
  .el-select{
    width: 100%;
  }
}
</style>
