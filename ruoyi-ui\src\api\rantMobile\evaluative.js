import request from '@/utils/request'
// import request from '@/utils/request-rant-mobile'
// 查询评价信息列表
export function listEvaluative(query) {
  return request({
    url: '/rant/evaluative/list',
    method: 'get',
    params: query
  })
}

// 查询评价信息详细
export function getEvaluative(id) {
  return request({
    url: '/rant/evaluative/' + id,
    method: 'get'
  })
}

// 新增评价信息
export function addEvaluative(data) {
  return request({
    url: '/rant/evaluative',
    method: 'post',
    data: data
  })
}

// 修改评价信息
export function updateEvaluative(data) {
  return request({
    url: '/rant/evaluative',
    method: 'put',
    data: data
  })
}

// 删除评价信息
export function delEvaluative(id) {
  return request({
    url: '/rant/evaluative/' + id,
    method: 'delete'
  })
}

// 查询评价信息列表
export function evaluativeList(params = {}) {
  return request({
    url: '/rant/evaluative/list',
    method: 'get',
    params
  })
}

// 查询评价平均分
export function avgScore(rantMattersId) {
  return request({
    url: '/rant/evaluative/avgScore/' + rantMattersId,
    method: 'get',
  })
}

