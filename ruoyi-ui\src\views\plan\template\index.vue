  <template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="专业名称" prop="specialityName">-->
<!--        <el-select v-model="queryParams.specialityName" placeholder="请选择专业名称" clearable>-->
<!--          <el-option v-for="dict in dict.type.departs"-->
<!--           :key="dict.value"-->
<!--           :label="dict.label"-->
<!--           :value="dict.value"-->
<!--        ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="模版名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模版名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['plan:template:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['plan:template:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plan:template:remove']"
        >删除</el-button>
      </el-col>
      <!--<el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:template:export']"
        >导出</el-button>
      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="55" align="center"/>
<!--      <el-table-column label="专业名称" align="center" prop="specialityName">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="dict.type.departs" :value="scope.row.specialityName"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="模版名称" align="center" prop="templateName">
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.templateName)">{{ scope.row.templateName || '无' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="模版状态" align="center" prop="templateStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.templateStatus === 0" type="info">草稿</el-tag>
          <el-tag v-else type="success">完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="更新者" align="center" prop="updateBy">
        <template slot-scope="scope">
          <span v-if="!!scope.row.updateBy">{{scope.row.updateBy}}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="createTime" width="150"/>
      <el-table-column label="更新日期" align="center" prop="updateTime" width="150"/>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plan:template:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['plan:template:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleChangeTemplateStatus(scope.row)"
            v-if="scope.row.status === 1"
          >禁用</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleChangeTemplateStatus(scope.row)"
            v-if="scope.row.status === 0"
          >启用</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document-copy"
            @click="handleCopyTemplate(scope.row)"
          >复制</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="handleListNode(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 复制模板   -->
    <el-dialog title="复制模板" :visible.sync="openCopy" width="500px" append-to-body>
      <el-form ref="formCopyTemplate" :model="formCopyTemplate" :rules="formCopyRules" label-width="80px">
        <el-form-item label="模版名称" prop="templateName">
          <el-input v-model="formCopyTemplate.templateName" placeholder="请输入模版名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormCopyTemplate">确 定</el-button>
        <el-button @click="openCopy=false;">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改计划模版对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
<!--        <el-form-item label="专业名称" prop="specialityName">-->
<!--          <el-select v-model="form.specialityName" placeholder="请选择专业名称" clearable class="width-100">-->
<!--            <el-option v-for="dict in dict.type.departs"-->
<!--                       :key="dict.value"-->
<!--                       :label="dict.label"-->
<!--                       :value="dict.value"-->
<!--            ></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="模版名称" prop="templateName">
          <el-input v-model="form.templateName" placeholder="请输入模版名称" />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-select v-model="form.status" class="width-100">
            <el-option :value="1" label="启用"></el-option>
            <el-option :value="0" label="禁用"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新增计划模板节点 -->
    <el-dialog :title="templateObjTitle" :visible.sync="templateObjOpen" append-to-body width="1350px">
      <div class="dialog-content">
        <NodeForm ref="updateNodeRef" :existNodeList="planNodeTemplateList" :templateId="id" :loading="planNodeSubmitLoading"></NodeForm>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="" @click="saveNodeForm">保存</el-button>
        <el-button type="primary" @click="submitNodeForm">提交</el-button>
        <el-button type="" @click="cancleNodeForm">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addTemplate,
  changeStatus,
  copyTemplate,
  delTemplate,
  getTemplate,
  listNode,
  listTemplate,
  saveTemplate,
  submitTemplate,
  updateTemplate
} from "@/api/plan/template";
import NodeForm from '@/views/plan/components/NodeForm/index.vue'

export default {
  name: "Template",
  dicts: ['departs' /*专业名称*/],
  components: {
    NodeForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      templateNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划模版表格数据
      templateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        specialityName: null,
        templateName: null,
        status: null,
        isEdit: null,
        tenantId: null,
        deptId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: "模版名称不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "是否启用不能为空", trigger: "blur" }
        ],
      },
      templateObjOpen: false,
      templateObjTitle: '修改计划模板',
      planNodeTemplateList: [], // 节点数组
      id: '', // 计划模板id
      planNodeSubmitLoading: false,
      openCopy: false,
      formCopyTemplate: {},
      formCopyRules: {
        templateName: [
          { required: true, message: "模版名称不能为空", trigger: "blur" }
        ],
      },
      nodeSection: []

    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleChangeTemplateStatus(row){ // 模板状态变更
      changeStatus({
        id: row.id,
        status: row.status === 1 ? 0 : 1,
      }).then(response => {
        this.$modal.msgSuccess(row.status === 1 ? "禁用成功" : "启用成功");
        this.queryParams.pageNum = 1;
        this.getList();
      })
    },
    handleListNode(row){
      this.planNodeSubmitLoading = true;
      this.templateObjOpen = true;
      this.templateObjTitle = '修改模板节点';
      this.id = row.id; // 保存模板id
      listNode({templateId: row.id}).then(response => { // 获取模板下的节点
        this.planNodeTemplateList = response.rows;
        // console.log('planNodeTemplateList---', this.planNodeTemplateList)
        this.planNodeSubmitLoading = false;
      })
    },
    // 计划节点保存
    async saveNodeForm() {
      this.planNodeSubmitLoading = true;
      try {
        let response = await saveTemplate(this.$refs.updateNodeRef.getUpdateNodeList());
        // console.log('response---', response)
        if(response.code === 200){
          this.$modal.msgSuccess("保存成功");
          this.planNodeSubmitLoading = false;
          this.queryParams.pageNum = 1;
          this.getList();
        }
        else{
          this.$modal.msgError("保存失败");
        }

      } catch (error) {
        // Handle error
        this.planNodeSubmitLoading = false;
        this.$modal.msgError("保存失败");
      }
    },
    // 计划节点提交
    async submitNodeForm(){
      // nodeIndex 节点顺序
      this.planNodeSubmitLoading = true;
      // this.$refs.updateNodeRef.setIsFormSubmitted(true);
      let data = this.$refs.updateNodeRef.getUpdateNodeList();
      // 对节点输入进行校验
      /*let valid = data.planNodeTemplateList.every(item => {
        return item.nodeName && item.nodeDesc && item.completeCriteria && item.departmentName
      })*/

      // 对节点输入进行校验
      let valid = true;
      let missingFields = [];
      data.planNodeTemplateList.forEach(item => {
        if (!item.nodeName) {
          missingFields.push("节点名称");
          valid = false;
        }
        if (!item.nodeDesc) {
          missingFields.push("节点描述");
          valid = false;
        }
        if (!item.completeCriteria) {
          missingFields.push("完成标准");
          valid = false;
        }
        if (!item.department) {
          missingFields.push("责任部门");
          valid = false;
        }
        if (item.isDel === "" || item.isDel === undefined) {
          missingFields.push("是否可删除");
          valid = false;
        }
      });

      if(valid){
        try {
          let response = await submitTemplate(data);
          if(response.code === 200){
            this.$modal.msgSuccess("提交成功");
            this.planNodeSubmitLoading = false;
            this.open = false;
            this.templateObjOpen = false;
            this.queryParams.pageNum = 1;
            this.getList();
          }
          else{
            this.$modal.msgWarning("提交失败");
          }
          // Handle success
        } catch (error) {
          this.planNodeSubmitLoading = false;
          this.$modal.msgWarning("提交失败");
          // Handle error
        }
      }
      else{
        // this.$modal.msgWarning("节点名称、节点描述、完成标准、责任部门必填");
        this.$modal.msgWarning(missingFields.join("、") + "必填");
        this.planNodeSubmitLoading = false;
      }

    },
    // 取消计划节点
    cancleNodeForm(){
      this.open = false;
      this.templateObjOpen = false;
    },
    /** 查询计划模版列表 */
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        this.templateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        specialityName: null,
        templateName: null,
        status: 0,
        isEdit: null,
        tenantId: null,
        deptId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.templateNames = selection.map(item => item.templateName)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加计划模版";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTemplate(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改计划模版";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.planNodeTemplateList = [];
            addTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.getList();
              this.open = false;
              this.templateObjOpen = true;
              this.id = response.data.id;
              this.planNodeTemplateList = [];
              /*this.planNodeTemplateList.push({
                templateId: this.id,
                nodeName: '', // 节点名称
                nodeDesc: '', // 节点描述
                completeCriteria: '', // 完成标准
                durationNum: '', // 标准工期
                department: '', // 责任部门
              });*/
              /*this.open = false;
              this.getList();*/
            });
          }
        }
      });
    },
    submitFormCopyTemplate(){
      this.$refs["formCopyTemplate"].validate(valid => {
        if (valid) {
          copyTemplate(this.formCopyTemplate).then(res => {
            this.$modal.msgSuccess("模板复制成功");
            this.openCopy = false;
            this.getList();
          });
        }
      });
    },
    handleCopyTemplate(row){
      this.openCopy = true;
      this.formCopyTemplate = {id: row.id, templateName: ''};

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const templateNames = row.templateName || this.templateNames
      this.$modal.confirm('是否确认删除计划模版名称为"' + templateNames + '"的数据项？').then(function() {
        return delTemplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('plan/planTemplate/export', {
        ...this.queryParams
      }, `template_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped lang="scss">
.mrb-15{
  margin-bottom: 15px;
}
.tip{
  margin-top: 15px;
}
.dialog-content {
  max-height: 70vh; /* 你可以根据需要设置这个值 */
  overflow-y: auto;
}
</style>
