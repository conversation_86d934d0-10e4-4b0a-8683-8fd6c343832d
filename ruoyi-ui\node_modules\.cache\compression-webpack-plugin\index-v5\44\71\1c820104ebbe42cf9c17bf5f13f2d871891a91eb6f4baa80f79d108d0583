
53498d8d9621f69a1707375d398118721f21ab97	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"60bcb86c96bf17d7bb735cc09717b5c9\"}","integrity":"sha512-hzNWmYgWcdN1Qgf/2nod7C+36otBHGGJVrXhpmplLd1k7QxMEhdnNSvO3tdXofvZEDUhcrM6Mdteh0xVlz3mSQ==","time":1754311558029,"size":12044653}