import request from '@/utils/request'

// 查询用户访问记录列表
export function listRecord(query) {
  return request({
    url: '/sales/user/record/list',
    method: 'get',
    params: query
  })
}

// 查询用户访问记录详细
export function getRecord(id) {
  return request({
    url: '/sales/user/record/' + id,
    method: 'get'
  })
}

// 查询用户访问详情记录列表
export function listRecordDetail(query) {
  return request({
    url: '/sales/user/record/detailList',
    method: 'get',
    params: query
  })
}
