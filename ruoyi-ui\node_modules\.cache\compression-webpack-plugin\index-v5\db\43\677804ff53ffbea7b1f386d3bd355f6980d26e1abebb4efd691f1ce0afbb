
89a7544bed1b1678053f896262cdbad42cbd3004	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"82d6311ac263aee95cf5d0646f45f963\"}","integrity":"sha512-WqioxZTFCEQ7aHOd9+yoBZa7ZG+qwnWinn7trZb41vFC2Tns4/IcQ4dqHrLJxzG0O/t3J8UZUWny9Lb9+XSyrg==","time":1754311467299,"size":12045270}