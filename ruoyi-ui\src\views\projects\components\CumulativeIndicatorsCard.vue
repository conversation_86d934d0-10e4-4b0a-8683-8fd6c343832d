<script>
export default {
  name: 'CumulativeIndicatorsCard',
  props: {
    value: {
      type: [Number, String],
      required: true
    },
    unit: {
      type: String,
      default: ''
    },
    currency: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'pink',
      validator: (value) => ['pink', 'blue', 'blue-sky', 'purple', 'green', 'cyan'].includes(value)
    },
    label: {
      type: String,
      default: '-'
    }
  }
}
</script>

<template>
  <div class="cumulative-indicators-card" :class="`type-${type}`">
    <div class="indicator-value">
      <span class="currency" v-if="currency">{{ currency }}</span>
      <span class="amount">{{ value }}</span>
      <span class="unit">{{ unit }}</span>
    </div>
    <div class="indicator-label">{{ label }}</div>
  </div>
</template>

<style scoped lang="scss">
.cumulative-indicators-card {
  width: 282px;
  padding: 29px;
  box-shadow: 0px 11 27px 0px rgba(123,111,242,0.5);
  border-radius: 5px 5px 5px 5px;
  color: white;
  text-align: center;

  // 粉色卡片 - 累计签约金额
  &.type-pink {
    background-color: #F28D61;
  }

  // 天蓝色卡片 - 累计回款金额
  &.type-blue {
    background-color: #3EB6CF;
  }

   // 蓝色卡片 - 动态货值
   &.type-blue-sky {
    background-color: #499DF2;
  }

  // 紫色卡片 - 动态成本
  &.type-purple {
    background-color: #7B6FF2;
  }

  // 绿色卡片 - 一级节点按期完成率
  &.type-green {
    background-color: #53BD88;
  }

  // 青色卡片 - 二级节点按期完成率
  &.type-cyan {
    background-color: #3EB6CF;
  }

  .card-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .indicator-value {
    margin-bottom: 16px;
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    .currency {
      font-size: 19px;
    }

    .unit {
      font-size: 19px;
    }
    .amount{
      font-size: 24px;
    }
  }

  .indicator-label {
    font-size: 19px;
  }
}
</style>
