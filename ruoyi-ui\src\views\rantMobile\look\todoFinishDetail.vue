<template>
  <div class="app-container app-container-feedback">
    <div class="rant-content margin-top-0">
      <div class="rant-detail-content">
        <!-- 基本信息 -->
        <div class="rant-form-title">
          <img src="@/assets/rant/rant-info.png" class="icon">
          基本信息
        </div>

        <van-form class="rant-detail">
          <div class="form-group">
            <div class="form-item">
              <div class="form-label">来源</div>
              <div class="form-value">{{ rantDetail.ranterName }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">类型</div>
              <div class="form-value cu-flex">
                <dict-tag v-for="(type, typeIndex) in rantDetail.mattersType?.split(',')" :key="typeIndex"
                  :options="mattersTypeOptionsLabel" :value="type" />
              </div>
            </div>

            <div class="form-item">
              <div class="form-label">分类</div>
              <div class="form-value">
                <dict-tag :options="classifyOptionsLabel" :value="rantDetail.rantClassify" />
              </div>
            </div>

            <div class="form-item">
              <div class="form-label">责任人</div>
              <div class="form-value">{{ rantDetail.responsiblePersonName }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">责任部门</div>
              <div class="form-value">{{ rantDetail.deptName }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">责任部门负责人</div>
              <div class="form-value">{{ rantDetail.respDeptResponsiblerName }}</div>
            </div>

            <div class="form-item" v-if="type != 'myRant'">
              <div class="form-label">分管领导</div>
              <div class="form-value">{{ rantDetail.respDeptLeaderName }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">计划完成时间</div>
              <div class="form-value">{{ rantDetail.planTime }}</div>
            </div>

            <div class="form-item" v-if="type == 'matters'">
              <div class="form-label">是否私密</div>
              <div class="form-value">{{ rantDetail.isPrivate == 1 ? "是" : "否" }}</div>
            </div>

            <div class="form-item" v-if="type == 'matters'">
              <div class="form-label">可见范围</div>
              <div class="form-value"> {{ rantDetail.visibleScope == null ? "所有人" : rantDetail.visibleScope }}</div>
            </div>

            <div class="form-item" v-if="type == 'matters'">
              <div class="form-label">创建人</div>
              <div class="form-value"> {{ rantDetail.createByName }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">是否结项</div>
              <div class="form-value"> {{ rantDetail.isCompletion ? "是" : "否" }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">内容</div>
              <div class="form-value content-text">{{ rantDetail.rantContent }}</div>
            </div>

            <div class="form-item">
              <div class="form-label">措施</div>
              <div class="form-value content-text">{{ rantDetail.solution }}</div>
            </div>
          </div>

          <!-- 本次进展 -->
          <div class="rant-form-title" style="margin-top:16px;">
            <img src="@/assets/rant/rant-this-progress.png" class="icon">
            最新进展
          </div>

          <div class="form-group text-area">
            <textarea class="progress-textarea" v-model="rantDetail.thisProgress" rows="6" placeholder="请输入本次进展"
              readonly></textarea>
          </div>

          <div class="form-group">
            <!--  <div class="form-item">
              <div class="form-label">是否结项</div>
              <div class="form-value">{{ rantDetail.isCompletion ? '是' : '否' }}</div>
            </div>

            <div class="form-item" v-if="rantDetail.isCompletion">
              <div class="form-label">结项时间</div>
              <div class="form-value">{{ rant.closingTime }}</div>
            </div> -->

            <div class="form-item">
              <div class="form-label">成果</div>
              <div class="form-value">
                <div v-if="rantDetail.fileList && rantDetail.fileList.length > 0" class="file-list">
                  <div v-for="(file, fileIndex) in rantDetail.fileList" :key="fileIndex" class="file-item">
                    <a :href="file.url" target="_blank" class="file-link">
                      <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
                      <span class="extension-part">{{ getFileExtension(file.name) }}</span>
                      <van-icon class="download-icon" name="download" @click.stop="downloadFile(file)" />

                    </a>
                  </div>
                </div>
                <div v-else class="form-empty">暂无文件</div>
              </div>
            </div>
          </div>
        </van-form>

        <!-- 进度情况 -->
        <ProgressList :progressList="recordDtoList" />
        <!-- 评价信息卡片 -->
        <div class="detail-card">
          <div class="card-header cu-flex">
            <div>
              <image class="header-icon" src="@/assets/rant/rant-rate.png" mode="aspectFit"></image>
              <div class="header-title">评价信息</div>
            </div>

            <div>
              <div class="">
                <div class="rating-stars cu-flex">
                  <van-rate :value="evaluateAvgScore / 2" :allow-half="true" readonly color="#ffd21e"  void-icon="star"
                            void-color="#eee"/>
<!--                  <div v-for="star in generateStars(evaluateAvgScore / 2)" :key="star.id" :class="star.class">★</div>-->
                  <div class="rating-score">{{ evaluateAvgScore }}分</div>
                </div>
              </div>
            </div>
          </div>

          <div class="rating-list">
            <div class="rating-item" v-for="(item, index) in rantEvaluativeInfoDtoList" :key="index">
              <div class="rating-header cu-flex">
                <div>
                  <div class="rating-stars small cu-flex">
                    <van-rate :value="item.score / 2" :allow-half="true" readonly color="#ffd21e" void-icon="star"
                              void-color="#eee"/>
<!--                    <div v-for="star in generateStars(item.score / 2)" :key="star.id" :class="star.class">★</div>-->
                    <div class="rating-score">{{ item.score }}分</div>
                  </div>
                </div>
                <div>
                  <div class="rating-info cu-flex">
                    <div class="rating-user">评价人：{{ item.evaluatorName || '--' }}</div>
                    <div class="rating-date">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
              <div class="rating-content">
                <div class="rating-text">{{ item.evaluationContent || '--' }}</div>

              </div>
            </div>

            <div class="empty-state" v-if="!rantEvaluativeInfoDtoList || rantEvaluativeInfoDtoList.length === 0">
              <div>暂无评价信息</div>
            </div>
          </div>
        </div>
        <div class="item-actions">
          <van-button type="primary" class="custom-btn-submit" size="small" @click="handleEvaluation(index)">评
            价</van-button>
        </div>
      </div>
    </div>

    <!-- 评价弹窗 -->
    <van-popup v-if="evaluationVisible" v-model="evaluationVisible" position="center" round closeable
      close-icon-position="top-right">
      <div class="evaluation-popup">
        <div class="popup-header">
          事项评价
        </div>

        <van-form ref="evaluationForm" @submit="submitEvaluation">
          <div class="form-content">
            <van-field name="score" label="评分" :rules="[{ required: true, message: '请选择评分' }]"
              style="padding-left:0;padding-right:0;">
              <template #input>
                <div class="rate-container">
                  <van-rate v-model="evaluationScore" :count="5" allow-half color="#FF9900" void-color="#C8C9CC"
                    size="20" @change="handleRateChange" />
                  <span class="score-text">{{ displayRating }}</span>
                </div>
              </template>
            </van-field>

            <van-field v-model="evaluationForm.evaluationContent" name="evaluationContent" type="textarea" label=""
              placeholder="请输入评价内容" rows="4" maxlength="200" show-word-limit
              style="border: 1px solid #666666;margin-bottom: 20px;"
              :rules="[{ required: true, message: '请输入评价内容' }]" />
          </div>

          <div class="popup-footer">
            <van-button type="primary" class="custom-btn-save" style="width: 40%;" size="small"
              @click="evaluationVisible = false">取
              消</van-button>
            <van-button type="primary" class="custom-btn-submit" style="width: 40%;" size="small"
              :loading="submitLoading"
              @click="submitEvaluation">提
              交</van-button>

          </div>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>
<script>
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/
import {close} from '@/utils'
import { daiBanLookRankMatter, readDaiBanChange } from "@/api/rantMobile/matters";
import { avgScore, evaluativeList, addEvaluative } from "@/api/rantMobile/evaluative";
import ProgressList from '../components/ProgressList/index.vue'
import { Button, Popup, Form, Field, Rate } from 'vant';
import { getDicts } from '@/api/rantMobile/common.js';
import mixin from '../mixins'
export default {
  name: "Deatil",
  components: {
    ProgressList,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Form.name]: Form,
    [Field.name]: Field,
    [Rate.name]: Rate,
  },
  mixins: [mixin],
  // dicts: ["rant_classify", "rant_matters_type"],
  data() {
    return {
      dictData: {
        rant_classify: [],
        rant_matters_type: []
      },
      // 遮罩层
      loading: true,
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      evaluativeTotal: 0,
      evaluativeQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      evaluateAvgScore: 0,
      evaluationVisible: false,
      evaluationForm: {
        id: null,
        score: 0,
        evaluationContent: ''
      },
      hoverRating: null,
      evaluationScore: 0,
      submitLoading: false,
      evaluationRules: {
        evaluationScore: [
          { required: true, message: '请选择评分', trigger: 'change' }
        ],
        evaluationContent: [
          { required: true, message: '请输入评价内容', trigger: 'blur' }
        ]
      },
    };
  },
  async mounted() {
    // this.initDictData();

    await this.fetchMattersType();
    await this.fetchRantClassify();
    const readTodoNoticeId = this.$route.query.readTodoNoticeId;
    this.handleGetRantDetail(readTodoNoticeId);
  },
  computed: {
    displayRating() {
      const value = this.hoverRating !== null ? this.hoverRating : this.evaluationScore
      return value !== null ? `${(value * 2).toFixed(1)}分` : '0.0分'
    },
  },
  methods: {
    // 生成评分星星
    generateStars(rating) {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        let starClass = 'star';
        if (rating >= i) {
          starClass = 'star filled';
        } else if (rating > i - 0.5) {
          starClass = 'star half-filled';
        }
        stars.push({
          id: `star-${i}`,
          class: starClass
        });
      }
      return stars;
    },
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    handleGetRantDetail(readTodoNoticeId) {
      daiBanLookRankMatter(readTodoNoticeId).then((response) => {
        this.rantDetail = response.data;
        this.evaluativeQueryParams.rantMattersId = response.data.id;
        this.recordDtoList = response.data.recordDtoList;
        this.getAvgScore(this.evaluativeQueryParams.rantMattersId);
        this.getEvaluativeList();
        this.loading = false;
        //调用已阅接口
        readDaiBanChange(readTodoNoticeId).then((response) => {
        });
      });
    },
    handleClose() {
      this.$modal.confirm("确认是否关闭当前页面").then(() => {
        this.$store.dispatch("tagsView/delView", this.$route);
      });
    },
    getAvgScore(id) {
      avgScore(id).then((response) => {
        this.evaluateAvgScore = response.data;
      });
    },
    getEvaluativeList() {
      evaluativeList(this.evaluativeQueryParams).then((response) => {
        this.rantEvaluativeInfoDtoList = response.rows;
        this.evaluativeTotal = response.total;
      });
    },
    /** 评价按钮操作 */
    handleEvaluation() {
      this.evaluationForm = {
        rantMattersId: this.evaluativeQueryParams.rantMattersId,
        score: 0,
        evaluationContent: ''
      }
      this.evaluationScore = 0
      this.evaluationVisible = true
    },
    // 评分变化处理
    handleRateChange(value) {
      this.evaluationScore = value
    },
    // 新增悬停事件处理
    handleHoverChange(value) {
      this.hoverRating = value
    },
    /** 提交评价 */
    submitEvaluation() {
      this.$refs.evaluationForm.validate().then(() => {
        // 转换评分值（前端保存0-5分，允许0.5分）
        this.evaluationForm.score = this.evaluationScore * 2 // 转换为10分制
        const payload = {
          ...this.evaluationForm,
        };
        this.submitLoading = true
        addEvaluative(payload).then(response => {
          this.$toast.success("评价成功，即将关闭页面")
          this.evaluationVisible = false
          setTimeout(() => {
            close();
          }, 1500);
        })
          .finally(() => {
            this.submitLoading = false
          })
      }).catch((error) => {
        console.log('表单验证失败', error)
      })
    }
  },
};
</script>
<style lang="scss" scoped>
@import '../css/common.scss';

.evaluation-popup {
  width: 90vw;
  padding: 20px;
  box-sizing: border-box;

  .popup-header {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    text-align: left;
  }

  .score-text {
    font-size: 14px;
    margin-left: 10px;
  }

  .popup-footer {
    display: flex;
    justify-content: space-between;

    .custom-btn-save {
      background: #C8DDFA;
      color: #3673FF;
      border-radius: 8px;
      height: 40px;
      border: none;
    }

    .custom-btn-submit {
      background: #3673FF;
      color: #FFFFFF;
      border-radius: 8px;
      border: none;
    }
  }
}

.item-actions {
  left: 0;
  right: 0;
  padding: 6px;
  background: #FFFFFF;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center !important;
  gap: 12px;
  z-index: 10;
  position: fixed;
  bottom: 0px;
  width: 100vw;

  .custom-btn-save {
    background: #C8DDFA;
    color: #3673FF;
  }

  .custom-btn-submit {
    background: #3673FF;
    color: #FFFFFF;
    border-radius: 8px;
  }
}



.cu-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 评价信息样式 */
.rating-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.rating-list {
  padding: 5px 0;
}

.rating-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.rating-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.rating-stars {
  display: flex;
  margin-right: 10px;
  justify-content: flex-start!important;
  align-items: center;
}

.rating-stars.small {
  transform: scale(0.8);
  transform-origin: left;
}

.star {
  font-size: 18px;
  color: #DDDDDD;
  margin-right: 2px;
}

.star.filled {
  color: #FFCC00;
}

.star.half-filled {
  position: relative;
  color: #DDDDDD;
}

.star.half-filled:after {
  content: '★';
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  overflow: hidden;
  color: #FFCC00;
}

.rating-score {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.rating-content {
  // padding-left: 30px;
}

.rating-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.rating-info {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.rating-user,
.rating-date {
  font-size: 12px;
  color: #999999;
  margin-right: 15px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}

/* 卡片通用样式 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F0F0F0;
}

.header-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.text-area {
  font-size: 14px;
}

.app-container-feedback {
  padding: 0;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
  padding-top: 12px !important;
}

.mb-100px {
  margin-bottom: 100px;
}

.cu-flex {
  display: flex;
  align-items: center;
  gap: 2px;
  flex-wrap: wrap;
}

.rant-container {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
}

.app-container-feedback {
  padding: 0;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #FFFFFF;
  padding: 0 16px;
  position: relative;

  .back-icon {
    font-size: 18px;
    color: #333;
    padding: 8px;
    margin-left: -8px;
  }

  .page-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 500;
    color: #333;
  }
}

.rant-content {
  margin: 12px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid #EBEEF5;

  span {
    display: flex;
    align-items: center;
  }

  .rant-checkbox {
    margin-right: 8px;
  }
}

.rant-detail-content {
  padding: 16px;
  height: calc(100vh - 25px);
  overflow-y: auto;
  margin-bottom: 100px;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.form-group {
  margin-bottom: 16px;
}

.form-empty {
  color: #999;
  font-size: 14px;
}

.empty-data {
  text-align: center;
  color: #999;
  padding: 20px 0;
  font-size: 14px;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;

  .select-all {
    margin-right: auto;
  }
}

.custom-btn-submit {
  width: 160px;
  height: 40px;
  background: #3673FF;
  color: #FFFFFF;
  margin-right: 16px;
  border-radius: 20px;
}

.custom-btn-danger {
  width: 160px;
  height: 40px;
  background: #FFFFFF;
  color: #ee0a24;
  border: 1px solid #ee0a24;
  border-radius: 20px;
}

.custom-btn-small {
  height: 36px;
  margin-left: 12px;
  border-radius: 18px;
}

.approve-popup {
  width: 90%;
  max-width: 500px;
  border-radius: 8px;

  .popup-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
  }

  .popup-content {
    padding: 16px;
  }

  .popup-footer {
    display: flex;
    border-top: 1px solid #ebedf0;

    .van-button {
      flex: 1;
      height: 40px;
      margin: 0;
      border: none;
      border-radius: 0;

      &:first-child {
        border-right: 1px solid #ebedf0;
      }
    }
  }
}

.form-item {
  display: flex;

  .form-value {
    flex: 1;
    min-width: 0;
  }
}

.file-link {
  color: #376DF7;
  font-size: 12px;
  font-weight: 400;
  max-width: 100%;
  display: flex;
  align-items: center;
  min-width: 0;
  gap: 0;

  .filename-part {
    min-width: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .extension-part {
    flex-shrink: 0;
    white-space: nowrap;
  }

  .download-icon {
    flex-shrink: 0;
    margin-left: 8px;
    font-size: 14px;
    color: #909399;
  }
}
</style>
