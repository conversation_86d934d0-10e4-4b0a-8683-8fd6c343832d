<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-date-picker
          clearable
          size="small"
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          placeholder="选择年份"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="阶段" prop="stage">
        <el-select
          clearable
          v-model="queryParams.stage"
          placeholder="请选择阶段"
        >
          <el-option
            v-for="dict in dict.type.kg_stage"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate"
        >导入模板下载
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile"
        >导入
        </el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="kgIndustryDataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" align="center" prop="id" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年份" align="center" prop="year"/>
      <el-table-column label="阶段" align="center" prop="stage">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_stage" :value="scope.row.stage"/>
        </template>
      </el-table-column>
      <el-table-column label="TOP10" align="center" prop="top10"/>
      <el-table-column label="TOP20" align="center" prop="top20"/>
      <el-table-column label="行业均值" align="center" prop="industryAvg"/>
      <el-table-column label="创建时间" align="center" prop="createdTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, scope.$index + 1)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加或修改客关行业数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="年份" prop="year">
          <el-date-picker
            clearable
            size="small"
            v-model="form.year"
            type="year"
            value-format="yyyy"
            placeholder="选择年份"
            class="w-100"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="阶段" prop="stage">
          <el-select
            clearable
            v-model="form.stage"
            placeholder="请选择阶段"
            class="w-100"
          >
            <el-option
              v-for="dict in dict.type.kg_stage"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="TOP10" prop="top10">
          <el-input v-model="form.top10" placeholder="请输入TOP10"/>
        </el-form-item>
        <el-form-item label="TOP20" prop="top20">
          <el-input v-model="form.top20" placeholder="请输入TOP20"/>
        </el-form-item>
        <el-form-item label="行业均值" prop="industryAvg">
          <el-input v-model="form.industryAvg" placeholder="请输入行业均值"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 隐藏的文件输入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<script>
import {
  getKgIndustryData,
  listKgIndustryData,
  addKgIndustryData,
  updateKgIndustryData,
  delKgIndustryData,
  importFile
} from "@/api/projectOperate/kg/kgIndustryData";
import importTable from "@/views/projectOperate/gcAssess/components/importTable.vue";

export default {
  components: {importTable},
  dicts: ["kg_stage"],
  name: "KgIndustryData",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      indexes: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客关行业数据表格数据
      kgIndustryDataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        year: null,
        stage: null,
        top10: null,
        top20: null,
        industryAvg: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        year: [
          {required: true, message: "年份不能为空", trigger: "blur"}
        ],
        stage: [
          {required: true, message: "阶段不能为空", trigger: "change"}
        ],
        top10: [
          {required: true, message: "TOP10不能为空", trigger: "blur"}
        ],
        top20: [
          {required: true, message: "TOP20不能为空", trigger: "blur"}
        ],
        industryAvg: [
          {required: true, message: "行业均值不能为空", trigger: "blur"}
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客关行业数据列表 */
    getList() {
      this.loading = true;
      listKgIndustryData(this.queryParams).then(response => {
        this.kgIndustryDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        year: null,
        stage: null,
        top10: null,
        top20: null,
        industryAvg: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      this.indexes = selection.map((item) => {
        const index = this.kgIndustryDataList.findIndex(row => row.id === item.id);
        return index !== -1 ? index + 1 : -1;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客关行业数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getKgIndustryData(id).then(response => {
        this.form = response.data;
        this.form.year = response.data.year.toString();
        this.open = true;
        this.title = "修改客关行业数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateKgIndustryData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addKgIndustryData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, index) {
      const ids = row.id || this.ids;
      const indexes = index || this.indexes;
      this.$modal.confirm('是否确认删除客关行业数据序号为"' + indexes + '"的数据项？').then(function () {
        return delKgIndustryData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/kgIndustryData/export', {
        ...this.queryParams
      }, `客关行业数据_${new Date().getTime()}.xlsx`)
    },
    //导入模版下载
    handleExportTemplate() {
      this.download('project/kgIndustryData/exportTemplate', {
        ...this.queryParams
      }, `客关行业数据导入模版_${new Date().getTime()}.xlsx`)
    },

    // 导入文件
    importFile() {
      this.$refs.fileInput.click(); // 触发文件输入的点击事件
    },
    handleFileChange(event) {
      this.loading = true;
      const files = event.target.files;
      const formData = new FormData();
      formData.append("file", files[0]);
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null;
      importFile(formData)
        .then((response) => {
          if (response.msg) {
            this.$msgbox({
              title: '结果提示',
              message: response.msg.replace(/\n/g, '<br>'),
              showCancelButton: false, // 关键配置：隐藏取消按钮
              confirmButtonText: '确认',
              type: 'error',
              dangerouslyUseHTMLString: true
            }).then(() => {
              // 确认后的操作
              this.loading = false;
            })
          } else {
            this.$modal.msgSuccess("导入成功");
            this.getList()
            this.loading = false; // 关闭加载状态
          }
        })
        .catch(() => {
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  }
};
</script>
