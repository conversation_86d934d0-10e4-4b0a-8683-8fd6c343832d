
614b575f64dc9cf6acc0b1af37e2e29715da9d64	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.c3b2d1db85a969ab5e1b.hot-update.js\",\"contentHash\":\"0d4df27df2a2826b0c08287da77c2c55\"}","integrity":"sha512-wkvqFV4XGWswevJcYxj+jyGRmD1rqPV5wf7Y28J8kbOWNkE9Yahc/HHL+CDxPqlc0bha1nLK+gX3M6sgbl0ueA==","time":1754311731522,"size":78312}