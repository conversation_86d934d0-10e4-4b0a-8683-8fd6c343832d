<template>
  <div class="my-weekly">
<!--    <van-nav-bar title="我的周报" left-arrow @click-left="onClickLeft" />-->

    <div class="filter-container">
      <van-dropdown-menu class="custom-dropdown">
        <van-dropdown-item v-model="year" :options="yearOptions" />
        <van-dropdown-item v-model="weekType" :options="weekOptions" />
      </van-dropdown-menu>
    </div>

    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list-container">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <van-swipe-cell v-for="(item, index) in list" :key="index">
          <div class="weekly-card" @click="handleDetail(item)">
            <div class="weekly-title">
              <div>{{ item.year }}年{{ item.week }}周</div>
              <div class="weekly-status">
                <van-tag :type="getStatusType(item.approveStatus)">{{
                  getStatusText(item.approveStatus)
                }}</van-tag>
              </div>
            </div>
            <div class="weekly-info">
              <div class="info-item">
                填报周期：{{ item.startDate }} ~ {{ item.endDate }}
              </div>
              <div class="info-item">审阅人：{{ item.approveUserName }}</div>
              <div class="info-item">
                审阅日期：{{ item.approveTime || "--" }}
              </div>
            </div>
            <div class="weekly-operate">
              <div
                v-if="
                    item.approveStatus === 3 ||
                  item.approveStatus === 0 ||
                  item.approveStatus === 4
                "
                class="weekly-operate-item"
                @click.stop="handleEdit(item)"
              >
                修改
              </div>
              <div
                v-if="item.approveStatus === 1"
                class="weekly-operate-item"
                @click.stop="handleCallback(item)"
              >
                撤回
              </div>
              <div
                v-if="item.approveStatus === 0"
                class="weekly-operate-item"
                @click.stop="handleSubmitDraft(item)"
              >
                提交
              </div>
              <div
                v-if="item.approveStatus === 0"
                class="weekly-operate-item"
                @click.stop="handleDelete(item)"
              >
              删除
              </div>
            </div>
          </div>
          <template #right>
           <!--  <van-button
              square
              text="删除"
              type="danger"
              class="delete-button"
              @click.stop="handleDelete(item)"
            /> -->
          </template>
        </van-swipe-cell>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import {
  NavBar,
  DropdownMenu,
  DropdownItem,
  PullRefresh,
  List,
  Tag,
  Button,
  SwipeCell,
  Dialog,
} from "vant";
// import { approveList, listInfo } from "@/api/weekly/mobile-reportInfo.js";
import {
  approveList,
  listInfo,
  submitDraft,
  delInfo,
  callbackWeekly,
} from "@/api/weekly/mobile-reportInfo.js";

export default {
  name: "MyWeekly",
  components: {
    [NavBar.name]: NavBar,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [PullRefresh.name]: PullRefresh,
    [List.name]: List,
    [Tag.name]: Tag,
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Dialog.name]: Dialog,
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      year: new Date().getFullYear().toString(),
      weekType: "全部周",
      yearOptions: this.generateYearOptions(),
      weekOptions: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        year: "",
        week: "",
      },
    };
  },
  created() {
    this.generateWeekOptions();
    // 初始化查询参数
    this.queryParams.year = this.year;
    this.queryParams.week = "";
  },
  methods: {
    handleCallback(row) {
      // console.log("row", row);
      this.loading = true;
      callbackWeekly({ id: row.id })
        .then((response) => {
          this.$toast.success("撤回成功");
          this.handleQuery();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      const year = row.year;
      const week = row.week;
      this.loading = true;
      this.$dialog
        .confirm({
          title: "提示",
          message: "是否确认删除" + year + "年第" + week + "周周报信息？",
        })
        .then(() => {
          return delInfo(ids);
        })
        .then(() => {
          this.handleQuery();
          this.$toast.success("删除成功");
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    handleEdit(row) {
      this.$router.push({
        path: `/wechatE/mobile/weeklyInput`,
        query: { id: row.id, type: "update" },
      });
    },
    handleDetail(row) {
      // console.log("row", row);
      // this.$router.push({ path: `/weekly/weeklyDetail/${row.id}` });
      this.$router.push({
        path: `/wechatE/mobile/weeklyDetail/${row.id}`,
      });
    },
    handleSubmitDraft(row) {
      this.loading = true;
      submitDraft({ id: row.id })
        .then((response) => {
          this.$toast.success("提交成功");
          this.handleQuery();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getStatusText(status) {
      // 审阅状态（0-草稿 1-待阅 2-已阅 3-已撤回 4-已驳回 5-部分已阅）
      const statusMap = {
        0: "草稿",
        1: "待阅",
        2: "已阅",
        3: "已撤回",
        4: "已驳回",
        5: "部分已阅",
      };
      return statusMap[status] || "未知";
    },
    getStatusType(status) {
      switch (status) {
        case 0:
          return "danger";
        case 1:
          return "primary";
        case 2:
          return "success";
        case 3:
          return "danger";
        case 4:
          return "danger";
        case 5:
          return "warning";
        default:
          return "success";
      }
    },
    onClickLeft() {
      this.$router.back();
    },
    // 生成年份选项
    generateYearOptions() {
      const years = [
       /* {
          text: "全部年",
          value: "",
        },*/
      ];
      const currentYear = new Date().getFullYear();
      // 从系统设计运行时间2025年开始
      for (let i = 2025; i <= currentYear; i++) {
        const year = i++;
        years.push({
          text: `${year}年`,
          value: year.toString(),
        });
      }
      return years;
    },
    // 生成周选项
    generateWeekOptions() {
      const year = parseInt(this.year);
      const weeks = [];
      weeks.push({ text: "全部周", value: "全部周" });

      // 获取该年第一天
      const firstDay = new Date(year, 0, 1);
      // 获取该年最后一天
      const lastDay = new Date(year, 11, 31);

      let currentDate = firstDay;
      let weekNum = 1;

      while (currentDate <= lastDay) {
        // 获取本周的开始日期（周一）
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1);

        // 获取本周的结束日期（周日）
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);

        // 格式化日期
        const startStr = this.formatDate(weekStart);
        const endStr = this.formatDate(weekEnd);

        weeks.push({
          text: `第${weekNum}周 (${startStr}-${endStr})`,
          value: weekNum.toString(),
        });

        // 移到下一周的第一天
        currentDate.setDate(currentDate.getDate() + 7);
        weekNum++;
      }

      this.weekOptions = weeks;
    },
    // 格式化日期
    formatDate(date) {
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${month}/${day}`;
    },
    async onLoad() {
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }

      try {
        const res = await listInfo(this.queryParams);
        this.list.push(...res.rows);
        this.list = Array.from(new Set(this.list.map(item => item.id))).map(id => this.list.find(item => item.id === id));
        this.loading = false;

        if (this.list.length >= res.total) {
          this.finished = true;
        }
        this.queryParams.pageNum++;
      } catch (error) {
        this.loading = false;
        this.finished = true;
      }
    },
    onRefresh() {
      this.finished = false;
      this.queryParams.pageNum = 1;
      this.onLoad();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.finished = false;
      this.list = [];
      this.onLoad();
    },
  },
  watch: {
    year(newVal) {
      this.weekType = "全部周";
      this.generateWeekOptions();
      this.queryParams.year = newVal;
      this.queryParams.week = "";
      this.handleQuery();
    },
    weekType(newVal, oldVal) {
      // 年份变化时会触发 weekType 变化，此时不需要重复请求
      if (oldVal === "全部周" && newVal === "全部周") {
        return;
      }
      this.queryParams.week = newVal === "全部周" ? "" : newVal;
      this.handleQuery();
    },
  },
};
</script>

<style lang="scss" scoped>
.weekly-operate {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  .weekly-operate-item {
    padding: 3px 15px;
    border: 1px solid #007aff;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    color: #3673ff;
  }
}
.list-container{
  height: calc(100% - 60px);
  overflow-y: auto;
}
:deep(.van-button--normal) {
  padding: 0 10px;
}
:deep(.van-button--primary) {
  background-color: #007aff;
  border-color: #007aff;
}
:deep(.van-nav-bar) {
  background-color: unset;
}
.my-weekly {
  background-image: url("~@/assets/weeklyMobile/weeklybac.png");
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
}

.filter-container {
  display: flex;
  align-items: center;
  padding: 20px 10px 0 10px;
  // background-color: #eef2f8;
  .filter-label {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-right: 20px;
  }
}

.custom-dropdown {
  flex: 1;
  :deep(.van-dropdown-menu__bar) {
    height: 36px;
    background-color: unset;
    box-shadow: none;
  }

  :deep(.van-dropdown-menu__item) {
    background-color: unset;
    margin: 0 4px;
    padding: 0 8px;
    border-radius: 44px 44px 44px 44px;
    border: 1px solid #99b8ff;

    .van-dropdown-menu__title {
      font-size: 14px;
      color: #606266;
    }

    .van-dropdown-menu__title::after {
      border-color: transparent transparent #606266 #606266;
    }
  }
}

.weekly-card {
  margin: 12px 10px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;

  .weekly-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 8px;
  }

  .weekly-info {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 18px;
    .info-item {
      margin-bottom: 4px;
    }
  }

  .weekly-status {
    //margin-top: 8px;
    text-align: right;
  }
}

.delete-button {
  height: 100%;
  width: 65px;
  color: #fff;
}
</style>
