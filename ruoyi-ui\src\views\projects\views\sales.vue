<template>
  <section class="projects-content-container w-100">
    <!-- 无权限状态 -->
    <div v-if="!hasPermi(['project:sales:manage'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>
    <!-- 无数据状态 -->
    <div v-else-if="!hasData" class="empty-status">
      <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
      <div class="desc">暂无数据</div>
    </div>
    <section v-else class="w-100">
      <div class="sales-content">
        <!-- 全周期销售进度模块 -->
        <div class="sales card mb-16 mt-16" v-loading="loadingSales">
          <div class="card-title">
            全周期销售进度
              <span class="total-target" v-if="hasPermi(['sales:all:price'])">全周期住宅均价 ¥ {{ allQyData.zzUnitPrice || '--' }}元</span>
          </div>
          <div class="flex-container">
            <div class="flex-item">
              <div class="progress-item">
                <div class="progress-item-title">签约</div>
                <Empty :no-authority="hasPermi(['sales:all:qy'])">
                  <div class="progress-item-content">
                  <div class="progress-item-content-item">
                    <div class="progress-item-content-chart">
                      <section class="card-with-title">
                        <Chart :option="qyProgressOption" class="chart"></Chart>
                        <section class="chart-title-block">
                          <div class="title-1" :style="{'color': qyProgressOption.title.textStyle.color}">
                            {{ qyProgressOption.title.text }}
                          </div>
                          <div class="title-2">{{ qyProgressOption.title.subtext }}</div>
                        </section>
                      </section>
                      <div class="progress-item-content-detail">
                        <div class="progress-item-content-detail-item">
                          <div class="progress-item-content-detail-item-title">¥ {{ $toFixed2(allQyData.dynamicAmount) }} 亿</div>
                          <div class="progress-item-content-detail-item-value">动态货值</div>
                        </div>
                        <div class="progress-item-content-detail-item">
                          <div class="progress-item-content-detail-item-title">¥ {{ $toFixed2(allQyData.qyAmount) }} 亿</div>
                          <div class="progress-item-content-detail-item-value">累计签约</div>
                        </div>
                      </div>
                    </div>
                    <div class="progress-item-content-data">
                      <table>
                        <tr>
                          <th></th>
                          <th class="progress-label">住宅</th>
                          <th class="progress-label">非住</th>
                        </tr>
                        <tbody>
                        <tr>
                          <td class="progress-label">动态货值</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.zzDynamicAmount) }} 亿</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.noZzDynamicAmount) }} 亿</td>
                        </tr>
                        <tr>
                          <td class="progress-label">累计签约</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.zzQyAmount) }} 亿</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.noZzDyAmount) }} 亿</td>
                        </tr>
                        <tr>
                          <td class="progress-label">完成比例</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.zzRate) }}%</td>
                          <td class="progress-text">{{ $toFixed2(allQyData.noZzRate) }}%</td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                </Empty>

              </div>
            </div>

            <div class="flex-item hk-item">
              <div class="progress-item">
                <div class="progress-item-title">回款</div>
<!--                <div class="progress-item-title">回款（权益）</div>-->
                <Empty :no-authority="hasPermi(['sales:all:hk'])">
                  <div class="progress-item-content">
                  <div class="progress-item-content-item">
                    <div class="progress-item-content-chart">
                      <section class="card-with-title">
                        <Chart :option="hkProgressOption" class="chart"></Chart>
                        <section class="chart-title-block">
                          <div class="title-1" :style="{'color': hkProgressOption.title.textStyle.color}">
                            {{ hkProgressOption.title.text }}
                          </div>
                          <div class="title-2">{{ hkProgressOption.title.subtext }}</div>
                        </section>
                      </section>
                      <div class="progress-item-content-detail">
                        <div class="progress-item-content-detail-item">
                          <div class="progress-item-content-detail-item-title">¥ {{ $toFixed2(allHkData.dynamicAmount) }} 亿</div>
                          <div class="progress-item-content-detail-item-value">动态货值</div>
<!--                          <div class="progress-item-content-detail-item-value">动态货值（权益）</div>-->
                        </div>
                        <div class="progress-item-content-detail-item">
                          <div class="progress-item-content-detail-item-title">¥ {{ $toFixed2(allHkData.hkAmount) }} 亿</div>
                          <div class="progress-item-content-detail-item-value">累计回款</div>
<!--                          <div class="progress-item-content-detail-item-value">累计回款（权益）</div>-->
                        </div>
                        <div class="progress-item-content-detail-item">
                          <div class="progress-item-content-detail-item-title">{{ allHkData.qyRate }}%</div>
                          <div class="progress-item-content-detail-item-value">权益比例</div>
                        </div>
                      </div>
                    </div>
                    <div class="progress-item-content-data">
                      <table>
                        <tr>
                          <th></th>
                          <th class="progress-label">住宅</th>
                          <th class="progress-label">非住</th>
                        </tr>
                        <tbody>
                        <tr>
                          <td class="progress-label">动态货值</td>
<!--                          <td class="progress-label">动态货值（权益）</td>-->
                          <td class="progress-text">{{ $toFixed2(allHkData.zzDynamicAmount) }} 亿</td>
                          <td class="progress-text">{{ $toFixed2(allHkData.noZzDynamicAmount) }} 亿</td>
                        </tr>
                        <tr>
                          <td class="progress-label">累计回款</td>
<!--                          <td class="progress-label">累计回款（权益）</td>-->
                          <td class="progress-text">{{ $toFixed2(allHkData.zzHkAmount) }} 亿</td>
                          <td class="progress-text">{{ $toFixed2(allHkData.noZzHkAmount) }} 亿</td>
                        </tr>
                        <tr>
                          <td class="progress-label">完成比例</td>
                          <td class="progress-text">{{ $toFixed2(allHkData.zzRate) }}%</td>
                          <td class="progress-text">{{ $toFixed2(allHkData.noZzRate) }}%</td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                </Empty>

              </div>

            </div>
          </div>
        </div>

        <!-- 全周期进度偏差模块 -->
        <div class="time-progress card mb-16 mt-16">
          <div class="card-title">全周期进度偏差</div>
          <div class="flex-container">
            <div class="flex-item" v-loading="loadingAllTargetQYProgress">
              <div class="target-title mb-4rem">签约</div>
              <Empty :no-authority="hasPermi(['sales:all:qyDeviation'])">
                <div class="progress-timeline">
                <div class="time-line-wrapper">
                  <span class="label">全盘</span>
                  <div class="timeline number-de"
                       :style="{
                    '--timeline-before-content': signDataByTime.openDate ? `'${signDataByTime.openDate}'` : `'--'`,
                    '--timeline-after-content': signDataByTime.liquidateDate ? `'${signDataByTime.liquidateDate}'` : `'--'` }">
                    <div class="marker blue"
                         :style="{left: `${filterRatioMax(signDataByTime.allSdRate)}%`}">
                    </div>
                    <div class="tooltip-progress blue sign-tooltip-1"
                         :style="getTooltipStyle(filterRatioMax(signDataByTime.allSdRate), 'sign-tooltip-1')">
                          <div>时点完成{{$toFixed2(signDataByTime.qyAmount)}}亿（{{$toFixed2(signDataByTime.allSdRate)}}%）</div>
                          <div>全盘动态货值{{$toFixed2(signDataByTime.dynamicAmount)}}亿</div>
                          <div>偏差率{{$toFixed2(signDataByTime.deviationRate)}}%</div>
                    </div>
                    <div class="down"
                         :style="getArrowStyle(filterRatioMax(signDataByTime.allSdRate))">
                    </div>

                    <div class="marker yellow"
                         :style="{left: `${filterRatioMax(signDataByTime.allSdTargetRate)}%`}">
                    </div>
                    <div class="tooltip-progress yellow  sign-tooltip-2"
                         :style="getTooltipStyle(filterRatioMax(signDataByTime.allSdTargetRate),  'sign-tooltip-2')">
                      <div>时点目标{{$toFixed2(signDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.allSdTargetRate)}}%）</div>
                      <div>全盘目标货值{{$toFixed2(signDataByTime.targetAmount)}}亿</div>
                    </div>
                    <div class="up"
                         :style="getArrowStyle(filterRatioMax(signDataByTime.allSdTargetRate))">
                    </div>
                  </div>
                </div>

                <div class="details">
                  <table>
                    <tr>
                      <td class="progress-label">住宅: 时点完成{{$toFixed2(signDataByTime.zzQyAmount)}}亿（{{$toFixed2(signDataByTime.zzSdRate)}}%)</td>
                      <td class="progress-text">| 动态货值{{$toFixed2(signDataByTime.zzDynamicAmount)}}亿</td>
                      <td class="progress-text">| 时点目标{{$toFixed2(signDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.zzSdTargetRate)}}%）</td>
                      <td class="progress-text">| 目标货值{{$toFixed2(signDataByTime.zzTargetAmount)}}亿</td>
<!--                      <td class="progress-text"> | 清盘日期{{ signDataByTime.zzQpDate }}</td>-->
                    </tr>
                    <tr>
                      <td class="progress-text pl-date"> 清盘日期 {{ $formatNull(signDataByTime.zzLiquidateDate) }}</td>
                    </tr>
                    <tr>
                      <td  class="progress-label">非住: 时点完成{{$toFixed2(signDataByTime.noZzQyAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdRate)}}%)</td>
                      <td>| 动态货值{{$toFixed2(signDataByTime.noZzDynamicAmount)}}亿</td>
                      <td>| 时点目标{{$toFixed2(signDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdTargetRate)}}%)</td>
                      <td>| 目标货值{{$toFixed2(signDataByTime.noZzTargetAmount)}}亿</td>
<!--                      <td> | 清盘日期{{ signDataByTime.noZzQpDate }}</td>-->
                    </tr>
                    <tr>
                      <td class="pl-date">清盘日期 {{ $formatNull(signDataByTime.noZzLiquidateDate) }}</td>
                    </tr>
                  </table>
                </div>
              </div></Empty>

            </div>
            <div class="flex-item" v-loading="loadingAllTargetHKProgress">
              <div class="target-title mb-4rem">回款</div>
<!--              <div class="target-title mb-4rem">回款（权益）</div>-->
              <Empty :no-authority="hasPermi(['sales:all:hkDeviation'])"><div class="progress-timeline">
                <div class="time-line-wrapper">
                  <span class="label">全盘</span>
                  <div class="timeline number-de"
                       :style="{
                    '--timeline-before-content': hkDataByTime.openDate ? `'${hkDataByTime.openDate}'` : `'--'`,
                    '--timeline-after-content': hkDataByTime.liquidateDate ? `'${hkDataByTime.liquidateDate}'` : `'--'` }">
                    <div class="marker blue"
                         :style="{left: `${filterRatioMax(hkDataByTime.allSdRate)}%`}">
                    </div>
                    <div class="tooltip-progress blue  hk-tooltip-1"
                         :style="getTooltipStyle(filterRatioMax(hkDataByTime.allSdRate), 'hk-tooltip-1')">
                      <div>时点完成{{$toFixed2(hkDataByTime.hkAmount)}}亿（{{$toFixed2(hkDataByTime.allSdRate)}}%）</div>
                      <div>全盘动态货值{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>
<!--                      <div>全盘动态货值（权益）{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>-->
                      <div>偏差率{{$toFixed2(hkDataByTime.deviationRate)}}%</div>
                    </div>
                    <div class="down"
                         :style="getArrowStyle(filterRatioMax(hkDataByTime.allSdRate))">
                    </div>

                    <div class="marker yellow"
                         :style="{left: `${filterRatioMax(hkDataByTime.allSdTargetRate)}%`}">
                    </div>
                    <div class="tooltip-progress yellow hk-tooltip-2"
                         :style="getTooltipStyle(filterRatioMax(hkDataByTime.allSdTargetRate), 'hk-tooltip-2')">
                      <div>时点目标{{$toFixed2(hkDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.allSdTargetRate)}}%）</div>
                      <div>全盘目标货值{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>
<!--                      <div>全盘目标货值（权益）{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>-->
                    </div>
                    <div class="up"
                         :style="getArrowStyle(filterRatioMax(hkDataByTime.allSdTargetRate))">
                    </div>
                  </div>
                </div>

                <div class="details">
                  <table>
                    <tr>
                      <td class="progress-label">住宅: 时点完成{{$toFixed2(hkDataByTime.zzHkAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdRate)}}%）</td>
                      <td class="progress-text">| 动态货值{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>
<!--                      <td class="progress-text">| 动态货值（权益）{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>-->
                      <td class="progress-text">| 时点目标{{$toFixed2(hkDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdTargetRate)}}%）</td>
                      <td class="progress-text">| 目标货值{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>
<!--                      <td class="progress-text">| 目标货值（权益）{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>-->
<!--                      <td class="progress-text"> | 清盘日期{{hkDataByTime.zzQpDate}}</td>-->
                    </tr>
                    <tr>
                      <td class="progress-text pl-date">清盘日期 {{$formatNull(hkDataByTime.zzLiquidateDate)}}</td>
                    </tr>
                    <tr>
                      <td class="progress-label">非住: 时点完成{{$toFixed2(hkDataByTime.noZzHkAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdRate)}}%）</td>
                      <td class="progress-text">| 动态货值{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>
<!--                      <td class="progress-text">| 动态货值（权益）{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>-->
                      <td class="progress-text">| 时点目标{{$toFixed2(hkDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdTargetRate)}}%）</td>
                      <td class="progress-text">| 目标货值{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>
<!--                      <td class="progress-text">| 目标货值（权益）{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>-->
<!--                      <td class="progress-text"> | 清盘日期{{hkDataByTime.noZzQpDate}}</td>-->
                    </tr>
                    <tr>
                      <td class="progress-text pl-date"> 清盘日期 {{$formatNull(hkDataByTime.noZzLiquidateDate)}}</td>
                    </tr>
                  </table>
                  <!-- <div>住宅: 时点完成{{hkDataByTime.zzHkAmount}}亿（{{hkDataByTime.allSdRate}}%） | 住宅动态货值{{hkDataByTime.zzDynamicAmount}}亿 | 时点目标{{hkDataByTime.zzSdTargetAmount}}亿（{{hkDataByTime.zzSdRate}}%） | 住宅目标货值{{hkDataByTime.targetAmount}}亿 | 清盘日期{{hkDataByTime.zzQpDate}}</div>
                  <div>非住: 时点完成{{hkDataByTime.noZzHkAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住动态货值{{hkDataByTime.noZzDynamicAmount}}亿 | 时点目标{{hkDataByTime.noZzSdTargetAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住目标货值{{hkDataByTime.noZzTargetAmount}}亿 | 清盘日期{{hkDataByTime.noZzQpDate}}</div> -->
                </div>
              </div>
            </Empty>

            </div>
          </div>
        </div>

        <!-- 年度销售进度模块 -->
        <div class="year-progress card mb-16 mt-16" v-loading="loadingYearProgress">
          <div class="card-title">
            年度销售进度
            <span class="total-target" v-if="hasPermi(['sales:year:price'])">年度住宅均价 ¥ {{ $toFixed2(yearQyData.zzUnitPrice)}}元</span>
          </div>
          <div class="flex-container">
            <div class="flex-item">
              <div class="target-title">年度签约目标 ¥ {{ $toFixed2(yearQyData.targetAmount)}} 亿</div>
              <Empty :no-authority="hasPermi(['sales:year:qy'])">
                <div class="progress-timeline">
                <div class="time-line-wrapper">
                  <span class="label">全盘</span>
                  <div class="timeline number">
                    <!-- 刻度线 -->
                    <div
                      class="marker blue"
                      :style="{left: `${filterRatioMax(yearQyData.ratio)}%`}"
                    ></div>
                    <!-- 提示框 -->
                    <div class="tooltip-progress blue year-tooltip-1" :style="getTooltipStyle(filterRatioMax(yearQyData.ratio), 'year-tooltip-1')">
                      <span>销售进度：{{ $toFixed2(yearQyData.totalAmount) }} 亿 ({{ $toFixed2(yearQyData.ratio) }}%)</span>
                    </div>
                    <!-- 三角形 -->
                    <div class="down" :style="getArrowStyle(filterRatioMax(yearQyData.ratio))"></div>
                    <div
                      class="marker yellow"
                      :style="{left: `${filterRatioMax(yearQyData.dayRatio)}%`}"
                    ></div>
                    <div class="tooltip-progress yellow year-tooltip-2" :style="getTooltipStyle(filterRatioMax(yearQyData.dayRatio), 'year-tooltip-2')">
                      <span>时间进度：{{ yearQyData.month }}月 ({{ $toFixed2(yearQyData.dayRatio) }}%)</span>
                    </div>
                    <div class="up" :style="getArrowStyle(filterRatioMax(yearQyData.dayRatio))"></div>
                  </div>
                </div>


                <div class="details">
                  <table>
                    <tr>
                      <td class="progress-label">住宅: 年度签约目标{{ $toFixed2(yearQyData.zzTargetAmount) }} 亿</td>
                      <td class="progress-text"> | 累计签约金额{{ $toFixed2(yearQyData.zzTotalAmount) }} 亿</td>
                      <td class="progress-text"> | {{ $toFixed2(yearQyData.zzRatio) }}%</td>
                    </tr>
                    <tr>
                      <td class="progress-label">非住: 年度签约目标{{ $toFixed2(yearQyData.noZzTargetAmount) }} 亿</td>
                      <td class="progress-label"> | 累计签约金额{{ $toFixed2(yearQyData.noZzTotalAmount) }} 亿</td>
                      <td class="progress-label"> | {{ $toFixed2(yearQyData.noZzRatio) }}%</td>
                    </tr>
                  </table>
                </div>
              </div>
              </Empty>

            </div>
            <div class="flex-item">
              <div class="target-title">年度回款目标 ¥ {{ $toFixed2(yearHkData.targetAmount) || '--' }} 亿</div>
              <Empty :no-authority="hasPermi(['sales:year:hk'])">
                <div class="progress-timeline">
                <div class="time-line-wrapper">
                  <span class="label">全盘</span>
                  <div class="timeline number">
                    <div class="marker blue" :style="{ left: `${filterRatioMax(yearHkData.ratio)}%` }"
                    ></div>
                    <div class="tooltip-progress blue year-hk-tooltip-1"
                         :style="getTooltipStyle(filterRatioMax(yearHkData.ratio), 'year-hk-tooltip-1')">
                      <span>销售进度：{{ $toFixed2(yearHkData.totalAmount) }} 亿 ({{ $toFixed2(yearHkData.ratio) }}%)</span>
                    </div>
                    <div class="down" :style="getArrowStyle(filterRatioMax(yearHkData.ratio))"></div>

                    <div class="marker yellow" :style="{ left: `${filterRatioMax(yearHkData.dayRatio)}%`}"
                    ></div>
                    <div class="tooltip-progress yellow year-hk-tooltip-2"
                         :style="getTooltipStyle(filterRatioMax(yearHkData.dayRatio), 'year-hk-tooltip-2')">
                      <span>时间进度：{{ yearHkData.month }}月 ({{ $toFixed2(yearHkData.dayRatio) }}%)</span>
                    </div>
                    <div class="up" :style="getArrowStyle(filterRatioMax(yearHkData.dayRatio))"></div>
                  </div>
                </div>

                <div class="details">
                  <table>
                    <tr>
                      <td class="progress-label">住宅: 年度回款目标{{ $toFixed2(yearHkData.zzTargetAmount) }} 亿</td>
                      <td class="progress-text"> | 累计回款金额{{ $toFixed2(yearHkData.zzTotalAmount) }} 亿</td>
                      <td class="progress-text"> | {{ $toFixed2(yearHkData.zzRatio) }}%</td>
                    </tr>
                    <tr>
                      <td class="progress-label">非住: 年度回款目标 {{ $toFixed2(yearHkData.noZzTargetAmount) }} 亿</td>
                      <td class="progress-label"> | 累计回款金额 {{ $toFixed2(yearHkData.noZzTotalAmount) }} 亿</td>
                      <td class="progress-label"> | {{ $toFixed2(yearHkData.noZzRatio) }}%</td>
                    </tr>
                  </table>
                </div>
              </div>
              </Empty>

            </div>
          </div>
        </div>

        <!-- 款齐模块 -->
        <div class="receiving-payments mb-16" v-loading="loadingPayments">
          <div class="card content-block">
            <div class="card-title-2">款齐</div>
            <Empty :no-authority="hasPermi(['sales:kq'])">
              <div class="basic-info-item flex-container">
              <div class="circle-chart chart-size">
                <section class="card-with-title">
                  <Chart :option="kqProgressOption" class="chart-size"></Chart>
                  <section class="chart-title-block">
                    <div class="title-1" :style="{'color': kqProgressOption.title.textStyle.color}">
                      {{ kqProgressOption.title.text }}
                    </div>
                    <div class="title-2">{{ kqProgressOption.title.subtext }}</div>
                  </section>
                </section>
              </div>
              <div class="card-wrapper">
                <div class="data-card" v-for="item in cardData"
                     :style="{ backgroundColor: item.color, boxShadow: `0px 8px 20px 0px ${item.color}` }">
                  <div class="card-label">{{ item.label }}</div>
                  <br/>
                  <div class="card-value">
                    <span class="currency">¥ </span>
                    <span>{{ $toFixed2(item.value) }} 亿</span>
                  </div>
                </div>
              </div>
            </div>
            </Empty>
          </div>
        </div>

        <!-- 表格数据模块 -->
        <div class="table-container mb-16" v-loading="loadingTables">
          <div class="card-title mb-16">销售分析</div>
          <Empty :no-authority="hasPermi(['sales:data'])">
            <CardTabSales v-model="queryTypeSign" :tabs="queryTypeSignList" @click="handleTabSign" class="mb-14"/>
          <el-table
            :data="qyData"
            style="width: 100%"
            :border="true"
            :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.5rem 0',
                    height: '2rem',
                    fontSize: '0.875rem',
                    lineHeight: '1.5rem'
                  }"
            :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontWeight: '400',
                    padding: '0.5rem 0',
                    height: '2rem',
                    fontSize: '0.875rem',
                     lineHeight: '1.5rem'
                  }"
            class="project-table mb-16">
            <!-- 签约table -->
            <el-table-column align="center" label="累计签约">
              <el-table-column label="金额" prop="qyAmount" align="center" min-width="12.5%">
                <template #default="scope">
                  {{
                    queryTypeSign === 1 || queryTypeSign === 2
                      ? (scope.row.qyAmount / 10000).toFixed(2) + '万'
                      : (scope.row.qyAmount / 100000000).toFixed(2) + '亿'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="面积" prop="qyArea" align="center" min-width="12.5%">
                <template #default="scope">
                  <span>{{ $toFixed2(scope.row.qyArea)}}</span>
                </template>
              </el-table-column>
              <el-table-column label="套数" prop="qyNum" align="center" min-width="12.5%"/>
              <el-table-column label="其中住宅套数" prop="zzNum" align="center" min-width="12.5%"/>
              <el-table-column label="其中退房套数" prop="backNum" align="center" min-width="12.5%"/>
              <el-table-column label="其中换房套数" prop="changeNum" align="center" min-width="12.5%"/>
            </el-table-column>

            <el-table-column align="center" label="累计到访转化率">
              <el-table-column label="到访人数" prop="dfNum" align="center" min-width="12.5%"/>
              <el-table-column label="转化率" prop="ratio" align="center" min-width="12.5%">
                <template #default="scope">
                  {{ $toFixed2(scope.row.ratio) }}%
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
          <!-- 认购table -->
          <el-table
            :data="rgData"
            style="width: 100%"
            :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.5rem 0',
                    height: '2rem',
                    fontSize: '0.875rem',
                    lineHeight: '1.2rem'
                  }"
                  :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontWeight: '400',
                    padding: '0.5rem 0',
                    height: '2rem',
                    fontSize: '0.875rem',
                    lineHeight: '1.2rem'
                  }"
            class="mb-16 project-table">
            <el-table-column align="center" label="累计认购" :colspan="6">
              <el-table-column label="金额" prop="rgAmount" align="center" min-width="10">
                <template #default="scope">
                  {{
                    queryTypeSign === 1 || queryTypeSign === 2
                      ? (scope.row.rgAmount / 10000).toFixed(2) + '万'
                      : (scope.row.rgAmount / 100000000).toFixed(2) + '亿'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="面积" prop="rgArea" align="center" min-width="10"/>
              <el-table-column label="套数" prop="tsNum" align="center" min-width="10"/>
              <el-table-column label="其中住宅套数" prop="zzNum" align="center" min-width="10"/>
              <el-table-column label="其中退房套数" prop="backNum" align="center" min-width="10"/>
              <el-table-column label="其中换房套数" prop="changeNum" align="center" min-width="10"/>
            </el-table-column>

            <el-table-column align="center" label="截至当前认未签" :colspan="5">
              <el-table-column label="金额" prop="noQyTotalAmount" align="center" min-width="10">
                <template #default="scope">
                  {{
                    (scope.row.noQyTotalAmount / 100000000).toFixed(2) + '亿'

                  }}
                </template>
              </el-table-column>
              <el-table-column label="面积" prop="noQyTotalArea" align="center" min-width="10"/>
              <el-table-column label="套数" prop="noQyTsNum" align="center" min-width="10"/>
              <el-table-column label="其中住宅套数" prop="noQyZzNum" align="center" min-width="10"/>
            </el-table-column>
          </el-table>
          </Empty>


          <div class="cost flex-container" style="min-height: 26.5rem;">
            <div class="flex-item chart">
              <div class="card-title mb-10">签约业态分布</div>
              <!--              <div class="sign-chart-block">-->
              <Empty :no-authority="hasPermi(['sales:bussDist'])">
                <!-- <Chart class="" :option="getQYYTOption(qyYTData)" v-if="qyYTData.length > 0"></Chart> -->
                <Chart class="" :option="qyYTOption"></Chart>
                <!-- <div v-else class="no-data">暂无数据</div> -->
              </Empty>
              <!--              </div>-->
            </div>
            <div class="flex-item" style="min-width:0;">
              <div class="card-title mb-10">货值分布表</div>
              <Empty :no-authority="hasPermi(['sales:goodsValue'])">
                <!-- max-height: 30rem; -->
                <el-table
                class="project-table"
                :data="analysisData"
                style="width: 100%; "
                :border="true"
                :span-method="handleSpanMethod"
                :height="425"
                :header-cell-style="{
                    background: '#E1EFFD',
                    color: '#666666',
                    fontWeight: '400',
                    padding: '0.2rem 0',
                    fontSize: '0.875rem',
                    height: '2rem',
                  }"
                :cell-style="{
                    borderColor: 'rgba(0,106,255,0.2);',
                    fontWeight: '400',
                    padding: '0.2rem 0',
                    height: '2rem',
                    fontSize: '0.875rem',
                  }"
              >
                <el-table-column label="业态" prop="projectType" align="center" min-width="8"/>
                <el-table-column label="户型" prop="hxName" align="center" min-width="12"/>
                <el-table-column label="总套数" align="center">
                  <el-table-column label="合计" prop="totalNum" align="center" min-width="8"/>
                  <el-table-column label="其中" align="center">
                    <el-table-column label="顶层" prop="allTopNum" align="center" min-width="8"/>
                    <el-table-column label="底层" prop="allBottomNum" align="center" min-width="8"/>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="总货值(万元)" prop="allHzAmount" min-width="12" align="center"></el-table-column>
                <el-table-column label="已售套数" align="center">
                  <el-table-column label="合计" prop="ysNum" align="center" min-width="8"/>
                  <el-table-column label="其中" align="center">
                    <el-table-column label="顶层" prop="ysTopNum" align="center" min-width="8"/>
                    <el-table-column label="底层" prop="ysBottomNum" align="center" min-width="8"/>
                  </el-table-column>
                </el-table-column>
                  <el-table-column label="已售货值(万元)" prop="ysHzAmount" min-width="12" align="center"></el-table-column>
                <el-table-column label="未售套数" align="center">
                  <el-table-column label="合计" prop="wsNum" align="center" min-width="8"/>
                  <el-table-column label="其中" align="center">
                    <el-table-column label="已供未售" prop="ygwsNum" align="center" min-width="8"/>
                    <el-table-column label="未供" prop="wgNum" align="center" min-width="8"/>
                    <el-table-column label="顶层" prop="wsTopNum" align="center" min-width="8"/>
                    <el-table-column label="底层" prop="wsBottomNum" align="center" min-width="8"/>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="未售货值(万元)" prop="wsHzAmount" min-width="12" align="center"></el-table-column>
              </el-table>
              </Empty>

            </div>
          </div>

        </div>

        <!-- 趋势图模块 -->
        <div class="trend mb-16" v-loading="loadingTrends">
          <div class="card-title mb-16">趋势分析</div>
          <CardTabSales v-model="queryTypeTrend" :tabs="queryTypeTrendList" @click="handleTrendTab" class="mb-14"/>
          <div class="flex-container">
            <div class="flex-item">
              <div class="chart-title mb-25">到访趋势</div>
              <Empty :no-authority="hasPermi(['sales:dfTrend'])">
                <div class="trend-chart-title">{{ dfOptionName }}</div>
                <Chart :option="dfOption" class="trend-chart"></Chart>
              </Empty>
            </div>
            <div class="flex-item">
              <div class="chart-title mb-25">认购趋势</div>
              <Empty :no-authority="hasPermi(['sales:rgTrend'])">
                <div class="trend-chart-title">{{ rgOptionName }}</div>
              <section class="flex-row-container">
                <div class="vertical-tabs">
                  <div
                    v-for="(tab, index) in tabs"
                    :key="index"
                    :class="['tab', { active: activeTabRGTrend === index }]"
                    @click="selectTabRGTrend(index)"
                  >
                    <div class="tab-text">{{ tab }}</div>
                  </div>
                </div>
                <Chart :option="rgOption" class="trend-chart"></Chart>
              </section>
              <section class="flex-row-container">
                <CardTabSales v-model="rgProjectType" :tabs="projectTypeList" @click="handleRgTab" class="mb-14"/>
                <!--            <CardTabSales v-model="activeTabRGTrend" :tabs="tabs" @click="selectTabRGTrend" class="mb-14"/>-->
              </section>
              </Empty>

            </div>
            <div class="flex-item">
              <div class="chart-title mb-25">签约趋势</div>
              <Empty :no-authority="hasPermi(['sales:qyTrend'])">
                <div class="trend-chart-title">{{ qyOptionName }}</div>
              <section class="flex-row-container">
                <div class="vertical-tabs">
                  <div
                    v-for="(tab, index) in tabs"
                    :key="index"
                    :class="['tab', { active: activeTabQYTrend === index }]"
                    @click="selectTabQYTrend(index)"
                  >
                    <div class="tab-text">{{ tab }}</div>
                  </div>
                </div>
                <Chart :option="qyOption" class="trend-chart"></Chart>
              </section>
              <section class="flex-row-container">
                <CardTabSales v-model="qyProjectType" :tabs="projectTypeList" @click="handleQyTab" class="mb-14"/>
                <!-- <CardTabSales v-model="activeTabQYTrend" :tabs="tabs" @click="selectTabQYTrend" class="mb-14"/> -->
              </section>
              </Empty>

            </div>
            <div class="flex-item">
              <div class="chart-title  mb-25">回款趋势</div>
              <Empty :no-authority="hasPermi(['sales:hkTrend'])">
                <div class="trend-chart-container">
                <div class="trend-chart-title">{{ hkOptionName }}</div>
                <Chart :option="hkOption" class="trend-chart"></Chart>
              </div>
              <!-- <Chart :option="hkOption" class="trend-chart"></Chart> -->
              <section class="flex-row-container">
                <CardTabSales v-model="hkProjectType" :tabs="projectTypeList" @click="handleHKTab" class="mb-14"/>
              </section>
              </Empty>

            </div>
          </div>
        </div>
      </div>
    </section>
  </section>

</template>
<script>
import ProgressBar from '@/views/projects/components/ProgressBar.vue'
import CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'
import ProgressCircle from '@/views/projects/constants/ProgressCircle'
import Chart from '@/views/projects/components/Chart.vue'
import BarOption from '@/views/projects/constants/bar'
import PieOption from '@/views/projects/constants/Pie'
import API from '@/views/projects/api'
import CardTabSales from "@/views/projects/components/CardTabSales.vue";
import Empty from "@/views/projects/components/empty.vue";

export default {
  name: 'Sales',
  components: {
    CumulativeIndicatorsCard,
    ProgressBar,
    Chart,
    CardTabSales,
    Empty
  },
  data() {
    return {
      yearQyData: {
        /*ratio: 0,
        dayRatio: 100,
        totalAmount: 0,
        month: 0,
        zzTotalAmount: 0,
        noZzTotalAmount: 0*/
      }, // 年度销售进度-年度签约数据
      yearHkData: {}, // 年度销售进度-年度回款数据
      signDataByTime: { // 序时销售进度-签约
      },
      loadingAllTargetQYProgress: false,
      loadingAllTargetHKProgress: false,
      hkDataByTime: { // 序时销售进度-回款
      },
      queryTypeSignList: [
        {
          code: 0,
          name: '全周期'
        },
        {
          code: 4,
          name: '年'
        },
        {
          code: 3,
          name: '月'
        },
        {
          code: 2,
          name: '周'
        },
        {
          code: 1,
          name: '日'
        },
      ],
      queryTypeSign: 2,
      qyData: [],
      rgData: [],
      ////////////
      queryTypeTrendList: [
        {
          code: 0,
          name: '全周期'
        },
        {
          code: 3,
          name: '月'
        },
        {
          code: 2,
          name: '周'
        },
        {
          code: 1,
          name: '日'
        }
      ],
      queryTypeTrend: 2,
      hkTrendData: {}, // 回款趋势图数据
      qyTrendData: {}, // 签约趋势图数据
      rgTrendData: {}, // 认购趋势图数据
      dfTrendData: {}, // 到访趋势图数据
      hkTrendRawData: {}, // 回款趋势图原始数据
      qyTrendRawData: {}, // 签约趋势图原始数据
      rgTrendRawData: {}, // 认购趋势图原始数据
      dfTrendRawData: {}, // 到访趋势图原始数据
      ProgressCircleOption: new ProgressCircle().getOption(),
      cardData: [ // 款齐
        {
          label: '年度目标',
          value: '--',
          color: '#53BD88'
        },
        {
          label: '年度款齐',
          value: '--',
          color: '#7B6FF2'
        },
        {
          label: '可结利',
          value: '--',
          color: '#3EB6CF'
        }
      ],
      projectCode: '',
      projectTypeList: [], // 业态集合
      hkProjectType: '',
      rgProjectType: '',
      qyProjectType: '',
      queryType: 2, // （0-全盘 1-今日 2-本周 3-本月）
      allQyData: {
        "dynamicAmount": '-',
        "qyAmount": '-',
        "rate": '-',
        "zzDynamicAmount": '-',
        "zzQyAmount": '-',
        "zzRate": '-',
        "noZzDynamicAmount": '-',
        "noZzDyAmount": '-',
        "noZzRate": '-',
        "zzUnitPrice": '-'
      }, //全盘销售进度 - 签约
      allHkData: {
        "dynamicAmount": '-',
        "qyAmount": '-',
        "rate": '-',
        "zzDynamicAmount": '-',
        "zzQyAmount": '-',
        "zzRate": '-',
        "noZzDynamicAmount": '-',
        "noZzDyAmount": '-',
        "noZzRate": '-',
        "zzUnitPrice": '-'
      }, // 全盘销售进度 - 回款
      kqData: {},
      qyYTData: [], // 签约业态分布
      tabs: [
        '套数',
        '金额',
        '面积'
      ],
      activeTabRGTrend: 0, // 套数
      activeTabQYTrend: 0, // 套数
      tooltipStyles: {
        blue: {left: '0px'},
        yellow: {left: '0px'}
      },
      dfOption: {},
      rgOption: {},
      qyOption: {},
      hkOption: {},
      analysisData: [], // 货值分析数据
      hasData: true,
      isDeveloping: false, // 控制是否显示开发中状态

      // 添加loading状态变量
      loadingSales: false,
      loadingTimeProgress: false,
      loadingYearProgress: false,
      loadingPayments: false,
      loadingTables: false,
      loadingTrends: false,
      hkOptionName: '',
      dfOptionName: '',
      rgOptionName: '',
      qyOptionName: ''
    }
  },
  created() {
    this.projectCode = this.$route.query.projectCode
  },
  async mounted() {
    await this.getProjectType(this.projectCode);
    this.initTrend(); // 初始化趋势图
    this.getAllQyData({ //全盘销售进度 - 签约
      queryType: 0,
      projectCode: this.projectCode
    });
    this.getAllHkData({ //全盘销售进度 - 回款
      queryType: 0,
      projectCode: this.projectCode
    });
    this.getkqData({ // 款齐
      queryType: 4,
      projectCode: this.projectCode
    });
    this.getQyDfData({ // 累���签约
      queryType: this.queryTypeSign,
      projectCode: this.projectCode
    });
    this.getRgData({ // 累计认购
      queryType: this.queryTypeSign,
      projectCode: this.projectCode
    });
    this.getQYYTData({ // 签约业态分布
      queryType: this.queryTypeSign,
      projectCode: this.projectCode
    });
    this.fetchAnalyse(this.projectCode) // 获取货值分析数据
    // 年度销售进度-年度签约数据
    this.getYearQyData({
      queryType: 4,
      projectCode: this.projectCode
    });
    // 年度销售进度-年度回款数据
    this.getYearHkData({
      queryType: 4,
      projectCode: this.projectCode
    });
    // 全周期进度偏差-签约数据
    this.getAllTargetQyData({
      queryType: 4,
      projectCode: this.projectCode
    });
    // 全周期进度偏差-回款数据
    this.getAllTargetHkData({
      queryType: 4,
      projectCode: this.projectCode
    });

  },
  methods: {
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions
      // Check for all permissions wildcard first
      if (permissions.includes('*:*:*')) {
        return true
      }
      // Check specific permissions
      return permissions.some(p => permission.includes(p))
    },
    filterRatioMax(ratio) {
      if (ratio > 100) {
        ratio = 100;
      } else if (ratio < 0) {
        ratio = 0;
      }
      return ratio;
    },
    getArrowStyle(ratio) {
      return {left: `${ratio}%`};
    },
    getTooltipStyle(ratio, tooltipClass) {
      const scale = window.innerWidth / 1680;
      // const tooltipWidth = 170 * scale; // tooltip的宽度(px)
      const tooltipWidth = document.querySelector(`.${tooltipClass}`)?.clientWidth || 0; // tooltip的宽度(px)
      const containerWidth = document.querySelector('.timeline')?.clientWidth || 0;
      const tooltipRatio = (tooltipWidth / containerWidth) * 100;
      if (ratio == '--') { // raio为--时，tooltip靠边
        return {
          left: `0`,
          textAlign: 'left'
        };
      }

      // 超出右边界
      if ((Math.ceil(ratio) + Math.ceil(tooltipRatio) / 2) > 100) {
        return {
          // right: `0`,
          right: `-0.725rem`,
          textAlign: 'left',
        };
        // bordrRadius: `0 0 0 0`

      }
      // 超出左边界
      else if ((Math.ceil(ratio) - Math.floor(tooltipRatio) / 2) <= 0) {
        return {
          left: `-0.725rem`,
          textAlign: 'left',
        };
        // borderRadius: `0 0  0 0`

      }

      // console.log('ratio---', ratio);
      // console.log('tooltipRatio---', tooltipRatio);
      if (ratio >= 50) {
        return {
          // right: `${100 - ratio - 8}%`,
          right: `${100 - ratio}%`,
          transform: `translateX(50%)`,
          textAlign: 'left'
        };
      }
      return {
        left: `${ratio}%`,
        transform: `translateX(-50%)`,
        textAlign: 'left'
      };
    },
    formatValue(value) {
      const absValue = Math.abs(value);
      let formattedValue;
      let unit = '';

      if (absValue >= 100000000) {
        formattedValue = (absValue / 100000000).toFixed(2);
        unit = '亿';
      } else if (absValue >= 10000) {
        formattedValue = (absValue / 10000).toFixed(2);
        unit = '万';
      } else {
        formattedValue = absValue.toFixed(2);
      }

      return {
        formattedValue: value < 0 ? `-${formattedValue}` : formattedValue,
        unit
      };
    },
    selectTabQYTrend(index) {
      this.activeTabQYTrend = index;
      // this.activeTabQYTrend = item.code;
      this.updateQYTrendData();
    },
    getQyOption() {
      this.qyOption = this.getBarOption(['#FF9B47'], this.qyTrendData);
      if (this.activeTabQYTrend === 1) {
        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {
          // this.qyOption.yAxis[1].name = '单位：万元';
          this.qyOptionName = '单位：万元';
          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {
            return (value / 10000).toFixed(2);
          }
          this.qyOption.series[0].label.formatter = function (params) {
            return (params.value / 10000).toFixed(2);
          }
        } else {
          //  this.qyOption.yAxis[1].name = '单位：亿元';
          this.qyOptionName = '单位：亿元';
          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {
            return (value / 100000000).toFixed(2);
          }
          this.qyOption.series[0].label.formatter = function (params) {
            return (params.value / 100000000).toFixed(2);
          }
        }
      } else if (this.activeTabQYTrend === 0) {
        // this.qyOption.yAxis[1].name = '单位：套';
        this.qyOptionName = '单位：套';
        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {
          return value;
        }
        this.qyOption.series[0].label.formatter = function (params) {
          return params.value;
        }
      } else if (this.activeTabQYTrend === 2) {
        //  this.qyOption.yAxis[1].name = '单位：m²';
        this.qyOptionName = '单位：m²';
        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {
          return value;
        }
        this.qyOption.series[0].label.formatter = function (params) {
          return params.value;
        }
      }
      this.qyOption.tooltip = {
        show: false
      };
    },
    updateQYTrendData() {
      const data = this.qyTrendRawData;
      switch (this.activeTabQYTrend) {
        case 0: // 套数
          this.filteredQYTrendData = data.map(item => item.num);
          break;
        case 1: // 金额
          this.filteredQYTrendData = data.map(item => item.value);
          break;
        case 2: // 面积
          this.filteredQYTrendData = data.map(item => item.area);
          break;
        default:
          this.filteredQYTrendData = data;
      }
      this.qyTrendData.series = this.filteredQYTrendData;
      this.getQyOption();
    },
    selectTabRGTrend(index) {
      this.activeTabRGTrend = index;
      this.updateRGTrendData();
    },
    getRgOption() {
      this.rgOption = this.getBarOption(['#376DF7'], this.rgTrendData)
    },
    updateRGTrendData() {
      const data = this.rgTrendRawData;
      switch (this.activeTabRGTrend) {
        case 0: // 套数
          this.filteredRGTrendData = data.map(item => item.num);
          break;
        case 1: // 金额
          this.filteredRGTrendData = data.map(item => item.value);
          break;
        case 2: // 面积
          this.filteredRGTrendData = data.map(item => item.area);
          break;
        default:
          this.filteredRGTrendData = data;
      }
      this.rgTrendData.series = this.filteredRGTrendData;
      this.getRgOption();
      if (this.activeTabRGTrend === 1) {
        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {
          // this.rgOption.yAxis[1].name = '单位：万元';
          this.rgOptionName = '单位：万元';
          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {
            return (value / 10000).toFixed(2);
          }
          this.rgOption.series[0].label.formatter = function (params) {
            return (params.value / 10000).toFixed(2);
          }
        } else {
          // this.rgOption.yAxis[1].name = '单位：亿元';
          this.rgOptionName = '单位：亿元';
          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {
            return (value / 100000000).toFixed(2);
          }
          this.rgOption.series[0].label.formatter = function (params) {
            return (params.value / 100000000).toFixed(2);
          }
        }
      } else if (this.activeTabRGTrend === 0) {
        // this.rgOption.yAxis[1].name = '单位：套';
        this.rgOptionName = '单位：套';
        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {
          return value;
        }
        this.rgOption.series[0].label.formatter = function (params) {
          return params.value;
          ;
        }
      } else if (this.activeTabRGTrend === 2) {
        // this.rgOption.yAxis[1].name = '单位：m²';
        this.rgOptionName = '单位：m²';
        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {
          return value;
        }
        this.rgOption.series[0].label.formatter = function (params) {
          return params.value;
        }
      }
      this.rgOption.tooltip = {
        show: false
      }
    },
    async getQYYTData(data) { // 签约业态分布
      const res = await API.Sales.bussDist(data);
      this.qyYTData = res.data;
    },
    getQYYTOption(data) { // 签约业���分布chart option
      const option = this.getPieOption();
      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];
      const scale = window.innerWidth / 1680;
      const rich = {
        yellow: {
          color: "#ffc72b",
          fontSize: 12 * scale,
          padding: [2, 0],
          align: 'center'
        },
        total: {
          color: "#ffc72b",
          fontSize: 40 * scale,
          align: 'center'
        },
        labelColor: {
          color: "#333333",
          align: 'center',
          fontSize: 12 * scale,
          padding: [2, 0]
        },
        blue: {
          color: '#49dff0',
          fontSize: 16 * scale,
          align: 'center'
        },
        hr: {
          borderColor: '#0b5263',
          width: '100%',
          borderWidth: 1 * scale,
          height: 0,
          margin: [5, 0]
        }
      }
      option.series[0].data = data.map(item => {
        return {
          value: this.$toFixed2(item.rate),
          name: item.bussName,
          ...item
        }
      });
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + 'px'
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          const formatAmount = (amount) => {
            if (!amount && amount !== 0) return '--';
            return `${(amount / 100000000).toFixed(2)}亿`;
          };

          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">名称: ${params.data.bussName}</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">套数: ${params.data.num}</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">面积: ${params.data.area}</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">金额: ${formatAmount(params.data.qyAmount)}</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">比例: ${this.$formatNull(params.data.rate)}%</div>
          </div>`;
        }
      };
      // option.series[0].avoidLabelOverlap = true
      option.series[0].labelLine = {
        show: true, // 显示连接线
        minTurnAngle: 90, // 限制转折角度，防止错位
        normal: {
          length: 20 * scale,
          length2: 12 * scale,
          lineStyle: {
            width: 1 * scale,
          }
        }
      };

      option.legend = {
        data: data.map(item => item.bussName),
        textStyle: {
          color: '#333333',
          fontSize: 12 * scale
        },
        itemGap: 10 * scale,
        itemWidth: 12 * scale,
        itemHeight: 12 * scale,
        bottom: '0',
        left: 'center',
        orient: 'horizontal',
        icon: 'circle', // 设置图例为小圆点
      };
      option.series[0].label = {
        show: true, // 显示标签
        position: 'outside', // 标签位置
        // overflow: 'truncate', // 防止标签超出容器
        distance: 5 * scale,
        normal: {
          formatter: function (params) {
            const formatAmount = (amount) => {
              if (!amount && amount !== 0) return '--';
              return `${(amount / 100000000).toFixed(2)}亿`;
            };
            return '{labelColor|' + params.name + '}\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';
          },
          rich: rich
        },
      };
      return option;
    },
    async getAllHkData(data) { // 全盘销售进度 - 回款
      this.loadingSales = true;
      try {
        const res = await API.Sales.allHkData(data);
        this.allHkData = res.data;
      } finally {
        this.loadingSales = false;
      }
    },
    async getAllQyData(data) { // 全盘销售进度 - 签约
      this.loadingSales = true;
      try {
        const res = await API.Sales.allQyData(data);
        this.allQyData = res.data;
      } finally {
        this.loadingSales = false;
      }
    },
    handleTrendTab(item) { // 趋势图查询类型切换
      this.queryTypeTrend = item.code;
      this.initTrend(); // 初始化趋势图
    },
    updateCSSVariable(value) {
      document.documentElement.style.setProperty('--timeline-before-content', value);
    },
    async getAllTargetQyData(data){ // 全周期进度偏差-签约数据
      this.loadingAllTargetQYProgress = true;
      try {
        const res = await API.Sales.allTargetQyData(data);
        // 使用$formatNull对res.data中的所有数据字段格式化
        this.signDataByTime = Object.fromEntries(
          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])
        );
      } finally {
        this.loadingAllTargetQYProgress = false;
      }
    },
    async getAllTargetHkData(data) { // 全周期进度偏差-回款数据
      this.loadingAllTargetHKProgress = true;
      try {
        const res = await API.Sales.allTargetHkData(data);
        this.hkDataByTime = Object.fromEntries(
          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])
        );
      } finally {
        this.loadingAllTargetHKProgress = false;
      }
    },
    async getYearQyData(data) { // 年度销售进度-年度签约数据
      this.loadingYearProgress = true;
      try {
        const res = await API.Sales.yearTargetQyData(data);
        this.yearQyData = Object.fromEntries(
          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])
        );
      } finally {
        this.loadingYearProgress = false;
      }
    },
    async getYearHkData(data) { // 年度销售进度-年度回款数据
      this.loadingYearProgress = true;
      try {
        const res = await API.Sales.yearTargetHkData(data);
        this.yearHkData = Object.fromEntries(
          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])
        );
        // this.yearHkData = res.data;
      } finally {
        this.loadingYearProgress = false;
      }
    },
    async getRgData(data) { // 累计认购
      this.loadingTables = true;
      try {
        const res = await API.Sales.rgData(data);
        this.rgData = [this.sanitizeData(res.data)];
      } finally {
        this.loadingTables = false;
      }
    },
    async getQyDfData(data) { // 累计签约
      this.loadingTables = true;
      try {
        const res = await API.Sales.qyDfData(data);
        this.qyData = [this.sanitizeData(res.data)];
      } finally {
        this.loadingTables = false;
      }
    },
    sanitizeData(data) { // 数据为空时，显示--
      if (!data) return {};
      return Object.fromEntries(
        Object.entries(data).map(([key, value]) => [key, value ?? '--'])
      );
    },
    handleTabSign(item) {
      this.queryTypeSign = item.code;
      this.getQyDfData({
        queryType: this.queryTypeSign,
        projectCode: this.projectCode
      });
      this.getRgData({
        queryType: this.queryTypeSign,
        projectCode: this.projectCode
      });
      this.getQYYTData({ // 签约业态分布
        queryType: this.queryTypeSign,
        projectCode: this.projectCode
      });
    },
    fetchAnalyse(projectCode) { // 货值分析-货值分布表
      API.Value.analyse(projectCode).then(res => {
        this.analysisData = res.data.map(item => ({
          projectType: item.projectType || '--',
          hxName: item.hxName || '--',
          totalNum: item.totalNum === null ? '--' : item.totalNum,
          ysNum: item.ysNum === null ? '--' : item.ysNum,
          wsNum: item.wsNum === null ? '--' : item.wsNum,
          ygwsNum: item.ygwsNum === null ? '--' : item.ygwsNum,
          wgNum: item.wgNum === null ? '--' : item.wgNum,
          allTopNum: item.allTopNum === null ? '--' : item.allTopNum,
          allBottomNum: item.allBottomNum === null ? '--' : item.allBottomNum,
          ysTopNum: item.ysTopNum === null ? '--' : item.ysTopNum,
          ysBottomNum: item.ysBottomNum === null ? '--' : item.ysBottomNum,
          wsTopNum: item.wsTopNum === null ? '--' : item.wsTopNum,
          wsBottomNum: item.wsBottomNum === null ? '--' : item.wsBottomNum,
          allHzAmount: item.allHzAmount === null ? '--' : item.allHzAmount,
          ysHzAmount: item.ysHzAmount === null ? '--' : item.ysHzAmount,
          wsHzAmount: item.wsHzAmount === null ? '--' : item.wsHzAmount,
        }));
      })
    },
    async getkqData(data) { // 款齐
      this.loadingPayments = true;
      try {
        const res = await API.Sales.kqData(data);
        this.kqData = res.data;
        this.cardData[0].value = this.kqData.kqTargetAmount;
        this.cardData[1].value = this.kqData.yearKqAmount;
        this.cardData[2].value = this.kqData.kjlKqAmount;
      } finally {
        this.loadingPayments = false;
      }
    },

    handleHKTab(item) {
      this.hkProjectType = item.code;
      this.getHkTrend();
    },
    handleRgTab(item) {
      this.rgProjectType = item.code;
      this.getRgTrend();
    },
    handleQyTab(item) {
      this.qyProjectType = item.code;
      this.getQyTrend();
    },
    async getProjectType(projectCode) { // 查询业态列表
      const res = await API.Common.getProjectType(projectCode);
      this.projectTypeList = res.data;
      this.hasData = this.projectTypeList.length > 0;
      this.qyProjectType = this.rgProjectType = this.hkProjectType
        = this.projectTypeList[0].code;
    },
    async initTrend() {
      this.loadingTrends = true;
      try {
        await Promise.all([
          this.getQyTrend(),
          this.getRgTrend(),
          this.getDfTrend(),
          this.getHkTrend()
        ]);
      } finally {
        this.loadingTrends = false;
      }
    },
    getDfOption() { // 到访趋势图数据
      this.dfOption = this.getBarOption(['#53B997'], this.dfTrendData, 'df')
      // this.dfOption.yAxis[1].name = '单位：人次';
      this.dfOptionName = '单位：人次';
      this.dfOption.tooltip = {
        show: false
      }
    },
    async getDfTrend() { // 销售页签 - 到访趋势
      const res = await API.Sales.dfTrend({
        queryType: this.queryTypeTrend,
        projectCode: this.projectCode
      });
      this.dfTrendData.xAxis = res.data.map(item => item.key.replace('-', '\n'));
      this.dfTrendData.series = res.data.map(item => item.value);
      this.getDfOption();
    },
    async getRgTrend() { // 销售页签 - 认购趋势
      const res = await API.Sales.rgTrend({
        queryType: this.queryTypeTrend,
        projectCode: this.projectCode,
        projectType: this.rgProjectType
      });
      this.rgTrendRawData = res.data;
      this.rgTrendData.xAxis = res.data.map(item => item.key.replace('-', '\n'));
      // this.rgTrendData.series = res.data.map(item => item.value);
      this.updateRGTrendData();
    },
    async getQyTrend() { // 销售页签 - 签约趋势
      const res = await API.Sales.qyTrend({
        queryType: this.queryTypeTrend,
        projectCode: this.projectCode,
        projectType: this.qyProjectType
      });
      this.qyTrendRawData = res.data;
      this.qyTrendData.xAxis = res.data.map(item => item.key.replace('-', '\n'));
      // this.qyTrendData.series = res.data.map(item => item.value);
      this.updateQYTrendData();
    },
    getHkOption() {
      this.hkOption = this.getBarOption(['#9D7BFF'], this.hkTrendData);
      if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {
        // this.hkOption.yAxis[1].name = '单位：万元';
        this.hkOptionName = '单位：万元'
        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {
          return (value / 10000).toFixed(2);
        }
        this.hkOption.series[0].label.formatter = function (params) {
          return (params.value / 10000).toFixed(2);
        }
      } else {
        // this.hkOption.yAxis[1].name = '单位：亿元';
        this.hkOptionName = '单位：万元';
        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {
          return (value / 100000000).toFixed(2);
        }
        this.hkOption.series[0].label.formatter = function (params) {
          return (params.value / 100000000).toFixed(2);
        }

      }
      this.hkOption.tooltip = {
        show: false
      };
    },
    async getHkTrend() { //销售页签 - 回款趋势
      const res = await API.Sales.hkTrend({
        queryType: this.queryTypeTrend,
        projectCode: this.projectCode,
        projectType: this.hkProjectType
      });

      this.hkTrendData.xAxis = res.data.map(item => item.key.replace('-', '\n'));
      this.hkTrendData.series = res.data.map(item => item.value);
      this.getHkOption();
    },
    getPieOption(color = ['#376DF7', '#53B997', '#6750AA', '#F8C541']) {
      const pieOption = new PieOption();
      pieOption.setColor(color);
      return pieOption.getOption();
    },
    getBarOption(color = ['#53B997'], data = {xAxis: [], series: []}, type = '') {
      const barOption = new BarOption();
      barOption.updateData(data.xAxis, data.series);
      barOption.setColor(color);
      const option = barOption.getOption();
      if (!Array.isArray(option.yAxis)) {
        option.yAxis = [{}];
      }
      return option;
    },
    handleSpanMethod({row, column, rowIndex, columnIndex}) {
      // 处理最后一行
      if (rowIndex === this.analysisData.length - 1) {
        if (columnIndex === 0) {  // 第一列
          return {
            rowspan: 1,
            colspan: 2  // 合并两列
          };
        }
        if (columnIndex === 1) {  // 第二列
          return {
            rowspan: 0,
            colspan: 0  // 隐藏第二列
          };
        }
      }

      // 处理其他行的第一列合并
      if (columnIndex === 0) {
        const projectType = row.projectType;

        // 向上查找相同业态的起始位置
        let startIndex = rowIndex;
        while (startIndex > 0 && this.analysisData[startIndex - 1].projectType === projectType) {
          startIndex--;
        }

        // 计算相同业态的行数
        let spanCount = 0;
        for (let i = startIndex; i < this.analysisData.length; i++) {
          if (this.analysisData[i].projectType === projectType) {
            spanCount++;
          } else {
            break;
          }
        }

        // 只在每组的第一行显示，其他行隐藏
        if (rowIndex === startIndex) {
          return {
            rowspan: spanCount,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
  },
  computed: {
    qyProgressOption() {
      const data = this.allQyData;
      const progressCircleOption = new ProgressCircle();

      if (data.rate > 100) {
        progressCircleOption.setBarItemStyle('#1433CC');
        progressCircleOption.setBackgroundStyle('#376DF7');
        progressCircleOption.setBarData([data.rate % 100]);
      } else {
        progressCircleOption.setBarItemStyle('#376DF7');
        progressCircleOption.setBackgroundStyle('#C2CEF8');
        progressCircleOption.setBarData([data.rate]);
      }

      const scale = window.innerWidth / 1680;
      const option = progressCircleOption.getOption();
      option.title = {
        show: false,
        subtext: '进度',
        text: `${this.$toFixed2(data.rate)}%`,
        textStyle: {
          color: '#376DF7',
          fontSize: 21 * scale
        },
        subtextStyle: {
          fontSize: 21 * scale * 0.6
        },
        itemGap: 10 * scale
      }
      option.series[0].roundCap = option.series[0].data[0] > 3
      option.polar.radius = ['80%', '100%']
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        show: true,
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + 'px'
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          const formatAmount = (amount) => {
            if (!amount && amount !== 0) return '--';
            if (this.queryTypeSign === 1) { // Daily view
              return `${(amount / 10000).toFixed(2)}万`;
            }
            return `${(amount / 100000000).toFixed(2)}亿`;
          };

          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">动态货值: ${data.dynamicAmount} 亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">累计签约: ${data.qyAmount} 亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">进度: ${this.$formatNull(data.rate)}%</div>
          </div>`;
        }
      }
      return option;
    },
    hkProgressOption() {
      const data = this.allHkData;
      const progressCircleOption = new ProgressCircle();

      if (data.rate > 100) {
        progressCircleOption.setBarItemStyle('#e56d16');
        progressCircleOption.setBackgroundStyle('#FF974C');
        progressCircleOption.setBarData([data.rate % 100]);
      } else {
        progressCircleOption.setBarItemStyle('#FF974C');
        progressCircleOption.setBackgroundStyle('#F9DEC8');
        progressCircleOption.setBarData([data.rate]);
      }

      const scale = window.innerWidth / 1680;
      const option = progressCircleOption.getOption();
      // option.title[0].subtext = '进度';
      // option.title[0].text = `${data.rate}%`;
      // option.title[0].textStyle.color = '#FF974C';
      // option.title[0].textStyle.fontSize = 21 * scale;
      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;
      // option.title[0].itemGap = 10 * scale;
      option.title = {
        show: false,
        subtext: '进度',
        text: `${data.rate}%`,
        textStyle: {
          color: '#FF974C',
          fontSize: 21 * scale
        },
        subtextStyle: {
          fontSize: 21 * scale * 0.6
        },
        itemGap: 10 * scale
      }
      option.polar.radius = ['80%', '100%']
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(18 * (windowWidth / 1680));
      option.tooltip = {
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + 'px'
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          const formatAmount = (amount) => {
            if (!amount && amount !== 0) return '--';
            if (this.queryTypeSign === 1) { // Daily view
              return `${(amount / 10000).toFixed(2)}万`;
            }
            return `${(amount / 100000000).toFixed(2)}亿`;
          };

          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">动态货值: ${data.dynamicAmount} 亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">累计回款: ${data.hkAmount} 亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">进度: ${this.$formatNull(data.rate)}%</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">权益比例: ${data.qyRate}%</div>
          </div>`;
        }
      }
      return option;
    },
    kqProgressOption() {
      const data = this.kqData.kqRatio;
      let text = '--';
      if (!(data === null || data === undefined)) {
        text = data;
      }

      const progressCircleOption = new ProgressCircle();
      progressCircleOption.setBarData([(data % 100).toFixed(2) || '--']);

      if (data > 100) {
        progressCircleOption.setBarItemStyle('#6A3DC4');
        progressCircleOption.setBackgroundStyle('#7B6FF2');
      } else {
        progressCircleOption.setBarItemStyle('#7B6FF2');
        progressCircleOption.setBackgroundStyle('#E2E0FB');
      }

      const scale = window.innerWidth / 1680;
      const option = progressCircleOption.getOption();
      option.series[0].roundCap = option.series[0].data[0] > 3
      // option.title[0].subtext = '';
      // option.title[0].text = `${text}%`;
      // option.title[0].textStyle.color = '#7B6FF2';
      // option.title[0].textStyle.fontSize = 21 * scale;
      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;
      // option.title[0].itemGap = 10 * scale;
      option.title = {
        show: false,
        subtext: '',
        text: `${text}%`,
        textStyle: {
          color: '#7B6FF2',
          fontSize: 21 * scale
        },
        subtextStyle: {
          fontSize: 21 * scale * 0.6
        },
        itemGap: 10 * scale
      }
      option.polar.radius = ['80%', '100%']
      const windowWidth = window.innerWidth;
      const fontSize = Math.floor(16 * (windowWidth / 1680));
      option.tooltip = {
        show: false,
        position: 'right',
        confine: true,
        textStyle: {
          fontSize: fontSize,
          lineHeight: Math.floor(fontSize * 1.5) + 'px'
        },
        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],
        formatter: (params) => {
          const formatAmount = (amount) => {
            if (!amount && amount !== 0) return '--';
            return `${(amount / 100000000).toFixed(2)}亿`;
          };

          return `<div style="font-size: ${fontSize}px;">
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">款齐目标金额: ${this.kqData.kqTargetAmount}亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">可结利款齐金额: ${this.kqData.kjlKqAmount}亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">年度款齐: ${this.kqData.yearKqAmount}亿</div>
            <div style="line-height: ${Math.floor(fontSize * 1.5)}px">款齐百分比: ${this.$formatNull(this.kqData.kqRatio)}%</div>
          </div>`;
        }
      }
      return option;
    },
    qyYTOption() { // 签约业态分布chart option
      return this.getQYYTOption(this.qyYTData);
      /* const option = this.getPieOption();
      const scale = window.innerWidth / 1680;
      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];

      option.series[0].labelLine = {
        show: true, // 显示标签
        position: 'outside', // 标签位置
        minTurnAngle: 90,
        normal: {
          length: 12 * scale,
          length2: 12 * scale,
          lineStyle: {
            width: 1 * scale,
          }
        }
      };

      option.series[0].data = this.qyYTData.map(item => ({
        value: item.rate,
        name: item.bussName,
        ...item
      }));

      option.tooltip = {
        show: true,
        padding: [0, 0],
        formatter: (params) => {
          const formatAmount = (amount) => {
            if (!amount && amount !== 0) return '--';
            return `${(amount / 100000000).toFixed(2)}亿`;
          };

          return `<div style="font-size: 0.875rem; line-height: 1.2;padding: 0.3rem 0.75rem;border-radius: 0.1rem;">
            <div>名称: ${params.data.bussName}</div>
            <div>套数: ${params.data.num}</div>
            <div>面积: ${params.data.area}</div>
            <div>金额: ${formatAmount(params.data.qyAmount)}</div>
            <div>比例: ${this.$formatNull(params.data.rate)}%</div>
          </div>`;
        }
      };

      option.legend = {
        data: this.qyYTData.map(item => item.bussName),
        textStyle: {
          color: '#333333',
          fontSize: 12 * scale
        },
        itemGap: 10 * scale,
        itemWidth: 12 * scale,
        itemHeight: 12 * scale,
        bottom: '0',
        left: 'center',
        orient: 'horizontal',
        icon: 'circle',
      };

      option.series[0].label = {
        show: true,
        position: 'outside',
        normal: {
          formatter: function (params) {
            const formatAmount = (amount) => {
              if (!amount && amount !== 0) return '--';
              return `${(amount / 100000000).toFixed(2)}亿`;
            };
            return '{labelColor|' + params.name + '}\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';
          },
          rich: rich
        },
      };

      return option; */
    }
  }
}
</script>
<style scoped lang="scss">
@import './sales.scss';
</style>
