<template>
  <div class="select-user-mobile">
    <van-popup
      v-model="visible"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
      @close="handleClose"
    >
      <div class="popup-container">
        <div class="popup-title">选择人员</div>

        <!-- 搜索区域 -->
        <div class="search-container">
          <van-search
            v-model="searchValue"
            placeholder="搜索姓名或手机号"
            shape="round"
            @search="handleSearch"
            @clear="handleClear"
            class="search-input"
          />
          <van-button type="primary" size="small" round @click="handleSearch">搜索</van-button>
        </div>

        <!-- 已选择用户区域 -->
        <div v-if="selectedUsers.length > 0" class="selected-users">
          <van-tag
            v-for="user in selectedUsers"
            :key="user.userId"
            closeable
            type="primary"
            class="selected-user-tag"
            @close="handleRemoveUser(user)"
          >
            {{ user.nickName }}
          </van-tag>
        </div>

        <!-- 用户列表 -->
        <div class="user-list-container">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad"
            >
              <van-cell
                v-for="item in userList"
                :key="item.userId"
                @click="handleSelectUser(item)"
              >
                <template #title>
                  <div class="user-item">
                    <div class="user-avatar">
                      <van-image
                        round
                        width="40"
                        height="40"
                        :src="item.avatar || require('@/assets/images/profile.jpg')"
                        fit="cover"
                      >
                        <template #error>
                          <div class="avatar-text">{{ item.nickName ? item.nickName.substring(0, 1) : '?' }}</div>
                        </template>
                      </van-image>
                    </div>
                    <div class="user-info">
                      <div class="user-name">{{ item.nickName }}-{{ item.userName}}</div>
                      <div class="user-dept">{{ item.dept ? item.dept.deptName : '' }}</div>
                    </div>
                    <div class="user-select">
                      <van-icon
                        :name="isSelected(item) ? 'success' : 'circle'"
                        :class="{ 'selected': isSelected(item) }"
                      />
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-list>
          </van-pull-refresh>
        </div>

<!--        :disabled="selectedUsers.length === 0"-->
        <!-- 底部按钮  -->
        <div class="bottom-buttons">
          <van-button
            block
            type="primary"
            @click="handleConfirm"
          >
            确定 ({{ selectedUsers.length }})
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
// import { listUser } from "@/api/system/user";
import {listUser} from '@/api/weekly/mobile-reportInfo'
import {
  Popup,
  Search,
  List,
  Cell,
  PullRefresh,
  Button,
  Icon,
  Image as VanImage,
  Tag
} from 'vant';

export default {
  name: "SelectUserMobile",
  components: {
    [Popup.name]: Popup,
    [Search.name]: Search,
    [List.name]: List,
    [Cell.name]: Cell,
    [PullRefresh.name]: PullRefresh,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [VanImage.name]: VanImage,
    [Tag.name]: Tag
  },
  props: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 已选择的用户
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      userList: [],
      selectedUsers: [],
      searchValue: '',
      loading: false,
      finished: false,
      refreshing: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        userName: undefined,
        phonenumber: undefined
      }
    };
  },
  watch: {
    show: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.selectedUsers = [...this.value];
          this.resetQuery();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 重置查询参数
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.userList = [];
      this.finished = false;
      this.loading = true;
      this.onLoad();
    },

    // 加载数据
    async onLoad() {
      if (this.refreshing) {
        this.userList = [];
        this.refreshing = false;
      }

      try {
        const res = await listUser(this.queryParams);
        this.userList.push(...res.rows);
        this.loading = false;

        if (this.userList.length >= res.total) {
          this.finished = true;
        }
        this.queryParams.pageNum++;
      } catch (error) {
        this.loading = false;
        this.finished = true;
        this.$message.error('获取用户列表失败');
      }
    },

    // 刷新
    onRefresh() {
      this.resetQuery();
    },

    // 搜索
    handleSearch() {
      if (this.searchValue) {
        // 判断是否是手机号
        if (/^1\d{10}$/.test(this.searchValue)) {
          this.queryParams.phonenumber = this.searchValue;
          this.queryParams.userName = undefined;
        } else {
          this.queryParams.userName = this.searchValue;
          this.queryParams.phonenumber = undefined;
        }
      } else {
        this.queryParams.userName = undefined;
        this.queryParams.phonenumber = undefined;
      }
      this.resetQuery();
    },

    // 清除搜索
    handleClear() {
      this.searchValue = '';
      this.queryParams.userName = undefined;
      this.queryParams.phonenumber = undefined;
      this.resetQuery();
    },

    // 选择用户
    handleSelectUser(user) {
      if (this.multiple) {
        // 多选模式
        const index = this.selectedUsers.findIndex(item => item.userName === user.userName);
        if (index > -1) {
          this.selectedUsers.splice(index, 1);
        } else {
          this.selectedUsers.push(user);
        }
      } else {
        // 单选模式
        this.selectedUsers = [user];
        // this.handleConfirm();
      }
    },

    // 判断是否已选择
    isSelected(user) {
      return this.selectedUsers.some(item => item.userName === user.userName);
    },

    // 确认选择
    handleConfirm() {
      this.$emit('input', this.selectedUsers);
      this.$emit('select', this.selectedUsers);
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false;
      this.$emit('update:show', false);
      this.$emit('close');
    },

    // 移除已选择的用户
    handleRemoveUser(user) {
      const index = this.selectedUsers.findIndex(item => item.userName === user.userName);
      if (index > -1) {
        this.selectedUsers.splice(index, 1);
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.select-user-mobile {
  .popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      padding: 16px 0;
      border-bottom: 1px solid #f2f2f2;
    }

    .search-container {
      padding: 8px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .search-input {
        flex: 1;
      }
    }

    .selected-users {
      padding: 8px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      border-bottom: 1px solid #f2f2f2;

      .selected-user-tag {
        margin-right: 8px;
        margin-bottom: 8px;
        padding: 4px 8px;
        border-radius: 6px;
      }
    }

    .user-list-container {
      flex: 1;
      overflow-y: auto;

      .user-item {
        display: flex;
        align-items: center;
        padding: 8px 0;

        .user-avatar {
          margin-right: 12px;

          .avatar-text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: #f2f2f2;
            color: #666;
            font-size: 16px;
            border-radius: 50%;
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .user-dept {
            font-size: 12px;
            color: #999;
          }
        }

        .user-select {
          padding: 0 8px;

          .van-icon {
            font-size: 20px;
            color: #dcdee0;

            &.selected {
              color: #1989fa;
            }
          }
        }
      }
    }

    .bottom-buttons {
      padding: 16px;
      border-top: 1px solid #f2f2f2;
    }
  }
}
</style>
