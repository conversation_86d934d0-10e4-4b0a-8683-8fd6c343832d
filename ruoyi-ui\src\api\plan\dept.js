import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDept(query) {
  return request({
    url: '/plan/dept/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDept(id) {
  return request({
    url: '/plan/dept/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addDept(data) {
  return request({
    url: '/plan/dept',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDept(data) {
  return request({
    url: '/plan/dept',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delDept(id) {
  return request({
    url: '/plan/dept/' + id,
    method: 'delete'
  })
}

// 复制提交
export function clone(data) {
  return request({
    url: '/plan/dept/clone',
    method: 'post',
    data
  })
}

// 得到默认反馈人
export function getRespPeople(data) {
  return request({
    url: '/plan/dept/respPeople',
    method: 'post',
    data
  })
}

