
75b354b7c2ddc03ab9ed1247c0d0d0e6ffc5d8db	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e047615ff8ba11ff9333416a14c4293f\"}","integrity":"sha512-8D/SApq0lHOjaA9pGBVo/ZOdSb84CY3NaUTloocZdKL1kAgfN2kD58NH25PwuQwnQgdW2/0G4qTCy5z7jpkrDQ==","time":1754311555169,"size":12044409}