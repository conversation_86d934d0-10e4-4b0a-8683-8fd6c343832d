<template>
  <treeselect
    v-model="select"
    :options="departList"
    :placeholder="placeholder"
    :disable-branch-nodes="true"
    append-to-body
    z-index="9000"
    :disabled="disabled"
  />
</template>

<script>
import { deptRoletreeselect } from "@/api/system/dept"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  components: { Treeselect },
  props: ['selectValue', 'placeholder', 'disabled'],
  data () {
    return {
      departmentOption: [],
      select: ''
    }
  },
  mounted () {
    this.select = this.selectValue || null
    if (!this.departList.length) {
      this.$store.dispatch('plan/fetchDepartList')
    }
  },
  computed: {
    departList () {
      return this.$store.state.plan.departList
    }
  },
  watch: {
    select (newval, oldval) {
      if (newval && newval != oldval) {
        this.$emit('changeEmit', newval)
      }
    }
  },
  methods: {
    getDepats () {
      deptRoletreeselect().then(res => {
        this.departmentOption = res.data
      }).catch(err => { })
    },
  }

};
</script>

<style lang="scss">
</style>
