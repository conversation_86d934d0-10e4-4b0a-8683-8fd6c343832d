<template>
  <ProjectLayout @click.native="handleDialogClick">
    <div class="dashboard-container">
<!--      <Watermark
        style="width: 100vw; height: 100vh;"
        text="机密文件"
        :font-size="20"
        font-family="Arial"
        color="red"
        :opacity="0.1"
        :rotate="-45"
        :width="220"
        :height="150"
      >-->
        <Header/>
        <div class="card-tool">
          <section class="card-content">
            <div class="tool-bar">
              <!-- <div class="card-title">{{ projectName }}</div> -->
              <div class="menus-block">
                <CompanyTree
                  ref="companyTree"
                  :projectCode="projectCode"
                  :defaultSelectedFirstCompany="true"
                  @company-change="handleCompanyChange"
                  @project-change="handleProjectChange"
                />
              </div>
            </div>
            <Tab v-model="activeTab" @tab-change="handleTabChange" class="mb-8"/>
          </section>

        </div>
        <div class="dashboard-content">
          <div class="content">
            <!--          <div class="card">-->
            <!--            <div class="tool-bar">-->
            <!--              &lt;!&ndash; <div class="card-title">{{ projectName }}</div> &ndash;&gt;-->
            <!--              <div class="menus-block">-->
            <!--                <CompanyTree-->
            <!--                  :projectCode="projectCode"-->
            <!--                  :defaultSelectedFirstCompany="true"-->
            <!--                  @company-change="handleCompanyChange"-->
            <!--                  @project-change="handleProjectChange"-->
            <!--                />-->
            <!--              </div>-->
            <!--            </div>-->
            <!--            <Tab v-model="activeTab" class="mb-8"/>-->
            <!--          </div>-->
            <div class="com-content">
              <component
                :is="activeComponent"
                v-if="shouldRender"
                ref="activeComponent"
                :params="params"
                :key="projectCode"
              />
            </div>
          </div>
        </div>
<!--      </Watermark>-->
    </div>
  </ProjectLayout>
</template>
<script>
// 城市公司
import Header from '@/views/projects/components/Header.vue'
import CompanyTree from '@/views/projects/components/CompanyTree.vue'
import Tab from '@/views/projects/components/Tab.vue'
import Info from '@/views/projects/views/info.vue'
import Plan from '@/views/projects/views/plan.vue'
import Sales from '@/views/projects/views/sales.vue'
import Cost from '@/views/projects/views/cost.vue'
import Value from '@/views/projects/views/value.vue'
import Cashflow from '@/views/projects/views/cashflow.vue'
import Engineering from '@/views/projects/views/engineering.vue'
import Operation from '@/views/projects/views/operation.vue'
import ProfitAnalysis from '@/views/projects/views/profitAnalysis.vue'
import QualityManage from '@/views/projects/views/qualityManage.vue'
import Risk from '@/views/projects/views/risk.vue'
import Satisfaction from '@/views/projects/views/satisfaction.vue'
import Tax from '@/views/projects/views/tax.vue'
import ProjectLayout from '@/views/projects/layout/ProjectLayout.vue'
import Watermark from "@/views/projects/components/Watermark.vue";
import { debounce } from 'lodash'
import API from '@/views/projects/api'
export default {
  name: 'Dashboard',
  components: {
    Watermark,
    Header,
    CompanyTree,
    Tab,
    Info,
    Plan,
    Sales,
    Cost,
    Cashflow,
    Engineering,
    Operation,
    ProfitAnalysis,
    QualityManage,
    Risk,
    Satisfaction,
    Tax,
    Value,
    ProjectLayout
  },
  data() {
    return {
      tabs: [
        {label: '基本信息', value: 'project'},//---
        {label: '经营指标', value: 'operation'}, //
        {value: 'quality-manage', label: '品质管理'},
        {value: 'risk', label: '风险管理'},
        {value: 'engineering', label: '工程评估'},
        {value: 'satisfaction', label: '满意度'},
        {value: 'plan', label: '计划管理'},//---
        {value: 'sales', label: '销售管理'},//---
        {value: 'cashflow', label: '现金流'},
        {value: 'value', label: '货值'},//---
        {value: 'cost', label: '成本'},//---
        {value: 'tax', label: '费用'},
        {value: 'profit-analysis', label: '利润'}
      ],
      activeTab: 'project',
      params: null,
      projectName: '',
      projectCode: '',
      debouncedResize: null,
      shouldRender: true
    }
  },
  computed: {
    activeComponent() {
      const componentMap = {
        'project': Info,
        'plan': Plan,
        'sales': Sales,
        'cost': Cost,
        'cashflow': Cashflow,
        'engineering': Engineering,
        'operation': Operation,
        'profit-analysis': ProfitAnalysis,
        'quality-manage': QualityManage,
        'risk': Risk,
        'satisfaction': Satisfaction,
        'tax': Tax,
        'value': Value
      }
      return componentMap[this.activeTab]
    }
  },
  created() {
    this.params = this.$route.query
    this.projectName = this.$route.query.projectName
    this.projectCode = this.$route.query.projectCode
    this.debouncedResize = debounce(this.handleResize, 100)
    window.addEventListener('resize', this.debouncedResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedResize)
  },
  methods: {
    /**
     * 处理弹窗点击事件
     * 当点击弹窗非树组件区域时，折叠项目树
     */
    handleDialogClick(event) {
      // 检查点击事件源是否在 CompanyTree 组件之外
      const treeEl = this.$refs.companyTree.$el;
      if (!treeEl.contains(event.target)) {
        // 如果 CompanyTree 组件有提供关闭/折叠的方法，调用它
        if (this.$refs.companyTree.handleCollapse) {
          this.$refs.companyTree.handleCollapse();
        } else {
          // 如果没有明确的方法，可以尝试调用组件上公开的其他方法
          console.log('CompanyTree 组件需要提供折叠方法');
        }
      }
    },
    findTabLabel(value){
      const tab = this.tabs.find(tab => tab.value === value);
      return tab ? tab.label : null;
    },
    handleTabChange(tab){
      this.handleUserRecord({recordModule: this.findTabLabel(tab)});
    },
    handleUserRecord(data){ // 埋点
      API.UserTrack.userRecord(data)
    },
    handleProjectChange(project) {
      // 先比较是否需要更新
      if (this.$route.query.projectCode !== project.projectCode) {
        const newQuery = {
          projectCode: project.projectCode,
          projectName: project.displayName
        }

        this.$router.replace({
          path: this.$route.path,
          query: newQuery
        })
      }

      // 更新本地数据
      this.projectName = project.displayName
      this.projectCode = project.projectCode
      this.params = {
        ...this.params,
        projectCode: project.projectCode,
        projectName: project.displayName
      }
    },
    handleCompanyChange(){

    },
    handleNavigate(path, query = {}, replace = false) {
      this.$router.push({path: '/projects/baseInfo', query}, replace)
    },
    handleResize() {
      this.shouldRender = false
      this.$nextTick(() => {
        setTimeout(() => {
          this.shouldRender = true
          this.$bus.$emit('chartResize')
        }, 300)
      })
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@/views/projects/styles/projects.scss';
.dashboard-container {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('~@/views/projects/assets/images/dashboard-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .card-tool{
    padding: 1rem 1.5rem 0;
    .card-content{
      padding: 1rem 1rem;
      background: #FFFFFF;
    }
  }
  .dashboard-content {
    //flex: 1;
    //background-image: url('~@/views/projects/assets/images/dashboard-bg.png');
    //background-size: 100% 100%;
    //background-repeat: no-repeat;
    //padding: 1.3125rem 2rem;
    padding: 1rem 1.5rem;
    .card{
      .tool-bar{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.0625rem;
      }
    }
    .com-content{
      max-height: calc(100vh - 17rem);
      box-sizing: border-box;
      overflow-y: auto;
      // Webkit 滚动条样式
      &::-webkit-scrollbar {
        width: 0.5rem; // 滚动条宽度
        height: 0.5rem;
      }

      &::-webkit-scrollbar-thumb {
        //background-color: #e7fafc;
        border-radius: 0.25rem;
        border: 0.125rem solid transparent;
        background-clip: padding-box;
      }

      &::-webkit-scrollbar-track {
        //background-color: #e7fafc;
        border-radius: 0.25rem;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #e7fafc;
      }

      // Firefox 滚动条样式
      scrollbar-width: thin; // 窄滚动条
      //scrollbar-color:  #e7fafc #c1c1c1;

    }


    //.company {
    //  display: flex;
    //  align-items: center;
    //  justify-content: space-between;
    //
    //  .company-name {
    //    font-family: PingFang SC, PingFang SC;
    //    font-weight: 600;
    //    font-size: 1.5rem;
    //    color: #222222;
    //    line-height: 1.75rem;
    //    text-align: left;
    //    font-style: normal;
    //    text-transform: none;
    //    display: flex;
    //    align-items: center;
    //
    //    &::before {
    //      content: '';
    //      display: inline-block;
    //      width: 2rem;
    //      height: 2rem;
    //      background-image: url('~@/views/projects/assets/images/building.png');
    //      background-size: 100% 100%;
    //      background-repeat: no-repeat;
    //      margin-right: 0.6875rem;
    //    }
    //  }
    //}


  }
}
</style>

