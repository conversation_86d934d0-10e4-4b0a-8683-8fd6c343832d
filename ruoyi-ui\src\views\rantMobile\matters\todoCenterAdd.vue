<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container padding-top-10" v-loading="loading">
      <!-- 基本信息卡片 -->
      <div class="form-card margin-top-0">
<!--        <div class="form-item">-->
<!--          <div class="form-label">来源</div>-->
<!--          <div class="form-value">{{ form.ranterName || '&#45;&#45;' }}</div>-->
<!--        </div>-->

        <div class="form-item">
          <div class="form-label">类型</div>
          <div class="form-value">{{ mattersTypeText || '--' }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">分类</div>
          <div class="form-value clickable">{{ form.rantClassify || '--' }}</div>
        </div>
      </div>

      <!-- 内容卡片 -->
      <div class="form-card">
        <div class="form-section-title">内容</div>
        <div class="form-item full-width">
          <div class="form-value content-text textarea-placeholder">{{ form.rantContent || '请填写' }}</div>
    </div>
        </div>

      <!-- 措施卡片 -->
      <div class="form-card">
        <div class="form-section-title"><span class="required-mark">*</span>措施</div>
        <div class="form-item full-width">
          <div class="form-value">
            <textarea
              class="custom-textarea"
              v-model="form.solution"
              rows="5"
              placeholder="请填写"
            ></textarea>
        </div>
        </div>
      </div>

      <!-- 责任信息卡片 -->
      <div class="form-card">
        <div class="form-item">
          <div class="form-label">责任人</div>
          <div class="form-value clickable">{{ form.responsiblePersonName || '--' }}</div>
      </div>

        <div class="form-item">
          <div class="form-label">责任部门</div>
          <div class="form-value clickable">{{ form.deptName || '--' }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">责任部门负责人</div>
          <div class="form-value clickable">{{ form.respDeptResponsiblerName || '--' }}</div>
        </div>

        <div class="form-item">
          <div class="form-label">分管领导</div>
          <div class="form-value clickable">{{ form.respDeptLeaderName || '--' }}</div>
        </div>

        <div class="form-item">
          <div class="form-label"><span class="required-mark">*</span>计划完成时间</div>
          <div class="form-value clickable" @click="!isRead && showDatePicker('planTime')" :class="{'custom-disabled-mobile': isRead}">
            <van-icon name="clock-o" /> {{ form.planTime || "请选择" }}
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">创建人</div>
          <div class="form-value clickable">{{ form.createByName || "--" }}</div>
        </div>
      </div>

      <!-- 权限设置卡片 -->
      <div class="form-card">
        <div class="form-item">
          <div class="form-label">是否私密</div>
          <div class="form-value">
            <div class="radio-group">
              <div class="radio-item"
                   :class="{'active': form.isPrivate === 1}"
                  >
                <div class="radio-circle" :class="{'selected': form.isPrivate === 1}"></div>
                <span>是</span>
              </div>
              <div class="radio-item"
                   :class="{'active': form.isPrivate === 0 || form.isPrivate === null}"
                   >
                <div class="radio-circle" :class="{'selected': form.isPrivate === 0 || form.isPrivate === null}"></div>
                <span>否</span>
              </div>
            </div>
          </div>
        </div>

        <template v-if="form.isPrivate === 1">
          <div class="form-item">
            <div class="form-label">可见范围</div>
            <div class="form-value clickable">{{ form.visibleScope == null ? "所有人" : form.visibleScope }}</div>

          </div>

          <!-- <div class="visible-departments" v-if="visibleDeptsText">
            <div class="dept-tag">{{ visibleDeptsText }}</div>
          </div> -->
        </template>
      </div>

      <!-- 日期选择弹窗 -->
      <van-popup v-model="showDatePickerDialog" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择日期"
          @confirm="confirmDate"
          @cancel="showDatePickerDialog = false"
        />
      </van-popup>

      <div class="bottom-buttons" v-if="!isRead">
        <van-button class="custom-btn-submit" @click="handleConfirm" :loading="loading">确 认</van-button>
        <van-button class="custom-btn-save" @click="cancel">取 消</van-button>
      </div>
    </div>

<!--    <select-user v-show="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData" />-->
    <select-user
      :show.sync="selectUserShow"
      ref="userRef"
      :roleId="roleName"
      :selectMultiple="isMultiple"
      @select="selectUserData"
      @close="onUserSelectorClose"
       />
  </div>
</template>

<script>
// http://localhost/wechatE/mobile/rant/todoCenterAddMatter?todoNoticeId=52
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}*/

import { close } from '@/utils';
import {
  rantDaiBanDetailForTodoCenter,
  talkFeedbackConfirm
} from "@/api/rantMobile/matters";
import SelectUser from "@/views/rantMobile/components/SelectUserMobile.vue";
import { rantStatusOption, isPrivateOption } from "@/constant";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listRespdet, listDept } from "@/api/rantMobile/respdet";

// 引入Vant组件
import {
  Form,
  Button,
  Popup,
  DatetimePicker,
  Toast,
  Icon,
} from 'vant';
import mixin from '../mixins'
export default {
  name: "Matters",
  mixins: [mixin],
  components: {
    SelectUser,
    StatusTag,
    Treeselect,
    [Form.name]: Form,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Icon.name]: Icon
  },
  // dicts: ["rant_classify", "rant_matters_type"],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      mattersType: [],
      form: {
        visibleDepts: []
      },
      formTitle: "督办事项填写",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranter: null,
        rantClassify: null,
        solution: null,
        status: null,
        mattersType: null,
        createBy: null,
        params: {
          beginCreateTime: null,
          endCreateTime: null,
        }
      },
      // 表单校验
      rules: {
        rantClassify: [
          { required: true, message: "吐槽分类不能为空", trigger: "blur" }
        ],
        rantContent: [
          { required: true, message: "吐槽内容不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "责任部门不能为空", trigger: "blur" }
        ],
        responsiblePerson: [
          { required: true, message: "责任人不能为空", trigger: "blur" }
        ],
        isPrivate: [
          { required: true, message: "是否私密不能为空", trigger: "blur" }
        ],
        planTime: [
          { required: true, message: "计划完成时间不能为空", trigger: "blur" }
        ],
        solution: [
          { required: true, message: "措施不能为空", trigger: "blur" }
        ],
      },
      rantStatusOption,
      isPrivateOption,
      title: "",
      selectUser: false,
      selectDeptPerson: '',
      // 部门树选项
      deptOptions: [],
      deptVisibleOptions: [],
      deptOptionsEdit: undefined,
      tempRow: {},
      // 角色选择
      selectUserShow: false,
      roleName: "",
      isMultiple: false,
      batchSelectIds: [],
      // 用户导入参数
     /*  upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/rantMatters/import"
      }, */
      openEditResponsiblePerson: false,
      openEditResponsiblePersonLoading: false,
      titleResponsiblePerson: "批量修改",
      batchEditForm: {
        selectedIds: [],
        isEditRanter: false,
        ranterEdit: "",
        ranterNameEdit: "",
        isEditMattersType: false,
        mattersTypeEdit: [],
        isEditRantClassify: false,
        rantClassifyEdit: "",
        isEditResponsiblePerson: false,
        responsiblePersonEdit: "",
        responsiblePersonNameEdit: "",
        isEditDeptId: false,
        deptIdEdit: null,
        deptNameEdit: null,
        respDeptResponsiblerEdit: "",
        respDeptResponsiblerNameEdit: "",
        respDeptLeaderEdit: "",
        respDeptLeaderNameEdit: "",
      },
      deptIdStr: "",
      deptNameStr: "",
      isHavaSave: false,

      // 日期选择相关
      showDatePickerDialog: false,
      currentDate: new Date(),
      dateFieldType: '',
      isRead: false
    };
  },
  computed: {
    mattersTypeText() {
      if (!this.mattersType || this.mattersType.length === 0) return '';

      // 根据数据字典将类型ID转为名称
      const typeLabels = this.mattersType.filter(typeId => typeId !== '').map(typeId => {
        const typeOption = this.mattersTypeOptionsLabel.find(item => item.value === typeId);
        return typeOption ? typeOption.label : '';
      });

      return typeLabels.join(',');
    },
    isPrivateText() {
      return this.form.isPrivate === 1 ? '是' : '否';
    },
    visibleDeptsText() {
      if (!this.form.visibleDepts || this.form.visibleDepts.length === 0) return '';
      return Array.isArray(this.form.visibleDeptNames) ? this.form.visibleDeptNames.join(', ') : '';
    }
  },
  created() {
    const todoNoticeId = this.$route.query.todoNoticeId
    if (todoNoticeId) {
      this.handleMatterById(todoNoticeId);
    }
    /** 获取部门下拉树列表 */
    this.getDeptTreeSelect();
    this.fetchMattersType();
  },
  mounted() {
  },
  methods: {
    // 用户选择器关闭
    onUserSelectorClose() {
      this.showUserSelector = false;
    },
    handleMatterById(todoNoticeId) {
      this.loading = true;
      rantDaiBanDetailForTodoCenter(todoNoticeId).then(response => {
        this.form = response.data;
        this.isRead = response.data.isRead;
        this.mattersType = this.form.mattersType ? this.form.mattersType.split(',') : [];
        this.loading = false;
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        rantClassify: null,
        rantContent: null,
        solution: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        deptId: null,
        deptName: null,
        status: "0",
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        isPrivate: 0,
        visibleDepts: [],
        visibleUsers: []
      };
      this.resetForm("form");
    },

    /** 选择角色 */
    selectUserFun(roleId) {
      this.roleName = roleId;
      if (roleId === 'visibleUser') {
        this.isMultiple = true;
      } else {
        this.isMultiple = false;
      }
      this.selectUserShow = true;
    },

    selectUserData(data) {
      this.selectUserShow = false;
      if (this.isMultiple) {
        // 多选
        switch (this.roleName) {
          case 'visibleUser':
            // 处理可见用户
            if (Array.isArray(data)) {
              let names = [];
              let ids = [];
              data.forEach(item => {
                if (item) {
                  names.push(item.nickName);
                  ids.push(item.userId);
                }
              });
              this.form.visibleUsers = ids;
              this.visibleUserNames = names.join(',');
            }
            break;
          default:
            break;
        }
      } else {
        // 单选
        switch (this.roleName) {
          case 'responsibilityer':
            if (data?.nickName) {
              this.form.responsiblePersonName = data.nickName;
              this.form.responsiblePerson = data.userId;
            }
            break;
          case 'respDeptResponsiblerEdit':
            if (data?.nickName) {
              this.batchEditForm.respDeptResponsiblerNameEdit = data.nickName;
              this.batchEditForm.respDeptResponsiblerEdit = data.userId;
            }
            break;
          case 'respDeptLeaderEdit':
            if (data?.nickName) {
              this.batchEditForm.respDeptLeaderNameEdit = data.nickName;
              this.batchEditForm.respDeptLeaderEdit = data.userId;
            }
            break;
          case 'responsiblePersonEdit':
            if (data?.nickName) {
              this.batchEditForm.responsiblePersonNameEdit = data.nickName;
              this.batchEditForm.responsiblePersonEdit = data.userId;
            }
            break;
          case 'ranterEdit':
            if (data?.nickName) {
              this.batchEditForm.ranterNameEdit = data.nickName;
              this.batchEditForm.ranterEdit = data.userId;
            }
                    break;
          default:
                      break;
        }
      }
    },

    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },

    /** 转换部门数据结构 */
    normalizerVisible(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },

    /** 查询部门下拉树结构 */
    getDeptTreeSelect() {
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
        this.deptVisibleOptions = this.handleTree(response.data, "deptId", "parentId");
        this.deptOptionsEdit = this.handleTree(response.data, "deptId", "parentId");
      });
    },

    /** 选择部门 */
    selectDept(deptId) {
      if (deptId) {
        listRespdet({ deptId }).then(response => {
          if (response.data && response.data.length) {
            const respdet = response.data[0];
            this.form.respDeptResponsiblerName = respdet.respDeptResponsiblerName;
            this.form.respDeptResponsibler = respdet.respDeptResponsibler;
            this.form.respDeptLeaderName = respdet.respDeptLeaderName;
            this.form.respDeptLeader = respdet.respDeptLeader;
          }
        });
      }
    },

    /** 选择部门 */
    selectDeptEdit(deptId) {
      if (deptId) {
        listRespdet({ deptId }).then(response => {
          if (response.data && response.data.length) {
            const respdet = response.data[0];
            this.batchEditForm.respDeptResponsiblerNameEdit = respdet.respDeptResponsiblerName;
            this.batchEditForm.respDeptResponsiblerEdit = respdet.respDeptResponsibler;
            this.batchEditForm.respDeptLeaderNameEdit = respdet.respDeptLeaderName;
            this.batchEditForm.respDeptLeaderEdit = respdet.respDeptLeader;
          }
        });
      }
    },

    // 取消按钮
    cancel() {
      close();
    },

    // 确认按钮
    handleConfirm() {
      if (!this.form.solution) {
        Toast('请输入措施');
        return;
      }

      if (!this.form.planTime) {
        Toast('请选择计划完成时间');
        return;
      }

      this.loading = true;
      talkFeedbackConfirm({
        id: this.form.id,
        planTime: this.form.planTime,
        solution: this.form.solution,
      }).then(response => {
        Toast.success('确认成功');
        setTimeout(() => {
          close();
        }, 1500);
      }).catch(() => {
        this.loading = false;
        });
    },

    // 显示日期选择器
    showDatePicker(type) {
      this.dateFieldType = type;
      this.currentDate = this.form[type] ? new Date(this.form[type]) : new Date();
      this.showDatePickerDialog = true;
    },

    // 确认日期选择
    confirmDate(date) {
      // 格式化日期
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      // 根据当前操作的字段类型更新相应的值
      this.form[this.dateFieldType] = formattedDate;

      // 关闭日期选择器
      this.showDatePickerDialog = false;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../css/common.scss';

.app-container-feedback {
  padding: 0;
  background-color: #CCDDFF;
}

.rant-container {
  padding-bottom: 70px;
}

.page-header {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #FFFFFF;
  padding: 0 16px;
  position: relative;

  .back-icon {
    font-size: 18px;
    color: #333;
    padding: 8px;
    margin-left: -8px;
  }

  .page-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 500;
    color: #333;
  }
}

.form-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  margin: 12px 12px 0;
  padding: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  &:last-of-type {
    margin-bottom: 12px;
  }
}

.form-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 12px 16px 0;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #EBEEF5;

  &:last-child {
    border-bottom: none;
  }

  .form-label {
    width: 100px;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
  }

  .form-value {
    flex: 1;
    font-size: 14px;
    //color: #333333;
    line-height: 20px;
    text-align: right;

    .el-icon-arrow-right {
      margin-left: 4px;
      color: #C0C4CC;
    }
  }

  &.full-width {
    display: block;

    .form-value {
      text-align: left;
      margin-top: 8px;
    }
  }
}

.clickable {
  color: #3673FF;
}

.textarea-placeholder {
  color: #999;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.custom-textarea {
  width: 100%;
  border: none;
  border-radius: 0;
  padding: 0;
  min-height: 60px;
  font-size: 14px;
  color: #333333;
  outline: none;
  resize: none;
  background-color: transparent;

  &::placeholder {
    color: #999;
  }
}

.radio-group {
  display: flex;
  justify-content: flex-end;

  .radio-item {
    display: flex;
    align-items: center;
    margin-left: 24px;

    .radio-circle {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      border: 1px solid #DCDFE6;
      margin-right: 6px;
  display: flex;
      align-items: center;
  justify-content: center;

      &.selected {
        border-color: #3673FF;

        &:after {
          content: "";
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #3673FF;
        }
      }
    }
  }
}

.visible-departments {
  padding: 0 16px 12px;

  .dept-tag {
    display: inline-block;
    background-color: #EBEEF5;
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 8px;
    margin-bottom: 8px;
    font-size: 12px;
  }
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  text-align: center;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;

  .custom-btn-submit {
    width: 160px;
    height: 40px;
    background: #3673FF;
    color: #FFFFFF;
    margin-right: 16px;
    border-radius: 20px;
  }

  .custom-btn-save {
    width: 160px;
    height: 40px;
    background: #FFFFFF;
    color: #3673FF;
    border: 1px solid #3673FF;
    border-radius: 20px;
  }
}
</style>
