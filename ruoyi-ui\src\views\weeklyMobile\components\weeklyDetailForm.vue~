<template>
  <div>
    <section class="head-block">
      <!-- 标题部分 -->
      <div class="weekly-title">
        {{ currentYear }}年第{{ weekNumber }}周（{{ dateRange }}）个人总结
      </div>
      <!-- 个人信息部分 -->
      <van-cell-group inset class="form-section" style="margin: 0px">
        <!-- 第一行：员工姓名和员工所属部门 -->
        <van-row>
          <van-col span="24">
            <van-field
              label="员工姓名"
              v-model="weeklyForm.nickName"
              readonly
              label-width="70px"
            />
          </van-col>
        </van-row>
        <van-row>
          <van-col span="24" class="custom-info-row">
            <!-- v-model="weeklyForm.deptName" -->
            <div class="label-text">所属部门</div>
            <div class="label-content">{{ weeklyForm.deptName }}</div>
            <!-- <van-field
              label="所属部门"
              v-model="weeklyForm.deptName"
              readonly
              label-width="70px"
              type="textarea"
              autosize
              style="min-height: 44px"
            /> -->
          </van-col>
        </van-row>

        <!-- 第二行：员工职位和其他字段 -->
        <van-row>
          <van-col span="24">
            <van-field
              readonly
              label="员工职位"
              v-model="weeklyForm.postName"
              placeholder="请输入员工职位"
              :rules="[{ required: true, message: '请输入员工职位' }]"
              label-width="70px"
            />
          </van-col>
        </van-row>
      </van-cell-group>
    </section>
    <!-- 本周工作总结 -->
    <section class="section-content">
      <div>
        <div class="section-title">本周工作总结</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workSummaryList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">
              {{ index + 1 }}
            </div>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workSummaryList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workSummaryList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                    weeklyForm.workSummaryList[index].completeStatus
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>
          <div class="card-divider"></div>
        </div>
      </div>
    </section>
    <!-- 下周工作计划 -->
    <section class="section-content">
      <div>
        <div class="section-title">下周工作计划</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workPlanList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">
              {{ index + 1 }}
            </div>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workPlanList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workPlanList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                    weeklyForm.workPlanList[index].completeStatus
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showPlanStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>
        </div>
      </div>
    </section>
    <section class="section-content">
      <div>
        <div class="section-title">每周反思</div>
      </div>
      <div class="card-content">
        <van-field
          v-model="weeklyForm.reflectInfo.progressScheme"
          type="textarea"
          placeholder="请输入每周反思"
          :rules="[{ required: true, message: '请输入每周反思' }]"
          class="card-field"
          autosize
        />
      </div>
    </section>
    <section class="section-content">
      <!-- 需部门支持事项 -->
      <div>
        <div class="section-title">需部门支持事项</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.workSupportList"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">
              {{ index + 1 }}
            </div>
          </div>

          <!-- 工作事项 -->
          <div class="card-item">
            <div class="card-label">工作事项</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workSupportList[index].workMatter"
                type="textarea"
                placeholder="请输入工作事项"
                :rules="[{ required: true, message: '请输入工作板块' }]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成进度/解决方案 -->
          <div class="card-item">
            <div class="card-label">完成进度/解决方案</div>
            <div class="card-content">
              <van-field
                readonly
                v-model="weeklyForm.workSupportList[index].progressScheme"
                type="textarea"
                placeholder="请填写完成进度/解决方案"
                :rules="[
                  { required: true, message: '请输入工作事项/完成进度' },
                ]"
                class="card-field"
                autosize
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>

          <!-- 完成及预警情况 -->
          <div class="card-item">
            <div class="card-label">完成及预警情况</div>
            <div class="card-content">
              <van-field
                readonly
                clickable
                :value="
                    weeklyForm.workSupportList[index].completeStatus
                "
                placeholder="请选择完成及预警情况"
                right-icon="arrow"
                @click="showSupportStatusPicker(index)"
                class="card-field"
              />
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="card-divider"></div>
        </div>
      </div>
    </section>

    <section class="section-content">
      <div>
        <div class="section-title">审阅详情</div>
      </div>

      <!-- 替换 el-table 为 vant 卡片布局 -->
      <div class="weekly-card-container">
        <div
          v-for="(item, index) in weeklyForm.reportReviewDetails"
          :key="index"
          class="weekly-card"
        >
          <!-- 序号和删除按钮 -->
          <div class="card-header">
            <div class="card-number">
              {{ index + 1 }}
            </div>
          </div>

          <!-- 工作事项 -->
          <div class="card-item approve-detail-item">
            <div class="card-label approve-detail-label">审阅人</div>
            <div class="card-content approve-detail-content">
              {{ item.approveUserName }}
              <!-- <van-field
                readonly
                v-model="item.approveUserName"
                type="textarea"
                class="card-field"
                autosize
              /> -->
            </div>
          </div>
          <div class="card-divider"></div>

          <div class="card-item approve-detail-item">
            <div class="card-label approve-detail-label">审阅状态</div>
            <div class="card-content approve-detail-content">
              <div class="weekly-status">
                <van-tag :type="getStatusType(item.approveStatus)">{{
                  getStatusText(item.approveStatus)
                }}</van-tag>
              </div>
            </div>
          </div>
          <div class="card-divider"></div>
          <div class="card-item approve-detail-item">
            <div class="card-label approve-detail-label">审阅日期</div>
            <div class="card-content approve-detail-content">
              {{ item.approveTime }}
              <!-- <van-field
                readonly
                v-model="item.approveTime"
                type="textarea"
                class="card-field"
                autosize
              /> -->
            </div>
          </div>
          <div class="card-divider"></div>
          <div class="card-item approve-detail-item">
            <div class="card-label approve-detail-label">审阅意见</div>
            <div class="card-content approve-detail-content">
              {{ item.approveDesc || "--" }}
              <!-- <van-field
                readonly
                v-model="item.approveDesc"
                type="textarea"
                class="card-field"
                autosize
              /> -->
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import Sortable from "sortablejs";
import {
  Cell,
  CellGroup,
  Col,
  Row,
  Field,
  Form,
  FormItem,
  Input,
  Button,
  Popup,
  Picker,
  Icon,
  Tag,
} from "vant";

export default {
  name: "WeeklyForm",
  // dicts: ["work_complete_status"],
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Col.name]: Col,
    [Row.name]: Row,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
  },
  props: {
    action: {
      type: String,
      default: "add", // add: 新增, detail: 详情 update: 编辑
    },
  },
  data() {
    return {
      roleName: "",
      labelPosition: "right",
      weeklyForm: {
        id: null,
        year: null, // 年份不能为空
        week: null, // 第几周不能为空
        userId: "", // 员工编码不能为空
        nickName: "", // 员工名称不能为空
        deptId: null, // 部门ID不能为空
        deptName: "", // 部门名称不能为空
        postName: "", // 岗位名称
        startDate: "", // 周开始日期不能为空
        endDate: "", // 周结束日期不能为空
        approveUserCode: "", // 审阅人编码不能为空
        approveUserName: "", // 审阅人名称不能为空
        circulateUserCodes: "", // 传阅人（多个用逗号隔开）
        userCode: "", // 员工编码不能为空
        // 本周工作总结
        workSummaryList: [
          /*  {
             workMatter: "", // 工作板块
             progressScheme: "", // 工作事项/完成进度
             completeStatus: "", // 完成及预警情况
             type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
             serialNumber: 1, // 序号
           }, */
        ],
        // 下周工作计划
        workPlanList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 2,
            serialNumber: 1,
          }, */
        ],
        // 需部门支持事项
        workSupportList: [
          /* {
            workMatter: "",
            progressScheme: "",
            completeStatus: "",
            type: 3,
            serialNumber: 1,
          }, */
        ],
        reportReviewDetails: [],
        approveDesc: "",
        selectUserIds: [], // 弹窗中选择的多个用户id数组
        reflectInfo: { type: 4, progressScheme: "" },
      },
      rules: {
        selectUserIds: [
          { required: true, message: "请选择审阅人", trigger: "change" },
        ],
      },
      approveUserName: "",
      // selectUserIds: [], // 弹窗中选择的多个用户id数组
      showStatusPickerVisible: false,
      showPlanStatusPickerVisible: false,
      showSupportStatusPickerVisible: false,
      currentEditIndex: -1,
      currentEditType: "", // 'summary', 'plan', 'support'
      statusColumns: [],
      showUserSelector: false,
      selectedUsers: [],
      approverNames: "",
      xxxx: "weeklyForm.deptNameweeklyForm.deptNameweeklyForm.deptNameweeklyForm.deptNameweeklyForm.deptNameweeklyForm.deptName",
    };
  },
  computed: {
    currentYear() {
      return this.weeklyForm.year;
    },
    weekNumber() {
      // 这里需要实现获取当前周数的逻辑
      return this.weeklyForm.week;
    },
    dateRange() {
      return this.formatDateRange(
        this.weeklyForm.startDate,
        this.weeklyForm.endDate
      );
    },
    department() {
      return this.weeklyForm.deptName;
    },
  },

  methods: {
    getCompleteStatusText(value) {
      if (!value && value !== 0) return "请选择完成及预警情况";
      const option =
        this.dict &&
        this.dict.type &&
        this.dict.type.work_complete_status &&
        this.dict.type.work_complete_status.find(
          (item) => item.value === value
        );
      return option ? option.label : "请选择完成及预警情况";
    },
    getStatusText(status) {
      // 审阅状态（0-草稿 1-待阅 2-已阅 3-已撤回 4-已驳回 5-部分已阅）
      const statusMap = {
        0: "草稿",
        1: "待阅",
        2: "已阅",
        3: "已撤回",
        4: "已驳回",
        5: "部分已阅",
      };
      return statusMap[status] || "未知";
    },
    getStatusType(status) {
      switch (status) {
        case "0":
          return "danger";
        case "1":
          return "primary";
        case "2":
          return "success";
        case "3":
          return "danger";
        case "4":
          return "danger";
        case "5":
          return "warning";
        default:
          return "success";
      }
    },
    formatDateRange(startDate, endDate) {
      return `${new Date(startDate).getMonth() + 1}月${new Date(
        startDate
      ).getDate()}日-${new Date(endDate).getMonth() + 1}月${new Date(
        endDate
      ).getDate()}日`;
    },
    getFormData() {
      return this.weeklyForm;
    },
    // 编辑模式下更新表单数据
    updateFormData(data) {
      // this.approveUserName = data.approveUserName
      this.weeklyForm = {
        ...this.weeklyForm,
        ...data,
        workSummaryList: data.workSummaryList || [], // 无数据时兜底，解决table报错
        workPlanList: data.workPlanList || [],
        workSupportList: data.workSupportList || [],
        reportReviewDetails: data.reportReviewDetails.map((item) => ({
          userName: item.approveUserCode,
          nickName: item.approveUserName,
          approveStatus: item.approveStatus,
          approveTime: item.approveTime,
          approveDesc: item.approveDesc,
          approveUserName: item.approveUserName,
        })),
        reflectInfo: data.reflectInfo || { type: 4, progressScheme: "" },
      };

      this.weeklyForm.selectUserIds = this.weeklyForm.reportReviewDetails.map(
        (item) => item.userName
      );
    },
    setFormData(data) {
      if (data) {
        this.weeklyForm = {
          ...this.weeklyForm,
          year: data.year,
          week: data.week,
          userId: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          userCode: data.userCode,
          workSummaryList: data.workSummaryList || [
            {
              workMatter: "", // 工作板块
              progressScheme: "", // 工作事项/完成进度
              completeStatus: "", // 完成及预警情况
              type: 1, // 1=-本周工作总结、2-下周工作计划、3-需部门支持事项
              serialNumber: 1, // 序号
            },
          ],
          reportReviewDetails: (data?.reportReviewDetails || []).map(
            (item) => ({
              userName: item.approveUserCode,
              nickName: item.approveUserName,
            })
          ),
        };
        this.weeklyForm.selectUserIds = (
          this.weeklyForm?.reportReviewDetails || []
        ).map((item) => item.userName);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.weekly-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
}
.section-title {
  margin: 0 0 18px 10px;
  font-weight: 600;
  text-align: left;
  display: inline-flex;
  align-items: center;
  position: relative;
  z-index: 2; // 添加 z-index 确保文字在上层

  &::before {
    content: " ";
    display: inline-block;
    height: 10px;
    background: #d8e4fb;
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    z-index: -1; // 将伪元素放到文字下方
  }
}
.head-block {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffffff;
  padding: 16px;
  margin-bottom: 12px;
}
.custom-input {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #dddddd;
}
.custom-label,
.custom-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.info-row {
  :deep(.el-form-item__label) {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  display: flex;
  align-items: center;
}
.weekly-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}

.submit-section {
  margin-top: 20px;
  text-align: center;
}

/* 添加深度选择器来修改 element-plus 表头样式 */
/* :deep(.el-table th) {
  background-color: #f0f2f5 !important;
} */

.weekly-form {
  /* 可以根据实际需要调整减去的高度 */
  /*  max-height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 20px; */
}

.approve-row {
  margin-top: 20px;
  text-align: center;
}

/* 添加以下样式来修复 label 和 input 换行的问题 */
.weekly-form :deep(.el-form-item) {
  display: flex;
  margin-bottom: 0px;
}

.weekly-form :deep(.el-form-item__label) {
  float: none;
  display: inline-flex;
  align-items: center;
}

.weekly-form :deep(.el-form-item__content) {
  flex: 1;
  margin-left: 0 !important;
}

.section-content {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffffff;
  padding: 16px;
  margin-bottom: 12px;

  /*  .section-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
  } */
}

:deep(.el-table__header-wrapper .el-table__cell) {
  background: rgba(54, 115, 255, 0.05);
  border-radius: 0px 0px 0px 0px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* Add these new styles */
/* :deep(.el-textarea__inner) {
  min-height: 80px !important;
}

:deep(.el-textarea__inner) {
  max-height: 150px !important;
} */
.weekly-form :deep(th.el-table__cell) {
  padding: 0;
}

.info-row {
  /* margin-bottom: 10px; */
}

.mb-20 {
  margin-bottom: 20px !important;
}

.font-size-15 {
  font-size: 15px;
}

.sortable-table .el-table__row {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.8;
  background: #f0f9eb;
}

.el-table__body tr {
  cursor: move;
}
.align-center {
  display: flex;
  align-items: center;
}

.weekly-card-container {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.weekly-card {
  padding: 16px;
  background-color: #fff;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.delete-btn {
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
}

.card-item {
  margin-bottom: 12px;
}

.card-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.card-content {
  color: #666;
}

.card-field {
  padding: 0;

  :deep(.van-field__control) {
    color: #666;
    font-size: 14px;
  }
}

.card-divider {
  height: 1px;
  background-color: #ebedf0;
  margin: 12px 0;
}

.add-button {
  padding: 16px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 500;
  font-size: 14px;
  color: #3673ff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 16px;
}
.delete-btn {
  color: red;
  width: 16px;
  height: 16px;
}

/* 添加自定义类名 */
.wrap-text-field {
  :deep(.van-field__control) {
    word-break: break-word;
    white-space: normal;
  }
}
.approve-detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.approve-detail-label {
  margin-bottom: 0px;
}
.approve-detail-content {
  flex: 1;
  text-align: right;
}
.custom-info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // gap: 10px;
  padding: 10px 16px;
  .label-text {
    font-size: 13px;
    color: #646566;
    margin-bottom: 8px;
    word-break: keep-all;
    width: 70px;
    margin-right: 12px;
  }
  .label-content {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    word-break: break-all;
    flex: 1;
    text-align: left;
  }
}


</style>
