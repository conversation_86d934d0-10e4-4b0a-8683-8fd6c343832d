<template>
  <!-- 授权用户 -->
  <el-dialog
    title="选择用户"
    :visible.sync="visible"
    width="900px"
    top="5vh"
    append-to-body
  >
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        @row-click="clickRow"
        ref="table"
        :data="userList"
        height="300px"
        stripe
        v-loading="loading"
        align="center"
        header-align="center"
      >
        <el-table-column width="85" label="选择" align="center">
          <template slot-scope="scope">
            <el-checkbox
              v-if="selectMultiple"
              v-model="userIds"
              :label="scope.row.userId"
              @change="selectRow(scope.row)"
            />
            <el-radio v-else :label="scope.row.userName" v-model="selectUserName">
              {{ "" }}
            </el-radio>
          </template>
        </el-table-column>
      <!--   <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        /> -->
        <el-table-column
          label="用户名称"
          prop="userName"
          :show-overflow-tooltip="true"
           align="center"
        />
        <el-table-column
          label="用户昵称"
          prop="nickName"
          :show-overflow-tooltip="true"
           align="center"
        />
        <el-table-column
          label="部门"
          prop="dept.deptName"
          :show-overflow-tooltip="true"
           align="center"
        />
        <el-table-column
          label="部门全路径"
          prop="dept.namePath"
          :show-overflow-tooltip="true"
           align="center"
        />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listUser } from "@/api/system/user"

export default {
  props: {
    // 角色编号
    roleId: {
      type: [Number, String]
    },
    selectMultiple: { // 是否多选
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        userName: undefined,
        phonenumber: undefined
      },
      loading: false,
      selectUserName: '',
      selectData: null,
      selectUserArr: [], // 选中用户数组
      selectMultipleUserIds: [], // 多选用户id
    }
  },

  methods: {
    selectRow (row) {
      /* if (this.selectMultiple) {
        const index = this.selectMultipleUserIds.indexOf(row.userId);
        console.log('index--------------', index)
        if (index > -1) {
          // 取消选择时，同步移除所有相关数组中的数据
          this.userIds.splice(index, 1);
          this.selectMultipleUserIds.splice(index, 1);
          const userArrIndex = this.selectUserArr.findIndex(item => item.userId === row.userId);
          if (userArrIndex > -1) {
            this.selectUserArr.splice(userArrIndex, 1);
          }
          console.log('this.selectUserArr--------------', this.selectUserArr)
          console.log('this.userIds--------------', this.userIds)
        } else {
          // 选择时，添加到所有相关数组
          this.selectMultipleUserIds.push(row.userId);
          this.selectUserArr.push(row);
        }
      } */

      if (this.selectMultiple) {
        const userArrIndex = this.selectUserArr.findIndex(item => item.userId === row.userId);
        if (userArrIndex > -1) {
          // 取消选择时，从所有相关数组中移除
          this.selectUserArr.splice(userArrIndex, 1);
          const userIdIndex = this.userIds.indexOf(row.userId);
          if (userIdIndex > -1) {
            this.userIds.splice(userIdIndex, 1);
          }
          const multipleIdIndex = this.selectMultipleUserIds.indexOf(row.userId);
          if (multipleIdIndex > -1) {
            this.selectMultipleUserIds.splice(multipleIdIndex, 1);
          }
        } else {
          // 选择时，添加到所有相关数组
          this.selectUserArr.push(row);
          this.userIds.push(row.userId);
          this.selectMultipleUserIds.push(row.userId);
        }
      }
    },
    clearSelect () {
      this.selectUserName = ''
      this.selectData = null
    },
    // 显示弹框
    show () {
      this.selectUserName = this.roleId
      this.queryParams.userName= undefined;
      this.queryParams.phonenumber= undefined;
      this.userList=[];
      //this.userIds=[];
      this.getList()
      this.visible = true
    },
    clickRow (row) {
      this.selectUserName = row.userName
      this.selectData = row
    },
    // 查询表数据
    getList () {
      this.loading = true
      listUser(this.queryParams).then(res => {
        this.loading = false
        this.userList = res.rows
        this.selectData = this.userList.filter(item => {
         return  (item.userName == this.selectUserName||item.userId==this.selectUserName)
        })[0]
        this.total = res.total
        //反赋值
        this.selectUserName=this.selectData?this.selectData.userName:''
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 选择授权用户操作 */
    handleSelectUser () {
      if(this.selectMultiple){
        this.$emit('feedbackEmit', this.selectUserArr)
      }else {
        this.$emit('feedbackEmit', this.selectData)
      }
      this.visible = false
    }
  }
};
</script>
