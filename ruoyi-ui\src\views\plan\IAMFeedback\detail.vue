<template>
  <div class="app-container">
      <div class="feedback-container" v-loading="loading">
        <div class="feedback-detail">
          <div class="el-icon-info feedback-form-title icon-primary">节点基本信息</div>
          <el-form class="feedback-detail">
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目名称：" prop="projectName">
                  {{feedbackDetail.projectName || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目分期：" prop="stageId">
                  <dict-tag :options="dict.type.stages" :value="feedbackDetail.stageId"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="节点顺序：" prop="nodeIndex">
                  {{feedbackDetail.nodeIndex || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="节点名称：" prop="nodeName">
                  {{feedbackDetail.nodeName || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="计划开始时间：" prop="startTime">
                  {{feedbackDetail.startTime || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划完成时间：" prop="endTime">
                  {{feedbackDetail.endTime || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="完成时间：" prop="actualCompletionDate">
                  {{feedbackDetail.actualCompletionDate || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际偏差：" prop="deviationNum">
                  {{feedbackDetail.deviationNum || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="完成标准：" prop="completeCriteria">
                  {{feedbackDetail.completeCriteria || '--'}}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="节点描述：" prop="nodeDesc">
                  {{feedbackDetail.nodeDesc || '--'}}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="feedback-file">
          <div class="el-icon-files feedback-form-title icon-primary">成果文件</div>
          <el-table :data="nodeOutcomeDocumentList" style="width: 100%;">
            <el-table-column type="index" width="55" align="center"/>
            <el-table-column label="成果类型" align="center" prop="type" width="150">
              <template slot-scope="scope">
                <span>{{scope.row.type || '--'}}</span>
              </template>
            </el-table-column>
            <el-table-column label="成果文件" align="center" prop="annexUrl">
              <template slot-scope="scope">
                <section style="display: flex;flex-direction: column;">
                  <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" class="link" target="_blank">
                    {{file.name || '--'}}</a>
                </section>
              </template>
            </el-table-column>
            <el-table-column label="上传时间" align="center" prop="createTime">
              <template slot-scope="scope">
                <span>{{scope.row.createTime || '--'}}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注说明" align="center" prop="outcomeDocumentsDesc">
              <template slot-scope="scope">
                <span>{{scope.row.outcomeDocumentsDesc || '--'}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
  </div>
</template>

<script>
import {
  getFeedback
} from "@/api/plan/feedback";
import Template from "@/views/plan/template/index.vue";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
export default {
  name: "Feedback",
  components: {Template, StatusTag},
  dicts: ['stages'],
  data() {
    return {
      // 遮罩层
      loading: true,
      nodeOutcomeDocumentList: [], // 反馈成果文件
      feedbackDetail: {}, // 反馈详情
    };
  },
  computed: {
    deptList() {
      return this.$store.state.plan.deptList
    },
  },
  mounted() {
    const id = this.$route.query.id;
    const planId = this.$route.query.planId;
    this.handleGetFeedbackDetail(planId, id);
  },
  methods: {
    handleGetFeedbackDetail(planId, id) {
      this.loading = true;
      getFeedback(planId, id).then(response => {
        this.feedbackDetail = response.data;
        this.nodeOutcomeDocumentList = response.data.nodeOutcomeDocumentList;
        this.loading = false;
      });
    },
    handleClose() {
      this.$modal.confirm("确认是否关闭当前页面")
        .then(() => {
          this.$store.dispatch("tagsView/delView", this.$route);
        })
    },
  }
};
</script>
<style lang="scss" scoped>
.feedback-container {
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;
  .feedback-form-title {
    margin-bottom: 20px;
    background-color: #f8f8f9;
    padding: 10px;
    width: 100%;
  }
  .icon-primary {
    color: #409eff;
  }
  .cursor {
    cursor: pointer;
  }
  .display-none {
    display: none !important;
  }
  .file-wrapper {
    .el-icon-circle-close {
      margin-left: 10px;
    }
  }
  .link {
    color: #409eff;
  }
}

.transfer-item {
  display: flex;
  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}

</style>
