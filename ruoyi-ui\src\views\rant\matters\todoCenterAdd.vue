<template>
  <div class="app-container" v-loading="loading">
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" class="form-block">
      <div class="card-title">{{ formTitle }}</div>
      <el-row>
        <!--        <el-col :span="8">-->
        <!--          <el-form-item label="来源" prop="ranterName">-->
        <!--            <el-input v-model="form.ranterName" placeholder="请选择来源" clearable disabled>-->
        <!--            </el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="类型" prop="mattersType">
            <el-select class="w-100" v-model="mattersType" multiple disabled placeholder="请选择类型"
              @keyup.enter.native="handleQuery">
              <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分类" prop="rantClassify">
            <el-select v-model="form.rantClassify" placeholder="请选择吐槽分类" @keyup.enter.native="handleQuery" disabled
              class="w-100">
              <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                :value="dict.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="内容" prop="rantContent">
            <el-input v-model="form.rantContent" type="textarea" placeholder="请输入内容" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="措施" prop="solution">
            <el-input v-model="form.solution" type="textarea" placeholder="请输入" :autosize="{ minRows: 5, maxRows: 20 }"
              :disabled="isRead" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="责任人" prop="responsiblePersonName">
            <el-input v-model="form.responsiblePersonName" placeholder="请选择责任人" clearable
              @keyup.enter.native="handleQuery" @focus="selectUserFun('responsibilityer')" disabled>
              <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('responsibilityer')"
                style="cursor: pointer;"></i></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任部门" prop="deptId">
            <treeselect v-model="form.deptId" :options="deptOptions" :normalizer="normalizer" @input="selectDept"
              placeholder="选择责任部门" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门负责人" prop="respDeptResponsiblerName">
            <el-input disabled v-model="form.respDeptResponsiblerName" placeholder="" />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>

        <el-col :span="8">
          <el-form-item label="分管领导" prop="respDeptLeaderName">
            <el-input disabled v-model="form.respDeptLeaderName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划完成时间" prop="planTime">
            <el-date-picker class="w-100" clearable size="small" v-model="form.planTime" type="date"
              value-format="yyyy-MM-dd" placeholder="选择计划完成时间" :disabled="isRead">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否私密" prop="isPrivate">
            <el-select class="w-100" v-model="form.isPrivate" disabled placeholder="请选择是否私密">
              <el-option v-for="item in isPrivateOption" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-if="form.isPrivate == 1" :span="8">
          <el-form-item label="可见部门">
            <treeselect v-model="form.visibleDepts" :multiple="true" :options="deptVisibleOptions" disabled
              :normalizer="normalizerVisible" placeholder="选择可见部门" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.isPrivate == 1" :span="8">
          <el-form-item label="可见人员">
            <el-input v-model="visibleUserNames" placeholder="请选择可见人员" disabled clearable
              @keyup.enter.native="handleQuery" @focus="selectUserFun('visibleUser')">
              <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('visibleUser')"
                style="cursor: pointer;"></i></el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <div class="dialog-footer" v-if="!isRead">
      <!--     <el-button class="submit-btn" v-if="isHavaSave" type="primary" @click="submitForm(1)">提 交
      </el-button>
      <el-button class="submit-btn" v-if="!isHavaSave" type="primary" @click="submitInForm()">提 交
      </el-button>
      <el-button class="save-btn" v-if="isHavaSave" type="primary" @click="submitForm(0)">保 存
      </el-button> -->
      <el-button class="submit-btn" v-loading="loading" type="primary" @click="handleConfirm">确认
      </el-button>
      <el-button class="cancel-btn" @click="cancel">取 消</el-button>
    </div>
    <!-- 督办事项导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量修改对话框 -->
    <el-dialog :title="titleResponsiblePerson" v-if="openEditResponsiblePerson"
      :visible.sync="openEditResponsiblePerson" width="700px" v-loading="openEditResponsiblePersonLoading"
      append-to-body>
      <el-form ref="form" :model="batchEditForm" :rules="rules" label-width="100px">
        <!-- 表单项分组 -->
        <el-divider content-position="left">来源与分类</el-divider>

        <el-form-item label="来源" prop="ranterEdit">
          <el-switch v-model="batchEditForm.isEditRanter" active-text="修改" inactive-text="保持不变"></el-switch>
          <el-input v-if="batchEditForm.isEditRanter" class="batch-edit-input" suffix-icon="el-icon-search"
            v-model="batchEditForm.ranterNameEdit" placeholder="请选择来源" clearable @focus="selectUserFun('ranterEdit')" />
        </el-form-item>

        <el-form-item label="类型" prop="mattersTypeEdit">
          <el-switch v-model="batchEditForm.isEditMattersType" active-text="修改" inactive-text="保持不变"></el-switch>
          <el-select v-if="batchEditForm.isEditMattersType" class="batch-edit-input"
            v-model="batchEditForm.mattersTypeEdit" multiple placeholder="请选择类型">
            <el-option v-for="dict in dict.type.rant_matters_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分类" prop="rantClassifyEdit">
          <el-switch v-model="batchEditForm.isEditRantClassify" active-text="修改" inactive-text="保持不变"></el-switch>
          <el-select v-if="batchEditForm.isEditRantClassify" class="batch-edit-input"
            v-model="batchEditForm.rantClassifyEdit" placeholder="请选择分类">
            <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
              :value="dict.label"></el-option>
          </el-select>
        </el-form-item>

        <el-divider content-position="left">责任信息</el-divider>

        <el-form-item label="责任人" prop="responsiblePersonEdit">
          <el-switch v-model="batchEditForm.isEditResponsiblePerson" active-text="修改" inactive-text="保持不变"></el-switch>
          <el-input v-if="batchEditForm.isEditResponsiblePerson" class="batch-edit-input" suffix-icon="el-icon-search"
            v-model="batchEditForm.responsiblePersonNameEdit" placeholder="请选择责任人" clearable
            @focus="selectUserFun('responsiblePersonEdit')" />
        </el-form-item>

        <el-form-item label="责任部门" prop="deptIdEdit">
          <el-switch v-model="batchEditForm.isEditDeptId" active-text="修改" inactive-text="保持不变"></el-switch>
          <treeselect v-if="batchEditForm.isEditDeptId" class="batch-edit-input" v-model="batchEditForm.deptIdEdit"
            :options="deptOptionsEdit" :normalizer="normalizer" @input="selectDeptEdit" placeholder="选择责任部门" />
        </el-form-item>

        <template v-if="batchEditForm.isEditDeptId">
          <el-form-item label="部门负责人" prop="respDeptResponsiblerEdit">
            <el-input class="full-width-input" suffix-icon="el-icon-search"
              v-model="batchEditForm.respDeptResponsiblerNameEdit" placeholder="请选择责任部门负责人" clearable
              @focus="selectUserFun('respDeptResponsiblerEdit')" />
          </el-form-item>

          <el-form-item label="分管领导" prop="respDeptLeaderEdit">
            <el-input class="full-width-input" suffix-icon="el-icon-search"
              v-model="batchEditForm.respDeptLeaderNameEdit" placeholder="请选择分管领导" clearable
              @focus="selectUserFun('respDeptLeaderEdit')" />
          </el-form-item>
        </template>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResponsiblePerson">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
      @feedbackEmit="selectUserData" />
  </div>
</template>

<script>
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}
import {
  getMatters,
  listMatters,
  addMatters,
  updateMatters,
  delMatters,
  changeRantMattersStatus,
  submitIn,
  editResponsiblePerson,
  batchRemove,
  batchSubmit,
  batchEdit,
  rantDaiBanDetailForTodoCenter,
  talkFeedbackConfirm
} from "@/api/rant/matters";
import SelectUser from "@/views/rant/components/SelectUser/index.vue";
import { getToken } from "@/utils/auth";
import { rantStatusOption, isPrivateOption } from "@/constant";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listRespdet, getRespdet } from "@/api/rant/respdet";
import { listDept, listDeptExcludeChild } from "@/api/system/dept";

export default {
  name: "Matters",
  components: { SelectUser, StatusTag, Treeselect },
  dicts: ['rant_classify', 'rant_matters_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      indexes: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 督办事项表格数据
      mattersList: [],
      // 弹出层标题
      title: "",
      formTitle: "新增督办事项",
      // 是否显示弹出层
      open: false,
      //是否显示保存
      isHavaSave: true,

      //选择用户
      selectUserType: "",
      ranter: null,
      ranterName: "",
      responsiblePerson: null,
      responsiblePersonName: "",
      deptId: null,
      deptName: "",
      roleName: "",
      isMultiple: false,
      responsiblePersonNameQuery: "",

      openEditDept: false,
      openEditResponsiblePerson: false,
      openEditResponsiblePersonLoading: false,
      titleResponsiblePerson: "批量修改",
      // 责任部门列表
      deptOptions: [],
      deptVisibleOptions: [],
      // 批量修改责任部门列表
      deptOptionsEdit: [],
      responsiblePersonNameEdit: null,
      responsiblePersonEdit: null,
      //可见部门
      visibleDepts: [],
      visibleUserNames: '',
      mattersType: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranter: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        responsiblePerson: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: null,
        isPrivate: null,
      },
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "销售目标导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/rant/matters/importData",
      },
      // 表单参数
      form: {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        respDeptResponsibler: null,
        respDeptResponsiblerName: null,
        respDeptLeader: null,
        respDeptLeaderName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        visibleDepts: [],
        visibleDeptNames: [],
        visibleUsers: [],
        visibleUserNames: '',
        isPrivate: null,
      },
      batchEditForm: {
        ids: [],
        responsiblePersonEdit: null,
        responsiblePersonNameEdit: null,
        isEditResponsiblePerson: false,
        isEditRanter: false,
        ranterNameEdit: null,
        ranterEdit: null,
        isEditMattersType: false,
        mattersTypeEdit: null,
        isEditRantClassify: false,
        rantClassifyEdit: null,
        isEditDeptId: false,
        deptIdEdit: null,
        deptIdNameEdit: null,
        respDeptResponsiblerEdit: null,
        respDeptResponsiblerNameEdit: null,
        respDeptLeaderEdit: null,
        respDeptLeaderNameEdit: null,

      },
      // 表单校验
      rules: {
        ranterName: [
          {
            required: true,
            message: "来源不能为空",
            trigger: ["blur", "change"],
          },
        ],
        deptId: [
          {
            required: true,
            message: "责任部门不能为空",
            trigger: ["blur", "change"],
          },
        ],
        isPrivate: [
          {
            required: true,
            message: "是否私密不能为空",
            trigger: ["blur", "change"],
          },
        ],

        rantClassify: [
          {
            required: true,
            message: "分类不能为空",
            trigger: ["blur", "change"],
          },
        ],
        planTime: [
          {
            required: true,
            message: "计划完成时间不能为空",
            trigger: ["blur", "change"],
          },
        ],
        responsiblePersonName: [
          {
            required: true,
            message: "责任人不能为空",
            trigger: ["blur", "change"],
          },
        ],
        rantContent: [
          {
            required: true,
            message: "内容不能为空",
            trigger: ["blur", "change"],
          },
        ],
        solution: [
          {
            required: true,
            message: "措施不能为空",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectUserShow: true,
      isRead: false
    };
  },
  created() {
    // this.getList();
    /*  this.type = this.$route.query.type;
     this.id = this.$route.query.id;
     this.isHavaSave = this.$route.query.isHavaSave === 'true';
     this.deptId = this.$route.query.deptId;
     console.log('isHavaSave-------', this.isHavaSave)
     if (this.type === 'add') {
       this.formTitle = '新增督办事项';
       this.handleAdd()
     }
     else if(this.type === 'edit'){
       this.formTitle = '编辑督办事项';
       this.handleUpdate(this.id,this.isHavaSave, this.deptId)
     } */
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, "id");
    });

  },
  mounted() {
    const todoNoticeId = this.$route.query.todoNoticeId;
    this.handleGetRantList(todoNoticeId);
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption;
    },
    isPrivateOption: function () {
      return isPrivateOption
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          talkFeedbackConfirm({
            id: this.form.id,
            planTime: this.form.planTime,
            solution: this.form.solution,
          }).then(response => {
            if (response.code === 200) {
              this.$message.success('确认成功，即将关闭页面')
              setTimeout(() => {
                close();
              }, 1500);
            }
          }).finally(() => {
            this.loading = false;
          })
        }
        else {
          this.loading = false;
        }
      })

    },
    handleGetRantList(todoNoticeId) {
      this.loading = true;
      rantDaiBanDetailForTodoCenter(todoNoticeId).then((response) => {
        this.form = response.data;
        this.isRead = response.data?.isRead;
        if (response.data.visibleUserNames) {
          this.visibleUserNames = response.data.visibleUserNames.join(',');
        }
        if (typeof this.form.mattersType == 'string') {
          this.mattersType = this.form.mattersType.split(',');
        }


        listDeptExcludeChild(this.form.deptId).then(response => {
          this.deptVisibleOptions = this.handleTree(response.data, "deptId");
          console.log('deptVisibleOptions-------', this.deptVisibleOptions)
        });

      }).finally(() => {
        this.loading = false;
      });
    },
    handleDialogClose() {
      console.log("handleDialogClose");
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);

    },
    handleClear() {
      this.selectUserShow = false;
      this.form.ranter = null;
      this.form.ranterName = null;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);
    },
    /** 查询督办事项列表 */
    getList() {
      this.loading = true;
      listMatters(this.queryParams).then((response) => {
        this.mattersList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      // this.open = false;
      // this.reset();
      // this.$tab.closeOpenPage({ path: '/rant/matters' });
      close();
    },
    // 表单重置
    reset() {
      this.mattersType = [];
      this.form = {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: null,
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        respDeptResponsibler: null,
        respDeptResponsiblerName: null,
        respDeptLeader: null,
        respDeptLeaderName: null,
        rantContent: null,
        solution: null,
        thisProgress: null,
        closingTime: null,
        achievement: null,
        status: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        visibleDepts: [],
        visibleDeptNames: [],
        visibleUsers: [],
        visibleUserNames: [],
        isPrivate: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.indexes = selection.map((item) => {
        const index = this.mattersList.findIndex(row => row.id === item.id);
        return index !== -1 ? index + 1 : -1;
      });
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      //this.form.visibleDepts = [119513,153,313];
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
      listDept().then(response => {
        this.deptVisibleOptions = this.handleTree(response.data, "deptId");
      });
      // this.open = true;
      this.isHavaSave = true;
      this.selectUserShow = true;
      // this.title = "添加督办事项";
    },
    /** 修改按钮操作 */
    handleUpdate(id, isHavaSave, deptId) {
      // this.reset();
      // const id = row.id;
      this.isHavaSave = isHavaSave;
      this.selectUserShow = true;
      getMatters(id).then((response) => {
        this.form = response.data;
        this.ranter = response.data.ranter;
        this.ranterName = response.data.ranterName;
        this.responsiblePersonName = response.data.responsiblePersonName;
        this.responsiblePerson = response.data.responsiblePerson;
        this.deptId = response.data.deptId;
        this.deptName = response.data.deptName;
        this.open = true;
        if (response.data.visibleUserNames) {
          this.visibleUserNames = response.data.visibleUserNames.join(',');
        }
        if (typeof this.form.mattersType == 'string') {
          this.mattersType = this.form.mattersType.split(',');
        }

        this.title = "修改督办事项";
      });
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, "id");
      });
      listDeptExcludeChild(deptId).then(response => {
        this.deptVisibleOptions = this.handleTree(response.data, "deptId");
      });
    },

    handleView(row) {
      this.$router.push({
        path: "/rant/rantDetail",
        query: {
          id: row.id,
          type: "matters",
        },
      });
    },


    /** 提交按钮 */
    submitForm(submitStatus) {

      this.$refs["form"].validate((valid) => {

        this.form.status = submitStatus;
        if (valid) {

          const findDept = (items) => {
            for (const item of items) {
              if (item.id === this.form.deptId) {
                return item;
              }
              if (item.children) {
                const found = findDept(item.children);
                if (found) return found;
              }
            }
            return null;
          };
          const dept = findDept(this.deptOptions);
          this.form.deptName = dept.name;
          if (this.form.isPrivate == 1) {
            if (this.form.visibleDepts.length == 0 && this.form.visibleUsers.length == 0) {
              this.$modal.msgError("请选择可见部门或用户");
              return;
            }
            else {
              const validNames = []
              const targetIds = this.form.visibleDepts;
              const findDeptById = (items, targetId) => {
                for (const item of items) {
                  if (item.deptId == Number(targetId)) {
                    return item;
                  }
                  if (item.children) {
                    const found = findDeptById(item.children, targetId);
                    if (found) return found;
                  }
                }
                return null;
              };
              for (const id of targetIds) {
                for (const item of this.deptVisibleOptions) {
                  if (item.deptId == Number(id)) {
                    validNames.push(item.name);
                    break;
                  } else if (item.children) {
                    const found = findDeptById(item.children, id);
                    if (found) {
                      validNames.push(found.deptName);
                      break;
                    };
                  }
                }

                /* const item = findDeptById(this.deptVisibleOptions, id);
                if (item) validNames.push(item.name); */
              }

              this.form.visibleDeptNames = validNames
            }
          }
          this.form.mattersType = this.mattersType.join(',');

          if (this.form.id != null) {

            updateMatters(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              // this.open = false;
              // this.getList();
              this.$tab.closeOpenPage({ path: '/rant/matters' });
            });
          } else {
            addMatters(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              // this.open = false;
              // this.getList();
              this.$tab.closeOpenPage({ path: '/rant/matters' });
            });
          }
        } else {
          this.loading = false
        }
      });
    },
    /** 提交按钮进心中的 */
    submitInForm() {
      this.$refs["form"].validate((valid) => {

        if (valid) {
          const findDept = (items) => {
            for (const item of items) {
              if (item.id === this.form.deptId) {
                return item;
              }
              if (item.children) {
                const found = findDept(item.children);
                if (found) return found;
              }
            }
            return null;
          };
          const dept = findDept(this.deptOptions);
          this.form.deptName = dept.name;
          if (this.form.isPrivate == 1) {
            if (this.form.visibleDepts.length == 0 && this.form.visibleUsers.length == 0) {
              this.$modal.msgError("请选择可见部门或用户");
              return;
            }
            else {
              const validNames = []
              const targetIds = this.form.visibleDepts;
              const findDeptById = (items, targetId) => {
                for (const item of items) {
                  if (item.deptId == Number(targetId)) {
                    return item;
                  }
                  if (item.children) {
                    const found = findDeptById(item.children, targetId);
                    if (found) return found;
                  }
                }
                return null;
              };
              for (const id of targetIds) {
                for (const item of this.deptVisibleOptions) {
                  if (item.deptId == Number(id)) {
                    validNames.push(item.name);
                    break;
                  } else if (item.children) {
                    const found = findDeptById(item.children, id);
                    if (found) {
                      validNames.push(found.deptName);
                      break;
                    };
                  }
                }

                /* const item = findDeptById(this.deptVisibleOptions, id);
                if (item) validNames.push(item.name); */
              }

              this.form.visibleDeptNames = validNames
            }
          }

          this.form.mattersType = this.mattersType.join(',');
          submitIn(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            // this.open = false;
            // this.getList();
            this.$tab.closeOpenPage({ path: '/rant/matters' });
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除督办事项编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMatters(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    //状态修改--终止、启用
    handleStatusChange(row, status) {
      let text = status === 5 ? "终止" : "启用";

      this.$modal
        .confirm('确认要"' + text + '""' + row.rantContent + '"此督办事项吗？')
        .then(function () {
          return changeRantMattersStatus(row.id, status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
          this.getList();
        })
        .catch(function () {
          //row.status = row.status === "0" ? "1" : "0"
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "rant/matters/export",
        {
          ...this.queryParams,
        },
        `督办事项_${new Date().getTime()}.xlsx`
      );
    },
    handleUpTemp() {
      this.download(
        "rant/matters/exportTemplate",
        {},
        `督办事项导入模板_${new Date().getTime()}.xlsx`
      );
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "督办事项导入";
      this.upload.open = true;
    },

    /** 批量修改反馈人按钮操作 */
    handleResponsiblePerson() {
      const ids = this.ids;
      if (ids.length > 0) {
        this.openEditResponsiblePerson = true;
        this.selectUserShow = true;
        listRespdet().then(response => {
          this.deptOptionsEdit = this.handleTree(response.data, "id");
        });
      } else {
        this.$modal.msgSuccess("请选择要修改的项");
      }
    },

    /** 批量删除按钮操作 */
    handleBatchDelete() {

      this.$modal.alert('请仔细阅读注意事项')
      const ids = this.ids;
      const indexes = this.indexes;
      this.$modal
        .confirm('是否确认删除督办事项序号为"' + indexes + '"的数据项？')
        .then(function () {
          return batchRemove({ indexes, ids });
        })
        .then((response) => {

          this.$msgbox({
            title: '结果提示',
            message: response.msg.replace(/\n/g, '<br>'),
            showCancelButton: false, // 关键配置：隐藏取消按钮
            confirmButtonText: '确认',
            type: 'success',
            dangerouslyUseHTMLString: true
          }).then(() => {
            // 确认后的操作
            this.getList();
          })
          // this.getList();
        })
        .catch(() => { });
    },
    /** 批量提交按钮操作 */
    handleBatchSubmit() {
      const ids = this.ids;
      const indexes = this.indexes;
      this.$modal
        .confirm('是否确认提交督办事项序号为"' + indexes + '"的数据项？')
        .then(function () {
          return batchSubmit({ indexes, ids });
        })
        .then((response) => {
          this.$msgbox({
            title: '结果提示',
            message: response.msg.replace(/\n/g, '<br>'),
            showCancelButton: false, // 关键配置：隐藏取消按钮
            confirmButtonText: '确认',
            type: 'success',
            dangerouslyUseHTMLString: true
          }).then(() => {
            // 确认后的操作
            this.getList();
          })
        })
        .catch(() => { });
    },

    resetBatchEditForm() {
      this.openEditResponsiblePerson = false;
      this.batchEditForm.responsiblePersonEdit = null;
      this.batchEditForm.responsiblePersonNameEdit = null;
      this.batchEditForm.isEditResponsiblePerson = false;
      this.batchEditForm.isEditDeptId = false;
      this.batchEditForm.deptIdEdit = null;
      this.batchEditForm.deptIdNameEdit = null;
      this.batchEditForm.respDeptResponsiblerEdit = null;
      this.batchEditForm.respDeptResponsiblerNameEdit = null;
      this.batchEditForm.respDeptLeaderEdit = null;
      this.batchEditForm.respDeptLeaderNameEdit = null;
      this.batchEditForm.isEditRanter = false;
      this.batchEditForm.ranterEdit = null;
      this.batchEditForm.ranterNameEdit = null;
      this.batchEditForm.isEditMattersType = false;
      this.batchEditForm.mattersTypeEdit = null;
      this.batchEditForm.isEditRantClassify = false;
      this.batchEditForm.rantClassifyEdit = null;
    },
    /** 提交批量修改按钮 */
    submitResponsiblePerson() {
      // 检查是否至少选择了一项进行修改
      if (!this.batchEditForm.isEditResponsiblePerson &&
        !this.batchEditForm.isEditRanter &&
        !this.batchEditForm.isEditMattersType &&
        !this.batchEditForm.isEditRantClassify &&
        !this.batchEditForm.isEditDeptId) {
        this.$modal.msgWarning("请至少选择一项进行修改");
        return;
      }

      // 检查各选中项是否填写了必要信息
      if (this.batchEditForm.isEditResponsiblePerson && !this.batchEditForm.responsiblePersonNameEdit) {
        this.$modal.msgWarning("请选择责任人");
        return;
      }

      if (this.batchEditForm.isEditRanter && !this.batchEditForm.ranterNameEdit) {
        this.$modal.msgWarning("请选择来源");
        return;
      }

      if (this.batchEditForm.isEditMattersType && (!this.batchEditForm.mattersTypeEdit || this.batchEditForm.mattersTypeEdit.length === 0)) {
        this.$modal.msgWarning("请选择类型");
        return;
      }

      if (this.batchEditForm.isEditRantClassify && (!this.batchEditForm.rantClassifyEdit || this.batchEditForm.rantClassifyEdit.length === 0)) {
        this.$modal.msgWarning("请选择分类");
        return;
      }

      if (this.batchEditForm.isEditDeptId && !this.batchEditForm.deptIdEdit) {
        this.$modal.msgWarning("请选择责任部门");
        return;
      }

      // 组装提交数据
      const submitData = {
        ids: this.ids
      };

      if (this.batchEditForm.isEditResponsiblePerson) {
        submitData.isEditResponsiblePerson = this.batchEditForm.isEditResponsiblePerson;
        submitData.responsiblePerson = this.batchEditForm.responsiblePersonEdit;
        submitData.responsiblePersonName = this.batchEditForm.responsiblePersonNameEdit;
      }

      if (this.batchEditForm.isEditRanter) {
        submitData.isEditRanter = this.batchEditForm.isEditRanter;
        submitData.ranter = this.batchEditForm.ranterEdit;
        submitData.ranterName = this.batchEditForm.ranterNameEdit;
      }

      if (this.batchEditForm.isEditMattersType) {
        submitData.isEditMattersType = this.batchEditForm.isEditMattersType;
        submitData.mattersType = this.batchEditForm.mattersTypeEdit.join(',');
      }

      if (this.batchEditForm.isEditRantClassify) {
        submitData.isEditRantClassify = this.batchEditForm.isEditRantClassify;
        submitData.rantClassify = this.batchEditForm.rantClassifyEdit;
      }

      if (this.batchEditForm.isEditDeptId) {
        submitData.isEditDeptId = this.batchEditForm.isEditDeptId;
        submitData.deptId = this.batchEditForm.deptIdEdit;
        submitData.deptName = this.batchEditForm.deptIdNameEdit;
        submitData.respDeptResponsibler = this.batchEditForm.respDeptResponsiblerEdit;
        submitData.respDeptResponsiblerName = this.batchEditForm.respDeptResponsiblerNameEdit;
        submitData.respDeptLeader = this.batchEditForm.respDeptLeaderEdit;
        submitData.respDeptLeaderName = this.batchEditForm.respDeptLeaderNameEdit;
      }


      this.openEditResponsiblePersonLoading = true;
      batchEdit(submitData).then(response => {
        resetBatchEditForm();
        this.$modal.msgSuccess("批量修改成功");
        this.openEditResponsiblePerson = false;
        this.queryParams.pageNum = 1;
        this.getList();
      }).catch(() => {
        this.openEditResponsiblePersonLoading = false;
      });
    },

    // 取消按钮
    cancelResponsiblePerson() {
      this.resetBatchEditForm();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    selectUserFun(type) {

      this.selectUserType = type;
      this.isMultiple = type == "ranter" || type == "visibleUser" || type == "ranterEdit";
      this.$refs.userRef.show();
    },
    selectUserData(data) {
      console.log(data, this.selectUserType);
      if (this.selectUserType == "ranter") {
        this.form.ranter = data.map((item) => item.userId).join(",");
        this.form.ranterName = data.map((item) => item.nickName).join(",");
      } else if (this.selectUserType == "responsibilityer") {
        this.responsiblePerson = data.userId;
        this.responsiblePersonName = data.nickName;
        this.form.responsiblePerson = data.userId;
        this.form.responsiblePersonName = data.nickName;
        this.deptId = data.deptId;
        this.deptName = data.dept.deptName;
      } else if (this.selectUserType == "responsibilityerQuery") {
        this.queryParams.responsiblePerson = data.userId;
        this.responsiblePersonNameQuery = data.nickName;
      } else if (this.selectUserType == "responsiblePersonEdit") {
        this.batchEditForm.responsiblePersonEdit = data.userId;
        this.batchEditForm.responsiblePersonNameEdit = data.nickName;
      } else if (this.selectUserType == "visibleUser") {
        this.form.visibleUsers = data.map((item) => item.userId);
        this.form.visibleUserNames = data.map((item) => item.nickName);
        this.visibleUserNames = this.form.visibleUserNames.join(',');
      } else if (this.selectUserType == "ranterEdit") {
        this.batchEditForm.ranterEdit = data.map((item) => item.userId).join(",");;
        this.batchEditForm.ranterNameEdit = data.map((item) => item.nickName).join(",");
      }
      this.selectUserShow = false;
      setTimeout(() => {
        this.selectUserShow = true;
      }, 0);

    },
    changeresponsibilityer() {
      if (
        this.responsiblePersonNameQuery == "" ||
        this.responsiblePersonNameQuery == null
      ) {
        this.queryParams.responsiblePerson = null;
      }
    },
    async selectDept(value) {
      if (value == null) {
        this.form.deptId = null;
        this.form.deptName = null;
        this.form.respDeptLeader = null;
        this.form.respDeptLeaderName = null;
        this.form.respDeptResponsibler = null;
        this.form.respDeptResponsiblerName = null;
        return;
      }

      const id = value;
      this.form.deptId = id;

      getRespdet(id).then(response => {
        this.form.deptName = response.data.name;
        this.form.respDeptLeader = response.data.responsibleLeader;
        this.form.respDeptLeaderName = response.data.responsibleLeaderName;
        this.form.respDeptResponsibler = response.data.respPeople;
        this.form.respDeptResponsiblerName = response.data.respPeopleName;
      });

    },

    selectDeptEdit(value) {
      if (value == null) {
        this.batchEditForm.deptIdNameEdit = null;
        this.batchEditForm.respDeptResponsiblerEdit = null;
        this.batchEditForm.respDeptResponsiblerNameEdit = null;
        this.batchEditForm.respDeptLeaderEdit = null;
        this.batchEditForm.respDeptLeaderNameEdit = null;
        return;
      }
      const id = value;
      this.batchEditForm.deptIdEdit = id;
      const findDept = (items) => {
        for (const item of items) {
          if (item.id === id) {
            return item;
          }
          if (item.children) {
            const found = findDept(item.children);
            if (found) return found;
          }
        }
        return null;
      };
      const dept = findDept(this.deptOptionsEdit);
      this.batchEditForm.deptIdNameEdit = dept.name;
      getRespdet(id).then(response => {
        this.batchEditForm.respDeptResponsiblerEdit = response.data.respPeople;
        this.batchEditForm.respDeptResponsiblerNameEdit = response.data.respPeopleName;
        this.batchEditForm.respDeptLeaderEdit = response.data.responsibleLeader;
        this.batchEditForm.respDeptLeaderNameEdit = response.data.responsibleLeaderName;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    /** 转换部门数据结构 */
    normalizerVisible(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-input.is-disabled .el-input__inner) {
  color: #000;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  color: #000;
}

:deep(.vue-treeselect--disabled .vue-treeselect__multi-value-item) {
  color: #000;
}

:deep(.el-select__tags-text) {
  color: #000;
}

.app-container {
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  border-radius: 0px 0px 0px 0px;
  height: calc(100vh - 84px);

  .card-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 32px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 13px;
      height: 17px;
      background: url("~@/assets/rant/rant-item.png") no-repeat;
      background-size: contain;
      margin-right: 10px;
    }
  }
}

.form-block {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  max-height: calc(100vh - 160px);
  overflow: auto;
}

.dialog-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 12px 304px;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  display: flex;
  justify-content: center;
  gap: 40px;

  .submit-btn {
    width: 100px;
  }

  .save-btn {
    width: 100px;
  }

  .cancel-btn {
    width: 100px;
  }
}

:deep(.el-table__cell .cell) {
  display: flex;
  justify-content: center;
}

/* 添加批量修改对话框的样式 */
.batch-edit-input {
  margin-top: 10px;
  width: 100%;
}

.full-width-input {
  width: 100%;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-switch) {
  margin-right: 10px;
}
</style>
