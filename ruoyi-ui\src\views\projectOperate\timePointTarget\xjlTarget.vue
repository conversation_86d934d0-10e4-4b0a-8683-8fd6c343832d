<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="城市公司" prop="cityId">
        <el-select
          v-model="queryParams.cityId"
          placeholder="请选择城市公司"
          @change="handleCitySearch"
          clearable
          class="width-100">
          <el-option
            v-for="item in cites"
            :key="item.cityId"
            :label="item.cityName"
            :value="item.cityId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectCode">
        <el-select
          v-model="queryParams.projectCode"
          placeholder="请选择项目"
          @change="handleProjectSearch"
          class="width-100"
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.displayName"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportTemplate"
        >导入模板下载
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="importFile"
        >导入
        </el-button
        >
      </el-col>
      <el-col :span="1">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >删除
        </el-button
        >
      </el-col>
      <el-col :span="17" style="text-align: right;">
        <span style="font-weight: bold;">单位：万元</span>
      </el-col>
    </el-row>

    <!-- 隐藏的文件输入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
    />

    <!-- 表格展示区域 -->
    <el-table :data="tableData" v-loading="loading" border style="width: 100%" :cell-style="cellStyle"
              :span-method="objectSpanMethod">
      <el-table-column prop="projectName" label="项目" width="180" header-align="center"></el-table-column>
      <el-table-column prop="totalAmount" label="合计" width="180" header-align="center"></el-table-column>
      <!-- 动态生成年份列 -->
      <el-table-column v-for="year in years" :key="year" :label="year.toString() + '年'" :width="180"
                       header-align="center">
        <template>
          <el-table-column v-for="month in months" :key="month" :label="month" :width="90" header-align="center">
            <template slot-scope="scope">
              {{ getTargetAmount(scope.row, year, month) }}
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {getXjlTimePointTarget, importXjlData, deleteData} from "@/api/projectOperate/timePointTarget";
import API from "@/views/projects/api";

export default {
  name: "xhlTarget",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目时点目标表格数据
      timePointTargetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        targetType: 3,
        cityId: null,
        projectCode: null,
        projectTypeCode: null,
        projectName: null,
        projectType: null,
        openDate: null,
        liquidateDate: null,
        year: null,
        dateType: null,
        month: null,
        targetAmount: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 城市公司列表
      cites: [],
      // 项目列表
      projects: [],
      //项目现金流数据
      xjlTargetData: [],
      tableData: [],
      months: [
        "1月", "2月", "3月", "4月", "5月", "6月",
        "7月", "8月", "9月", "10月", "11月", "12月"
      ],
      years: [], // 初始化为空数组，后续在created钩子中赋值
    };
  },
  created() {
    this.getCities();
    this.generateYears(); // 在created钩子中调用生成年份的方法
  },
  methods: {
    handleQuery() {
      if (this.queryParams.projectCode == null || this.queryParams.projectCode == '') {
        //提示选择项目
        this.$message.error('请先选择项目');
        return;
      }
      getXjlTimePointTarget(this.queryParams).then(res => {
        this.xjlTargetData = res.data;
        this.generateTableData();
      }).catch(err => {
        this.$message.error(err.message || '获取项目现金流数据失败');
      });
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.xjlTargetData = [];
      this.generateTableData();
    },
    getProjects(cityId) {
      API.Common.getProject(cityId).then(res => {
        if (res.code === 200) {
          this.projects = res.data
        } else {
          this.$message.error(res.message || '获取项目信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取项目信息失败')
      })
    },
    getCities() {
      API.Common.getCity().then(res => {
        if (res.code === 200) {
          this.cites = res.data
        } else {
          this.$message.error(res.message || '获取城市公司信息失败')
        }
      }).catch(err => {
        this.$message.error(err.message || '获取城市公司信息失败')
      })
    },
    handleCitySearch(val) {
      this.projects = null;
      this.queryParams.projectCode = null;
      !!this.queryParams.cityId ? this.getProjects(this.queryParams.cityId) : null;
      this.xjlTargetData = [];
      this.generateTableData();
    },
    handleProjectSearch(val) {
      getXjlTimePointTarget(this.queryParams).then(res => {
        console.log(res.data);
        this.xjlTargetData = res.data;
        this.generateTableData();
      }).catch(err => {
        this.$message.error(err.message || '获取项目现金流目标数据失败');
      });
      //根据projectCode获取projectName
      this.projects.forEach(project => {
        if (project.projectCode === val) {
          this.queryParams.projectName = project.displayName;
        }
      })
    },
    generateTableData() {
      this.tableData = this.xjlTargetData.map((project, index) => {
        const projectData = {
          projectName: project.projectName,
          projectType: project.projectType,
          openDate: project.openDate,
          liquidateDate: project.liquidateDate,
          totalAmount: project.totalAmount,
          _rowKey: index, // 添加唯一标识符
        };

        // 提取所有年份
        const years = project.yearDataDtoList.map(y => y.year);
        this.years = [...new Set(years)].sort((a, b) => a - b);

        this.years.forEach(year => {
          const yearData = project.yearDataDtoList.find(y => y.year === year);
          if (yearData) {
            yearData.monthDataDtoList.forEach(monthData => {
              projectData[`${year}-${monthData.month}`] = monthData.targetAmount;
            });
          }

        });

        return projectData;
      });
    },
    getTargetAmount(row, year, month) {
      return row[`${year}-${month}`] || '';
    },
    cellStyle() {
      return {textAlign: 'center'};
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) { // 项目列
        if (rowIndex === 0) {
          return {
            rowspan: this.tableData.length,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    /** 导出按钮操作 */
    handleExportTemplate() {
      this.download(
        "project/timePointTarget/exportXjlTemplate",
        {
          ...this.queryParams,
        },
        `现金流时点目标导入模板（单位：万元）_${new Date().getTime()}.xlsx`
      );
    },
    // 导入文件
    importFile() {
      this.$refs.fileInput.click(); // 触发文件输入的点击事件
    },
    handleFileChange(event) {
      this.loading = true;
      const files = event.target.files;
      const formData = new FormData();
      formData.append("file", files[0]);
      // 重置 input，以便下次能够重新触发 change 事件
      event.target.value = null;
      importXjlData(formData)
        .then((response) => {
          this.$modal.msgSuccess("导入成功");
          this.loading = false; // 关闭加载状态
        })
        .catch(() => {
          this.loading = false; // 关闭加载状态
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/timePointTarget/xjlExport', {
        ...this.queryParams
      }, `现金流时点目标_${new Date().getTime()}.xlsx`)
    },
    generateYears() {
      const currentYear = new Date().getFullYear();
      const startYear = 2023;
      const endYear = currentYear + 7;
      this.years = Array.from({length: endYear - startYear + 1}, (_, i) => startYear + i);
    },
    handleDelete() {
      if (this.queryParams.projectCode === null || this.queryParams.projectCode === '' || this.queryParams.projectCode === undefined) {
        this.$message.error("删除前请选择对应项目");
        return;
      }
      var title = "删除【" + this.queryParams.projectName + "】时点现金流目标数据?";
      let _this = this
      this.$confirm(title, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        deleteData(_this.queryParams).then(() => {
          _this.$modal.msgSuccess("删除成功");
          if (_this.queryParams.projectCode == null || _this.queryParams.projectCode == '') {
            //提示选择项目
            _this.xjlTargetData = []
            _this.generateTableData();
            return;
          }
          getXjlTimePointTarget(_this.queryParams).then(res => {
            _this.xjlTargetData = res.data;
            _this.generateTableData();
          }).catch(err => {
            _this.$message.error(err.message || '获取项目现金流目标数据失败');
          });
        }).catch(() => {
        })
      });
    }
  }
};
</script>
