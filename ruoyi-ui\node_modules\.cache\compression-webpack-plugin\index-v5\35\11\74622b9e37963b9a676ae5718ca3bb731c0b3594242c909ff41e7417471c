
17b58013c286a08fa58e62b85f036e555f60fcc3	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.d3911eceb77eb4c78df9.hot-update.js\",\"contentHash\":\"259c03543b37ab38d9674e7a5a9c7859\"}","integrity":"sha512-/BICgp3ReLEXDik/CoQH2fgeY/QB5YC1DsKmrbaZIAqZG/UwjIRkU3CzgPFMSOLk9thpALsf7tlxq5YfBEaCSQ==","time":1754311456447,"size":78412}