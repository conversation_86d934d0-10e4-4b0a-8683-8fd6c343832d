<template>
  <div v-loading="loading" class="weekly-input">
    <WeeklyForm ref="weeklyFormRef" class="weekly-input-form" />
    <div class="bottom-button">
      <el-button class="primary-btn" type="primary" @click="saveDraft">保存草稿</el-button>
      <el-button class="primary-btn" type="primary" v-if="type === 'update'" @click="updateWeekly">提交</el-button>
      <el-button class="primary-btn" type="primary" v-else @click="submitWeekly">提交</el-button>
      <el-button class="second-btn" @click="cancelHandle">取消</el-button>
    </div>
  </div>
</template>
<script>
import { addInfo, addInit, getInfo, updateInfo, addDraft } from "@/api/weekly/reportInfo";
import WeeklyForm from "./components/weeklyForm.vue";
import { MessageBox } from 'element-ui';
export default {
  name: "WeeklyInput",
  components: {
    WeeklyForm,
  },
  data() {
    return {
      form: {},
      loading: false,
      type: "", // 新增还是编辑模式
      id: "", // 周报ID
    };
  },
  created() {
    // 获取路由参数
    const { type, id } = this.$route.query;
    this.type = type;
    this.id = id;


  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
  },
  mounted() {
    if (this.type === "update" && this.id) {
      this.getWeeklyDetail();
    } else {
      const weeklyData = this.$store.state.weekly.weeklyFormData;
      // console.log("Mounted weeklyData:", weeklyData);
      if (weeklyData && this.$refs.weeklyFormRef) {
        this.$refs.weeklyFormRef.setFormData(weeklyData);
      }
    }
  },
  methods: {
    async saveDraft() {
      if(this.loading) return
      this.loading = true;
      try {
        const formData = await this.$refs.weeklyFormRef.draftForm();
        await addDraft(formData);
        this.$modal.msgSuccess("草稿保存成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("草稿保存失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async getWeeklyDetail() {
      this.loading = true;
      try {
        const { data } = await getInfo(this.id);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        this.$refs.weeklyFormRef.updateFormData(data);
      } catch (error) {
        // this.$modal.msgError("获取详情失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async addWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        await addInfo(data);
        this.$modal.msgSuccess("新增成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("新增失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },

    async handleInit() {
      try {
        const { data } = await addInit();
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }

        const formFields = [
          "year",
          "week",
          "nickName",
          "deptId",
          "deptName",
          "postName",
          "startDate",
          "endDate",
          "workSummaryList",
          "userCode",
        ];
        const formData = {};
        formFields.forEach((field) => (formData[field] = data[field]));

        this.$refs.weeklyFormRef.setFormData(formData);
      } catch (error) {
        // this.$modal.msgError("初始化失败：" + error.message);
      }
    },
    async updateWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        const formData = {
          id: this.id,
          year: data.year,
          week: data.week,
          userCode: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          // approveUserName: data.approveUserName,
          // approveUserCode: data.approveUserCode,
          workSummaryList: data.workSummaryList,
          workPlanList: data.workPlanList,
          workSupportList: data.workSupportList,
          reportReviewDetails: data.reportReviewDetails,
          reflectInfo: data.reflectInfo,
        };
        await updateInfo(formData);
        this.$modal.msgSuccess("修改成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("修改失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 编辑周报
    async updateWeekly() {
      if(this.loading) return
      this.loading = true;
      try{
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await this.updateWeeklyHandle(formData);
      }finally{
        this.loading = false;
      }
    },
    // 新增周报
    async submitWeekly() {
      try{
        if(this.loading) return
        this.loading = true;
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await this.addWeeklyHandle(formData);
      }
      finally {
        this.loading = false;
      }

    },
    closeCurrentPage() {
      this.$tab.closeOpenPage({ path: "/weekly/myWeekly" });
    },
    cancelHandle() {
      this.closeCurrentPage();
    },
    handleBeforeUnload(event) {
      // 组织默认的 beforeunload 行为 (某些浏览器可能需要)
      event.preventDefault();
      // Chrome, Firefox, Safari, IE 8+
      event.returnValue = '您确定要离开此页面吗？您所做的更改可能不会被保存。';
      // 返回的字符串会显示在确认对话框中
      return event.returnValue; // 必须返回，某些浏览器需要
    }
  },
};
</script>
<style scoped lang="scss">
.weekly-input {
  min-height: 0;
  display: flex;
  flex-direction: column;

  .weekly-input-form {
    flex: 1;
    overflow: auto;
  }
}

.bottom-button {
  margin-top: 20px;
  text-align: center;
}

.primary-btn {
  width: 160px;
  height: 42px;
  background: #3673FF;
  border-radius: 4px 4px 4px 4px;
}

.second-btn {
  width: 160px;
  height: 42px;
  background: rgba(54, 115, 255, 0.2);
  border-radius: 4px 4px 4px 4px;
  color: #3673FF;
}
</style>
