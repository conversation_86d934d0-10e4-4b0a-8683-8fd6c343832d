import request from '@/utils/request'

// 查询客关项目信息列表
export function listKgProjectData(query) {
  return request({
    url: '/project/kgProjectData/list',
    method: 'get',
    params: query
  })
}

// 查询客关项目信息列表
export function allList() {
  return request({
    url: '/project/kgProjectData/allList',
    method: 'get',
  })
}

// 查询客关项目信息详细
export function getKgProjectData(kgProjectId) {
  return request({
    url: '/project/kgProjectData/' + kgProjectId,
    method: 'get'
  })
}

// 新增客关项目信息
export function addKgProjectData(data) {
  return request({
    url: '/project/kgProjectData',
    method: 'post',
    data: data
  })
}

// 修改客关项目信息
export function updateKgProjectData(data) {
  return request({
    url: '/project/kgProjectData',
    method: 'put',
    data: data
  })
}

// 删除客关项目信息
export function delKgProjectData(kgProjectId) {
  return request({
    url: '/project/kgProjectData/' + kgProjectId,
    method: 'delete'
  })
}
