
03dc74ccae4a130473a24566778bfed10843e00c	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4d34bdb91a08d3b21a2085675ca75b14\"}","integrity":"sha512-h6b5N+MiIqdio3XcHFY2qXu7lwOE12iLjOHTbVljGDfv+DqIpMWU5kqwyMEPUTFRD1U946o9H1iaUiLC3PrNFQ==","time":1754311454799,"size":12043744}