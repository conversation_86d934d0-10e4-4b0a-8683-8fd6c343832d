
14284cea87a1796421c070ad98e9aafe72313181	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.a8ed39fd9b20befcda29.hot-update.js\",\"contentHash\":\"06d730acab26b71c19cb33aa077f5dc5\"}","integrity":"sha512-0clZwuOAwhLHZVXyOPsaoxrySn82DPFDyTKeVTXVMc8L+VIIR+PMXX3eqXdNbHFj7aBJP7F1UpkABKiOiscVpQ==","time":1754312354889,"size":78202}