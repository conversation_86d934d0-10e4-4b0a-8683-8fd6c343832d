<template>
  <div class="app-container" style="padding-top: 0">
    <el-row>
      <el-col :span="8">
        <div
          class="textStyle"
          style="display:flex;flex-row:row,alignItem:center"
        >
          <div class="textStyle">城市公司：{{ info.deptName }}</div>
          <!-- <div>城市公司：</div>
          <dict-tag :options="cityCompanys" :value="info.deptNum" />-->
        </div>
      </el-col>
      <el-col :span="8">
        <div class="textStyle">项目名称：{{ info.projectName }}</div>
      </el-col>
      <el-col :span="8">
        <div
          class="textStyle"
          style="display:flex;flex-row:row,alignItem:center"
        >
          <div>分期：</div>
          <dict-tag :options="dict.type.stages" :value="info.stageId" />
        </div>
      </el-col>
      <el-col :span="8">
        <div
          class="textStyle"
          style="display:flex;flex-row:row,alignItem:center"
        >
          <div>标段：</div>
          <dict-tag :options="dict.type.project_lot" :value="info.lot" />
        </div>
      </el-col>
      <el-col :span="8">
        <p class="textStyle">计划名称：{{ info.planName }}</p>
      </el-col>
      <el-col :span="8">
        <p class="textStyle">版本号：{{ info.version }}</p>
      </el-col>
    </el-row>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:info:export']"
          >导出</el-button
        >
      </el-col>

    </el-row>
    <el-table v-loading="loading" :data="nodeList">
      <el-table-column label="序号" align="center" prop="id" width="65">
        <template slot-scope="scope">
          <span class="textStyle">
            {{ scope.$index + 1 }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="节点阶段" align="center" prop="nodeStage" />
      <el-table-column label="节点等级" align="center" prop="nodeLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.levels" :value="scope.row.nodeLevel" />
        </template>
      </el-table-column> -->
      <el-table-column label="状态" align="center" prop="status" v-if="statusShow">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column v-else label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <status-tag :status="scope.row.status" :options="nodeStatusOption"/>
        </template>
      </el-table-column>
      <el-table-column
        label="节点名称"
        align="center"
        prop="nodeName"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="计划开始时间"
        align="center"
        prop="startTime"
        width="120"
      >
      </el-table-column>
      <el-table-column
        label="计划完成时间"
        align="center"
        prop="endTime"
        width="120"
      >
      </el-table-column>
      <el-table-column
        label="实际完成时间"
        align="center"
        prop="actualCompletionDate"
        width="120"
      >
        <template slot-scope="scope">
          <div style="min-width: 120px; font-weight: 600" class="textStyle">
            {{ parseTime(scope.row.actualCompletionDate, "{y}-{m}-{d}") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="责任部门"
        align="center"
        prop="departmentNames"
        width="150"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="反馈人"
        align="center"
        prop="feedbackUserName"
        width="120"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row, scope.index)"
            >详情</el-button
          >
          <!--添加具名插槽-->
          <slot
            name="progressFeedback"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
          <slot
            name="finishedFeedback"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改计划-节点对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-row>
        <el-col :span="12">
          <div class="textStyleFont">节点序号： {{ form.nodeCode }}</div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">
            节点名称： {{ form.standardNodeName }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">标准工期： {{ form.durationNum }}</div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont" style="display: flex; align-items: center">
            责任部门：{{ form.departmentNames }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">反馈人： {{ form.feedbackUserName }}</div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">计划开始时间： {{ form.startTime }}</div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">计划完成时间： {{ form.endTime }}</div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">
            是否可用： {{ form.isValid ? "是" : "否" }}
          </div>
        </el-col>
        <el-col :span="12">
          <div class="textStyleFont">
            完成标准： {{ form.completeCriteria }}
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import SelectTree from '@/components/SelectTree.vue'
import { isEditOption } from '@/views/plan/constant'
import { listNode } from "@/api/plan/node"
import Template from "@/views/plan/template/index.vue";
import StatusTag from "@/views/plan/components/StatusTag/index.vue";
import {planStatusOption,nodeStatusOption} from "@/constant";

export default {
  name: "PlanAndNodeInfo",
  components: {
    StatusTag, Template,
    SelectTree
  },
  props: ['data', 'statusShow'],
  dicts: ['stages', 'project_lot'],
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划-节点表格数据
      nodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        nodeLevel: null,
        nodeName: null,
        planId: null,
        version: null
      },
      // 表单参数
      form: {},
      // 表单校验`
      rules: {
        nodeName: [{
          required: true,
          message: "节点名称不能为空",
          trigger: "blur"
        }],
        durationNum: [{
          required: true,
          message: "标准工期不能为空",
          trigger: "blur"
        }],
        startTime: [{
          required: true,
          message: "开始时间不能为空",
          trigger: "change"
        }],
        endTime: [{
          required: true,
          message: "开始时间不能为空",
          trigger: "change"
        }],
        isEdit: [{
          required: true,
          message: "是否可以编辑不能为空",
          trigger: "change"
        }],
        department: [{
          required: true,
          message: "责任部门不能为空",
          trigger: "change"
        }],
        responsibilityPeople: [{
          required: true,
          message: "责任人不能为空",
          trigger: "change"
        }]
      },
      editIndex: 0,
      info: null,
      versionList: []
    }
  },
  created () {
    this.info = this.data
    this.queryParams.planId = this.data.id
    this.getList()
    //获取部门列表
    this.$store.dispatch('plan/fetchDepartList')
  },
  computed: {
    nodeStatusOption(){
      return nodeStatusOption;
    },
    planStatusOption() {
      return planStatusOption;
    },
    isEditOption: () => isEditOption,
    cityCompanys () {
      console.log(this.$store.state.plan.deptList)
      return this.$store.state.plan.deptList
    },
    departList () {
      return this.$store.state.plan.departList
    }
  },
  methods: {
    saveFun () {
      //保存至本地
      save({
        id: this.planId,
        planNodeList: this.nodeList
      }).then(res => {
        console.log(res)
      }).catch(err => {
        console.log(err)
      })
    },
    //提交审批
    submitFun () {

    },
    /** 查询计划-节点列表 */
    receiveData (data) {
      this.form.department = data
    },
    getList () {
      this.loading = true
      listNode(this.queryParams).then(response => {
        this.nodeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 修改按钮操作 */
    handleDetail (row, index) {
      console.log(row)
      this.form = row
      this.open = true
      this.title = "节点明细详情"
    },
     /** 导出按钮操作 */
     handleExport () {
      this.download('plan/node/exportDashboardNodeList', {
        ...this.queryParams
      }, `计划节点列表（计划查看）_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
.textStyle {
  font-size: 14px;
  color: #333;
}
.textStyleFont {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 40px;
}
</style>
