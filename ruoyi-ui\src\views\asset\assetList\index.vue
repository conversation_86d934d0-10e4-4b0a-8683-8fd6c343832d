<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
     <el-form-item label="名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="useStatus">
        <el-select
          v-model="queryParams.useStatus"
          placeholder="请选择状态"
          clearable
          @change="handleQueryProject"
          filterable
        >
          <el-option
            v-for="dict in assetDetailLists"
            :key="dict.zyId"
            :label="dict.deptName"
            :value="dict.zyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <template v-if="showAllSearch">
         <!-- 在文档里字段为公司代码 -->
        <el-form-item label="资产所属单位" prop="companyCode">
            <el-input
                v-model="queryParams.companyCode"
                placeholder="请输入资产所属单位"
                clearable
                @keyup.enter.native="handleQuery"
            />
        </el-form-item>
        <!-- 在文档里字段为资产分类 -->
        <el-form-item label="资产类型" prop="assetClassification">
            <el-select
            v-model="queryParams.assetClassification"
            placeholder="请选择资产类型"
            clearable
            @change="handleQueryProject"
            filterable
            >
            <el-option
                v-for="dict in assetClassificationList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
            </el-select>
        </el-form-item>
        <!-- 在文档里字段为明细类别 -->
        <el-form-item label="资产类别" prop="detailedCategories">
            <el-select
            v-model="queryParams.detailedCategories"
            placeholder="请选择资产类别"
            clearable
            filterable
            @change="handleQueryProject"
            >
            <el-option
                v-for="dict in assetDetailLists"
                :key="dict.zyId"
                :label="dict.deptName"
                :value="dict.zyId"
            ></el-option>
            </el-select>
        </el-form-item>
        <!-- 在文档里字段为使用部门 -->
        <el-form-item label="资产使用部门" prop="usingProjects">
            <el-input
                v-model="queryParams.usingProjects"
                placeholder="请输入资产使用部门"
                clearable
                @keyup.enter.native="handleQuery"
            />
        </el-form-item>
        <el-form-item label="资产编号" prop="assetCode">
            <el-input
                v-model="queryParams.assetCode"
                placeholder="请输入资产编号"
                clearable
                @keyup.enter.native="handleQuery"
            />
        </el-form-item>
        <el-form-item label="资产状态" prop="status">
            <el-select
            v-model="queryParams.status"
            placeholder="请选择资产状态"
            clearable
            @change="handleQueryProject"
            filterable
            >
            <el-option
                v-for="dict in assetDetailLists"
                :key="dict.zyId"
                :label="dict.deptName"
                :value="dict.zyId"
            ></el-option>
            </el-select>
        </el-form-item>
      </template>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" class="search-btn">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" size="mini" @click="moreFilter">更多筛选</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport(1)"
          >导出</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="assetList"
      :row-class-name="() => 'custom-row'"
      @row-click="showPlanAndNodeDialogFun"
      @selection-change="handleSelectionChange"
    >
     <el-table-column
       type="selection"
       width="55"
       fixed="left">
      </el-table-column>
      <el-table-column
        label="资产编号"
        fixed="left"
        width="150"
        prop="assetCode"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.assetCode || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="资产名称"
        align="center"
        prop="assetName"
        width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.assetName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="资产分类"
        align="center"
        prop="assetClassification"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.assetClassification || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总价"
        align="center"
        prop="totalPrice"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.totalPrice || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="数量"
        align="center"
        prop="number"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.number || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="添加时间"
        align="center"
        prop="startDate"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startDate || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding"
        fixed="right"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click.stop="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleEdit(scope.row)"
          >编辑</el-button>
           <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <AssetDetail :assetDetailVisible="isView" :assetDetail="assetDetailList" :assetDetailTitle="'资产详情'" @closeViewHandle="isView = false"></AssetDetail>
    <AssetEdit :assetEditVisible="isEdit" :assetDetail="assetDetailList" :assetTitle="'资产编辑'" @closeEditHandle="closeEdit"></AssetEdit>
  </div>
</template>

<script>
import { getAssetList, delAsset, getAssetDetail, getCommon, } from "@/api/asset/list";
import AssetDetail from './assetDetail.vue'
import AssetEdit from './assetEdit.vue'

export default {
  name: "AssetList",
  components: {
    AssetDetail,
    AssetEdit,
  },
  data () {
    return {
      currentTabComponent: null,
      feedbackOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        assetName: null,
      },
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划编制表格数据
      assetList: [],
      // projectList: [],
      rowData: null,
      showPlanAndNodeDialog: false,
      // 遮罩层
      loading: true,
      resultOpen: false,
      multipleSelection: [],
      showAllSearch: false,
      isView: false,
      assetDetailList: {},
      assetDetailLists: [],
      assetClassificationList: [], // 资产类型下拉
      isEdit: false,
    }
  },
  mounted () {
    this.getList()
  },
  watch: {
    showAllSearch(data) {
      if(data){
        // 资产类型
        this.getCommonList('assetCategory')
      }
    }
  },
  methods: {
    handleQueryProject(value){

    },
    handleResultOpen(open){ // 打开、关闭成果文件选择弹窗
      this.resultOpen = open;
    },
    showPlanAndNodeDialogFun (row, column) {
      this.rowData = row;
    },
    // 查询资产列表
    getList () {
      this.loading = true
      // getAssetList(this.queryParams).then(response => {
      //   this.assetList = response.rows
      //   this.total = response.total
      //   this.loading = false
      // })

      // 自己模拟数据
      this.assetList = [
        {
          id: 1,
          assetCode: '发货的健身房'
        }
      ]
      this.loading = false
    },
    // 搜索按钮操作
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 导出
    handleExport () {
      this.download('asset/export', {
        ...this.queryParams
      }, `资产列表_${new Date().getTime()}.xlsx`)
    },
    handleDelete(row) {
      const ids = row.id;
      const nodeNames = row.assetName;
      this.$modal.confirm('是否确认删除"' + nodeNames + '"的数据项？').then(function() {
        return delAsset(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 更多筛选
    moreFilter () {
      this.showAllSearch = !this.showAllSearch
      if (!this.showAllSearch) {
        this.handleQuery()
      }
    },
    // 查看详情
    handleView(row) {
      this.isView = true
      getAssetDetail(row.id).then(res => {
        if(res){
          this.assetDetailList = res.data
        }
      })
    },
    // 关闭详情
    closeView(){
      this.isView = false
    },
    getCommonList(type){
      this.getDicts(type).then(res => {
        let data = res.data
        let list =  data.map(item => {
          return {
              label: item.dictLabel,
              value: item.dictValue,
            }
          }
        )
        if(type === 'assetCategory'){ // 资产类型
          this.assetClassificationList = list
        }
      })
    },
    handleEdit(row){
      this.isEdit = true
      getAssetDetail(row.id).then(res => {
        if(res){
          this.assetDetailList = res.data
        }
      })
    },
    closeEdit(){
      this.isEdit = false
    },
  }
};
</script>
<style lang="scss">
  .el-table__body tr.custom-row:hover > td {
    cursor: pointer;
  }
</style>

<style lang="scss" scoped>
  .search-btn{
    margin-left: 40px;
  }
</style>
