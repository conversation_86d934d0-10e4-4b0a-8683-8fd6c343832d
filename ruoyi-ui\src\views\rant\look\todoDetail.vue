<template>
  <div class="app-container">
    <div class="rant-container" v-loading="loading">
      <div class="rant-detail">
        <div class="rant-form-title"><img src="@/assets/rant/rant-info.png" class="icon">基本信息</div>
        <el-form class="rant-detail-form" label-position="right" label-width="120px">
          <el-row>
            <el-col :span="6">
              <el-form-item label="来源" prop="ranter">
                {{ rantDetail.ranterName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="类型" prop="mattersType">
                <section class="custom-matters-type">
                  <dict-tag v-for="(type, index) in rantDetail.mattersType?.split(',')" :key="index"
                            :options="dict.type.rant_matters_type" :value="type" style="height: 29px" />
                </section>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="分类" prop="rantClassify">
                <dict-tag :options="dict.type.rant_classify" :value="rantDetail.rantClassify" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="责任人" prop="responsiblePerson">
                {{ rantDetail.responsiblePersonName }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="责任部门" prop="deptName">
                {{ rantDetail.deptName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="责任部门负责人" prop="respDeptResponsiblerName">
                {{ rantDetail.respDeptResponsiblerName }}
              </el-form-item>
            </el-col>
            <el-col v-if="type != 'myRant'" :span="6">
              <el-form-item label="分管领导" prop="respDeptLeaderName">
                {{ rantDetail.respDeptLeaderName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计划完成时间" prop="planTime">
                {{ rantDetail.planTime }}
              </el-form-item>
            </el-col>
            <el-col v-if="type == 'matters'" :span="6">
              <el-form-item label="是否私密" prop="isPrivate">
                {{ rantDetail.isPrivate == 1 ? "是" : "否" }}
              </el-form-item>
            </el-col>
            <el-col v-if="type == 'matters'" :span="6">
              <el-form-item label="可见范围" prop="visibleScope">
                {{ rantDetail.visibleScope == null ? "所有人" : rantDetail.visibleScope }}
              </el-form-item>
            </el-col>
            <el-col v-if="type == 'matters'" :span="6">
              <el-form-item label="创建人" prop="createByName">
                {{ rantDetail.createByName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否结项" prop="isCompletion">
                {{ rantDetail.isCompletion ? "是" : "否" }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="内容" prop="rantContent">
                {{ rantDetail.rantContent }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="措施" prop="solution">
                {{ rantDetail.solution }}
              </el-form-item>
            </el-col>

          </el-row>
          <el-row>

          </el-row>
          <el-row>
            <el-col v-if="rantDetail.isCompletion" :span="8">
              <el-form-item label="结项时间" prop="closingTime">
                {{ rantDetail.closingTime || "--" }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="成果" prop="achievement">
                <template v-if="rantDetail.fileList">
                  <!-- <a v-for="(file, index) in rantDetail.fileList" :key="index" :href="file.url" class="link to-block"
                     target="_blank">
                    {{ file.name || "--" }}</a> -->
                    <file-link v-for="(file, index) in rantDetail.fileList" :key="index" :file="file" />
                </template>
                <span v-if="!rantDetail.fileList"> --</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="最新进展" prop="thisProgress">
                <!--                <div v-html="rantDetail.thisProgress"></div>-->
                <div v-html="rantDetail.thisProgress || '--'"></div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="rant-detail">
        <div class="rant-form-title"><img src="@/assets/rant/rant-progress.png" class="icon">进度情况</div>
        <el-table :data="recordDtoList" style="width: 100%">
          <el-table-column align="center" label="序号" prop="id" width="80" :header-row-class-name="'header-row'">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="实际进展" align="center" prop="actualProgress">
            <template slot-scope="scope">
              <div v-html="scope.row.actualProgress"></div>
            </template>
          </el-table-column>
          <el-table-column label="汇报时间" align="center" width="100" prop="feedbackTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.feedbackTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="成果" align="center" width="300" prop="achievementFileUrl">
            <template slot-scope="scope">
              <section class="file-wrapper">
                <!-- <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" class="link"
                   target="_blank">
                  {{ file.name || "--" }}</a> -->
                  <file-link v-for="(file, index) in scope.row.fileList" :key="index" :file="file" />
              </section>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
//http://localhost/rant/todoDetail?readTodoNoticeId=49
import { daiBanLookRankMatter, readDaiBanChange } from "@/api/rant/matters";

export default {
  name: "Deatil",
  dicts: ["rant_classify", "rant_matters_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
    };
  },
  mounted() {
    const readTodoNoticeId = this.$route.query.readTodoNoticeId;
    this.handleGetRantDetail(readTodoNoticeId);
  },
  methods: {
    handleGetRantDetail(readTodoNoticeId) {
      daiBanLookRankMatter(readTodoNoticeId).then((response) => {
        this.rantDetail = response.data;
        this.recordDtoList = response.data.recordDtoList;
        this.loading = false;
        //调用已阅接口
        readDaiBanChange(readTodoNoticeId).then((response) => {
        });
      });
    },
    handleClose() {
      this.$modal.confirm("确认是否关闭当前页面").then(() => {
        this.$store.dispatch("tagsView/delView", this.$route);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.cu-flex{
  display: flex;
  justify-content: center;
}
.score-text {
  color: #606266;
  font-size: 14px;
  min-width: 60px;
}
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

:deep(.el-form-item__content) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__header .el-table__cell) {
  background: rgba(54, 115, 255, 0.1);
}

:deep(.el-table__header .el-table__cell .cell) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__body .el-table__row .el-table__cell) {
  background-color: #F3F8FC;
}

:deep(.el-table__empty-block) {
  background-color: #F3F8FC;
}


.app-container {
  height: 100%;
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  border-radius: 0px 0px 0px 0px;
}

.rant-detail {
  //background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.7);

  &.base-info {}

  .rant-detail-form {
    padding: 0 16px;
  }
}

.rant-container {
  // max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;

  .rant-form-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
    display: flex;
    align-items: center;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .icon-primary {
    color: #409eff;
  }

  .cursor {
    cursor: pointer;
  }

  .display-none {
    display: none !important;
  }

  .file-wrapper {
    .el-icon-circle-close {
      margin-left: 10px;
    }
  }

  .link {
    color: #409eff;
  }
}

.transfer-item {
  display: flex;

  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}
</style>
