
ed092e9c63ad131962821b31fa6acdf95417992e	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"ab4d00999cafe340e357b7f26f9347a3\"}","integrity":"sha512-IeeuD5zSvnsaYNXJ5xSzbz4BqrIGu6GjFw7cp+N2Cb1N1gju6KhDZVG+OIancJ5mvDwwn/Z3Dt5j9ApP5VGoPg==","time":1754311745580,"size":12044140}