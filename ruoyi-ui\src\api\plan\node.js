import request from '@/utils/request'

// 查询计划-节点列表
export function listNode (query) {
  return request({
    url: '/plan/node/list',
    method: 'get',
    params: query
  })
}

export function listAllNode (query) {
  return request({
    url: '/plan/node/all',
    method: 'get',
    params: query
  })
}

// 查询计划-节点详细
export function getNode (id) {
  return request({
    url: '/plan/node/' + id,
    method: 'get'
  })
}

// 新增计划-节点
export function addNode (data) {
  return request({
    url: '/plan/node',
    method: 'post',
    data: data
  })
}

// 修改计划-节点
export function updateNode (data) {
  return request({
    url: '/plan/node',
    method: 'put',
    data: data
  })
}

// 删除计划-节点
export function delNode (id) {
  return request({
    url: '/plan/node/' + id,
    method: 'delete'
  })
}

// 编制保存计划-节点
export function save (data) {
  return request({
    url: '/plan/node/save',
    method: 'post',
    data: data
  })
}

// 调整保存计划-节点
export function adjustSave (data) {
  return request({
    url: '/plan/node/adjustSave',
    method: 'post',
    data: data
  })
}
// 计划编制导入节点文件
export function importNodeFile (data) {
  return request({
    url: '/plan/node/importData',
    method: 'post',
    data: data
  })
}
// 计划调整导入节点文件
export function importAdjustNodeFile (data) {
  return request({
    url: '/plan/node/importAdjustData',
    method: 'post',
    data: data
  })
}

// 编制提交
export function submit (data) {
  return request({
    url: '/plan/node/submit',
    method: 'post',
    data: data
  })
}

// 提交
export function adjustSubmit (data) {
  return request({
    url: '/plan/node/adjustSubmit',
    method: 'post',
    data: data
  })
}

// 查询计划-节点反馈人列表
export function feedbackUserList (query) {
  return request({
    url: '/plan/node/feedbackUserList',
    method: 'get',
    params: query
  })
}


// 计划节点-批量修改责任部门提交接口
export function editDepart (data) {
  return request({
    url: '/plan/node/editDepart',
    method: 'post',
    data: data
  })
}
// 计划节点-批量修改反馈人提交接口
export function editFeedbackUser (data) {
  return request({
    url: '/plan/node/editFeedbackUser',
    method: 'post',
    data: data
  })
}
// 调整列表
export function adjustList (query) {
  return request({
    url: '/plan/node/adjustList',
    method: 'get',
    params: query
  })
}
// 导出调整列表
export function exportAdjustmentNodeList (data) {
  return request({
    url: '/plan/node/exportAdjustmentNodeList',
    method: 'post',
    data
  })
}
