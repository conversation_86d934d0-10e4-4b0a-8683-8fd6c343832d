<script>
import * as echarts from 'echarts'

export default {
  name: 'Chart',
  props: {
    option: {
      type: Object,
      required: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'line'
    },
    resizeChartFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null,
      updateTimer: null,
      resizeTimer: null,
      chartShow: true
    }
  },
  watch: {
    option: {
      deep: true,
      handler(newOption) {
        console.log('Chart option changed:', newOption);
        this.debounceUpdate(newOption);
      }
    }
  },
  mounted() {
    console.log('Chart mounted, creating echarts instance');
    this.chart = echarts.init(this.$refs.el);
    this.$bus.$on('chartResize', () => {
      console.log('chartResize---');
      this.resizeChart();
    })
  },
  methods: {
    async waitForEl(maxAttempts = 100, interval = 300) {
      let attempts = 0;
      while (!this.$refs.el && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
        attempts++;
      }
      return !!this.$refs.el;
    },
    async waitForChartInstance(maxAttempts = 100, interval = 300) {
      let attempts = 0;
      while (!this.chart && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
        attempts++;
      }
      return !!this.chart;
    },
    resizeChart() {
      this.chartShow = false;
      this.chart = null; // 重要：重置 chart 实例
      setTimeout(async () => {
        this.chartShow = true;

        if (await this.waitForEl()) {
          this.chart = echarts.init(this.$refs.el);
          // 确保 Echarts 实例已经创建
          await this.waitForChartInstance();
          this.debounceUpdate(this.option);
        } else {
          console.warn('Failed to find chart element after multiple attempts');
        }
      }, 200);
    },
    clearChart() {
      if (this.chart) {
        this.chart.resize();
        this.chart.clear();
      }
    },
    // 防抖函数封装
    debounce(fn, delay = 300) {
      return (...args) => {
        if (this[`${fn.name}Timer`]) {
          clearTimeout(this[`${fn.name}Timer`])
        }
        this[`${fn.name}Timer`] = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    },

    // 使用防抖的更新和resize方法
    debounceUpdate(option) {
      this.debounce(this.updateChart)(option)
    },

    debounceResize() {
      this.debounce(this.handleResize)()
    },
    calculateFontSize(scale = 1) {
      const baseSize = 21;
      const width = window.innerWidth;
      return baseSize * (width / 1680) * scale
    },
    updateFontSize() {
      if (this.option.title && Array.isArray(this.option.title) && this.option.title.length > 0) {
        this.option.title[0].textStyle.fontSize = this.calculateFontSize();
        this.option.title[0].subtextStyle.fontSize = this.calculateFontSize(0.6);
      }
    },
    async updateChart(option) {
      if(!this.resizeChartFlag) {
        console.log('Skipping chart update: resizeChartFlag is false');
        return;
      }
      console.log('updateChart starting with option:', option);
      if (!await this.waitForEl()) {
        console.warn('Chart element not found');
        return;
      }

      // 确保 Echarts 实例已经创建
      if (!this.chart) {
        console.log('Creating new echarts instance');
        this.chart = echarts.init(this.$refs.el, null, {
          devicePixelRatio: window.devicePixelRatio // 自动适配设备像素比
        });
      }

      if (option) {
        console.log('Setting chart option');
        this.chart.clear();
        this.chart.setOption(option);
        this.chart.resize();
      } else {
        console.warn('No option provided to updateChart');
      }
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.debounceResize);
    if (this.updateTimer) clearTimeout(this.updateTimer);
    if (this.resizeTimer) clearTimeout(this.resizeTimer);
  }
}

</script>

<template>
    <div
      ref="el"
      class="chart-container"
      v-if="chartShow"
    ></div>
</template>

<style scoped>
.chart-container {
  z-index: 100;
  width: 100%;
  height: 100%;
}
</style>
