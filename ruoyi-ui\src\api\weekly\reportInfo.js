import request from '@/utils/request'

// 查询我的周报信息主列表
export function listInfo(query) {
  return request({
    url: '/weekly/weeklyReport/list',
    method: 'get',
    params: query
  })
}

// 查询审批周报信息主列表
export function approveList(query) {
  return request({
    url: '/weekly/weeklyReport/approveList',
    method: 'get',
    params: query
  })
}
//查询审批待办列表
export function approveDaiBanList(query) {
  return request({
    url: '/weekly/weeklyReport/approveDaiBanList',
    method: 'get',
    params: query
  })
}

// 获取审批待办周报集合
export function approveDaiBanInfo(data) {
  return request({
    url: '/weekly/weeklyReport/approveDaiBanInfo',
    method: 'post',
    data: data
  })
}


// 查询传阅周报信息主列表
export function circulateList(query) {
  return request({
    url: '/weekly/weeklyReport/circulateList',
    method: 'get',
    params: query
  })
}

// 查询周报信息主详细
export function getInfo(id) {
  return request({
    url: '/weekly/weeklyReport/' + id,
    method: 'get'
  })
}

// 查询周报信息主详细
export function getDaiBanInfo(id) {
  return request({
    url: '/weekly/weeklyReport/daiBanInfo/' + id,
    method: 'get'
  })
}

// 新增周报信息主
export function addInfo(data) {
  return request({
    url: '/weekly/weeklyReport',
    method: 'post',
    data: data
  })
}

// 新增周报信息主
export function addDraft(data) {
  return request({
    url: '/weekly/weeklyReport/draft',
    method: 'post',
    data: data
  })
}

// 草稿提交
export function submitDraft(data) {
  return request({
    url: '/weekly/weeklyReport/draftSubmit',
    method: 'post',
    data: data
  })
}

// 获取基础信息
export function addInit() {
  return request({
    url: '/weekly/weeklyReport/addInit',
    method: 'get',
  })
}


export function addInitNew(todoNoticeId) {
  return request({
    url: `/weekly/weeklyReport/addInit/${todoNoticeId}`,
    method: 'get',
  })
}


// 修改周报信息主
export function updateInfo(data) {
  return request({
    url: '/weekly/weeklyReport',
    method: 'put',
    data: data
  })
}

// 删除周报信息主
export function delInfo(id) {
  return request({
    url: '/weekly/weeklyReport/' + id,
    method: 'delete'
  })
}

// 周报审批
export function approveWeekly(data) {
  return request({
    url: '/weekly/weeklyReport/approve',
    method: 'post',
    data: data
  })
}

// 周报撤回
export function callbackWeekly(data) {
  return request({
    url: '/weekly/weeklyReport/revoke',
    method: 'post',
    data: data
  })
}

// 获取待办中周报集合
export function daiBanZbInfo(data) {
  /*{
    userName: "", // 用户名不能为空
    year: "", //年份不能为空
    week: "", // 第几周不能为空
  }*/
  return request({
    url: '/weekly/weeklyReport/daiBanZbInfo',
    method: 'post',
    data: data
  })
}

// 待办周报审阅
export function daiBanApprove(data) {
  /*{
    id: "", // 周报id不能为空
    approveDesc: "", //approveDesc
  }*/
  return request({
    url: '/weekly/weeklyReport/daiBanApprove',
    method: 'post',
    data: data
  })
}

// 待办周报完成审阅
export function daiBanFinish(data) {
  /*{
    userName: "", // 用户名不能为空
    year: "", //年份不能为空
    week: "", // 第几周不能为空
  }*/
  return request({
    url: '/weekly/weeklyReport/daiBanFinish',
    method: 'post',
    data: data
  })
}

// 周报驳回
export function weeklyReject(data) {
  /*{
    id: "", // 周报id不能为空
    approveDesc: "", //审阅意见

   */
  return request({
    url: '/weekly/weeklyReport/reject',
    method: 'post',
    data: data
  })
}

// 企业微信登录
export function wechatLogin(code) {
  return request({
    url: '/weekly/weeklyReport/login',
    method: 'post',
    data: {code}
  })
}

// 获取部门下拉树列表
export function deptTree(params) {
  return request({
    url: '/weekly/leader/dept/tree',
    method: 'get',
    params
  })
}

// 周报统计列表
export function staList(params) {
  return request({
    url: '/weekly/weeklyReport/statList',
    method: 'get',
    params
  })
}


// 周报统计列表导出
export function statExport(data) {
  return request({
    url: '/weekly/weeklyReport/statExport',
    method: 'post',
    data
  })
}

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 用于周报统计的人员选择接口
export function listUserNew(query) {
  return request({
    url: '/system/user/weeklyList',
    method: 'get',
    params: query
  })
}


// 群组周报列表
export function groupList(params) {
  return request({
    url: '/weekly/weeklyReport/groupList',
    method: 'get',
    params
  })
}
