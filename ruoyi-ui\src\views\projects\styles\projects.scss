.flex-1{
    flex: 1;
}
.mb-8{
  margin-bottom: 0.5rem; // 8px to rem
}
.mb-10{
  margin-bottom: 0.625rem; // 10px to rem
}
.mt-12{
    margin-top: 0.75rem; // 12px to rem
}
.mt-16{
  margin-top: 1rem; // 16px to rem
}
.mt-90{
  margin-top: 5.625rem; // 90px to rem
}
.mb-12{
    margin-bottom: 0.75rem; // 12px to rem
}
.mb-14{
    margin-bottom: 0.875rem; // 14px to rem
}
.mb-16{
    margin-bottom: 1rem; // 16px to rem
}
.mb-20{
  margin-bottom: 1.25rem; // 20px to rem
}
.mb-21{
    margin-bottom: 1.3125rem; // 21px to rem
}
.mb-24 {
    margin-bottom: 1.5rem; // 24px to rem
}
.mb-25{
  margin-bottom: 1.5625rem; // 25px to rem
}
.mb-30{
    margin-bottom: 1.875rem; // 30px to rem
}
.mb-32{
    margin-bottom: 2rem; // 32px to rem
}
.mb-43{
    margin-bottom: 2.6875rem; // 43px to rem
}
.mb-100px{
  margin-bottom: 3.25rem; // 100px to rem
}
.w-100{
  width: 100%;
}
.card-title{
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 0.875rem; // 14px to rem
  color: #222222;
  line-height: 1.8125rem; // 29px to rem
  text-align: left;
  font-style: normal;
  text-transform: none;
  position: relative;
  background-image: url('~@/views/projects/assets/images/title-bac.png');
  background-size: 5rem 0.5rem; // 80px to rem
  background-repeat: no-repeat;
  background-position: left bottom;  // 将背景图片向上移动4px
}
.card-title-2{
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 0.875rem; // 14px to rem
  color: #222222;
  line-height: 1.8125rem; // 29px to rem
  text-align: left;
  font-style: normal;
  text-transform: none;
  position: relative;
  background-image: url('~@/views/projects/assets/images/title-bac.png');
  background-size: 1.875rem 0.5rem; // 30px to rem
  background-repeat: no-repeat;
  background-position: left bottom;  // 将背景图片向上移动4px
}
.card{
  padding: 0.5rem 1rem;
  // padding: 1rem; // 16px to rem
  background: rgba(255,255,255,0.7);
}
.no-data{
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 0.875rem; // 14px to rem
  color: #666666;
  text-align: center;
  font-style: normal;
  text-transform: none;
  padding: 1.875rem; // 30px to rem
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-table{
  ::v-deep .el-table__row{
    .cell{
      //padding: 0.4rem;
      //line-height: 1.5rem;
    }
  }
  ::v-deep .el-table__header-wrapper{
    .cell{
      //padding: 0.4rem;
      //line-height: 1.5rem;
    }
    th{
      //height: 2.5rem;
    }
  }

}

// 使用 ::v-deep 来穿透样式作用域
.project-table ::v-deep {
  // 针对 WebKit 浏览器（如 Chrome 和 Safari）
  .el-table__body-wrapper::-webkit-scrollbar {
    height: 0.3rem; /* 滚动条的宽度 */
  }

  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 0.25rem;
    background-color: rgba(0,0,0,.2); /* 滚动条的背景颜色 */
  }

  .el-table__body-wrapper::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* 滚动条轨道的颜色 */
  }

  // 对于 Firefox
  .el-table__body-wrapper {
    scrollbar-width: thin;
    scrollbar-color: rgba(0,0,0,.2) #f1f1f1;
  }
}

.project-table ::v-deep  {
  border: 0.125rem solid #dfe6ec; // 修改表格边框颜色和宽度
  border-radius: 0.5rem; // 添加圆角

  // 修改表头边框
  .el-table__header-wrapper {
    border-bottom: 0.125rem solid #dfe6ec;
  }

  .el-table__cell{
    border-bottom: 0.125rem solid #dfe6ec;
    border-right: 0.125rem solid #dfe6ec;
  }
  .el-table__cell.is-leaf{
    border-bottom: 0.125rem solid #dfe6ec;
  }
  // 修改表格内容边框
  .el-table__body-wrapper {
    border: none; // 去掉表格主体边框
  }
  &::before{
    content: '';
    display: none
  }
  &::after{
    content: '';
    display: none
  }
  .el-table__empty-block{
    height: 100%;
    font-size: 1rem;

  }
  th{
    .cell{
      line-height: 1rem;
      padding-left: 0!important;
      padding-right: 0!important;
    }
  }
  td{
    .cell{
      line-height: 1rem;
      padding-left: 0!important;
      padding-right: 0!important;
    }
  }
}

.projects-content-container {
  // min-height: 100vh;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .desc{
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: #333333;
    line-height: 1.625rem;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}

.empty-status {
  width: 100%;
  padding-top: 10rem;
  text-align: center;

  img {
    margin-top: 7.5rem;
    width: 18.75rem;
    height: 12.5rem;
    // margin-bottom: 20px;
  }
}

.card-with-title{
  position: relative;
  .chart-title-block{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    text-align: center;
    z-index: 0;
    .title-1{
      font-size: 1.15rem;
    }
    .title-2{
      color: #aaaaaa;
      font-size: 0.75rem;
    }
  }
}
.trend-chart-title{
  text-align: right;
  font-size: 0.75rem;
  color: #aaaaaa;
  margin-right: 3rem;
}
.left-middle{
  margin-left: 50%;
  transform: translateX(-50%);
}

