<!DOCTYPE html>
<html>
    <head>
        <title>SheetJS Live Grid Demo</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <link rel="icon" type="image/png" href="assets/img/logo.png" />
        <link rel="stylesheet" href="assets/css/sheetjs.css">
    </head>
    <body>
        <script src="assets/vendor/alertify.js"></script>
        <script src="assets/vendor/jquery.min.js"></script>
        <script src="assets/vendor/jquery.handsontable.full.js"></script>

        <link rel="stylesheet" media="screen" href="assets/vendor/jquery.handsontable.full.css">
        <link rel="stylesheet" media="screen" href="assets/vendor/samples.css">
        <link rel="stylesheet" media="screen" href="assets/vendor/alertify.css">

        <div id="body">
            <div id="left">
            <div id="drop">Drop a file here</div>
            <h3> Choose a worksheet:</h3>
            <div id="buttons"></div>
        </div>
        <div id="right">
            <div id="header">
                <pre id="out"></pre>
                <h2>SheetJS In-Browser Live Grid Demo</h2>
            </div>
            <div id="hot" style="overflow: scroll" class="handsontable"></div>
        </div>

        <script src="assets/js/shim.js"></script>
        <script src="assets/js/xlsx.full.min.js"></script>
        <script src="assets/js/dropsheet.js"></script>
        <script src="assets/js/main.js"></script>

        <script src="assets/vendor/spin.js"></script>
    </body>
</html>
