<template>
  <div class="building-viewer-container" ref="container">
    <div class="image-wrapper" @mouseenter="onMouseEnterImage" @mouseleave="onMouseLeaveImage">
      <img :src="buildingConfigPic" class="building-image" ref="imageRef" @error="onImageError"  @load="onImageLoad" draggable="false"/>
      <div
        v-for="(item, index) in buildingConfigList"
        v-if="enterImage"
        :key="index"
        class="building-annotation"
        :style="[getAnnotationStyle(item), { opacity: hoveredItem === item ? '1' : '0' }]"
        @mouseenter="showTooltip(item, $event)"
        @mouseleave="hideTooltip"
      >
      </div>
    </div>

    <!-- Tooltip -->
    <div v-show="tooltipVisible" class="tooltip-building" :style="tooltipStyle">
      <div v-if="currentItem">
        <!-- 使用辅助函数displayValue来处理空值显示 -->
        <div><span>楼栋名称：</span>{{ displayValue(currentItem.buildingName) }}</div>
        <div><span>总货值：</span>{{ displayValue(currentItem.allhzAmount) }}亿</div>
        <div><span>总套数：</span>{{ displayValue(currentItem.allhzTs) }}</div>
        <div><span>已售套数：</span>{{ displayValue(currentItem.yshzTs) }}</div>
        <div><span>剩余套数：</span>{{ displayValue(currentItem.wshzTs) }}</div>
        <div><span>套数去化比例：</span>{{ displayValue(currentItem.rate) }}%</div>
      </div>
    </div>
  </div>
</template>

<script>
import { saveImageToDB, getImageFromDB }  from '@/views/projects/utils/imageCache';
import Utils from '@/views/projects/utils/index'
import API from '@/views/projects/api'
import {getImage} from "@/views/projects/api/common";

export default {
  props: {
    buildingConfigPic: {
      type: String,
      required: true
    },
    buildingConfigList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      hoveredItem: null,
      imageRect: {width: 0, height: 0, left: 0, top: 0},
      tooltipVisible: false,
      tooltipStyle: {},
      currentItem: null,
      imgSrc: '',
      enterImage: false
    }
  },
  watch: {
    buildingConfigPic: {
      handler(newVal) {
        if(!!newVal){
          this.loadOrCacheImage(newVal);
        }
      },
      immediate: true
    }
  },
  mounted() {

    // this.getImageData(this.buildingConfigPic);
  },
  methods: {
    onMouseEnterImage() {
      console.log('onMouseEnterImage')
      this.enterImage = true
    },
    onMouseLeaveImage() {
      console.log('onMouseLeaveImage')
      this.enterImage = false
    },
    onImageError(e) {
      console.error('Image load error:', e);
    },
    async getImageData(imageUrl){
      return await API.Common.getImage({imageUrl})
    },
    async loadOrCacheImage() {
      try {
        // 1. 生成简短 key
        const key = Utils.generateHashKey(this.buildingConfigPic);

        // 2. 先尝试从 IndexedDB 获取
        let cachedData = await getImageFromDB(key);
        if (cachedData) {
          // 如果之前存的是 Blob 则需要转换成对象URL
          if (cachedData instanceof Blob) {
            this.imgSrc = URL.createObjectURL(cachedData);
          } else if (typeof cachedData === 'string') {
            // 如果存的是 base64
            this.imgSrc = cachedData;
          }
          return;
        }

        // 3. 如果不存在，则 fetch 新的图片
        // const response = await fetch(this.buildingConfigPic);
        const response = await this.getImageData(this.buildingConfigPic);
        // const blob = await response.blob();
        const blob = new Blob([response], { type: 'image/jpeg' }); // 或 'image/png'
        // console.log('blob----', blob)

        // 4a. 直接用 Blob 存储
        await saveImageToDB(key, blob);
        this.imgSrc = URL.createObjectURL(blob);

        // （可选）4b. 如果想存 Base64，看下列示例
        // const base64Data = await this.blobToBase64(blob);
        // await saveImageToDB(key, base64Data);
        // this.imgSrc = base64Data;

      } catch (error) {
        console.error('图片缓存出现错误：', error);
      }
    },

    // 可选：将 Blob 转为 base64
    async blobToBase64(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (err) => reject(err);
        reader.readAsDataURL(blob);
      });
    },
    onImageLoad() {
      this.$nextTick(() => {
        const img = this.$refs.imageRef
        const rect = img.getBoundingClientRect()
        this.imageRect = {
          width: rect.width,
          height: rect.height,
          left: rect.left,
          top: rect.top
        }
      })
    },
    getAnnotationStyle(item) {
      const x = (item.axCoordinate / 100) * this.imageRect.width
      const y = (item.ayCoordinate / 100) * this.imageRect.height
      const w = (item.axLength / 100) * this.imageRect.width
      const h = (item.ayLength / 100) * this.imageRect.height

      return {
        position: 'absolute',
        left: x + 'px',
        top: y + 'px',
        width: w + 'px',
        height: h + 'px',
        border: '0.125rem solid red',
        background: 'rgba(255,0,0,0.2)',
        cursor: 'pointer'
      }
    },
    showTooltip(item, event) {
      this.hoveredItem = item
      this.currentItem = item
      this.tooltipVisible = true

      // 获取容器的位置信息
      const containerRect = this.$refs.container.getBoundingClientRect()
      const targetRect = event.target.getBoundingClientRect()

      // 计算相对于容器的位置
      const tooltipX = targetRect.left - containerRect.left + (targetRect.width / 2)
      const tooltipY = targetRect.top - containerRect.top

      this.tooltipStyle = {
        position: 'absolute',
        left: tooltipX < 0 ? 0 : tooltipX + 'px',
        top: tooltipY + 'px',
        // transform: 'translate(-50%, -100%)',
        transform: 'translate(0, -100%)',
        background: 'rgba(0,0,0,0.8)',
        color: '#ffffff',
        padding: '0.5rem 1rem',
        borderRadius: '0.3rem',
        whiteSpace: 'nowrap',
        zIndex: 9999
      }
    },
    hideTooltip() {
      this.tooltipVisible = false
      this.currentItem = null
    },
    displayValue(val) {
      // 值为null或空字符串时返回'--'
      if (val === null || val === '') {
        return '--'
      }
      return val
    }
  }
}
</script>

<style scoped>
.building-viewer-container {
  position: relative;
  display: inline-block;
  width: 100%;
  overflow: visible;
}

.image-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  border: 0.125rem solid #ccc;
  user-select: none;
}

.building-image {
  display: block;
  width: 100%;
  height: 100%;
}

.building-annotation {
  position: absolute;
  /* 样式在JS中动态计算 */
}

.tooltip-building {
  position: absolute;
  pointer-events: none;
}
</style>
