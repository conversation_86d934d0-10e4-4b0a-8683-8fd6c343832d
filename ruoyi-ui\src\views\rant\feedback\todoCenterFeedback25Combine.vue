<!--25号推送进度反馈，一个合并的页面-->
<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container" v-loading="loading">
      <div v-for="(rant, index) in rantList" :key="index" class="rant-content">
        <div class="section-title" v-if="rantList.length > 1">
          <span>
            <img src="@/assets/rant/rant-item.png" class="icon" alt="">
          督办事项{{ index + 1 }}
          </span>
          <section class="expand" @click="toggleCollapse(index)">
            <img src="@/assets/rant/rant-collapse.png" class="icon" :class="{'is-active': collapseStates[index]}"
                 alt="">
            {{ !collapseStates[index] ? '收起' : '展开' }}
          </section>
        </div>
        <template v-if="!collapseStates[index]">
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-info.png" class="icon">基本信息</div>
            <el-form class="rant-detail-form" label-position="right" label-width="120px" :rules="rules" :model="rant">
              <el-row>
                <!--                <el-col :span="6">
                                  <el-form-item label="来源" prop="ranter">
                                    {{ rant.ranterName }}
                                  </el-form-item>
                                </el-col>-->
                <el-col :span="6">
                  <el-form-item label="类型" prop="mattersType">
                    <section class="custom-matters-type">
                      <dict-tag v-for="(type, index) in rant.mattersType?.split(',')" :key="index"
                                :options="dict.type.rant_matters_type" :value="type" style="height: 29px"/>
                    </section>

                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分类" prop="rantClassify">
                    <dict-tag :options="dict.type.rant_classify" :value="rant.rantClassify"/>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任人" prop="responsiblePerson">
                    {{ rant.responsiblePersonName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="责任部门" prop="deptName">
                    {{ rant.deptName }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

                <el-col :span="6">
                  <el-form-item label="责任部门负责人" prop="respDeptResponsiblerName">
                    {{ rant.respDeptResponsiblerName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分管领导" prop="respDeptLeaderName">
                    {{ rant.respDeptLeaderName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="计划完成时间" prop="planTime">
                    {{ rant.planTime }}
                  </el-form-item>
                </el-col>
                <el-col v-if="type == 'matters'" :span="6">
                  <el-form-item label="是否私密" prop="isPrivate">
                    {{ rant.isPrivate == 1 ? "是" : "否" }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="内容" prop="rantContent">
                    {{ rant.rantContent }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="措施" prop="solution">
                    {{ rant.solution }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

              </el-row>
              <div class="rant-form-title">
                <img src="@/assets/rant/rant-this-progress.png" class="icon">
                本次进展
                <span class="required-icon">*</span>
              </div>
              <editor v-model="rant.thisProgress" :readOnly="isRead" :height="192" class="width-100 mb-16"/>
              <el-row>
                <el-col :span="6">
                  <el-form-item label="是否结项" prop="isCompletion" class="custom-form-item-bac"
                                :rules="[{ required: true, message: '请选择是否结项', trigger: 'blur' }]">
                    <el-select v-model="rant.isCompletion" placeholder="请选择" :disabled="isRead">
                      <el-option label="是" :value="true"></el-option>
                      <el-option label="否" :value="false"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="rant.isCompletion" :span="6">
                  <el-form-item label="结项时间" prop="closingTime" class="custom-form-item-bac"
                                :rules="[{ required: rant.isCompletion, message: '请选择结项时间', trigger: ['blur', 'change'] }]">
                    <el-date-picker clearable v-model="rant.closingTime" type="date" :disabled="isRead"
                                    class="width-100-important"
                                    value-format="yyyy-MM-dd" placeholder="选择结项时间"
                                    :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="成果" prop="achievement" class="custom-form-item-bac"
                                :rules="[{ required: rant.isCompletion, message: '请上传成果文件', trigger: 'blur' }]">
                    <section class="file-wrapper" :class="{'custom-disabled': isRead}">
                      <label v-if="!rant.fileList || rant.fileList.length === 0" :for="'uploadFile_' + index"
                             class="cursor"
                             :class="{
                              'custom-warning': !!rantAchievementFiles.fileListError,
                            }">
                        <i class="el-icon-folder-add" style="color: #3673FF;font-size: 16px;"></i>
                        {{ rantAchievementFiles.fileListError || "请上传文件" }}
                      </label>
                      <section v-else @click="triggerFileUpload(index)">
                        <div v-for="(file, index) in rant.fileList" :key="index" class="file-show">
                          <a :href="file.url" class="link" target="_blank" :title="file.name">{{
                              file.name || "--"
                            }}</a>
                          <i class="el-icon-circle-close cursor link"
                             @click.stop="handleDeleteAnnexUrl(index, rant.fileList)"></i>
                        </div>
                      </section>
                    </section>
                    <input type="file" v-if="!isRead" class="display-none" multiple :id="'uploadFile_' + index"
                           @change="(event) => uploadFileHandle(event, index)"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="rant-detail">
            <div class="rant-form-title"><img src="@/assets/rant/rant-progress.png" class="icon">进度情况</div>
            <el-table :data="rant.recordDtoList" style="width: 100%">
              <el-table-column align="center" label="序号" prop="id" width="80" :header-row-class-name="'header-row'">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="实际进展" align="center" prop="actualProgress">
                <template slot-scope="scope">
                  <div v-html="scope.row.actualProgress"></div>
                </template>
              </el-table-column>
              <el-table-column label="汇报时间" align="center" width="100" prop="feedbackTime">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.feedbackTime, "{y}-{m}-{d}") }}</span>
                </template>
              </el-table-column>
              <el-table-column label="成果" align="center" width="300" prop="achievementFileUrl">
                <template slot-scope="scope">
                  <section style="display: flex; flex-direction: column">
                    <a v-for="(file, index) in scope.row.fileList" :key="index" :href="file.url" class="link"
                       target="_blank">
                      {{ file.name || "--" }}</a>
                  </section>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-buttons" v-if="!isRead">
      <el-button class="custom-btn-save" type="primary" @click="handleSave(0)">保 存</el-button>
      <el-button class="custom-btn-submit" type="primary" @click="handleSubmit(1)">提交</el-button>
    </div>
  </div>
</template>
<script>
// http://localhost/rant/todoCenterFeedback25Combine?todoNoticeId=3
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}

import {getMatters, feedbackRankMatterList, submitFeedback} from "@/api/rant/matters";
import {submitTodoFeedback} from "@/api/rant/record";
import {uploadFileMultiple} from "@/api/rant/common";
import mixin from "@/views/rant/mixins/mixin";
export default {
  name: "Feedback",
  mixins: [mixin],
  dicts: ["rant_classify", "rant_completion_status", "rant_matters_type"],
  data() {
    return {
      // 遮罩层
      loading: false,
      rantList: [], // 吐槽详情
      recordList: [], // 进度记录
      fileList: [],
      rantAchievementFiles: {}, // 附件信息
      uploadFileLoading: false,
      isCompletion: false,
      collapseStates: [], // 控制每个事项的折叠状态
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      hoverScore: null,
      rules: {
        closingTime: [
          {required: true, message: "请选择结项时间", trigger: "blur"}
        ],
      },
      isRead: false,
      isEnable: null,
      limitDay: null,
    };
  },

  mounted() {
    const todoNoticeId = this.$route.query.todoNoticeId;
    this.handleGetRantList(todoNoticeId);
    // 添加自动滚动到顶部
  },
  async created() {
    await this.getLimitDayIsEnable();
  },
  methods: {
    async getLimitDayIsEnable() {
      let isEnable = await this.getConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
      let limitDay = await this.getConfigKey("rant.finish.date.limit.day"); // 设置限制天数
      this.isEnable = parseInt(isEnable.msg);
      this.limitDay = parseInt(limitDay.msg);
    },
    toggleCollapse(index) {
      this.$set(this.collapseStates, index, !this.collapseStates[index]);
    },
    handleGetRantList(todoNoticeId) {
      this.loading = true;
      feedbackRankMatterList(todoNoticeId).then((response) => {
        // this.rantList = response.data;
        this.rantList = response?.data?.rankMattersFeedbackDtoList || [];
        this.isRead = response?.data?.isRead;
        // 初始化折叠状态，默认折叠
        this.collapseStates = new Array(this.rantList.length).fill(true);
        if (this.collapseStates.length > 0) {
          this.collapseStates[0] = false; // 设置第一个元素为 false
        }
        this.$nextTick(() => {
          const container = document.querySelector('html');
          if (container) {
            container.scrollTop = 0;
          }
        });

      }).finally(() => {
        this.loading = false;
      });
    },

    uploadFileHandle(event, index) {
      const files = event.target.files;
      if (!files) {
        //
        // 没有选择文件
        return;
      }
      this.loading = true;
      this.uploadFileLoading = true;
      const formData = new FormData();
      for (let item of files) {
        formData.append("files", item);
      }
      uploadFileMultiple(formData)
        .then((response) => {
          console.log(response);
          if (!this.rantList[index].fileList) this.rantList[index].fileList = [];
          this.rantList[index].fileList = [...this.rantList[index].fileList, ...response.data];
          console.log(this.rantList[index].fileList);
          this.$forceUpdate();
          this.uploadFileLoading = false;
        })
        .catch((error) => {
          this.uploadFileLoading = false;
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDeleteAnnexUrl(index, fileList, event) {
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      fileList.splice(index, 1);
    },
    triggerFileUpload(index) {
      if (!this.isRead) {
        document.getElementById(`uploadFile_${index}`).click();
      }
    },
    handleSubmit(submitStatus) {
      for (let i = 0; i < this.rantList.length; i++) {
        this.rantList[i].submitStatus = submitStatus;
        this.rantList[i].rantMattersId = this.rantList[i].id;
        if (!this.rantList[i].thisProgress) {
          this.$modal.msgError("督办事项" + (i + 1) + ":请输入本次进展");
          this.loading = false;
          return;
        }
        if (this.rantList[i].isCompletion) {
          if (!this.rantList[i].closingTime) {
            this.$modal.msgError("督办事项" + (i + 1) + ":请输入结项时间");
            this.loading = false;
            return;
          }
          if (!this.rantList[i].fileList || this.rantList[i].fileList.length === 0) {
            this.$modal.msgError("督办事项" + (i + 1) + ":请上传成果文件");
            this.loading = false;
            return;
          }
        }
      }
      this.submitForm(submitStatus)
    },
    handleSave(submitStatus) {
      this.submitForm(submitStatus)
    },
    /** 提交按钮 */
    submitForm(submitStatus) {
      this.loading = true;
      submitTodoFeedback({
        todoNoticeId: this.$route.query.todoNoticeId,
        rantList: this.rantList.map(item => {
          return {
            todoNoticeId: this.$route.query.todoNoticeId,
            id: item.id,
            lastProgressId: item.lastProgressId,
            rantMattersId: item.id,
            thisProgress: item.thisProgress,
            isCompletion: item.isCompletion,
            closingTime: item.closingTime,
            fileList: item.fileList,
            submitStatus: submitStatus,
          }
        }),
      }).then((response) => {
        if (submitStatus === 0) {
          this.$modal.msgSuccess("保存成功");
        } else {
          this.$modal.msgSuccess("提交成功，即将关闭页面");
          setTimeout(() => {
            close();
          }, 1500);
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.ql-toolbar.ql-snow) {
  background: rgba(54, 115, 255, 0.05);
}

:deep(.custom-editor) {
  .el-form-item__content {
    width: 100%;
  }
}

.expand {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #3673FF;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    transition: transform 0.3s;

    &.is-active {
      transform: rotate(180deg);
    }
  }
}

// 添加折叠展开动画
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease-out;
  max-height: 1000px; // 设置一个足够大的高度
  overflow: hidden;
}

.fade-slide-enter,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.rant-content {
  transition: all 0.3s ease-out;
  background: rgba(255, 255, 255, 0.7);
  margin-bottom: 16px;
  border-radius: 8px 8px 8px 8px;
}

:deep(.custom-form-item-bac input) {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(55, 109, 247, 0.3);
  background: #F1F7FE;
}

:deep(.custom-form-item-bac .el-form-item__label) {
  line-height: 36px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

:deep(.el-form-item__content) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.el-table__header .el-table__cell) {
  //background: rgba(54, 115, 255, 0.05);
  background: rgba(54, 115, 255, 0.1);

}

:deep(.el-table__header .el-table__cell .cell) {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 16px;
  font-style: normal;
  text-transform: none;

}

:deep(.el-table__body .el-table__row .el-table__cell) {
  background-color: #F3F8FC;
}

:deep(.el-table__empty-block) {
  background-color: #F3F8FC;
}

.custom-btn-save {
  width: 160px;
  background: #C8DDFA;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  color: #3673FF;
}

.custom-btn-submit {
  width: 160px;
  background: #3673FF;
  border-radius: 4px 4px 4px 4px;
  color: #FFFFFF;
}

.app-container {
  //max-height: 90vh;
  //height: (100vh- 56px);
  //overflow-y: auto;
  //overflow-x: hidden;
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  overflow-y: auto;
}

.rant-detail-content {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 0px 0px 8px 8px;
  border: 1px solid #FFFFFF;
  padding: 16px;
}

.rant-detail {
  margin-bottom: 44px;
  padding: 19px 16px 0 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.rant-container {
  height: (100vh- 56px);
  overflow-y: auto;

  /*  .rant-form-title {
     margin-bottom: 20px;
     background-color: #f8f8f9;
     padding: 10px;
     width: 100%;
   } */
  .rant-form-title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 18px;
    display: flex;
    align-items: center;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }

  .icon-primary {
    color: #409eff;
  }

  .cursor {
    cursor: pointer;
  }

  .display-none {
    display: none !important;
  }

  .file-wrapper {
    width: 400px;
    min-height: 36px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(55, 109, 247, 0.3);

    > label {
      display: inline-block;
      width: 100%;
      height: 100%;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 36px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 12px;
    }

    .el-icon-circle-close {
      margin-left: 10px;
    }
  }

  .link {
    color: #409eff;
    margin-bottom: 4px;
    margin-top: 4px;
    margin-left: 8px;
  }
}

.transfer-item {
  display: flex;

  .item {
    //width: 25%;
    text-align: center;
    margin-right: 5px;
  }
}

.rant-content {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  // background-color: #f1ee10;
  .el-icon-arrow-down {
    transition: transform 0.3s;

    &.is-active {
      transform: rotate(-180deg);
    }
  }

  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0px 4px 20px 0px rgba(55, 109, 247, 0.1);
  border-radius: 8px 8px 0px 0px;
  border: 1px solid #FFFFFF;
  //background: rgba(54, 115, 255, 0.2);
  padding: 13px 16px;
  //margin: 20px 0 0;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  // background-color: #fff;
  text-align: center;
  z-index: 1000;
  //background: rgba(255,255,255,0.5);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  background-color: #EDF7F9;
}

// 为了防止底部按钮遮挡内容，给容器添加底部内边距
.rant-container {
  padding-bottom: 60px;
}

.collapse-transition-enter-active,
.collapse-transition-leave-active {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.collapse-transition-enter,
.collapse-transition-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  height: 0;
}

.collapse-transition-enter-to,
.collapse-transition-leave {
  opacity: 1;
  height: auto;
}
</style>
