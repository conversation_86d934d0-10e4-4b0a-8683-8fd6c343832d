
0cf715c5e98f9aa48ddc6cb29ae2fabc314b194c	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e8331f7012b7d03dea0341fbd41bb943\"}","integrity":"sha512-+442HgRx3pJLkkl9nbQ24dsJ90axuxueWLmNWApBLlWGzxUAq/BxhiodHAbLklfeEzcHenM35aeg7ynaMgtfXw==","time":1754311999441,"size":12043662}