<template>
  <project-info-form
    mode="edit"
    :id="id"
    @submit="handleSubmit"
  />
</template>

<script>
import ProjectInfoForm from './ProjectInfoForm'
import { updateInfo } from "@/api/projectOperate/projectInfo"

export default {
  name: "EditProjectInfo",
  components: {
    ProjectInfoForm
  },
  data() {
    return {
      id: null
    }
  },
  created() {
    this.id = this.$route.params.id
  },
  methods: {
    handleSubmit(formData) {
      updateInfo(formData).then(response => {
        this.$modal.msgSuccess("修改成功")
        this.$bus.$emit('refreshProjectInfoList')
        const obj = {
          path: `/project/dashboard/project`,
        }
        this.$tab.closeOpenPage(obj)
      })
    }
  }
}
</script>
