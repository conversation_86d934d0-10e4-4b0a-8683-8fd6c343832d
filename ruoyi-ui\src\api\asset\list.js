import request from '@/utils/request'

// 查询资产列表
export function getAssetList (query) {
  return request({
    url: '/asset/info/list',
    method: 'get',
    params: query
  })
}

// 删除资产
export function delAsset(id) {
  return request({
    url: '/asset/info/delete/' + id,
    method: 'delete'
  })
}

// 资产详情
export function getAssetDetail(id) {
  return request({
    url: '/asset/info/' + id,
    method: 'get'
  })
}

// 编辑资产
export function editAsset(data) {
  return request({
    url: '/asset/info/edit',
    method: 'post',
    data: data
  })
}
