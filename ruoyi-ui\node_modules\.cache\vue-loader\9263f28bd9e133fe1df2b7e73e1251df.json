{"remainingRequest": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\src\\views\\projects\\views\\sales.vue", "mtime": 1754312350764}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743920556726}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743920555671}, {"path": "E:\\KZDProjects\\YuanJing@zhidi@planSystem\\ruoyi-cloud-zjzd\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743920555710}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sales.vue"], "names": [], "mappings": ";AA8rBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "sales.vue", "sourceRoot": "src/views/projects/views", "sourcesContent": ["<template>\r\n  <section class=\"projects-content-container w-100\">\r\n    <!-- 无权限状态 -->\r\n    <div v-if=\"!hasPermi(['project:sales:manage'])\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-auth.png\" alt=\"无权限\">\r\n      <div class=\"desc\">暂无权限</div>\r\n      <div class=\"desc\">请联系管理员</div>\r\n    </div>\r\n    <!-- 无数据状态 -->\r\n    <div v-else-if=\"!hasData\" class=\"empty-status\">\r\n      <img src=\"@/views/projects/assets/images/no-data.png\" alt=\"无数据\">\r\n      <div class=\"desc\">暂无数据</div>\r\n    </div>\r\n    <section v-else class=\"w-100\">\r\n      <div class=\"sales-content\">\r\n        <!-- 全周期销售进度模块 -->\r\n        <div class=\"sales card mb-16 mt-16\" v-loading=\"loadingSales\">\r\n          <div class=\"card-title\">\r\n            全周期销售进度\r\n              <span class=\"total-target\" v-if=\"hasPermi(['sales:all:price'])\">全周期住宅均价 ¥ {{ allQyData.zzUnitPrice || '--' }}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">签约</div>\r\n                <Empty :no-authority=\"hasPermi(['sales:all:qy'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"qyProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': qyProgressOption.title.textStyle.color}\">\r\n                            {{ qyProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ qyProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allQyData.qyAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计签约</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计签约</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzQyAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzDyAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allQyData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"flex-item hk-item\">\r\n              <div class=\"progress-item\">\r\n                <div class=\"progress-item-title\">回款</div>\r\n<!--                <div class=\"progress-item-title\">回款（权益）</div>-->\r\n                <Empty :no-authority=\"hasPermi(['sales:all:hk'])\">\r\n                  <div class=\"progress-item-content\">\r\n                  <div class=\"progress-item-content-item\">\r\n                    <div class=\"progress-item-content-chart\">\r\n                      <section class=\"card-with-title\">\r\n                        <Chart :option=\"hkProgressOption\" class=\"chart\"></Chart>\r\n                        <section class=\"chart-title-block\">\r\n                          <div class=\"title-1\" :style=\"{'color': hkProgressOption.title.textStyle.color}\">\r\n                            {{ hkProgressOption.title.text }}\r\n                          </div>\r\n                          <div class=\"title-2\">{{ hkProgressOption.title.subtext }}</div>\r\n                        </section>\r\n                      </section>\r\n                      <div class=\"progress-item-content-detail\">\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.dynamicAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">动态货值</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">动态货值（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">¥ {{ $toFixed2(allHkData.hkAmount) }} 亿</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">累计回款</div>\r\n<!--                          <div class=\"progress-item-content-detail-item-value\">累计回款（权益）</div>-->\r\n                        </div>\r\n                        <div class=\"progress-item-content-detail-item\">\r\n                          <div class=\"progress-item-content-detail-item-title\">{{ allHkData.qyRate }}%</div>\r\n                          <div class=\"progress-item-content-detail-item-value\">权益比例</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"progress-item-content-data\">\r\n                      <table>\r\n                        <tr>\r\n                          <th></th>\r\n                          <th class=\"progress-label\">住宅</th>\r\n                          <th class=\"progress-label\">非住</th>\r\n                        </tr>\r\n                        <tbody>\r\n                        <tr>\r\n                          <td class=\"progress-label\">动态货值</td>\r\n<!--                          <td class=\"progress-label\">动态货值（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzDynamicAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzDynamicAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">累计回款</td>\r\n<!--                          <td class=\"progress-label\">累计回款（权益）</td>-->\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzHkAmount) }} 亿</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzHkAmount) }} 亿</td>\r\n                        </tr>\r\n                        <tr>\r\n                          <td class=\"progress-label\">完成比例</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.zzRate) }}%</td>\r\n                          <td class=\"progress-text\">{{ $toFixed2(allHkData.noZzRate) }}%</td>\r\n                        </tr>\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </Empty>\r\n\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 全周期进度偏差模块 -->\r\n        <div class=\"time-progress card mb-16 mt-16\">\r\n          <div class=\"card-title\">全周期进度偏差</div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetQYProgress\">\r\n              <div class=\"target-title mb-4rem\">签约</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:all:qyDeviation'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': signDataByTime.openDate ? `'${signDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': signDataByTime.liquidateDate ? `'${signDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue sign-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdRate), 'sign-tooltip-1')\">\r\n                          <div>时点完成{{$toFixed2(signDataByTime.qyAmount)}}亿（{{$toFixed2(signDataByTime.allSdRate)}}%）</div>\r\n                          <div>全盘动态货值{{$toFixed2(signDataByTime.dynamicAmount)}}亿</div>\r\n                          <div>偏差率{{$toFixed2(signDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(signDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow  sign-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(signDataByTime.allSdTargetRate),  'sign-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(signDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(signDataByTime.targetAmount)}}亿</div>\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(signDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(signDataByTime.zzQyAmount)}}亿（{{$toFixed2(signDataByTime.zzSdRate)}}%)</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(signDataByTime.zzDynamicAmount)}}亿</td>\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(signDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(signDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{ signDataByTime.zzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{ $formatNull(signDataByTime.zzLiquidateDate) }}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td  class=\"progress-label\">非住: 时点完成{{$toFixed2(signDataByTime.noZzQyAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdRate)}}%)</td>\r\n                      <td>| 动态货值{{$toFixed2(signDataByTime.noZzDynamicAmount)}}亿</td>\r\n                      <td>| 时点目标{{$toFixed2(signDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(signDataByTime.noZzSdTargetRate)}}%)</td>\r\n                      <td>| 目标货值{{$toFixed2(signDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td> | 清盘日期{{ signDataByTime.noZzQpDate }}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"pl-date\">清盘日期 {{ $formatNull(signDataByTime.noZzLiquidateDate) }}</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div></Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\" v-loading=\"loadingAllTargetHKProgress\">\r\n              <div class=\"target-title mb-4rem\">回款</div>\r\n<!--              <div class=\"target-title mb-4rem\">回款（权益）</div>-->\r\n              <Empty :no-authority=\"hasPermi(['sales:all:hkDeviation'])\"><div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number-de\"\r\n                       :style=\"{\r\n                    '--timeline-before-content': hkDataByTime.openDate ? `'${hkDataByTime.openDate}'` : `'--'`,\r\n                    '--timeline-after-content': hkDataByTime.liquidateDate ? `'${hkDataByTime.liquidateDate}'` : `'--'` }\">\r\n                    <div class=\"marker blue\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress blue  hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdRate), 'hk-tooltip-1')\">\r\n                      <div>时点完成{{$toFixed2(hkDataByTime.hkAmount)}}亿（{{$toFixed2(hkDataByTime.allSdRate)}}%）</div>\r\n                      <div>全盘动态货值{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>\r\n<!--                      <div>全盘动态货值（权益）{{$toFixed2(hkDataByTime.dynamicAmount)}}亿</div>-->\r\n                      <div>偏差率{{$toFixed2(hkDataByTime.deviationRate)}}%</div>\r\n                    </div>\r\n                    <div class=\"down\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdRate))\">\r\n                    </div>\r\n\r\n                    <div class=\"marker yellow\"\r\n                         :style=\"{left: `${filterRatioMax(hkDataByTime.allSdTargetRate)}%`}\">\r\n                    </div>\r\n                    <div class=\"tooltip-progress yellow hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(hkDataByTime.allSdTargetRate), 'hk-tooltip-2')\">\r\n                      <div>时点目标{{$toFixed2(hkDataByTime.allSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.allSdTargetRate)}}%）</div>\r\n                      <div>全盘目标货值{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>\r\n<!--                      <div>全盘目标货值（权益）{{$toFixed2(hkDataByTime.targetAmount)}}亿</div>-->\r\n                    </div>\r\n                    <div class=\"up\"\r\n                         :style=\"getArrowStyle(filterRatioMax(hkDataByTime.allSdTargetRate))\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 时点完成{{$toFixed2(hkDataByTime.zzHkAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.zzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.zzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.zzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.zzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.zzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\">清盘日期 {{$formatNull(hkDataByTime.zzLiquidateDate)}}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 时点完成{{$toFixed2(hkDataByTime.noZzHkAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 动态货值{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 动态货值（权益）{{$toFixed2(hkDataByTime.noZzDynamicAmount)}}亿</td>-->\r\n                      <td class=\"progress-text\">| 时点目标{{$toFixed2(hkDataByTime.noZzSdTargetAmount)}}亿（{{$toFixed2(hkDataByTime.noZzSdTargetRate)}}%）</td>\r\n                      <td class=\"progress-text\">| 目标货值{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>\r\n<!--                      <td class=\"progress-text\">| 目标货值（权益）{{$toFixed2(hkDataByTime.noZzTargetAmount)}}亿</td>-->\r\n<!--                      <td class=\"progress-text\"> | 清盘日期{{hkDataByTime.noZzQpDate}}</td>-->\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-text pl-date\"> 清盘日期 {{$formatNull(hkDataByTime.noZzLiquidateDate)}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <!-- <div>住宅: 时点完成{{hkDataByTime.zzHkAmount}}亿（{{hkDataByTime.allSdRate}}%） | 住宅动态货值{{hkDataByTime.zzDynamicAmount}}亿 | 时点目标{{hkDataByTime.zzSdTargetAmount}}亿（{{hkDataByTime.zzSdRate}}%） | 住宅目标货值{{hkDataByTime.targetAmount}}亿 | 清盘日期{{hkDataByTime.zzQpDate}}</div>\r\n                  <div>非住: 时点完成{{hkDataByTime.noZzHkAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住动态货值{{hkDataByTime.noZzDynamicAmount}}亿 | 时点目标{{hkDataByTime.noZzSdTargetAmount}}亿（{{hkDataByTime.noZzSdRate}}%） | 非住目标货值{{hkDataByTime.noZzTargetAmount}}亿 | 清盘日期{{hkDataByTime.noZzQpDate}}</div> -->\r\n                </div>\r\n              </div>\r\n            </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年度销售进度模块 -->\r\n        <div class=\"year-progress card mb-16 mt-16\" v-loading=\"loadingYearProgress\">\r\n          <div class=\"card-title\">\r\n            年度销售进度\r\n            <span class=\"total-target\" v-if=\"hasPermi(['sales:year:price'])\">年度住宅均价 ¥ {{ $toFixed2(yearQyData.zzUnitPrice)}}元</span>\r\n          </div>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度签约目标 ¥ {{ $toFixed2(yearQyData.targetAmount)}} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:qy'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <!-- 刻度线 -->\r\n                    <div\r\n                      class=\"marker blue\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.ratio)}%`}\"\r\n                    ></div>\r\n                    <!-- 提示框 -->\r\n                    <div class=\"tooltip-progress blue year-tooltip-1\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.ratio), 'year-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearQyData.totalAmount) }} 亿 ({{ $toFixed2(yearQyData.ratio) }}%)</span>\r\n                    </div>\r\n                    <!-- 三角形 -->\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearQyData.ratio))\"></div>\r\n                    <div\r\n                      class=\"marker yellow\"\r\n                      :style=\"{left: `${filterRatioMax(yearQyData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-tooltip-2\" :style=\"getTooltipStyle(filterRatioMax(yearQyData.dayRatio), 'year-tooltip-2')\">\r\n                      <span>时间进度：{{ yearQyData.month }}月 ({{ $toFixed2(yearQyData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearQyData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度签约目标{{ $toFixed2(yearQyData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计签约金额{{ $toFixed2(yearQyData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearQyData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度签约目标{{ $toFixed2(yearQyData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计签约金额{{ $toFixed2(yearQyData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearQyData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"target-title\">年度回款目标 ¥ {{ $toFixed2(yearHkData.targetAmount) || '--' }} 亿</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:year:hk'])\">\r\n                <div class=\"progress-timeline\">\r\n                <div class=\"time-line-wrapper\">\r\n                  <span class=\"label\">全盘</span>\r\n                  <div class=\"timeline number\">\r\n                    <div class=\"marker blue\" :style=\"{ left: `${filterRatioMax(yearHkData.ratio)}%` }\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress blue year-hk-tooltip-1\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.ratio), 'year-hk-tooltip-1')\">\r\n                      <span>销售进度：{{ $toFixed2(yearHkData.totalAmount) }} 亿 ({{ $toFixed2(yearHkData.ratio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"down\" :style=\"getArrowStyle(filterRatioMax(yearHkData.ratio))\"></div>\r\n\r\n                    <div class=\"marker yellow\" :style=\"{ left: `${filterRatioMax(yearHkData.dayRatio)}%`}\"\r\n                    ></div>\r\n                    <div class=\"tooltip-progress yellow year-hk-tooltip-2\"\r\n                         :style=\"getTooltipStyle(filterRatioMax(yearHkData.dayRatio), 'year-hk-tooltip-2')\">\r\n                      <span>时间进度：{{ yearHkData.month }}月 ({{ $toFixed2(yearHkData.dayRatio) }}%)</span>\r\n                    </div>\r\n                    <div class=\"up\" :style=\"getArrowStyle(filterRatioMax(yearHkData.dayRatio))\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"details\">\r\n                  <table>\r\n                    <tr>\r\n                      <td class=\"progress-label\">住宅: 年度回款目标{{ $toFixed2(yearHkData.zzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | 累计回款金额{{ $toFixed2(yearHkData.zzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-text\"> | {{ $toFixed2(yearHkData.zzRatio) }}%</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td class=\"progress-label\">非住: 年度回款目标 {{ $toFixed2(yearHkData.noZzTargetAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | 累计回款金额 {{ $toFixed2(yearHkData.noZzTotalAmount) }} 亿</td>\r\n                      <td class=\"progress-label\"> | {{ $toFixed2(yearHkData.noZzRatio) }}%</td>\r\n                    </tr>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 款齐模块 -->\r\n        <div class=\"receiving-payments mb-16\" v-loading=\"loadingPayments\">\r\n          <div class=\"card content-block\">\r\n            <div class=\"card-title-2\">款齐</div>\r\n            <Empty :no-authority=\"hasPermi(['sales:kq'])\">\r\n              <div class=\"basic-info-item flex-container\">\r\n              <div class=\"circle-chart chart-size\">\r\n                <section class=\"card-with-title\">\r\n                  <Chart :option=\"kqProgressOption\" class=\"chart-size\"></Chart>\r\n                  <section class=\"chart-title-block\">\r\n                    <div class=\"title-1\" :style=\"{'color': kqProgressOption.title.textStyle.color}\">\r\n                      {{ kqProgressOption.title.text }}\r\n                    </div>\r\n                    <div class=\"title-2\">{{ kqProgressOption.title.subtext }}</div>\r\n                  </section>\r\n                </section>\r\n              </div>\r\n              <div class=\"card-wrapper\">\r\n                <div class=\"data-card\" v-for=\"item in cardData\"\r\n                     :style=\"{ backgroundColor: item.color, boxShadow: `0px 8px 20px 0px ${item.color}` }\">\r\n                  <div class=\"card-label\">{{ item.label }}</div>\r\n                  <br/>\r\n                  <div class=\"card-value\">\r\n                    <span class=\"currency\">¥ </span>\r\n                    <span>{{ $toFixed2(item.value) }} 亿</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            </Empty>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表格数据模块 -->\r\n        <div class=\"table-container mb-16\" v-loading=\"loadingTables\">\r\n          <div class=\"card-title mb-16\">销售分析</div>\r\n          <Empty :no-authority=\"hasPermi(['sales:data'])\">\r\n            <CardTabSales v-model=\"queryTypeSign\" :tabs=\"queryTypeSignList\" @click=\"handleTabSign\" class=\"mb-14\"/>\r\n          <el-table\r\n            :data=\"qyData\"\r\n            style=\"width: 100%\"\r\n            :border=\"true\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.5rem'\r\n                  }\"\r\n            :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                     lineHeight: '1.5rem'\r\n                  }\"\r\n            class=\"project-table mb-16\">\r\n            <!-- 签约table -->\r\n            <el-table-column align=\"center\" label=\"累计签约\">\r\n              <el-table-column label=\"金额\" prop=\"qyAmount\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.qyAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.qyAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"qyArea\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  <span>{{ $toFixed2(scope.row.qyArea)}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"套数\" prop=\"qyNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"12.5%\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"累计到访转化率\">\r\n              <el-table-column label=\"到访人数\" prop=\"dfNum\" align=\"center\" min-width=\"12.5%\"/>\r\n              <el-table-column label=\"转化率\" prop=\"ratio\" align=\"center\" min-width=\"12.5%\">\r\n                <template #default=\"scope\">\r\n                  {{ $toFixed2(scope.row.ratio) }}%\r\n                </template>\r\n              </el-table-column>\r\n            </el-table-column>\r\n          </el-table>\r\n          <!-- 认购table -->\r\n          <el-table\r\n            :data=\"rgData\"\r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n                  :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.5rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: '1.2rem'\r\n                  }\"\r\n            class=\"mb-16 project-table\">\r\n            <el-table-column align=\"center\" label=\"累计认购\" :colspan=\"6\">\r\n              <el-table-column label=\"金额\" prop=\"rgAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    queryTypeSign === 1 || queryTypeSign === 2\r\n                      ? (scope.row.rgAmount / 10000).toFixed(2) + '万'\r\n                      : (scope.row.rgAmount / 100000000).toFixed(2) + '亿'\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"rgArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"tsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"zzNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中退房套数\" prop=\"backNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中换房套数\" prop=\"changeNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n\r\n            <el-table-column align=\"center\" label=\"截至当前认未签\" :colspan=\"5\">\r\n              <el-table-column label=\"金额\" prop=\"noQyTotalAmount\" align=\"center\" min-width=\"10\">\r\n                <template #default=\"scope\">\r\n                  {{\r\n                    (scope.row.noQyTotalAmount / 100000000).toFixed(2) + '亿'\r\n\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"面积\" prop=\"noQyTotalArea\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"套数\" prop=\"noQyTsNum\" align=\"center\" min-width=\"10\"/>\r\n              <el-table-column label=\"其中住宅套数\" prop=\"noQyZzNum\" align=\"center\" min-width=\"10\"/>\r\n            </el-table-column>\r\n          </el-table>\r\n          </Empty>\r\n\r\n\r\n          <div class=\"cost flex-container\" style=\"min-height: 26.5rem;\">\r\n            <div class=\"flex-item chart\">\r\n              <div class=\"card-title mb-10\">签约业态分布</div>\r\n              <!--              <div class=\"sign-chart-block\">-->\r\n              <Empty :no-authority=\"hasPermi(['sales:bussDist'])\">\r\n                <!-- <Chart class=\"\" :option=\"getQYYTOption(qyYTData)\" v-if=\"qyYTData.length > 0\"></Chart> -->\r\n                <Chart class=\"\" :option=\"qyYTOption\"></Chart>\r\n                <!-- <div v-else class=\"no-data\">暂无数据</div> -->\r\n              </Empty>\r\n              <!--              </div>-->\r\n            </div>\r\n            <div class=\"flex-item\" style=\"min-width:0;\">\r\n              <div class=\"card-title mb-10\">货值分布表</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:goodsValue'])\">\r\n                <!-- max-height: 30rem; -->\r\n                <el-table\r\n                class=\"project-table\"\r\n                :data=\"analysisData\"\r\n                style=\"width: 100%;max-height: 30rem; \"\r\n                :border=\"true\"\r\n                :span-method=\"handleSpanMethod\"\r\n                height=\"'30rem'\"\r\n                :header-cell-style=\"{\r\n                    background: '#E1EFFD',\r\n                    color: '#666666',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    fontSize: '0.875rem',\r\n                    height: '2rem',\r\n                  }\"\r\n                :cell-style=\"{\r\n                    borderColor: 'rgba(0,106,255,0.2);',\r\n                    fontWeight: '400',\r\n                    padding: '0.2rem 0',\r\n                    height: '2rem',\r\n                    fontSize: '0.875rem',\r\n                  }\"\r\n              >\r\n                <el-table-column label=\"业态\" prop=\"projectType\" align=\"center\" min-width=\"8\"/>\r\n                <el-table-column label=\"户型\" prop=\"hxName\" align=\"center\" min-width=\"12\"/>\r\n                <el-table-column label=\"总套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"totalNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"allTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"allBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"总货值(万元)\" prop=\"allHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"已售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"ysNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"顶层\" prop=\"ysTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"ysBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                  <el-table-column label=\"已售货值(万元)\" prop=\"ysHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n                <el-table-column label=\"未售套数\" align=\"center\">\r\n                  <el-table-column label=\"合计\" prop=\"wsNum\" align=\"center\" min-width=\"8\"/>\r\n                  <el-table-column label=\"其中\" align=\"center\">\r\n                    <el-table-column label=\"已供未售\" prop=\"ygwsNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"未供\" prop=\"wgNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"顶层\" prop=\"wsTopNum\" align=\"center\" min-width=\"8\"/>\r\n                    <el-table-column label=\"底层\" prop=\"wsBottomNum\" align=\"center\" min-width=\"8\"/>\r\n                  </el-table-column>\r\n                </el-table-column>\r\n                <el-table-column label=\"未售货值(万元)\" prop=\"wsHzAmount\" min-width=\"12\" align=\"center\"></el-table-column>\r\n              </el-table>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n        <!-- 趋势图模块 -->\r\n        <div class=\"trend mb-16\" v-loading=\"loadingTrends\">\r\n          <div class=\"card-title mb-16\">趋势分析</div>\r\n          <CardTabSales v-model=\"queryTypeTrend\" :tabs=\"queryTypeTrendList\" @click=\"handleTrendTab\" class=\"mb-14\"/>\r\n          <div class=\"flex-container\">\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">到访趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:dfTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ dfOptionName }}</div>\r\n                <Chart :option=\"dfOption\" class=\"trend-chart\"></Chart>\r\n              </Empty>\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">认购趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:rgTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ rgOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabRGTrend === index }]\"\r\n                    @click=\"selectTabRGTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"rgOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"rgProjectType\" :tabs=\"projectTypeList\" @click=\"handleRgTab\" class=\"mb-14\"/>\r\n                <!--            <CardTabSales v-model=\"activeTabRGTrend\" :tabs=\"tabs\" @click=\"selectTabRGTrend\" class=\"mb-14\"/>-->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title mb-25\">签约趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:qyTrend'])\">\r\n                <div class=\"trend-chart-title\">{{ qyOptionName }}</div>\r\n              <section class=\"flex-row-container\">\r\n                <div class=\"vertical-tabs\">\r\n                  <div\r\n                    v-for=\"(tab, index) in tabs\"\r\n                    :key=\"index\"\r\n                    :class=\"['tab', { active: activeTabQYTrend === index }]\"\r\n                    @click=\"selectTabQYTrend(index)\"\r\n                  >\r\n                    <div class=\"tab-text\">{{ tab }}</div>\r\n                  </div>\r\n                </div>\r\n                <Chart :option=\"qyOption\" class=\"trend-chart\"></Chart>\r\n              </section>\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"qyProjectType\" :tabs=\"projectTypeList\" @click=\"handleQyTab\" class=\"mb-14\"/>\r\n                <!-- <CardTabSales v-model=\"activeTabQYTrend\" :tabs=\"tabs\" @click=\"selectTabQYTrend\" class=\"mb-14\"/> -->\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n            <div class=\"flex-item\">\r\n              <div class=\"chart-title  mb-25\">回款趋势</div>\r\n              <Empty :no-authority=\"hasPermi(['sales:hkTrend'])\">\r\n                <div class=\"trend-chart-container\">\r\n                <div class=\"trend-chart-title\">{{ hkOptionName }}</div>\r\n                <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart>\r\n              </div>\r\n              <!-- <Chart :option=\"hkOption\" class=\"trend-chart\"></Chart> -->\r\n              <section class=\"flex-row-container\">\r\n                <CardTabSales v-model=\"hkProjectType\" :tabs=\"projectTypeList\" @click=\"handleHKTab\" class=\"mb-14\"/>\r\n              </section>\r\n              </Empty>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </section>\r\n\r\n</template>\r\n<script>\r\nimport ProgressBar from '@/views/projects/components/ProgressBar.vue'\r\nimport CumulativeIndicatorsCard from '@/views/projects/components/CumulativeIndicatorsCard.vue'\r\nimport ProgressCircle from '@/views/projects/constants/ProgressCircle'\r\nimport Chart from '@/views/projects/components/Chart.vue'\r\nimport BarOption from '@/views/projects/constants/bar'\r\nimport PieOption from '@/views/projects/constants/Pie'\r\nimport API from '@/views/projects/api'\r\nimport CardTabSales from \"@/views/projects/components/CardTabSales.vue\";\r\nimport Empty from \"@/views/projects/components/empty.vue\";\r\n\r\nexport default {\r\n  name: 'Sales',\r\n  components: {\r\n    CumulativeIndicatorsCard,\r\n    ProgressBar,\r\n    Chart,\r\n    CardTabSales,\r\n    Empty\r\n  },\r\n  data() {\r\n    return {\r\n      yearQyData: {\r\n        /*ratio: 0,\r\n        dayRatio: 100,\r\n        totalAmount: 0,\r\n        month: 0,\r\n        zzTotalAmount: 0,\r\n        noZzTotalAmount: 0*/\r\n      }, // 年度销售进度-年度签约数据\r\n      yearHkData: {}, // 年度销售进度-年度回款数据\r\n      signDataByTime: { // 序时销售进度-签约\r\n      },\r\n      loadingAllTargetQYProgress: false,\r\n      loadingAllTargetHKProgress: false,\r\n      hkDataByTime: { // 序时销售进度-回款\r\n      },\r\n      queryTypeSignList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 4,\r\n          name: '年'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        },\r\n      ],\r\n      queryTypeSign: 2,\r\n      qyData: [],\r\n      rgData: [],\r\n      ////////////\r\n      queryTypeTrendList: [\r\n        {\r\n          code: 0,\r\n          name: '全周期'\r\n        },\r\n        {\r\n          code: 3,\r\n          name: '月'\r\n        },\r\n        {\r\n          code: 2,\r\n          name: '周'\r\n        },\r\n        {\r\n          code: 1,\r\n          name: '日'\r\n        }\r\n      ],\r\n      queryTypeTrend: 2,\r\n      hkTrendData: {}, // 回款趋势图数据\r\n      qyTrendData: {}, // 签约趋势图数据\r\n      rgTrendData: {}, // 认购趋势图数据\r\n      dfTrendData: {}, // 到访趋势图数据\r\n      hkTrendRawData: {}, // 回款趋势图原始数据\r\n      qyTrendRawData: {}, // 签约趋势图原始数据\r\n      rgTrendRawData: {}, // 认购趋势图原始数据\r\n      dfTrendRawData: {}, // 到访趋势图原始数据\r\n      ProgressCircleOption: new ProgressCircle().getOption(),\r\n      cardData: [ // 款齐\r\n        {\r\n          label: '年度目标',\r\n          value: '--',\r\n          color: '#53BD88'\r\n        },\r\n        {\r\n          label: '年度款齐',\r\n          value: '--',\r\n          color: '#7B6FF2'\r\n        },\r\n        {\r\n          label: '可结利',\r\n          value: '--',\r\n          color: '#3EB6CF'\r\n        }\r\n      ],\r\n      projectCode: '',\r\n      projectTypeList: [], // 业态集合\r\n      hkProjectType: '',\r\n      rgProjectType: '',\r\n      qyProjectType: '',\r\n      queryType: 2, // （0-全盘 1-今日 2-本周 3-本月）\r\n      allQyData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, //全盘销售进度 - 签约\r\n      allHkData: {\r\n        \"dynamicAmount\": '-',\r\n        \"qyAmount\": '-',\r\n        \"rate\": '-',\r\n        \"zzDynamicAmount\": '-',\r\n        \"zzQyAmount\": '-',\r\n        \"zzRate\": '-',\r\n        \"noZzDynamicAmount\": '-',\r\n        \"noZzDyAmount\": '-',\r\n        \"noZzRate\": '-',\r\n        \"zzUnitPrice\": '-'\r\n      }, // 全盘销售进度 - 回款\r\n      kqData: {},\r\n      qyYTData: [], // 签约业态分布\r\n      tabs: [\r\n        '套数',\r\n        '金额',\r\n        '面积'\r\n      ],\r\n      activeTabRGTrend: 0, // 套数\r\n      activeTabQYTrend: 0, // 套数\r\n      tooltipStyles: {\r\n        blue: {left: '0px'},\r\n        yellow: {left: '0px'}\r\n      },\r\n      dfOption: {},\r\n      rgOption: {},\r\n      qyOption: {},\r\n      hkOption: {},\r\n      analysisData: [], // 货值分析数据\r\n      hasData: true,\r\n      isDeveloping: false, // 控制是否显示开发中状态\r\n\r\n      // 添加loading状态变量\r\n      loadingSales: false,\r\n      loadingTimeProgress: false,\r\n      loadingYearProgress: false,\r\n      loadingPayments: false,\r\n      loadingTables: false,\r\n      loadingTrends: false,\r\n      hkOptionName: '',\r\n      dfOptionName: '',\r\n      rgOptionName: '',\r\n      qyOptionName: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.projectCode = this.$route.query.projectCode\r\n  },\r\n  async mounted() {\r\n    await this.getProjectType(this.projectCode);\r\n    this.initTrend(); // 初始化趋势图\r\n    this.getAllQyData({ //全盘销售进度 - 签约\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getAllHkData({ //全盘销售进度 - 回款\r\n      queryType: 0,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getkqData({ // 款齐\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQyDfData({ // 累���签约\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getRgData({ // 累计认购\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.getQYYTData({ // 签约业态分布\r\n      queryType: this.queryTypeSign,\r\n      projectCode: this.projectCode\r\n    });\r\n    this.fetchAnalyse(this.projectCode) // 获取货值分析数据\r\n    // 年度销售进度-年度签约数据\r\n    this.getYearQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 年度销售进度-年度回款数据\r\n    this.getYearHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-签约数据\r\n    this.getAllTargetQyData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n    // 全周期进度偏差-回款数据\r\n    this.getAllTargetHkData({\r\n      queryType: 4,\r\n      projectCode: this.projectCode\r\n    });\r\n\r\n  },\r\n  methods: {\r\n    hasPermi(permission) {\r\n      const permissions = this.$store.getters.permissions\r\n      // Check for all permissions wildcard first\r\n      if (permissions.includes('*:*:*')) {\r\n        return true\r\n      }\r\n      // Check specific permissions\r\n      return permissions.some(p => permission.includes(p))\r\n    },\r\n    filterRatioMax(ratio) {\r\n      if (ratio > 100) {\r\n        ratio = 100;\r\n      } else if (ratio < 0) {\r\n        ratio = 0;\r\n      }\r\n      return ratio;\r\n    },\r\n    getArrowStyle(ratio) {\r\n      return {left: `${ratio}%`};\r\n    },\r\n    getTooltipStyle(ratio, tooltipClass) {\r\n      const scale = window.innerWidth / 1680;\r\n      // const tooltipWidth = 170 * scale; // tooltip的宽度(px)\r\n      const tooltipWidth = document.querySelector(`.${tooltipClass}`)?.clientWidth || 0; // tooltip的宽度(px)\r\n      const containerWidth = document.querySelector('.timeline')?.clientWidth || 0;\r\n      const tooltipRatio = (tooltipWidth / containerWidth) * 100;\r\n      if (ratio == '--') { // raio为--时，tooltip靠边\r\n        return {\r\n          left: `0`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n\r\n      // 超出右边界\r\n      if ((Math.ceil(ratio) + Math.ceil(tooltipRatio) / 2) > 100) {\r\n        return {\r\n          // right: `0`,\r\n          right: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // bordrRadius: `0 0 0 0`\r\n\r\n      }\r\n      // 超出左边界\r\n      else if ((Math.ceil(ratio) - Math.floor(tooltipRatio) / 2) <= 0) {\r\n        return {\r\n          left: `-0.725rem`,\r\n          textAlign: 'left',\r\n        };\r\n        // borderRadius: `0 0  0 0`\r\n\r\n      }\r\n\r\n      // console.log('ratio---', ratio);\r\n      // console.log('tooltipRatio---', tooltipRatio);\r\n      if (ratio >= 50) {\r\n        return {\r\n          // right: `${100 - ratio - 8}%`,\r\n          right: `${100 - ratio}%`,\r\n          transform: `translateX(50%)`,\r\n          textAlign: 'left'\r\n        };\r\n      }\r\n      return {\r\n        left: `${ratio}%`,\r\n        transform: `translateX(-50%)`,\r\n        textAlign: 'left'\r\n      };\r\n    },\r\n    formatValue(value) {\r\n      const absValue = Math.abs(value);\r\n      let formattedValue;\r\n      let unit = '';\r\n\r\n      if (absValue >= 100000000) {\r\n        formattedValue = (absValue / 100000000).toFixed(2);\r\n        unit = '亿';\r\n      } else if (absValue >= 10000) {\r\n        formattedValue = (absValue / 10000).toFixed(2);\r\n        unit = '万';\r\n      } else {\r\n        formattedValue = absValue.toFixed(2);\r\n      }\r\n\r\n      return {\r\n        formattedValue: value < 0 ? `-${formattedValue}` : formattedValue,\r\n        unit\r\n      };\r\n    },\r\n    selectTabQYTrend(index) {\r\n      this.activeTabQYTrend = index;\r\n      // this.activeTabQYTrend = item.code;\r\n      this.updateQYTrendData();\r\n    },\r\n    getQyOption() {\r\n      this.qyOption = this.getBarOption(['#FF9B47'], this.qyTrendData);\r\n      if (this.activeTabQYTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.qyOption.yAxis[1].name = '单位：万元';\r\n          this.qyOptionName = '单位：万元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          //  this.qyOption.yAxis[1].name = '单位：亿元';\r\n          this.qyOptionName = '单位：亿元';\r\n          this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.qyOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabQYTrend === 0) {\r\n        // this.qyOption.yAxis[1].name = '单位：套';\r\n        this.qyOptionName = '单位：套';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      } else if (this.activeTabQYTrend === 2) {\r\n        //  this.qyOption.yAxis[1].name = '单位：m²';\r\n        this.qyOptionName = '单位：m²';\r\n        this.qyOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.qyOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.qyOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    updateQYTrendData() {\r\n      const data = this.qyTrendRawData;\r\n      switch (this.activeTabQYTrend) {\r\n        case 0: // 套数\r\n          this.filteredQYTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredQYTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredQYTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredQYTrendData = data;\r\n      }\r\n      this.qyTrendData.series = this.filteredQYTrendData;\r\n      this.getQyOption();\r\n    },\r\n    selectTabRGTrend(index) {\r\n      this.activeTabRGTrend = index;\r\n      this.updateRGTrendData();\r\n    },\r\n    getRgOption() {\r\n      this.rgOption = this.getBarOption(['#376DF7'], this.rgTrendData)\r\n    },\r\n    updateRGTrendData() {\r\n      const data = this.rgTrendRawData;\r\n      switch (this.activeTabRGTrend) {\r\n        case 0: // 套数\r\n          this.filteredRGTrendData = data.map(item => item.num);\r\n          break;\r\n        case 1: // 金额\r\n          this.filteredRGTrendData = data.map(item => item.value);\r\n          break;\r\n        case 2: // 面积\r\n          this.filteredRGTrendData = data.map(item => item.area);\r\n          break;\r\n        default:\r\n          this.filteredRGTrendData = data;\r\n      }\r\n      this.rgTrendData.series = this.filteredRGTrendData;\r\n      this.getRgOption();\r\n      if (this.activeTabRGTrend === 1) {\r\n        if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n          // this.rgOption.yAxis[1].name = '单位：万元';\r\n          this.rgOptionName = '单位：万元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 10000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 10000).toFixed(2);\r\n          }\r\n        } else {\r\n          // this.rgOption.yAxis[1].name = '单位：亿元';\r\n          this.rgOptionName = '单位：亿元';\r\n          this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n            return (value / 100000000).toFixed(2);\r\n          }\r\n          this.rgOption.series[0].label.formatter = function (params) {\r\n            return (params.value / 100000000).toFixed(2);\r\n          }\r\n        }\r\n      } else if (this.activeTabRGTrend === 0) {\r\n        // this.rgOption.yAxis[1].name = '单位：套';\r\n        this.rgOptionName = '单位：套';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n          ;\r\n        }\r\n      } else if (this.activeTabRGTrend === 2) {\r\n        // this.rgOption.yAxis[1].name = '单位：m²';\r\n        this.rgOptionName = '单位：m²';\r\n        this.rgOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return value;\r\n        }\r\n        this.rgOption.series[0].label.formatter = function (params) {\r\n          return params.value;\r\n        }\r\n      }\r\n      this.rgOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getQYYTData(data) { // 签约业态分布\r\n      const res = await API.Sales.bussDist(data);\r\n      this.qyYTData = res.data;\r\n    },\r\n    getQYYTOption(data) { // 签约业���分布chart option\r\n      const option = this.getPieOption();\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n      const scale = window.innerWidth / 1680;\r\n      const rich = {\r\n        yellow: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0],\r\n          align: 'center'\r\n        },\r\n        total: {\r\n          color: \"#ffc72b\",\r\n          fontSize: 40 * scale,\r\n          align: 'center'\r\n        },\r\n        labelColor: {\r\n          color: \"#333333\",\r\n          align: 'center',\r\n          fontSize: 12 * scale,\r\n          padding: [2, 0]\r\n        },\r\n        blue: {\r\n          color: '#49dff0',\r\n          fontSize: 16 * scale,\r\n          align: 'center'\r\n        },\r\n        hr: {\r\n          borderColor: '#0b5263',\r\n          width: '100%',\r\n          borderWidth: 1 * scale,\r\n          height: 0,\r\n          margin: [5, 0]\r\n        }\r\n      }\r\n      option.series[0].data = data.map(item => {\r\n        return {\r\n          value: this.$toFixed2(item.rate),\r\n          name: item.bussName,\r\n          ...item\r\n        }\r\n      });\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">名称: ${params.data.bussName}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">套数: ${params.data.num}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">面积: ${params.data.area}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n      // option.series[0].avoidLabelOverlap = true\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示连接线\r\n        minTurnAngle: 90, // 限制转折角度，防止错位\r\n        normal: {\r\n          length: 20 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: data.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle', // 设置图例为小圆点\r\n      };\r\n      option.series[0].label = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        // overflow: 'truncate', // 防止标签超出容器\r\n        distance: 5 * scale,\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n      return option;\r\n    },\r\n    async getAllHkData(data) { // 全盘销售进度 - 回款\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allHkData(data);\r\n        this.allHkData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    async getAllQyData(data) { // 全盘销售进度 - 签约\r\n      this.loadingSales = true;\r\n      try {\r\n        const res = await API.Sales.allQyData(data);\r\n        this.allQyData = res.data;\r\n      } finally {\r\n        this.loadingSales = false;\r\n      }\r\n    },\r\n    handleTrendTab(item) { // 趋势图查询类型切换\r\n      this.queryTypeTrend = item.code;\r\n      this.initTrend(); // 初始化趋势图\r\n    },\r\n    updateCSSVariable(value) {\r\n      document.documentElement.style.setProperty('--timeline-before-content', value);\r\n    },\r\n    async getAllTargetQyData(data){ // 全周期进度偏差-签约数据\r\n      this.loadingAllTargetQYProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetQyData(data);\r\n        // 使用$formatNull对res.data中的所有数据字段格式化\r\n        this.signDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetQYProgress = false;\r\n      }\r\n    },\r\n    async getAllTargetHkData(data) { // 全周期进度偏差-回款数据\r\n      this.loadingAllTargetHKProgress = true;\r\n      try {\r\n        const res = await API.Sales.allTargetHkData(data);\r\n        this.hkDataByTime = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingAllTargetHKProgress = false;\r\n      }\r\n    },\r\n    async getYearQyData(data) { // 年度销售进度-年度签约数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetQyData(data);\r\n        this.yearQyData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getYearHkData(data) { // 年度销售进度-年度回款数据\r\n      this.loadingYearProgress = true;\r\n      try {\r\n        const res = await API.Sales.yearTargetHkData(data);\r\n        this.yearHkData = Object.fromEntries(\r\n          Object.entries(res.data).map(([key, value]) => [key, this.$formatNull(value)])\r\n        );\r\n        // this.yearHkData = res.data;\r\n      } finally {\r\n        this.loadingYearProgress = false;\r\n      }\r\n    },\r\n    async getRgData(data) { // 累计认购\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.rgData(data);\r\n        this.rgData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    async getQyDfData(data) { // 累计签约\r\n      this.loadingTables = true;\r\n      try {\r\n        const res = await API.Sales.qyDfData(data);\r\n        this.qyData = [this.sanitizeData(res.data)];\r\n      } finally {\r\n        this.loadingTables = false;\r\n      }\r\n    },\r\n    sanitizeData(data) { // 数据为空时，显示--\r\n      if (!data) return {};\r\n      return Object.fromEntries(\r\n        Object.entries(data).map(([key, value]) => [key, value ?? '--'])\r\n      );\r\n    },\r\n    handleTabSign(item) {\r\n      this.queryTypeSign = item.code;\r\n      this.getQyDfData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getRgData({\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.getQYYTData({ // 签约业态分布\r\n        queryType: this.queryTypeSign,\r\n        projectCode: this.projectCode\r\n      });\r\n    },\r\n    fetchAnalyse(projectCode) { // 货值分析-货值分布表\r\n      API.Value.analyse(projectCode).then(res => {\r\n        this.analysisData = res.data.map(item => ({\r\n          projectType: item.projectType || '--',\r\n          hxName: item.hxName || '--',\r\n          totalNum: item.totalNum === null ? '--' : item.totalNum,\r\n          ysNum: item.ysNum === null ? '--' : item.ysNum,\r\n          wsNum: item.wsNum === null ? '--' : item.wsNum,\r\n          ygwsNum: item.ygwsNum === null ? '--' : item.ygwsNum,\r\n          wgNum: item.wgNum === null ? '--' : item.wgNum,\r\n          allTopNum: item.allTopNum === null ? '--' : item.allTopNum,\r\n          allBottomNum: item.allBottomNum === null ? '--' : item.allBottomNum,\r\n          ysTopNum: item.ysTopNum === null ? '--' : item.ysTopNum,\r\n          ysBottomNum: item.ysBottomNum === null ? '--' : item.ysBottomNum,\r\n          wsTopNum: item.wsTopNum === null ? '--' : item.wsTopNum,\r\n          wsBottomNum: item.wsBottomNum === null ? '--' : item.wsBottomNum,\r\n          allHzAmount: item.allHzAmount === null ? '--' : item.allHzAmount,\r\n          ysHzAmount: item.ysHzAmount === null ? '--' : item.ysHzAmount,\r\n          wsHzAmount: item.wsHzAmount === null ? '--' : item.wsHzAmount,\r\n        }));\r\n      })\r\n    },\r\n    async getkqData(data) { // 款齐\r\n      this.loadingPayments = true;\r\n      try {\r\n        const res = await API.Sales.kqData(data);\r\n        this.kqData = res.data;\r\n        this.cardData[0].value = this.kqData.kqTargetAmount;\r\n        this.cardData[1].value = this.kqData.yearKqAmount;\r\n        this.cardData[2].value = this.kqData.kjlKqAmount;\r\n      } finally {\r\n        this.loadingPayments = false;\r\n      }\r\n    },\r\n\r\n    handleHKTab(item) {\r\n      this.hkProjectType = item.code;\r\n      this.getHkTrend();\r\n    },\r\n    handleRgTab(item) {\r\n      this.rgProjectType = item.code;\r\n      this.getRgTrend();\r\n    },\r\n    handleQyTab(item) {\r\n      this.qyProjectType = item.code;\r\n      this.getQyTrend();\r\n    },\r\n    async getProjectType(projectCode) { // 查询业态列表\r\n      const res = await API.Common.getProjectType(projectCode);\r\n      this.projectTypeList = res.data;\r\n      this.hasData = this.projectTypeList.length > 0;\r\n      this.qyProjectType = this.rgProjectType = this.hkProjectType\r\n        = this.projectTypeList[0].code;\r\n    },\r\n    async initTrend() {\r\n      this.loadingTrends = true;\r\n      try {\r\n        await Promise.all([\r\n          this.getQyTrend(),\r\n          this.getRgTrend(),\r\n          this.getDfTrend(),\r\n          this.getHkTrend()\r\n        ]);\r\n      } finally {\r\n        this.loadingTrends = false;\r\n      }\r\n    },\r\n    getDfOption() { // 到访趋势图数据\r\n      this.dfOption = this.getBarOption(['#53B997'], this.dfTrendData, 'df')\r\n      // this.dfOption.yAxis[1].name = '单位：人次';\r\n      this.dfOptionName = '单位：人次';\r\n      this.dfOption.tooltip = {\r\n        show: false\r\n      }\r\n    },\r\n    async getDfTrend() { // 销售页签 - 到访趋势\r\n      const res = await API.Sales.dfTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode\r\n      });\r\n      this.dfTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.dfTrendData.series = res.data.map(item => item.value);\r\n      this.getDfOption();\r\n    },\r\n    async getRgTrend() { // 销售页签 - 认购趋势\r\n      const res = await API.Sales.rgTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.rgProjectType\r\n      });\r\n      this.rgTrendRawData = res.data;\r\n      this.rgTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.rgTrendData.series = res.data.map(item => item.value);\r\n      this.updateRGTrendData();\r\n    },\r\n    async getQyTrend() { // 销售页签 - 签约趋势\r\n      const res = await API.Sales.qyTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.qyProjectType\r\n      });\r\n      this.qyTrendRawData = res.data;\r\n      this.qyTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      // this.qyTrendData.series = res.data.map(item => item.value);\r\n      this.updateQYTrendData();\r\n    },\r\n    getHkOption() {\r\n      this.hkOption = this.getBarOption(['#9D7BFF'], this.hkTrendData);\r\n      if (this.queryTypeTrend === 1 || this.queryTypeTrend === 2) {\r\n        // this.hkOption.yAxis[1].name = '单位：万元';\r\n        this.hkOptionName = '单位：万元'\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 10000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 10000).toFixed(2);\r\n        }\r\n      } else {\r\n        // this.hkOption.yAxis[1].name = '单位：亿元';\r\n        this.hkOptionName = '单位：万元';\r\n        this.hkOption.yAxis[0].axisLabel.formatter = function (value) {\r\n          return (value / 100000000).toFixed(2);\r\n        }\r\n        this.hkOption.series[0].label.formatter = function (params) {\r\n          return (params.value / 100000000).toFixed(2);\r\n        }\r\n\r\n      }\r\n      this.hkOption.tooltip = {\r\n        show: false\r\n      };\r\n    },\r\n    async getHkTrend() { //销售页签 - 回款趋势\r\n      const res = await API.Sales.hkTrend({\r\n        queryType: this.queryTypeTrend,\r\n        projectCode: this.projectCode,\r\n        projectType: this.hkProjectType\r\n      });\r\n\r\n      this.hkTrendData.xAxis = res.data.map(item => item.key.replace('-', '\\n'));\r\n      this.hkTrendData.series = res.data.map(item => item.value);\r\n      this.getHkOption();\r\n    },\r\n    getPieOption(color = ['#376DF7', '#53B997', '#6750AA', '#F8C541']) {\r\n      const pieOption = new PieOption();\r\n      pieOption.setColor(color);\r\n      return pieOption.getOption();\r\n    },\r\n    getBarOption(color = ['#53B997'], data = {xAxis: [], series: []}, type = '') {\r\n      const barOption = new BarOption();\r\n      barOption.updateData(data.xAxis, data.series);\r\n      barOption.setColor(color);\r\n      const option = barOption.getOption();\r\n      if (!Array.isArray(option.yAxis)) {\r\n        option.yAxis = [{}];\r\n      }\r\n      return option;\r\n    },\r\n    handleSpanMethod({row, column, rowIndex, columnIndex}) {\r\n      // 处理最后一行\r\n      if (rowIndex === this.analysisData.length - 1) {\r\n        if (columnIndex === 0) {  // 第一列\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2  // 合并两列\r\n          };\r\n        }\r\n        if (columnIndex === 1) {  // 第二列\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0  // 隐藏第二列\r\n          };\r\n        }\r\n      }\r\n\r\n      // 处理其他行的第一列合并\r\n      if (columnIndex === 0) {\r\n        const projectType = row.projectType;\r\n\r\n        // 向上查找相同业态的起始位置\r\n        let startIndex = rowIndex;\r\n        while (startIndex > 0 && this.analysisData[startIndex - 1].projectType === projectType) {\r\n          startIndex--;\r\n        }\r\n\r\n        // 计算相同业态的行数\r\n        let spanCount = 0;\r\n        for (let i = startIndex; i < this.analysisData.length; i++) {\r\n          if (this.analysisData[i].projectType === projectType) {\r\n            spanCount++;\r\n          } else {\r\n            break;\r\n          }\r\n        }\r\n\r\n        // 只在每组的第一行显示，其他行隐藏\r\n        if (rowIndex === startIndex) {\r\n          return {\r\n            rowspan: spanCount,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    qyProgressOption() {\r\n      const data = this.allQyData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#1433CC');\r\n        progressCircleOption.setBackgroundStyle('#376DF7');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#376DF7');\r\n        progressCircleOption.setBackgroundStyle('#C2CEF8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${this.$toFixed2(data.rate)}%`,\r\n        textStyle: {\r\n          color: '#376DF7',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: true,\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计签约: ${data.qyAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    hkProgressOption() {\r\n      const data = this.allHkData;\r\n      const progressCircleOption = new ProgressCircle();\r\n\r\n      if (data.rate > 100) {\r\n        progressCircleOption.setBarItemStyle('#e56d16');\r\n        progressCircleOption.setBackgroundStyle('#FF974C');\r\n        progressCircleOption.setBarData([data.rate % 100]);\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#FF974C');\r\n        progressCircleOption.setBackgroundStyle('#F9DEC8');\r\n        progressCircleOption.setBarData([data.rate]);\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      // option.title[0].subtext = '进度';\r\n      // option.title[0].text = `${data.rate}%`;\r\n      // option.title[0].textStyle.color = '#FF974C';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '进度',\r\n        text: `${data.rate}%`,\r\n        textStyle: {\r\n          color: '#FF974C',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(18 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            if (this.queryTypeSign === 1) { // Daily view\r\n              return `${(amount / 10000).toFixed(2)}万`;\r\n            }\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">动态货值: ${data.dynamicAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">累计回款: ${data.hkAmount} 亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">进度: ${this.$formatNull(data.rate)}%</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">权益比例: ${data.qyRate}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    kqProgressOption() {\r\n      const data = this.kqData.kqRatio;\r\n      let text = '--';\r\n      if (!(data === null || data === undefined)) {\r\n        text = data;\r\n      }\r\n\r\n      const progressCircleOption = new ProgressCircle();\r\n      progressCircleOption.setBarData([(data % 100).toFixed(2) || '--']);\r\n\r\n      if (data > 100) {\r\n        progressCircleOption.setBarItemStyle('#6A3DC4');\r\n        progressCircleOption.setBackgroundStyle('#7B6FF2');\r\n      } else {\r\n        progressCircleOption.setBarItemStyle('#7B6FF2');\r\n        progressCircleOption.setBackgroundStyle('#E2E0FB');\r\n      }\r\n\r\n      const scale = window.innerWidth / 1680;\r\n      const option = progressCircleOption.getOption();\r\n      option.series[0].roundCap = option.series[0].data[0] > 3\r\n      // option.title[0].subtext = '';\r\n      // option.title[0].text = `${text}%`;\r\n      // option.title[0].textStyle.color = '#7B6FF2';\r\n      // option.title[0].textStyle.fontSize = 21 * scale;\r\n      // option.title[0].subtextStyle.fontSize = 21 * scale * 0.6;\r\n      // option.title[0].itemGap = 10 * scale;\r\n      option.title = {\r\n        show: false,\r\n        subtext: '',\r\n        text: `${text}%`,\r\n        textStyle: {\r\n          color: '#7B6FF2',\r\n          fontSize: 21 * scale\r\n        },\r\n        subtextStyle: {\r\n          fontSize: 21 * scale * 0.6\r\n        },\r\n        itemGap: 10 * scale\r\n      }\r\n      option.polar.radius = ['80%', '100%']\r\n      const windowWidth = window.innerWidth;\r\n      const fontSize = Math.floor(16 * (windowWidth / 1680));\r\n      option.tooltip = {\r\n        show: false,\r\n        position: 'right',\r\n        confine: true,\r\n        textStyle: {\r\n          fontSize: fontSize,\r\n          lineHeight: Math.floor(fontSize * 1.5) + 'px'\r\n        },\r\n        padding: [8 * (windowWidth / 1680), 12 * (windowWidth / 1680)],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: ${fontSize}px;\">\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐目标金额: ${this.kqData.kqTargetAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">可结利款齐金额: ${this.kqData.kjlKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">年度款齐: ${this.kqData.yearKqAmount}亿</div>\r\n            <div style=\"line-height: ${Math.floor(fontSize * 1.5)}px\">款齐百分比: ${this.$formatNull(this.kqData.kqRatio)}%</div>\r\n          </div>`;\r\n        }\r\n      }\r\n      return option;\r\n    },\r\n    qyYTOption() { // 签约业态分布chart option\r\n      return this.getQYYTOption(this.qyYTData);\r\n      /* const option = this.getPieOption();\r\n      const scale = window.innerWidth / 1680;\r\n      option.color = ['#8af3a9', '#f8cb7f', '#fa9f92', '#6bc0f3'];\r\n\r\n      option.series[0].labelLine = {\r\n        show: true, // 显示标签\r\n        position: 'outside', // 标签位置\r\n        minTurnAngle: 90,\r\n        normal: {\r\n          length: 12 * scale,\r\n          length2: 12 * scale,\r\n          lineStyle: {\r\n            width: 1 * scale,\r\n          }\r\n        }\r\n      };\r\n\r\n      option.series[0].data = this.qyYTData.map(item => ({\r\n        value: item.rate,\r\n        name: item.bussName,\r\n        ...item\r\n      }));\r\n\r\n      option.tooltip = {\r\n        show: true,\r\n        padding: [0, 0],\r\n        formatter: (params) => {\r\n          const formatAmount = (amount) => {\r\n            if (!amount && amount !== 0) return '--';\r\n            return `${(amount / 100000000).toFixed(2)}亿`;\r\n          };\r\n\r\n          return `<div style=\"font-size: 0.875rem; line-height: 1.2;padding: 0.3rem 0.75rem;border-radius: 0.1rem;\">\r\n            <div>名称: ${params.data.bussName}</div>\r\n            <div>套数: ${params.data.num}</div>\r\n            <div>面积: ${params.data.area}</div>\r\n            <div>金额: ${formatAmount(params.data.qyAmount)}</div>\r\n            <div>比例: ${this.$formatNull(params.data.rate)}%</div>\r\n          </div>`;\r\n        }\r\n      };\r\n\r\n      option.legend = {\r\n        data: this.qyYTData.map(item => item.bussName),\r\n        textStyle: {\r\n          color: '#333333',\r\n          fontSize: 12 * scale\r\n        },\r\n        itemGap: 10 * scale,\r\n        itemWidth: 12 * scale,\r\n        itemHeight: 12 * scale,\r\n        bottom: '0',\r\n        left: 'center',\r\n        orient: 'horizontal',\r\n        icon: 'circle',\r\n      };\r\n\r\n      option.series[0].label = {\r\n        show: true,\r\n        position: 'outside',\r\n        normal: {\r\n          formatter: function (params) {\r\n            const formatAmount = (amount) => {\r\n              if (!amount && amount !== 0) return '--';\r\n              return `${(amount / 100000000).toFixed(2)}亿`;\r\n            };\r\n            return '{labelColor|' + params.name + '}\\n{labelColor|' + formatAmount(params.data.qyAmount) + ' ' + params.data.rate + '%' + '}';\r\n          },\r\n          rich: rich\r\n        },\r\n      };\r\n\r\n      return option; */\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import './sales.scss';\r\n</style>\r\n"]}]}