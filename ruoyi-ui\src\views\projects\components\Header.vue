<template>
  <div class="header">
    <img src="@/views/projects/assets/images/logo.png" alt="logo" class="logo" @click="navigateDashboard">
    <div class="header-middle">
        <div class="project-title line-1" @click="navigateDashboard">
          <div>项目经营看板</div>
        </div>
    </div>
    <div class="header-right">
      <div class="user-name project-text-1">你好，{{user.nickName}}</div>
    </div>
  </div>
</template>
<script>
  import { mapState } from 'vuex'

  export default {
    data() {
      return {
        // user: {},
        avatar: ''
      };
    },
    // props: {
    //   user: {
    //     type: Object,
    //     required: false,
    //     default: () => ({
    //       nickName: '',
    //       role: '',
    //       permissions: []
    //     })
    //   }
    // },
    computed: {
      ...mapState({
        user: state => state.projects.user
      })
    },
    created() {
      // this.getUser();
    },
    methods: {
      navigateDashboard() {
        this.$router.push({path: '/projects'});
      }
    }
  };
</script>
<style scoped lang="scss">
.header {
    height: 4.6875rem;
    background: rgba(255,255,255,0.9);
    border-radius: 0rem;
    border: 0.0625rem solid #FFFFFF;
    display: flex;
    align-items: center;
    padding: 0 1.3125rem;
    box-sizing: border-box;
    gap: 1rem;
    .logo{
        height: 100%;
        cursor: pointer;
    }
    .header-middle{
        flex: 1;
        text-align: left;
    }
    .line-1{
        display: flex;
        align-items: center;
        &::before{
            content: '';
            display: inline-block;
            width: 0.125rem;
            height: 1.125rem;
            background: #DDDDDD;
            margin-right: 0.75rem;
        }
    }
    .project-title{
       font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.25rem;
        color: #222222;
        line-height: 1.9375rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
    }
    .project-text-1{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 0.75rem;
        color: #333333;
        line-height: 1.1875rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
  .header-right {
    display: flex;
    align-items: center;
    .avatar {
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 0rem;
      border: 0.0625rem solid #FFFFFF;
      margin-right: 0.5rem;
    }
  }
}
</style>
