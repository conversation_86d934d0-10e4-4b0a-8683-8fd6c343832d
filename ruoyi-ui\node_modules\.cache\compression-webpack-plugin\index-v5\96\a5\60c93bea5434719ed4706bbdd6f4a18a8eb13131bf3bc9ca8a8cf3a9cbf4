
1f6a12923a510c0c2e57f110577c6039ac2fdaf4	{"key":"{\"nodeVersion\":\"v20.19.3\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"app.841dee789a0ba91cad43.hot-update.js\",\"contentHash\":\"d3db6e7c998ed97b26736f267b9d72c3\"}","integrity":"sha512-SLzIb5F00czPByxUYaPEi97IzitKY/L3IR7uzsOGYVNSPiWpwhFzwaay6XzMbQ4hzspcLh50VgT0ksqrtthCEg==","time":1754311725386,"size":77984}