<template>
  <div class="company-tree">
    <div class="company-tree-title" :class="{ 'border': expanded }" @click="toggleExpand">
      <span>{{ getDisplayName() }}</span>
      <img src="@/views/projects/assets/images/up.png" alt="logo" class="up-icon" :class="{ 'is-expanded': expanded }">
    </div>
    <div class="company-tree-content" :class="{ 'is-expanded': expanded }">
      <div v-for="company in companies" :key="company.cityId" class="company-item"
           @click="toggleCompanyExpand(company)">
        <div class="company-header">
          <img v-if="company.expanded" src="@/views/projects/assets/images/minus.png" alt="logo" class="expand-icon">
          <img v-if="!company.expanded" src="@/views/projects/assets/images/plus.png" alt="logo" class="expand-icon">
          <span class="company-name">{{ company.cityName }}</span>
          <img v-if="company.expanded" src="@/views/projects/assets/images/city.png" alt="logo" class="expand-icon">
          <img v-if="!company.expanded" src="@/views/projects/assets/images/city-inactive.png" alt="logo"
               class="expand-icon">
        </div>
        <transition name="expand">
          <div class="project-list" :class="{'is-expand': company.expanded}">
            <div
              v-for="project in company.projectDataList"
              :key="project.projectCode"
              class="project-item"
              :class="{'active': project.projectCode === projectCode}"
              @click="handleProjectClick(project)"
            >
              {{ project.displayName }}
            </div>
          </div>
        </transition>
      </div>
    </div>

  </div>
</template>

<script>
import API from '@/views/projects/api'

export default {
  name: 'CompanyTree',
  props: {
    defaultCityId: { // 默认选中的城市公司id,优先级高于defaultSelectedFirstCompany
      type: String,
      default: ''
    },
    defaultSelectedFirstCompany: { // 默认选中第一个城市公司
      type: Boolean,
      default: false
    },
    projectCode: { // 项目code
      type: String,
      default: ''
    }
  },
  data() {
    return {
      expanded: false,
      companies: [
        /*{
          "cityId": "9a62219c-5ddd-4f45-b22b-f964b9360e4e",
          "cityName": "京西公司",
          "frCityName": null,
          "myCityName": null,
          "adCityName": null,
          "qjCityName": null,
          "zxCityName": null,
          "gfCityName": null,
          "isCity": true,
          "projectDataList": [
            {
              "projectCode": "102001",
              "displayName": "中建·京西印玥",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "102007",
              "displayName": "中建·国贤府（北京）",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "103004",
              "displayName": "中建·国贤府（天津）",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "10200317",
              "displayName": "中建·京华国贤府",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "102004",
              "displayName": "中建·春和印象",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "102005",
              "displayName": "中建·国府（房山）",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "102002",
              "displayName": "中建·学府印悦",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "102003",
              "displayName": "房山东羊庄19地块",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            }
          ]
        },
        {
          "cityId": "39327c8d-2dcb-4380-9430-375b3d02b270",
          "cityName": "京东公司",
          "frCityName": null,
          "myCityName": null,
          "adCityName": null,
          "qjCityName": null,
          "zxCityName": null,
          "gfCityName": null,
          "isCity": true,
          "projectDataList": [
            {
              "projectCode": "101005",
              "displayName": "中建·宸园",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101009",
              "displayName": "中建·璞园PARK",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101002",
              "displayName": "中建·宸庐",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101003",
              "displayName": "中建·宸庐云起",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101007",
              "displayName": "中建·璞园",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101008",
              "displayName": "中建·北京宸园",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "101010",
              "displayName": "朝阳电子城项目",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            }
          ]
        },
        {
          "cityId": "5c3a8bad-b8ba-4f5a-a897-59bfd0d3e3c2",
          "cityName": "沪苏公司",
          "frCityName": null,
          "myCityName": null,
          "adCityName": null,
          "qjCityName": null,
          "zxCityName": null,
          "gfCityName": null,
          "isCity": true,
          "projectDataList": [
            {
              "projectCode": "104003",
              "displayName": "中建·虹溪璟庭",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "104002",
              "displayName": "中建·晴翠璟园",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "105001",
              "displayName": "中建·龍宸壹號",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "105006",
              "displayName": "中建·国贤府（常州）",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            },
            {
              "projectCode": "106002",
              "displayName": "闵行保障房07项目",
              "isCity": false,
              "projectMyGuid": null,
              "projectMyFqGuid": null,
              "projectAd4Id": null
            }
          ]
        },
        {
          "cityId": "212cc994-7af0-4757-90a4-34af60392c1f",
          "cityName": "总部直管",
          "frCityName": null,
          "myCityName": null,
          "adCityName": null,
          "qjCityName": null,
          "zxCityName": null,
          "gfCityName": null,
          "isCity": true,
          "projectDataList": []
        },
        {
          "cityId": "6ab635a4-b228-4140-ba9c-a5a53b532366",
          "cityName": "城建公司",
          "frCityName": null,
          "myCityName": null,
          "adCityName": null,
          "qjCityName": null,
          "zxCityName": null,
          "gfCityName": null,
          "isCity": true,
          "projectDataList": []
        }*/
      ],
      selectedCompany: null, // 选中的城市公司，默认为
    }
  },
  mounted() {
    this.getData()
  },
  watch: {
    projectCode: {
      immediate: true,
      handler(newCode) {
        console.log('projectCode', newCode)
        if (newCode) {
          this.findAndSelectCompanyByProject(newCode);
        }
      }
    }
  },
  methods: {
    handleSetDefault() {
      if (this.defaultCityId) {
        const company = this.companies.find(c => c.cityId === this.defaultCityId);
        if (company) {
          this.toggleCompanyExpand(company);
        }
      } else if (this.defaultSelectedFirstCompany) {
        this.toggleCompanyExpand(this.companies[0]);
      }
    },
    getDisplayName() {
      if (this.projectCode) {
        // Find the project across all companies
        for (const company of this.companies) {
          const project = company.projectDataList.find(p => p.projectCode === this.projectCode);
          if (project) {
            return project.displayName;
          }
        }
      }
      return this.selectedCompany?.cityName ?? '选择城市公司-项目';
    },
    formatterData(data) {
      return data.map(item => {
        return {
          ...item,
          expanded: false
        }
      })
    },
    getData() {
      API.Common.getProjectTree().then(res => {
        if (res.code !== 200) {
          this.$message.error(res.msg || '获取城市公司数据失败')
          return
        }
        this.companies = this.formatterData(res.data)
        this.findAndSelectCompanyByProject(this.projectCode);
      })
    },
    toggleExpand() {
      this.expanded = !this.expanded
    },
    handleCollapse(){
      this.expanded = false;
    },
    toggleCompanyExpand(company) {
      this.selectedCompany = company;
      this.companies.forEach(c => {
        if (c !== company) {
          c.expanded = false;
        }
      });
      company.expanded = !company.expanded;
      this.$emit('company-change', company)
    },
    handleProjectClick(project) {
      this.$emit('project-change', project)
      this.expanded = false;
    },
    findAndSelectCompanyByProject(projectCode) {
      if (!projectCode) return;

      for (const company of this.companies) {
        const project = company.projectDataList.find(p => p.projectCode === projectCode);
        if (project) {
          this.toggleCompanyExpand(company);
          break;
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.company-tree {
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  font-family: PingFang SC, PingFang SC;
  box-shadow: 0px 5 27px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0.3125rem;
  border: 0.0625rem solid #FFFFFF;
  min-width: 23.3125rem;
  width: fit-content;
  // background: #fff;
  position: relative;

  .company-tree-title {
    font-weight: 600;
    font-size: 1rem;
    color: #222222;
    line-height: 1.375rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 0.5rem 1.3125rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.border {
      //border-bottom: 2px solid #f5f5f5;
    }

    .up-icon {
      margin-left: 0.625rem;
      width: 1.3125rem;
      height: 1.3125rem;
      transform: rotate(180deg);

      &.is-expanded {
        transform: rotate(0);
      }
    }
  }

  .company-tree-content {
    z-index: 1000;
    background: #fff;
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    font-family: PingFang SC, PingFang SC;
    max-height: 0;
    overflow: auto;
    transition: all .3s ease-in-out;

    .expand-icon {
      font-weight: bold;
      transition: all 30s ease;
      display: inline-block;
      width: 1.3125rem;
      height: 1.3125rem;
    }

    &.is-expanded {
      max-height: 43.75rem;
      overflow: auto;
    }
  }
}

.company-name {
  padding: 0.8125rem 0.6875rem;
  flex: 1;
  font-weight: 400;
  font-size: 0.875rem;
  color: #222222;
  line-height: 1rem;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.company-header {
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  cursor: pointer;

  .expand-icon {
    width: 21px;
    height: 21px;

    &.is-expand {
      transform: rotate(90deg);
    }
  }
}

.project-list {
  // position: absolute;
  // left: 0;
  width: 100%;
  background: #fff;
  z-index: 1;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;

  &.is-expand {
    max-height: 37.5rem;
    overflow: auto;
  }
}

.project-item {
  padding: 0.8125rem 3rem;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 400;
  font-size: 0.875rem;
  color: #666666;
  line-height: 1.125rem;
  text-align: left;
  font-style: normal;
  text-transform: none;

  &.active {
    background: #f5f5f5;
    color: #2D5BFF;
  }
}

.project-item:hover {
  background: #f5f5f5;
}

/* 展开动画 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  max-height: 18.75rem;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.expand-enter-to,
.expand-leave-from {
  max-height: 18.75rem;
  opacity: 1;
}

.company-item {
  position: relative;
}
</style>
