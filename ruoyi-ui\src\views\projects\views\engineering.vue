<template>
  <div class="projects-content-container">
    <!-- 无权限状态 -->
    <div v-if="!hasPermi(['project:gc'])" class="empty-status">
      <img src="@/views/projects/assets/images/no-auth.png" alt="无权限">
      <div class="desc">暂无权限</div>
      <div class="desc">请联系管理员</div>
    </div>

    <!-- 有权限时显示的内容 -->
    <div v-else style="width: 100%">
      <!-- 开发中状态 -->
      <div v-if="isDeveloping" class="empty-status">
        <img src="@/views/projects/assets/images/developing.png" alt="开发中">
        <div class="desc">正在开发中</div>
        <div class="desc">敬请期待</div>
      </div>

      <!-- 无数据状态 -->
    <!--   <div v-else-if="!hasData" class="empty-status">
        <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
        <div class="desc">暂无数据</div>
      </div> -->
      <div v-else style="width: 100%;">
        <div class="category-tab-wrapper">
          <CardTab v-model="activeTab" :tabs="categoryTabs" class="mb-14" @click="handleTabClick" />
        </div>

        <div v-if="!hasData" class="empty-status">
          <img src="@/views/projects/assets/images/no-data.png" alt="无数据">
          <div class="desc">暂无数据</div>
        </div>
        <div v-else class="evaluate-content-wrapper">
          <div v-for="item in quarterData" :key="item.year" v-loading="contentLoading" class="evaluate-content">
            <div class="card-title mb-12">{{ item.year }}年</div>
            <section class="projects-content-container-content">
              <div v-for="(quarter, index) in item.gcHistoryQuarterDtoList" :key="index"
                class="projects-content-container-content-item" @click="handleHistoryClick(quarter, item.year)">
                <EvaluateChart :chart-data="quarter.gcQuarterDetailDtoList.map(
                  ({ gcProjectName, score, isProject, rankNum }) => ({
                    name: gcProjectName,
                    value: score,
                    isProject,
                    rankNum
                  })
                )
                  "
                 :theme-color="chartColor[quarter.quarter].themeColor"
                  :highlight-index="quarter.gcQuarterDetailDtoList.findIndex(item => item.isProject)"
                  :highlight-color="chartColor[quarter.quarter].highlightColor" :average-score="quarter.average"
                  :title="item.year + '年' + quarter.quarter + '季度'"
                  :resizeChartFlag="resizeChartFlag"
                />
              </div>
            </section>
          </div>
        </div>
      </div>
      <!-- 添加弹窗组件 -->
      <el-dialog v-loading="dialogLoading" :visible.sync="dialogVisible" :title="dialogTitle" class="history-dialog"
        width="80%" element-loading-text="数据加载中..." element-loading-spinner="el-icon-loading"  @click.native="handleDialogClick"
        element-loading-background="rgba(255, 255, 255, 0.8)" :before-close="handleClose">
        <section>
          <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <div class="tree-wrapper">
              <CompanyTree :project-code="projectCode" :default-selected-first-company="true" class="company-tree-dialog"
                           @company-change="handleCompanyChange" @project-change="handleProjectChange" ref="companyTree"/>
            </div>
            <el-select v-model="selectedYear" placeholder="选择年份" class="custom-select"
              @change="handleYearChange">
              <el-option v-for="year in yearOptions" :key="year" :label="year + '年'" :value="year" />
            </el-select>
            <el-select v-model="selectedQuarter" placeholder="选择季度" class="custom-select"
              @change="handleQuarterChange">
              <el-option v-for="quarter in quarterOptions" :key="quarter" :label="'第' + quarter + '季度'"
                :value="quarter" />
            </el-select>
            <!-- <el-button type="primary" @click="handleSearch" style="margin-left: 16px;">查询</el-button> -->
          </div>
        </section>
        <section class="evaluate-dialog-content-wrapper">
          <section v-for="(item, index) in categoryTabs" :key="index">
          <div class="card-title mb-16">{{ item.name }}</div>
          <el-table :data="$data[item.detailDatakey]" row-key="id" border default-expand-all
            :ref="item.detailDatakey"
            style="width: 100%;margin-bottom: 32px;overflow-y: auto;"
            :header-cell-style="{
              background: '#E1EFFD',
              color: '#666666',
              fontWeight: '400',
              padding: '0.5rem 0',
              height: '2rem',
              fontSize: '0.875rem',
              lineHeight: '1.5rem',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }"
            :cell-style="(row, column, rowIndex, columnIndex) => getCellStyle(row, column, rowIndex, columnIndex)"
            :span-method="(params) => objectSpanMethod(params, item.detailDatakey, $data[item.detailDatakey])"
            :show-summary="true" :summary-method="(param) => getSummaries(param, $data[item.detailAllDataKey], $data[item.summaryDataColumns])">
            <el-table-column type="index" label="序号" width="80" align="center" />
            <el-table-column v-for="column in item.columns" :key="column.prop" :prop="column.prop" :label="column.label"
              align="center" />
          </el-table>
        </section>
        </section>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose" size="medium">关 闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import CardTab from '@/views/projects/components/CardTabSales.vue'
import EvaluateChart from '@/views/projects/components/EvaluateChart.vue'
import API from '@/views/projects/api'
import CompanyTree from '@/views/projects/components/CompanyTree.vue'
export default {
  name: 'QualityManage',
  components: {
    CardTab,
    EvaluateChart,
    CompanyTree
  },
  data() {
    return {
      resizeChartFlag: true,
      isDeveloping: false, // 控制是否显示开发中状态
      hasData: true, // 控制是否有数据
      activeTab: 'processAssess', // 活动标签;
      categoryTabs: [
        {
          name: '过程评估',
          code: 'processAssess',
          detailDatakey: 'processAssessData',
          detailAllDataKey: 'processAssessAllData',
          summaryDataColumns: 'processAssessSummaryColumns',
          columns: [ // 详情表格列
            {
              label: '项目名称',
              prop: 'gcProjectName'
            },
            {
              label: '评估阶段',
              prop: 'assessStage'
            },
            {
              label: '总分',
              prop: 'totalScore'
            },
            {
              label: '实测实量',
              prop: 'actualMeasureScore'
            },
            {
              label: '质量风险',
              prop: 'qualityRiskScore'
            },
            {
              label: '安全文明',
              prop: 'secureCultureScore'
            }
          ]
        },
        {
          name: '交付评估',
          code: 'deliverAssess',
          detailDatakey: 'deliverAssessData',
          detailAllDataKey: 'deliverAssessAllData',
          summaryDataColumns: 'deliverAssessSummaryColumns',
          columns: [
            {
              label: '项目名称',
              prop: 'gcProjectName'
            },
            {
              label: '交付形式',
              prop: 'deliverType'
            },
            {
              label: '综合得分',
              prop: 'totalScore'
            },
            {
              label: '户内观感',
              prop: 'indoorFeelScore'
            },
            {
              label: '实测实量',
              prop: 'actualMeasureScore'
            },
            {
              label: '公共部位',
              prop: 'commonPartScore'
            },
            {
              label: '园林景观',
              prop: 'gardensLandscapeScore'
            },
            {
              label: '外立面',
              prop: 'facadeScore'
            }
          ]
        },
        {
          name: '地下工程评估',
          code: 'undergroundAssess',
          detailDatakey: 'undergroundAssessData',
          detailAllDataKey: 'undergroundAssessDataAll',
          summaryDataColumns: 'undergroundAssessSummaryColumns',
          columns: [
            {
              label: '项目名称',
              prop: 'gcProjectName'
            },
            {
              label: '综合得分',
              prop: 'totalScore'
            },
            {
              label: '桩基工程',
              prop: 'pileBaseScore'
            },
            {
              label: '基坑工程安全专项',
              prop: 'baseHoleSecureScore'
            },
            {
              label: '地下室质量风险',
              prop: 'basementQualityScore'
            }
          ]
        },
        {
          name: '材料飞检',
          code: 'materialCheck',
          detailDatakey: 'materialCheckData',
          detailAllDataKey: 'materialCheckDataAll',
          summaryDataColumns: 'materialCheckSummaryColumns',
          columns: [
            {
              label: '项目名称',
              prop: 'gcProjectName'
            },
            {
              label: '综合得分',
              prop: 'totalScore'
            },
            {
              label: '材料封样',
              prop: 'materialSealScore'
            },
            {
              label: '材料实测',
              prop: 'materialActualTestScore'
            },
            {
              label: '材料品牌及型号符合度倒扣分',
              prop: 'deductScore'
            }
          ]
        },
        {
          name: '大型机械检查',
          code: 'largeMachineryCheck',
          detailDatakey: 'largeMachineryCheckData',
          detailAllDataKey: 'largeMachineryCheckDataAll',
          summaryDataColumns: 'largeMachinerySummaryColumns',
          columns: [
            {
              label: '项目名称',
              prop: 'gcProjectName'
            },
            {
              label: '综合得分',
              prop: 'comprehensiveScore'
            },
            {
              label: '塔吊（50%）',
              prop: 'towerCraneScore'
            },
            {
              label: '施工升降机（30%）',
              prop: 'constructionHoistScore'
            },
            {
              label: '电动吊篮（20%）',
              prop: 'electricHangBasketScore'
            }
          ]
        }
      ],
      projectCode: this.$route.query.projectCode,
      quarterData: [],
      chartColor: {
        4: {
          themeColor: '#CEEAE5',
          highlightColor: '#61CDBB'
        },
        3: {
          themeColor: '#BED2FB',
          highlightColor: '#376DF7'
        },
        2: {
          themeColor: '#FCE4BD',
          highlightColor: '#FAB232'
        },
        1: {
          themeColor: '#EDD0F8',
          highlightColor: '#CC67ED'
        }
      },
      dialogVisible: false,
      dialogTitle: null,
      processAssessData: [], // 过程评估详情
      processAssessAllData: {},
      // processAssessSummaryColumns前两个元素空是为了满足均值行跨列设置，和不同的模块mergeSummaryColumns-colSpan对应
      processAssessSummaryColumns: ['', '', 'totalScoreAvg', 'actualMeasureScoreAvg', 'qualityRiskScoreAvg', 'secureCultureScoreAvg'],
      deliverAssessData: [], // 交付评估详情
      deliverAssessAllData: {},
      deliverAssessSummaryColumns: ['', '', 'totalScoreAvg', 'indoorFeelScoreAvg', 'actualMeasureScoreAvg', 'commonPartScoreAvg', 'gardensLandscapeScoreAvg', 'facadeScoreAvg'],
      undergroundAssessData: [], // 地下工程评估详情
      undergroundAssessDataAll: {},
      undergroundAssessSummaryColumns: ['', 'totalScoreAvg', 'pileBaseScoreAvg', 'baseHoleSecureScoreAvg', 'basementQualityScoreAvg'],
      materialCheckData: [], // 材料飞检详情
      materialCheckDataAll: {},
      materialCheckSummaryColumns: ['','totalScoreAvg', 'materialSealScoreAvg', 'materialActualTestScoreAvg', 'deductScoreAvg'],
      largeMachineryCheckData: [], // 大型机械检查详情
      largeMachineryCheckDataAll: {}, // 大型机械检查所有详情数据
      // 平均数据key，按照在table中的顺序分布
      largeMachinerySummaryColumns: ['', 'comprehensiveScoreAvg', 'towerCraneScoreAvg', 'constructionHoistScoreAvg', 'electricHangBasketScoreAvg'],
      selectedYear: new Date().getFullYear(), // 默认为当前年份
      yearOptions: (function () {
        const currentYear = new Date().getFullYear()
        const years = []
        for (let year = currentYear; year >= 2000; year--) {
          years.push(year)
        }
        return years
      })(),
      selectedQuarter: 1, // 默认季度
      quarterOptions: [1, 2, 3, 4],
      dialogLoading: false,
      contentLoading: false
    }
  },
  mounted() {
    this.fetchData(this.activeTab)
    document.querySelector('.com-content').style.overflow = 'hidden'
  },
  beforeDestroy() {
    document.querySelector('.com-content').style.overflow = 'auto'
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
      this.resizeChartFlag = true;
    },
    handleYearChange(year) {
      this.handleSearch()
    },
    handleQuarterChange(quarter) {
      this.handleSearch()
    },
    handleCompanyChange() { },
    handleProjectChange(project) {
      if (project && project.projectCode) {
        this.projectCode = project.projectCode
        // 重置数据
        this.processAssessData = []
        this.deliverAssessData = []
        this.undergroundAssessData = []
        this.materialCheckData = []
        this.largeMachineryCheckData = []

        // 如果已经选择了年份和季度，则自动查询
        // if (this.selectedYear && this.selectedQuarter) {
        this.handleSearch()
        // }
      }
    },
    handleHistoryClick(quarter, year) {
      this.resizeChartFlag = false;
      this.dialogVisible = true
      // this.dialogTitle = `${year}年${quarter.quarter}季度评估`
      this.selectedQuarter = quarter.quarter
      this.selectedYear = year
      this.handleSearch()
      // this.fetchDetailData(this.projectCode, quarter.quarter, year);
    },
    fetchDetailData(projectCode, quarter, year) {
      this.dialogLoading = true
      API.ProjectEvaluate.processAssessDetail({
        projectCode,
        year,
        quarter
      }).then((res) => {
        if (res.code === 200) {
          this.processAssessData = res?.data?.dataDtoList
          this.processAssessAllData= res?.data;
        } else {
          this.processAssessData = []
        }
        this.$nextTick(() => {
          this.mergeSummaryColumns(this.$refs.processAssessData[0].$el, 3);
        })
      })
      API.ProjectEvaluate.deliverAssessDetail({
        projectCode,
        year,
        quarter
      }).then((res) => {
        if (res.code === 200) {
          this.deliverAssessData = res?.data?.dataDtoList
          this.deliverAssessAllData = res?.data;
        } else {
          this.deliverAssessData = []
        }
        this.$nextTick(() => {
          this.mergeSummaryColumns(this.$refs.deliverAssessData[0].$el, 3);
        })
      })
      API.ProjectEvaluate.undergroundAssessDetail({
        projectCode,
        year,
        quarter
      }).then((res) => {
        if (res.code === 200) {
          this.undergroundAssessData = res?.data?.dataDtoList
          this.undergroundAssessDataAll = res?.data;
        } else {
          this.undergroundAssessData = []
        }
        this.$nextTick(() => {
          this.mergeSummaryColumns(this.$refs.undergroundAssessData[0].$el, 2);
        })
      })
      API.ProjectEvaluate.materialCheckDetail({
        projectCode,
        year,
        quarter
      }).then((res) => {
        if (res.code === 200) {
          this.materialCheckData = res?.data?.dataDtoList
          this.materialCheckDataAll = res?.data;
        } else {
          this.materialCheckData = []
        }
        this.$nextTick(() => {
          this.mergeSummaryColumns(this.$refs.materialCheckData[0].$el, 2);
        })
      })
      API.ProjectEvaluate.largeMachineryCheckDetail({
        projectCode,
        year,
        quarter
      }).then((res) => {
        if (res.code === 200) {
          this.largeMachineryCheckData = res?.data?.dataDtoList
          this.largeMachineryCheckDataAll = res.data;
        } else {
          this.largeMachineryCheckData = []
        }
        this.$nextTick(() => {
          this.mergeSummaryColumns(this.$refs.largeMachineryCheckData[0].$el, 2);
        })
      }).finally(() => {
        this.dialogLoading = false
      })
    },
    fetchData(tab) {
      this.contentLoading = true
      if (tab === 'processAssess') {
        API.ProjectEvaluate.processAssess(this.projectCode).then((res) => {
          if (res.code === 200) {
            this.quarterData = res.data
          } else {
            this.quarterData = []
          }
          this.hasData = this.quarterData.length > 0
        }).finally(() => {
          this.contentLoading = false
        })
      }
      if (tab === 'deliverAssess') {
        API.ProjectEvaluate.deliverAssess(this.projectCode).then((res) => {
          if (res.code === 200) {
            this.quarterData = res.data
          } else {
            this.quarterData = []
          }
          this.hasData = this.quarterData.length > 0
        }).finally(() => {
          this.contentLoading = false
        })
      }
      if (tab === 'undergroundAssess') {
        API.ProjectEvaluate.undergroundAssess(this.projectCode).then((res) => {
          if (res.code === 200) {
            this.quarterData = res.data
          } else {
            this.quarterData = []
          }
          this.hasData = this.quarterData.length > 0
        }).finally(() => {
          this.contentLoading = false
        })
      }
      if (tab === 'materialCheck') {
        API.ProjectEvaluate.materialCheck(this.projectCode).then((res) => {
          if (res.code === 200) {
            this.quarterData = res.data
          } else {
            this.quarterData = []
          }
          this.hasData = this.quarterData.length > 0
        }).finally(() => {
          this.contentLoading = false
        })
      }
      if (tab === 'largeMachineryCheck') {
        API.ProjectEvaluate.largeMachineryCheck(this.projectCode).then(
          (res) => {
            if (res.code === 200) {
              this.quarterData = res.data
            } else {
              this.quarterData = []
            }
            this.hasData = this.quarterData.length > 0
          }
        ).finally(() => {
          this.contentLoading = false
        })
      }
    },
    handleTabClick(item) {
      console.log(item)
      this.activeTab = item.code
      this.fetchData(item.code)
    },
    hasPermi(permission) {
      const permissions = this.$store.getters.permissions
      // Check for all permissions wildcard first
      if (permissions.includes('*:*:*')) {
        return true
      }
      // Check specific permissions
      return permissions.some((p) => permission.includes(p))
    },
    handleSearch() {
      if (!this.selectedYear || !this.selectedQuarter) {
        this.$message.warning('请选择年份和季度')
        return
      }
      this.fetchDetailData(this.projectCode, this.selectedQuarter, this.selectedYear)
    },
    getSummaries(param, allData = {}, avgColumns = []) {
      console.log(param, allData, avgColumns)
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '均值'
          return
        }
        if(!!avgColumns[index - 1]){
          sums[index] = allData[avgColumns[index - 1]]
        }

        /* const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = (sums[index] / values.filter(value => !isNaN(value)).length).toFixed(2)
        } else {
          sums[index] = ''
        }*/
      })
      return sums
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }, key, data) {
      // console.log(row, column, rowIndex, columnIndex, key)
      // 处理跨列的情况，如果需要某些行的第一列跨多列，可以在这里实现
      // 目前不做特殊处理，返回默认值
      if (key === 'processAssessData' || key === 'deliverAssessData') {
        // console.log(key)
        return {
          rowspan: 1,
          colspan: 1
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      }
    },
    // 渲染后合并合计行的列
    mergeSummaryColumns(table, colSpan) {
      this.$nextTick(() => {
        // const table = this.$refs[refName].$el;
        const footerRow = table.querySelector(".el-table__footer-wrapper tbody tr");
        if (!footerRow) return;

        // 合并前两列（例如：合并“日期”和“姓名”列）
        const cells = footerRow.cells;
        cells[0].colSpan = colSpan; // 第一列跨两列
        for (let i = 1; i < colSpan; i++) {
          cells[i].style.display = "none"; // 隐藏被合并的列
        }
      });
    },
    getCellStyle({ row, column, rowIndex, columnIndex }) {
      const baseStyle = {
        borderColor: 'rgba(0,106,255,0.2);',
        padding: '0.5rem 0',
        height: '2rem',
        fontSize: '0.875rem',
        lineHeight: '1.5rem',
      };

      // 如果行数据的isProject属性为true，则加深字体颜色并加粗
      // console.log(row, row.isProject)
      if (row.isProject) {
        return {
          ...baseStyle,
          color: '#222222',
          fontWeight: '600'
        };
      }

      return {
        ...baseStyle,
        fontWeight: '400',
        color: '#606266'
      };
    },
    /**
     * 处理弹窗点击事件
     * 当点击弹窗非树组件区域时，折叠项目树
     */
    handleDialogClick(event) {
      // 检查点击事件源是否在 CompanyTree 组件之外
      const treeEl = this.$refs.companyTree.$el;
      if (!treeEl.contains(event.target)) {
        // 如果 CompanyTree 组件有提供关闭/折叠的方法，调用它
        if (this.$refs.companyTree.handleCollapse) {
          this.$refs.companyTree.handleCollapse();
        } else {
          // 如果没有明确的方法，可以尝试调用组件上公开的其他方法
          console.log('CompanyTree 组件需要提供折叠方法');
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/views/projects/styles/projects.scss";
:deep(.el-table__footer-wrapper td){
  color: #000!important;
  font-size: 0.8rem!important;
}
.custom-select{
  width: 9.375rem;
  margin-left: 1rem;
  :deep(input){
    font-weight: 600;
    font-size: 1rem;
    color: #222222;
    line-height: 1.375rem;
  }
}
/*:deep(.el-dialog__header) {
  display: none;
}*/
.year {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 1.25rem;
  color: #333333;
  line-height: 1.4375rem;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-top: 1.25rem;
  padding-left: 1.5rem;
  padding-bottom: 1rem;
  background: rgba(255, 255, 255, 0.7);
}

.projects-content-container {
  // height: calc(100vh - 250px);
}

.evaluate-content-wrapper {
  max-height: calc(100vh - 19.75rem) !important;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin
}

.projects-content-container-content {
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2.8125rem;

  .projects-content-container-content-item {
    height: 18.75rem;
    width: 47%;
  }
}
.tree-wrapper{
  display: flex;
  align-items: center;
  border: 1px solid #DCDFE6;
  height: 36px;
}
.company-tree-dialog {
  //display: flex;
  //align-content: center;
  :deep(.company-tree-title) {
    flex: 1;
  }
}

.category-tab-wrapper {
  /*  position: sticky;
  top: 0;
  z-index: 10;*/
}

.evaluate-content {
  //display: none;
}
.evaluate-dialog-content-wrapper{
  height: 70vh;
  overflow-y: auto;
  scrollbar-width: thin
}
</style>
