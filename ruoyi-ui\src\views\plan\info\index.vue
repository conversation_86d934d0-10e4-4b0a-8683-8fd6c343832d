<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="城市公司" prop="deptNum">
        <el-select
          v-model="queryParams.deptNum"
          placeholder="请选择城市公司"
          clearable
          @change="handleQueryProject"
        >
          <el-option
            v-for="dict in cityCompanys"
            :key="dict.deptNum"
            :label="dict.deptName"
            :value="dict.deptNum"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择项目"
          clearable
          filterable
        >
          <el-option
            v-for="dict in storeProjectList"
            :key="dict.name"
            :label="dict.name"
            :value="dict.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划名称" prop="planName" v-show="showAllSearch">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分期" prop="stageId">
        <el-select v-model="queryParams.stageId" placeholder="请选择分期"
                   clearable
                   filterable>
          <el-option
            v-for="dict in stages"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="标段" prop="lot" v-show="showAllSearch">
        <el-select v-model="queryParams.lot" placeholder="请选择标段"
                   clearable
                   filterable>
          <el-option
            v-for="dict in projectLot"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="计划状态" prop="status" v-show="showAllSearch">
        <el-select v-model="queryParams.status" placeholder="请选择计划状态"
                   clearable
                   filterable>
          <el-option
            v-for="dict in plan_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          :icon="!showAllSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          size="mini"
          @click="toggleAllSearch"
        >更多筛选</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['plan:info:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['plan:info:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
      ref="table"
      @row-click="showPlanAndNodeDialogFun"
    >
      <el-table-column label="序号" align="center" prop="id" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag
            :options="plan_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <!--<el-table-column-->
        <!--label="主责专业"-->
        <!--align="center"-->
        <!--prop="professionId"-->
        <!--width="80"-->
      <!--&gt;-->
        <!--<template slot-scope="scope">-->
          <!--<dict-tag-->
            <!--:options="departs"-->
            <!--:value="scope.row.professionId"-->
          <!--/>-->
        <!--</template>-->
      <!--</el-table-column>-->
      <el-table-column
        label="城市公司"
        align="center"
        prop="deptName"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
<!--          @click="$copyToClipboard(scope.row.deptName)"-->
          <span>{{ scope.row.deptName || '无' }}</span>
        </template>
        <!-- <template slot-scope="scope">
          <dict-tag :options="cityCompanys" :value="scope.row.deptNum" />
        </template> -->
      </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        width="200"
        prop="projectName"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
<!--          @click="$copyToClipboard(scope.row.projectName)"-->
          <span class="wrap-line">{{ scope.row.projectName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分期" align="center" prop="stageId" width="70">
        <template slot-scope="scope">
          <dict-tag :options="stages" :value="scope.row.stageId" />
        </template>
      </el-table-column>
      <el-table-column label="标段" align="center" prop="lot" width="70">
        <template slot-scope="scope">
          <dict-tag :options="projectLot" :value="scope.row.lot" />
        </template>
      </el-table-column>
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
        width="260"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span @click="$copyToClipboard(scope.row.planName)">{{ scope.row.planName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="版本号"
        align="center"
        prop="version"
        width="100">
      <template slot-scope="scope">
        <span>V{{ scope.row.version}}</span>
      </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="hidden-column"
        min-width="230"
        label-class-name="hidden-column"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === 0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(scope.row)"
            v-hasPermi="['plan:info:edit']"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.status == 0 || scope.row.status == 5 || scope.row.status == 3"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(scope.row)"
            v-hasPermi="['plan:info:remove']"
            >删除</el-button
          >
<!--          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="showPlanAndNodeDialogFun(scope.row)"
            v-if="scope.row.status != 0 && scope.row.status != 5"
            >查看</el-button>-->
          <el-button
            v-if="scope.row.status === 0 || scope.row.status === 4 || scope.row.status === 5 || scope.row.status == 3"
            size="mini"
            type="text"
            icon="el-icon-info"
            @click.stop="showDialogFun(scope.row)"
            >编辑</el-button
          >
<!--          <el-popconfirm
            title="是否提交？"
            @confirm="submitApproval(scope.row)"
          >-->
            <el-button
              slot="reference"
              v-if="scope.row.status === 0 || scope.row.status === 4 || scope.row.status === 5 || scope.row.status == 3"
              size="mini"
              type="text"
              icon="el-icon-video-play"
              @click.stop="submitApprovalHandle(scope.row)"
              >提交</el-button
            >
<!--          </el-popconfirm>-->
          <el-button
            v-if="scope.row.status != 0 && scope.row.status != 3"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click.stop="fetchApprovalUser(scope.row)"
            >查看审批流</el-button
          >
          <el-button
            v-if="scope.row.status == 1"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click.stop="refreshApproval(scope.row, scope.$index)"
            >刷新</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计划编制对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="900px"
      append-to-body
      v-if="open"
      v-loading="planLoading"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!--<el-form-item label="主责专业" prop="professionId">-->
          <!--<el-select v-model="form.professionId" placeholder="请选择主责专业" @change="professionChangeHandle">-->
            <!--<el-option-->
              <!--v-for="dict in departs"-->
              <!--:key="dict.value"-->
              <!--:label="dict.label"-->
              <!--:value="dict.value"-->
            <!--&gt;</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <el-form-item label="城市公司" prop="deptNum">
          <el-select v-model="form.deptNum" placeholder="请选择城市公司">
            <el-option
              v-for="dict in cityCompanys"
              :key="dict.deptNum"
              :label="dict.deptName"
              :value="dict.deptNum"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择项目">
            <el-option
              v-for="dict in projectList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分期" prop="stageId">
          <el-select v-model="form.stageId" placeholder="请选择分期">
            <el-option
              v-for="dict in stages"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标段" prop="lot">
          <el-select v-model="form.lot" placeholder="请选择分期">
            <el-option
              v-for="dict in projectLot"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划模板" prop="templateId">
          <el-select v-model="form.templateId" placeholder="请选择计划模板"
                     clearable
                     filterable>
            <el-option
              v-for="template in allTemplateList"
              :key="template.id"
              :value="template.id"
              :label="template.templateName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划类型" prop="planType">
          <el-select v-model="form.planType" placeholder="请选择计划类型"
                     clearable
                     filterable
          @change="planTypeSelect">
            <el-option
              v-for="item in planTypeList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="form.planName" :disabled="form.planType == 1" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="计划说明" prop="planExplain">
          <el-input
            type="textarea"
            autosize
            v-model="form.planExplain"
            placeholder="请输入计划说明"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addInfo, delInfo, getApproveFlow, getInfo, getPlanStatus, listInfo, updateInfo } from "@/api/plan/info"
import { listProject } from "@/api/plan/project"
import { listNode, submit } from "@/api/plan/node"
import HistoryVersion from '@/views/plan/components/HistoryVersion/index.vue'
import PlanAndNodeInfo from '@/views/plan/components/PlanAndNodeInfo/index.vue'
import { mapState } from 'vuex';
import mixin from "@/mixins";
export default {
  mixins: [mixin],
  name: "Info",
  components: {
    HistoryVersion,
    PlanAndNodeInfo
  },
  data () {
    return {
      planTypeList: [
        { id: 1, name: '全周期三级节点计划' },
        { id: 2, name: '其他类型' }
      ],
      planLoading: false,
      departs:[],
      stages:[],
      projectLot:[],
      plan_status:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPlanNode: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划编制表格数据
      infoList: [],
      // 计划-节点表格数据
      planNodeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        deptNum: null,
        projectId: null,
        stageId: null,
        lot: null,
        status: null,
        planName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // professionId: [{
        //   required: true,
        //   message: "专业不能为空",
        //   trigger: "change"
        // }],
        deptNum: [{
          required: true,
          message: "城市公司不能为空",
          trigger: "change"
        }],
        projectId: [{
          required: true,
          message: "项目不能为空",
          trigger: "change"
        }],
        stageId: [{
          required: true,
          message: "分期不能为空",
          trigger: "change"
        }],
        lot: [{
          required: true,
          message: "标段不能为空",
          trigger: "change"
        }],
        planType: [{
          required: true,
          message: "计划类型不能为空",
          trigger: "change"
        }],

        planName: [{
          required: true,
          message: "计划名称不能为空",
          trigger: "blur"
        }],
        templateId: [{
          required: false,
          message: "计划模板不能为空",
          trigger: "change"
        }],
        version: [{
          required: true,
          message: "版本号不能为空",
          trigger: "blur"
        }],
      },
      projectList: [],
      planId: null,
      showHistoryDialog: false,
      rowData: null,
      dialogVisible: false,
      approveUserInfo: null,
      showPlanAndNodeDialog: false
    }
  },
  mounted () {
    this.getList()
    //获取部门列表
    this.$store.dispatch('plan/fetchDepartList')
    //获取城市公司
    this.$store.dispatch('plan/fetchDeptList')
    // 事件总成监听
    this.$bus.$on('refreshInfoList', () => {
      this.getList()
    })
    //获取字典
    this.getdicts()
  },
  beforeDestroy () {
    //收尾操作，销毁
    this.$bus.$off('refreshInfoList')  //$off解绑当前组件所用到的事件
  },
  watch: {
    'form.deptNum': {
      handler (newVal, oldVal,) {
        if (newVal && newVal != oldVal) {
          this.fetchProjectByCity(newVal)
        }
      }
    },
    'queryParams.deptNum': {
      handler (newVal, oldVal,) {
        if (newVal && newVal != oldVal) {
          this.fetchProjectByCity(newVal)
        }
      }
    }
  },
  computed: {
    allTemplateList () {
      return this.$store.state.plan.allTemplateList
    },
    cityCompanys () {
      return this.$store.state.plan.deptList
    },
    ...mapState('plan', {storeProjectList: 'projectList'})
  },
  methods: {
    planTypeSelect(){
      // 当选择全周期三级节点计划时，计划名称自动生成，不允许手改，生成规则：全周期三级节点计划
      // 当选择其他类型时，逻辑跟原来一样，需要手动填写计划名称

      if(this.form.planType == 1){
        this.form.planName='全周期三级节点计划'
      }
      else{
        this.form.planName=''
      }
    },
    professionChangeHandle(){
      this.$store.dispatch('plan/fetchAllTemplateData')
    },
    handleQueryProject(value){
      this.$store.dispatch('plan/fetchProjectList', {name: '', company: value})
    },
    submitApprovalHandle(row){
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitApproval(row)
      }).catch(() => {
      });
    },
    getdicts(){
      // this.getDicts('departs').then(res=>{
      //   let data=res.data
      //   this.departs=data.map(item=>{
      //     return {
      //       label:item.dictLabel,
      //       value:item.dictValue,
      //       raw:item
      //     }}
      //   )
      // }).catch(err=>{
      //   console.log(err)
      // })
      this.getDicts('stages').then(res=>{
        let data=res.data
        this.stages=data.map(item=>{
          return {
            label:item.dictLabel,
            value:item.dictValue,
            raw:item
          }}
        )
      }).catch(err=>{
        console.log(err)
      })
      this.getDicts('plan_status').then(res=>{
        let data=res.data
        this.plan_status=data.map(item=>{
          return {
          label:item.dictLabel,
          value:item.dictValue,
            raw:item
          }}
        )
      }).catch(err=>{
        console.log(err)

      })
      this.getDicts('project_lot').then(res=>{
        let data=res.data
        this.projectLot=data.map(item=>{
          return {
            label:item.dictLabel,
            value:item.dictValue,
            raw:item
          }}
        )
      }).catch(err=>{
        console.log(err)

      })

    },
    showPlanAndNodeDialogFun (row) {
      console.log('row---', row)
      // this.showPlanAndNodeDialog = true
      // this.rowData = row
      this.$router.push({
        path: `/plan/planNodeInfo`,
        query: {
          planId: row.id
        }
      })
    },
    // 刷新审批状态
    refreshApproval (row, index) {
      this.loading = true
      getPlanStatus(row.id).then(res => {
        this.loading = false
        this.infoList[index].status = res.data
        this.infoList = [...this.infoList]
      }).catch(err => {
        this.loading = false
      })
    },
    fetchApprovalUser (row) {
      this.loading = true
      getApproveFlow(row.id).then(res => {
        this.loading = false
        window.open(res.data)
      }).catch(err => {
        this.loading = false

      })
    },
    //提交审批
    async submitApproval (row) {
      this.loading = true
      let data
      try {
        data = await listNode({ planId: row.id })
        this.loading = false
      } catch (err) {
        this.loading = false
      }
      this.loading = true
      submit({
        id: row.id,
        planNodeList: data.rows || []
      }).then(res => {
        this.loading = false
        this.$message.success("提交成功")
        window.open(res.data)
        this.getList()
      }).catch(err => {
        this.loading = false
      })
    },
    cancelHistoryDialog () {
      this.showHistoryDialog = false
    },
    showHistoryFun (row) {
      this.$router.push({
        path: `/plan/historyVersion`,
        query: {
          planId: row.id,
          planCode: row.planCode,
          version: row.version
        }
      })
    },
    showDialogFun (row) {
      this.$router.push({
        path: `/plan/nodeInfo`,
        query: {
          planId: row.id,
          planStatus: row.status,
          planName: row.planName
        }
      })
    },

    // 获取城市公司下面的项目
    fetchProjectByCity (params) {
      // this.loading = true
      listProject({
        name: '',
        company: params
      }).then(res => {
        this.projectList = res.rows
        this.queryParams.projectId = null
        this.loading = false
      }).catch(err => {
        this.loading = false

      })
    },
    /** 查询计划编制列表 */
    getList () {
      this.loading = true
      listInfo(this.queryParams).then(response => {
        console.log(JSON.parse(JSON.stringify(response)))
        this.infoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        deptNum: null,
        professionId: null, // 专业
        projectId: null, //项目
        stageId: null, //分期
        templateId: null,
        planName: null, //计划名称
        planExplain: ''
      }
      this.planNodeList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    async handleAdd () {
      //重新获取字典数据
      this.getdicts()
      //获取所有模板
      await this.$store.dispatch('plan/fetchAllTemplateData')
      //获取城市公司
      await this.$store.dispatch('plan/fetchDeptList')

      this.reset()
      this.open = true
      this.title = "添加计划编制"
    },
    /** 修改按钮操作 */
    async handleUpdate (row) {
      //重新获取字典数据
      this.getdicts()
      //获取所有模板
      await this.$store.dispatch('plan/fetchAllTemplateData')
      //获取城市公司
      await this.$store.dispatch('plan/fetchDeptList')
      this.reset()
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data
        this.planNodeList = response.data.planNodeList
        this.open = true
        this.title = "修改计划编制"
      })
    },
    /** 提交按钮 */
    submitForm () {
      // console.log(this.form,this.stages)
      if(this.planLoading) return
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.planLoading = true
          //this.form.planNodeList = this.planNodeList
           let labelObj={
             deptName:this.cityCompanys.filter(item=>item.deptNum==this.form.deptNum)[0].deptName,
             projectName:this.projectList.filter(item=>item.id==this.form.projectId)[0].name,
             stageName:this.stages.filter(item=>item.value==this.form.stageId)[0].label,
           }
           let params={...this.form,...labelObj}
          if (this.form.id != null) {
            this.loading = true
            updateInfo(params).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.loading = false
              this.planLoading = false
              //刷新列表
              this.handleQuery()
            }).catch(err => {
              this.loading = false
              this.planLoading = false
            })
          } else {
            this.loading = true
            addInfo(params).then(response => {
              this.loading = false
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.planLoading = false
              //刷新列表
              this.handleQuery()
            }).catch(err => {
              this.loading = false
              this.planLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      this.$modal.confirm('是否确认删除计划名称为"' + row.planName + '"的数据项？').then(function () {
        return delInfo(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => { })
    },
    /** 计划-节点序号 */
    rowPlanNodeIndex ({
      row,
      rowIndex
    }) {
      row.index = rowIndex + 1
    },
    /** 计划-节点添加按钮操作 */
    handleAddPlanNode () {
      let obj = {}
      obj.status = ""
      obj.projectId = ""
      obj.stageId = ""
      obj.depart = ""
      obj.nodeCode = ""
      obj.nodeLevel = ""
      obj.nodeName = ""
      obj.startTime = ""
      obj.endTime = ""
      obj.department = ""
      obj.responsibilityPeople = ""
      obj.creator = ""
      obj.updater = ""
      obj.deleted = ""
      this.planNodeList.push(obj)
    },
    /** 计划-节点删除按钮操作 */
    handleDeletePlanNode () {
      if (this.checkedPlanNode.length == 0) {
        this.$modal.msgError("请先选择要删除的计划-节点数据")
      } else {
        const planNodeList = this.planNodeList
        const checkedPlanNode = this.checkedPlanNode
        this.planNodeList = planNodeList.filter(function (item) {
          return checkedPlanNode.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handlePlanNodeSelectionChange (selection) {
      this.checkedPlanNode = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('plan/info/export', {
        ...this.queryParams
      }, `计划列表（编制）_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style>
.hidden-column {
  border: none;
}
</style>
<style scoped>
.infoStyle {
  height: 34px;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
</style>
