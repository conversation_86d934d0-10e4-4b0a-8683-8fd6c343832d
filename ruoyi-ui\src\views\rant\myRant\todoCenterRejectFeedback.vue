<template>
  <div class="app-container" v-loading="loading">
    <div class="step-box">
      <rant-step :stepActive="stepActive"/>
    </div>
    <!-- 添加或修改督办事项对话框 -->
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" class="form-box">
      <el-row>
        <el-col :span="8">
          <el-form-item label="来源" prop="ranterName">
            <el-input v-model="form.ranterName" placeholder="请选择来源" clearable disabled>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="分类" prop="rantClassify">
            <el-select v-model="form.rantClassify" placeholder="请选择吐槽分类" @keyup.enter.native="handleQuery"
                       class="w-100" :disabled="isRead"
            >
              <el-option v-for="dict in dict.type.rant_classify" :key="dict.value" :label="dict.label"
                         :value="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划完成时间" prop="planTime">
            <el-date-picker clearable size="small" v-model="form.planTime" type="date" class="w-100"
                            value-format="yyyy-MM-dd" placeholder="选择计划完成时间" disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="责任人" prop="responsiblePersonName">
            <el-input v-model="form.responsiblePersonName" placeholder="请选择责任人" disabled clearable @focus="selectUserFun('responsibilityer')"
            >
              <i slot="suffix" class="el-icon-search" @click.stop="selectUserFun('responsibilityer')"
                 style="cursor: pointer;"
              ></i></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任部门" prop="deptId">
            <treeselect v-model="form.deptId" :options="deptOptions" disabled :normalizer="normalizer"
                        @input="selectDept" placeholder="选择责任部门"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门负责人" prop="respDeptResponsiblerName">
            <el-input class="full-width-input" disabled v-model="form.respDeptResponsiblerName" readonly="readonly"/>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="内容" prop="rantContent">
            <el-input v-model="form.rantContent" type="textarea" placeholder="请输入内容" :autosize="{ minRows: 3, maxRows: 20}" :disabled="isRead"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="措施" prop="solution">
            <el-input v-model="form.solution" type="textarea" disabled :autosize="{ minRows: 3, maxRows: 20}"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="审核意见" prop="approveDesc">
            <el-input v-model="form.approveDesc" type="textarea" disabled placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="dialog-footer">
      <el-button  class="submit-btn"  type="primary" v-if="(status == 0 || status == 7) && !isRead" @click="submitForm(1)">
        提 交
      </el-button>
      <el-button  class="save-btn" type="primary"  v-if="(status == 0 || status == 7) && !isRead" @click="submitForm(0)">保 存
      </el-button>
      <el-button  @click="cancel" class="cancel-btn" v-if="!isRead">取 消</el-button>
    </div>
    <select-user v-if="selectUserShow" ref="userRef" :roleId="roleName" :selectMultiple="isMultiple"
                 @feedbackEmit="selectUserData"
    />
  </div>
</template>

<script>
function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}
import {
  getMatters,
  listMatters,
  addMatters,
  updateMatters,
  delMatters,
  myTaskList,
  myRantList,
  myRantAdd,
  myRantEdit,
  rantDaiBanInfo,
  myRantApprove
} from '@/api/rant/matters'
import SelectUser from '@/views/plan/components/SelectUser/index.vue'
import { getInfo } from '@/api/login'
import { rantStatusOption } from '@/constant'
import StatusTag from '@/views/plan/components/StatusTag/index.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listRespdet, getRespdet } from '@/api/rant/respdet'
import { listDept, listDeptExcludeChild } from '@/api/system/dept'
import RantStep from '@/views/rant/components/RantStep/index.vue'

export default {
  name: 'MyRant',
  components: { SelectUser, StatusTag, Treeselect, RantStep },
  dicts: ['rant_classify'],
  data() {
    return {
      stepActive: 1, // 当前步骤
      // 遮罩层
      loading: false,
      // 是否显示弹出层
      open: false,
      //选择用户
      selectUserType: '',
      ranter: null,
      ranterName: '',
      responsiblePerson: null,
      responsiblePersonName: '',
      deptId: null,
      deptName: '',
      roleName: '',
      responsiblePersonNameQuery: '',
      selectUserShow: true,
      isMultiple: false,
      // 责任部门列表
      deptOptions: [],
      status: null,
      // 表单参数
      form: {
        "todoNoticeId": null,
        "id": null,
        "ranter": null, // 来源:人(多个用“,”隔开)
        "ranterName": null, // 来源:人(多个用“,”隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "status": 0, // 状态（6-同意，7-驳回）
        "approveDesc": '', // 审批意见
      },
      // 表单校验
      rules: {
        ranter: [
          { required: true, message: '吐槽人不能为空', trigger: 'blur' }
        ],
        rantClassify: [
          { required: true, message: '吐槽分类不能为空', trigger: 'blur' }
        ],

        rantContent: [
          { required: true, message: '吐槽内容不能为空', trigger: 'blur' }
        ],
      },
      type: '',
      rantId: '',
      isRead: false
    }
  },
  computed: {
    rantStatusOption() {
      return rantStatusOption
    }
  },
  created() {
    this.todoNoticeId = this.$route.query.todoNoticeId
    this.form.todoNoticeId = this.todoNoticeId;
    this.fetchDaiBanInfo(this.todoNoticeId)
    listRespdet().then(response => {
      this.deptOptions = this.handleTree(response.data, 'id')
    })
  },
  methods: {
    fetchDaiBanInfo(todoNoticeId) {
      rantDaiBanInfo(todoNoticeId).then((res) => {
        if (res.code === 200) {
          this.form.id = res.data?.id;
          this.form.ranter = res.data?.ranter;
          this.form.ranterName = res.data?.ranterName;
          this.form.rantClassify = res.data?.rantClassify;
          this.form.rantContent = res.data?.rantContent;
          this.form.planTime = res.data?.planTime;
          this.form.deptId = res.data?.deptId;
          this.form.deptName = res.data?.deptName;
          this.form.responsiblePerson = res.data?.responsiblePerson;
          this.form.responsiblePersonName = res.data?.responsiblePersonName;
          this.form.respDeptResponsibler = res.data.respDeptResponsibler;
          this.form.respDeptResponsiblerName = res.data?.respDeptResponsiblerName;
          this.form.approveDesc = res.data?.approveDesc;
          this.status = res.data?.status;
          this.form.solution = res.data?.solution;
          this.isRead = res.data?.isRead;
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      this.closeCurrentPage()
    },
    // 表单重置
    reset() {
      this.form = {
        "todoNoticeId": null,
        "id": null,
        "mattersType": null, // 类型
        "ranter": null, // 来源:人(多个用“,”隔开)
        "ranterName": null, // 来源:人(多个用“,”隔开)
        "rantClassify": null, // 分类
        "rantContent": null, // 内容
        "solution": null, // 措施
        "planTime": null, // 计划完成时间
        "deptId": null, // 责任部门
        "deptName": null, // 责任部门
        "responsiblePerson": null, // 责任人
        "responsiblePersonName": null, // 责任人名称
        "respDeptResponsibler": null, // 责任部门负责人
        "respDeptResponsiblerName": null, // 责任部门负责人名称
        "status": 0, // 状态（6-同意，7-驳回）
        "approveDesc": '', // 审批意见
      };
      this.resetForm('form')
    },
    /** 编辑按钮操作 */
    handleUpdate(id) {
      this.reset()
      this.selectUserShow = true
      getMatters(id).then((response) => {
        this.form = response.data

        this.responsiblePersonName = response.data.responsiblePersonName
        this.responsiblePerson = response.data.responsiblePerson

      })
      listRespdet().then(response => {
        this.deptOptions = this.handleTree(response.data, 'id')
      })
    },
    closeCurrentPage() {
      close();
      // this.$tab.closeOpenPage({ path: '/rant/myRant' })
    },
    /** 提交按钮 */
    submitForm(submitStatus) {
      this.loading = true
      this.$refs['form'].validate((valid) => {
        this.form.status = submitStatus
        if (valid) {
            myRantEdit(this.form).then((response) => {
              this.loading = false
              if(response.code === 200){
                this.$message.success('提交成功，即将关闭页面')
                setTimeout(() => {
                  this.closeCurrentPage();
                }, 1500);
                return;
              }
              // this.$modal.msgError(response.msg)
            }).finally(() => {
              this.loading = false
            })
        }
      })
    },
    selectUserFun(type) {
      this.selectUserType = type
      this.$refs.userRef.show()
    },
    selectUserData(data) {
      if (this.selectUserType == 'responsibilityer') { // 责任人
        this.form.responsiblePerson = data.userId
        this.form.responsiblePersonName = data.nickName
      } else if (this.selectUserType == 'respDeptResponsibler') { // 部门负责人
        this.form.respDeptResponsibler = data.userId
        this.form.respDeptResponsiblerName = data.nickName

      }
    },
    changeresponsibilityer() {
      if (this.responsiblePersonNameQuery == '' || this.responsiblePersonNameQuery == null) {
        this.queryParams.responsiblePerson = null
      }

    },
    selectDept(value) {
      if (value) {
        this.form.deptId = value
        getRespdet(this.form.deptId).then(response => {
          this.form.deptName = response.data.name;
          this.form.respDeptResponsibler = response.data.respPeople;
          this.form.respDeptResponsiblerName = response.data.respPeopleName;
        });
      } else {
        this.form.deptId = null
        this.form.deptName = null
        this.form.respDeptResponsibler = null
        this.form.respDeptResponsiblerName = null
      }
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.el-input.is-disabled .el-input__inner){
  color: #000;
}
:deep(.el-textarea.is-disabled .el-textarea__inner){
  color: #000;
}
.app-container {
  padding: 20px 16px;
  background: linear-gradient(180deg, #CCDDFF 0%, #DAEEF2 100%);
  border-radius: 0px 0px 0px 0px;
  height: calc(100vh - 84px);

  .step-box {
    margin-bottom: 12px;
    padding: 16px;
    width: 100%;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;

    :deep(.el-step__icon) {
      background-color: unset;
    }

    :deep(.el-step__title) {
      color: #666666;
    }


  }

  .form-box {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #FFFFFF;
    padding: 32px;
  }

  .dialog-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 12px 304px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 0px 0px;
    display: flex;
    justify-content: center;
    gap: 40px;

    .submit-btn {
      width: 100px;
    }

    .save-btn {
      width: 100px;
      background: rgba(54,115,255,0.2);
      color: #3673FF;
    }

    .cancel-btn {
      width: 100px;
    }
  }
}
</style>
