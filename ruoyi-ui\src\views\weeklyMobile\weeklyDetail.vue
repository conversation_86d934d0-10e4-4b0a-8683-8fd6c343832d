<template>
  <div class="weekly-index">
    <div class="content">
      <weeklyDetailForm ref="weeklyFormRef" :action="action"></weeklyDetailForm>
    </div>
    <div class="bottom-button">
      <div class="second-btn" @click="cancelHandle">关闭详情</div>
    </div>
  </div>
</template>
<script>
// http://localhost/wechatE/mobile/weeklyInput
import weeklyDetailForm from "./components/weeklyDetailForm.vue";
import {
  addDraft,
  addInfo,
  addInit,
  getInfo,
  updateInfo,
} from "@/api/weekly/mobile-reportInfo";
export default {
  name: "WeeklyIndex",
  components: {
    weeklyDetailForm,
  },
  data() {
    return {
      form: {},
      loading: false,
      type: "", // 新增还是编辑模式
      id: "", // 周报ID
      action: "detail",
    };
  },
  created() {
    // 获取路由参数
    const { id } = this.$route.params;
    this.id = id;
  },
  mounted() {
    if (this.id) {
      this.getWeeklyDetail();
    }
  },
  methods: {
    async saveDraft() {
      this.loading = true;
      try {
        const formData = await this.$refs.weeklyFormRef.getFormData();
        await addDraft(formData);
        this.$modal.msgSuccess("草稿保存成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("草稿保存失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async getWeeklyDetail() {
      this.loading = true;
      try {
        const { data } = await getInfo(this.id);
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }
        if(!data.reflectInfo){
          data.reflectInfo = {
            type: 4, progressScheme: ""
          }
        }
        this.$refs.weeklyFormRef.updateFormData(data);
      } catch (error) {
        // this.$modal.msgError("获取详情失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    async addWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        await addInfo(data);
        this.$modal.msgSuccess("新增成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("新增失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },

    async handleInit() {
      try {
        const { data } = await addInit();
        if (!this.$refs.weeklyFormRef) {
          return this.$modal.msgWarning("表单组件未加载完成");
        }

        const formFields = [
          "year",
          "week",
          "nickName",
          "deptId",
          "deptName",
          "postName",
          "startDate",
          "endDate",
          "workSummaryList",
          "userCode",
        ];
        const formData = {};
        formFields.forEach((field) => (formData[field] = data[field]));

        this.$refs.weeklyFormRef.setFormData(formData);
      } catch (error) {
        // this.$modal.msgError("初始化失败：" + error.message);
      }
    },
    async updateWeeklyHandle(data = {}) {
      this.loading = true;
      try {
        const formData = {
          id: this.id,
          year: data.year,
          week: data.week,
          userCode: data.userCode,
          nickName: data.nickName,
          deptId: data.deptId,
          deptName: data.deptName,
          postName: data.postName,
          startDate: data.startDate,
          endDate: data.endDate,
          // approveUserName: data.approveUserName,
          // approveUserCode: data.approveUserCode,
          workSummaryList: data.workSummaryList,
          workPlanList: data.workPlanList,
          workSupportList: data.workSupportList,
          reportReviewDetails: data.reportReviewDetails,
        };
        await updateInfo(formData);
        this.$modal.msgSuccess("修改成功");
        this.closeCurrentPage();
      } catch (error) {
        // this.$modal.msgError("修改失败：" + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 编辑周报
    async updateWeekly() {
      if (!this.$refs.weeklyFormRef) {
        return this.$modal.msgWarning("表单组件未加载完成");
      }
      const formData = await this.$refs.weeklyFormRef.getFormData();
      await this.updateWeeklyHandle(formData);
    },
    // 新增周报
    async submitWeekly() {
      if (!this.$refs.weeklyFormRef) {
        return this.$modal.msgWarning("表单组件未加载完成");
      }
      const formData = await this.$refs.weeklyFormRef.getFormData();
      await this.addWeeklyHandle(formData);
    },
    closeCurrentPage() {
      // this.$tab.closeOpenPage({ path: "/wechatE/mobile/myWeekly" });
      this.$tab.closeOpenPage({ path: "/wechatE/mobile/weekly" });
    },
    cancelHandle() {
      this.closeCurrentPage();
    },
    handleBeforeUnload(event) {
      // 组织默认的 beforeunload 行为 (某些浏览器可能需要)
      event.preventDefault();
      // Chrome, Firefox, Safari, IE 8+
      event.returnValue = "您确定要离开此页面吗？您所做的更改可能不会被保存。";
      // 返回的字符串会显示在确认对话框中
      return event.returnValue; // 必须返回，某些浏览器需要
    },
  },
};
</script>
<style scoped lang="scss">
.weekly-index {
  width: 100%;
  // height: 100%;
  min-height: 100vh;
  overflow-y: auto;
  background-image: url("~@/assets/weeklyMobile/weeklybac.png");
  background-size: cover;
  background-repeat: no-repeat;

  .header {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 11px 0;
    //margin-bottom: 60px;
  }
  .content {
    padding: 16px 10px;
    .content-item {
      .content-item-image {
        width: 100%;
        margin-bottom: 42px;
      }
    }
    margin-bottom: 42px;
  }
  .bottom-button {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 16px;
    .second-btn {
      height: 42px;
      background: #3673ff;
      border-radius: 8px 8px 8px 8px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      text-align: center;
      line-height: 42px;
    }
  }
}
</style>
