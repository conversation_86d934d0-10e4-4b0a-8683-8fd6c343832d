import request from '@/utils/request'

// 查询过程评估数据列表
export function listData(query) {
  return request({
    url: '/project/gcProcessAssessData/list',
    method: 'get',
    params: query
  })
}

// 查询过程评估数据详细
export function getData(id) {
  return request({
    url: '/project/gcProcessAssessData/' + id,
    method: 'get'
  })
}

// 新增过程评估数据
export function addData(data) {
  return request({
    url: '/project/gcProcessAssessData',
    method: 'post',
    data: data
  })
}

// 修改过程评估数据
export function updateData(data) {
  return request({
    url: '/project/gcProcessAssessData',
    method: 'put',
    data: data
  })
}

// 删除过程评估数据
export function delData(id) {
  return request({
    url: '/project/gcProcessAssessData/' + id,
    method: 'delete'
  })
}

// 文件导入
export function importFile(data) {
  return request({
    url: '/project/gcProcessAssessData/import',
    method: 'post',
    data: data
  })
}

// 过程评估导入数据提交
export function importDataSubmit(data) {
  return request({
    url: '/project/gcProcessAssessData/importDataSubmit',
    method: 'post',
    data: data
  })
}
