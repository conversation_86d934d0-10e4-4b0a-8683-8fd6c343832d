import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import plan from './modules/plan'
import getters from './getters'
import projects from './projects'
import weekly from './modules/weekly'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    plan,
    projects,
    weekly
  },
  getters
})

export default store
