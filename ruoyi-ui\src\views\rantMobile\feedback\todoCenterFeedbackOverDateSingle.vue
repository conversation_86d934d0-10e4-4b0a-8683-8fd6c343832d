<!--25号推送进度反馈，一个合并的页面-->
<template>
  <div class="app-container app-container-feedback">
    <div class="rant-container" v-loading="loading">
      <div v-for="(rant, index) in rantList" :key="index" class="rant-content">
        <div class="section-title" v-if="rantList.length > 1">
          <span>
            <img src="@/assets/rant/rant-item.png" class="icon" alt="">
            督办事项{{ index + 1 }}
          </span>
          <section class="expand" @click="toggleCollapse(index)">
            <img src="@/assets/rant/rant-collapse.png" class="icon" :class="{'is-active': collapseStates[index]}" alt="">
            {{ !collapseStates[index] ? '收起' : '展开' }}
          </section>
        </div>

        <template v-if="!collapseStates[index]">
          <div class="rant-detail-content">
            <div class="rant-form-title">
              <img src="@/assets/rant/rant-info.png" class="icon">
              基本信息<span v-if="rant.submitStatus == 0" style="margin-left: 10px; color: red; font-size: small"></span>
            </div>

            <van-form class="rant-detail">
              <div class="form-group">
                <div class="form-item">
                  <div class="form-label">来源</div>
                  <div class="form-value">{{ rant.ranterName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">类型</div>
                  <div class="form-value">
                    <section class="custom-matters-type">
                      <dict-tag v-for="(type, idx) in rant.mattersType?.split(',')" :key="idx"
                                :options="mattersTypeOptionsLabel" :value="type" />
                    </section>
                  </div>
                </div>

                <div class="form-item">
                  <div class="form-label">分类</div>
                  <div class="form-value">
                    <dict-tag :options="classifyOptionsLabel" :value="rant.rantClassify" />
                  </div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任人</div>
                  <div class="form-value">{{ rant.responsiblePersonName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任部门</div>
                  <div class="form-value">{{ rant.deptName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">责任部门负责人</div>
                  <div class="form-value">{{ rant.respDeptResponsiblerName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">分管领导</div>
                  <div class="form-value">{{ rant.respDeptLeaderName }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">计划完成时间</div>
                  <div class="form-value">{{ rant.planTime }}</div>
                </div>

                <div class="form-item" v-if="type == 'matters'">
                  <div class="form-label">是否私密</div>
                  <div class="form-value">{{ rant.isPrivate == 1 ? "是" : "否" }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">内容</div>
                  <div class="form-value">{{ rant.rantContent }}</div>
                </div>

                <div class="form-item">
                  <div class="form-label">措施</div>
                  <div class="form-value">{{ rant.solution }}</div>
                </div>
              </div>

              <div class="rant-form-title" style="margin-top:16px;">
                <img src="@/assets/rant/rant-this-progress.png" class="icon">
                本次进展
                <span class="required-mark">*</span>
              </div>

              <div class="form-group text-area">
                <van-field
                  class="progress-textarea"
                  v-model="rant.thisProgress"
                  rows="6"
                  autosize
                  type="textarea"
                  placeholder="请输入本次进展"
                  :disabled="isRead"
                />
              </div>

              <div class="form-group">
                <div class="form-item">
                  <div class="form-label"><span class="required-mark">*</span>是否结项</div>
                  <div class="form-value">
                    <div class="completion-switch">
                      <div
                        class="switch-btn"
                        :class="{ active: rant.isCompletion }"
                        @click="!isRead && (rant.isCompletion = true)"
                      >
                        是
                      </div>
                      <div
                        class="switch-btn"
                        :class="{ active: !rant.isCompletion }"
                        @click="!isRead && (rant.isCompletion = false)"
                      >
                        否
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-item" v-if="rant.isCompletion">
                  <div class="form-label"><span class="required-mark">*</span>结项时间</div>
                  <div class="form-value align-right" @click="!isRead && (handleSelectClosingTime(index))" :class="{'custom-disabled-mobile': isRead}">
                    <van-icon name="clock-o" />{{ rant.closingTime || "请选择结项时间" }}
                  </div>
                </div>

                <div class="form-item">
                  <div class="form-label"><span class="required-mark" v-if="rant.isCompletion">*</span>成果</div>
                  <div class="form-value" @click="!isRead && (triggerFileUpload(index))" :class="{'custom-disabled-mobile': isRead}">
                    <section class="file-wrapper">
                      <template v-if="!rant.fileList || rant.fileList.length === 0">
                        <div class="upload-button" :class="{'file-error': !!rantAchievementFiles.fileListError}">
                          <van-icon name="notes-o" /> {{ rantAchievementFiles.fileListError || "请上传文件" }}
                        </div>
                      </template>
                      <div v-else class="file-list">
                        <div v-for="(file, fileIndex) in rant.fileList" :key="fileIndex" class="file-item">
<!--                          <van-button type="info" size="small" icon="description" @click.stop="openFile(file.url)">
                            {{ file.name || "&#45;&#45;" }}
                          </van-button>-->
                          <!-- :href="file.url" target="_blank"  -->
                          <div class="file-link"  @click.stop="openFile(file.url)">
                            <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
                            <span class="extension-part">{{ getFileExtension(file.name) }}</span>
                          </div>
                          <van-icon name="cross" @click.stop="handleDeleteAnnexUrl(fileIndex, rant.fileList)" class="delete-icon" />
                        </div>
                        <div class="add-file-btn" v-if="false">
                          <van-button type="primary" size="small" icon="plus" plain @click.stop="triggerFileUpload(index)">
                            添加文件
                          </van-button>
                        </div>
                      </div>
                    </section>
                    <input type="file" class="display-none" multiple :id="'uploadFile_' + index"
                      @change="(event) => uploadFileHandle(event, index)" />
                  </div>
                </div>
              </div>
            </van-form>

           <!--  <div class="rant-form-title">
              <img src="@/assets/rant/rant-progress.png" class="icon">进度情况
            </div> -->
            <ProgressList :progressList="rant.recordDtoList" />
            <div class="progress-list" v-if="false">
              <div v-for="(record, recordIndex) in rant.recordDtoList" :key="recordIndex" class="progress-item">
                <div class="progress-header">
                  <div class="progress-number">{{ recordIndex + 1 }}</div>
                  <div class="progress-date">{{ parseTime(record.feedbackTime, "{y}-{m}-{d}") }}</div>
                </div>
                <div class="progress-content" v-html="record.actualProgress"></div>
                <div v-if="record.fileList && record.fileList.length > 0" class="progress-files">
                  <div class="files-title">成果文件：</div>
                  <div v-for="(file, fileIdx) in record.fileList" :key="fileIdx" class="file-link">
<!--                    <van-button type="info" size="small" icon="description" @click.stop="openFile(file.url)">
                      {{ file.name || "&#45;&#45;" }}
                    </van-button>-->
                      <span class="filename-part">{{ getTruncatedFileName(file.name) }}</span>
                      <span class="extension-part">{{ getFileExtension(file.name) }}</span>
                  </div>
                </div>
              </div>
              <div v-if="!rant.recordDtoList || rant.recordDtoList.length === 0" class="no-data">
                暂无进度记录
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 日期选择弹窗 -->
      <van-popup v-model="showDatePicker" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择日期"
          :min-date="isEnable === 1? minDate: undefined"
          @confirm="confirmDate"
          @cancel="showDatePicker = false"
        />
      </van-popup>
    </div>
    <div class="empty-data" v-if="!loading && rantList.length === 0">
      暂无反馈事项
    </div>
    <div class="bottom-buttons" v-if="rantList.length > 0 && !isRead">
      <van-button class="custom-btn-save" @click="handleSave(0)">保 存</van-button>
      <van-button class="custom-btn-submit" @click="handleSubmit(1)">提 交</van-button>
    </div>
  </div>
</template>

<script>
// http://localhost/rant/todoCenterFeedbackOverDateSingle?todoNoticeId=5
// http://localhost/wechatE/mobile/rant/todoCenterFeedbackOverDateSingle?todoNoticeId=5
/*function close() {
  if (
    navigator.userAgent.indexOf("Firefox") != -1 ||
    navigator.userAgent.indexOf("Chrome") != -1
  ) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }

}*/
import {close} from '@/utils'
import ProgressList from '../components/ProgressList/index.vue'
import {feedbackRankMatterList } from "@/api/rantMobile/matters";
import { submitExpireTodoFeedback } from "@/api/rantMobile/record";
import { uploadFileMultiple } from "@/api/rantMobile/common";
import mixin from '../mixins'
// 引入 Vant 组件
import {
  Form,
  Field,
  Button,
  Icon,
  Popup,
  DatetimePicker,
  Toast
} from "vant";

export default {
  name: "Feedback",
  mixins: [mixin],
  // dicts: ["rant_classify", "rant_completion_status", "rant_matters_type"],
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    ProgressList
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      rantList: [], // 吐槽详情
      recordList: [], // 进度记录
      fileList: [],
      rantAchievementFiles: {}, // 附件信息
      uploadFileLoading: false,
      isCompletion: false,
      collapseStates: [], // 控制每个事项的折叠状态
      rantDetail: {}, // 吐槽详情
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      hoverScore: null,

      // 日期选择相关
      showDatePicker: false,
      currentDate: new Date(),
      minDate: new Date(),
      currentRantIndex: 0,
      isRead: false,
      isEnable: null,
      limitDay: null,
    };
  },

  async mounted() {
    await this.fetchMattersType();
    await this.fetchRantClassify();
    const todoNoticeId = this.$route.query.todoNoticeId;
    this.handleGetRantList(todoNoticeId);
    // 添加自动滚动到顶部
  },
 /* async created() {
    await this.getLimitDayIsEnable();
  },*/
  methods: {
   /* async getLimitDayIsEnable() {
      let isEnable = await this.getMobileConfigKey("rant.finish.date.switch"); // 0-关闭 1-开启
      let limitDay = await this.getMobileConfigKey("rant.finish.date.limit.day"); // 设置限制天数
      this.isEnable = parseInt(isEnable.msg);
      this.limitDay = parseInt(limitDay.msg);
      this.minDate = new Date(new Date().getTime() - this.limitDay * 24 * 60 * 60 * 1000)
    },*/
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    toggleCollapse(index) {
      this.$set(this.collapseStates, index, !this.collapseStates[index]);
    },

    handleGetRantList(todoNoticeId) {
      this.loading = true;
      feedbackRankMatterList(todoNoticeId).then((response) => {
        // this.rantList = response.data;  // 这里只会获取到一条督办事项
        this.rantList = response?.data?.rankMattersFeedbackDtoList || [];
        this.isRead = response?.data?.isRead;
        // 初始化折叠状态，默认折叠
        this.collapseStates = new Array(this.rantList.length).fill(true);
        if (this.collapseStates.length > 0) {
          this.collapseStates[0] = false; // 设置第一个元素为 false
        }
        this.$nextTick(() => {
          const container = document.querySelector('html');
          if (container) {
            container.scrollTop = 0;
          }
        });
      }).finally(() => {
        this.loading = false;
      });
    },

    openFile(url) {
      window.open(url);
    },

    triggerFileUpload(index) {
      document.getElementById('uploadFile_' + index).click();
    },

    handleDeleteAnnexUrl(index, fileList) {
      fileList.splice(index, 1);
    },

    uploadFileHandle(event, index) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      const formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        formData.append("files", files[i]);
      }

      this.uploadFileLoading = true;
      uploadFileMultiple(formData)
        .then((response) => {
          if (!this.rantList[index].fileList) {
            this.$set(this.rantList[index], 'fileList', []);
          }

          response.data.forEach((file) => {
            console.log('file', file, typeof file);
            this.rantList[index].fileList.push({
              name: file.name,
              url: file.url
            });
          });
          console.log('this.rantList[index].fileList', this.rantList[index].fileList);
          event.target.value = '';
          Toast.success('上传成功');
        })
        .catch(() => {
          Toast.fail('上传失败');
        })
        .finally(() => {
          this.uploadFileLoading = false;
        });
    },

    handleSelectClosingTime(index) {
      this.currentRantIndex = index;
      this.currentDate = this.rantList[index].closingTime ? new Date(this.rantList[index].closingTime) : new Date();
      this.showDatePicker = true;
    },

    confirmDate(date) {
      this.rantList[this.currentRantIndex].closingTime = this.parseTime(date, "{y}-{m}-{d}");
      this.showDatePicker = false;
    },

    handleSave(submitStatus) {
      if (this.uploadFileLoading) {
        Toast.fail('文件正在上传中，请稍后再试');
        return;
      }

      for (let rant of this.rantList) {
        if (!rant.thisProgress || rant.thisProgress.trim() === '') {
          Toast.fail('请输入本次进展');
          this.loading = false;
          return;
        }

        /*if (rant.isCompletion && !rant.closingTime) {
          Toast.fail('请选择结项时间');
          return;
        }*/
        if (rant.isCompletion) {
          if (!rant.closingTime) {
            Toast.fail("请选择结项时间");
            this.loading = false;
            return;
          }
          if (!rant.fileList || rant.fileList.length === 0) {
            Toast.fail("请上传成果文件");
            this.loading = false;
            return;
          }
        }
      }

      this.submitForm(submitStatus);
    },

    handleSubmit(submitStatus) {
      this.handleSave(submitStatus);
    },

    submitForm(submitStatus) {
      this.loading = true;

      // 构建提交数据
     /* const submitData = this.rantList.map(rant => ({
        id: rant.id,
        thisProgress: rant.thisProgress,
        isCompletion: rant.isCompletion,
        closingTime: rant.closingTime,
        fileIds: rant.fileList?.map(file => ({
          fileName: file.name,
          filePath: file.url
        })) || [],
        submitStatus: submitStatus
      }));*/
      const item = this.rantList[0] || {}
      submitExpireTodoFeedback({
        todoNoticeId: this.$route.query.todoNoticeId,
        id: item.id,
        lastProgressId: item.lastProgressId,
        rantMattersId: item.id,
        thisProgress: item.thisProgress,
        isCompletion: item.isCompletion,
        closingTime: item.closingTime,
        fileList: item.fileList,
        submitStatus: submitStatus,
      })
        .then((res) => {
          if (res.code === 200) {
            if (submitStatus === 0) {
              Toast.success('保存成功');
            } else {
              Toast.success('提交成功，即将关闭页面');
              setTimeout(() => {
                close();
              }, 1500);
            }
          } else {
            Toast.fail(res.msg || '操作失败');
          }
        })
        .catch(() => {
          Toast.fail('操作失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    parseTime(time, pattern) {
      if (!time) return '';
      let date = new Date(time);
      let formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      };
      const timeStr = pattern.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        if (key === 'a') {
          return ['日', '一', '二', '三', '四', '五', '六'][value];
        }
        if (result.length > 0 && value < 10) {
          value = '0' + value;
        }
        return value || 0;
      });
      return timeStr;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../css/common.scss';
.file-link{
  color: #376DF7;
  font-size: 12px;
  font-weight: 400;
  max-width: 100%;
  display: inline-flex;
  .filename-part{
    min-width: 0;
    flex-grow: 1;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .extension-part{
    word-break: keep-all;
  }
}
.form-value{
  min-width: 0;
}
</style>
