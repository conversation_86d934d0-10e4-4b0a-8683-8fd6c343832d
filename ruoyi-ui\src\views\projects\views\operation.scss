@import '~@/views/projects/styles/projects.scss';
.history-dialog ::v-deep .el-dialog__body {
  padding-top: 5px;
}
.history-dialog ::v-deep .el-dialog__header{
  padding: 20px;
}
.cost-content {
  .card-content {
    padding: 1rem;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
  }

  .chart-size-220 {
    height: 13.75rem;
    width: 100%;
  }

  .card-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 0.875rem;
    color: #222222;
    line-height: 1.375rem;
    text-align: left;
  }

  .cost-card-item {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    min-height: 0px;
  }

  .cost-card-item-content {
    flex: 1;
    padding: 0.75rem;
    border-radius: 4px;

    .title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 0.875rem;
      color: #222222;
      line-height: 1.375rem;
      text-align: left;
    }

    .data-content {
      font-family: PingFang SC;
      line-height: 1.375rem;
      text-align: center;
      display: flex;
      justify-content: space-evenly;
      margin-top: -1.25rem;

      .value-item {
        .value {
          font-weight: 500;
          font-size: 1.25rem;
          margin-bottom: 0.5rem;
        }

        .label {
          font-weight: 400;
          font-size: 0.75rem;
          color: #666666;
        }
      }
    }

    &.cost {
      background: rgba(0, 106, 255, 0.05);
      .value-item .value { color: #006AFF; }
    }

    &.change {
      background: rgba(61, 204, 133, 0.05);
      .value-item .value { color: #3DCC85; }
    }

    &.marketing {
      background: rgba(255, 151, 77, 0.05);
      .value-item .value { color: #FF974D; }
    }

    &.sign {
      background: rgba(109, 61, 204, 0.05);
      .value-item .value { color: #6D3DCC; }
    }

    &.payment {
      background: rgba(61, 204, 204, 0.05);
      .value-item .value { color: #3DCCCC; }
    }
  }
}

.analysis {
  display: flex;
  gap: 2rem;

  .analysis-item {
    flex: 1;
  }

  .cost-card-item {
    // height: 26.125rem;
  }
}

.header-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  .header-icon {
    margin-left: 0.5rem;
    width: 0.875rem;
    height: 0.875rem;
  }
}
.expand-icon {
  cursor: pointer;
}


.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.7);
}

.analysis-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;

  th, td {
    border: 0.125rem solid rgba(0,106,255,0.2);
    padding: 0.5rem;
    font-size: 0.875rem;
    line-height: 1rem;
  }

  th {
    background: #E1EFFD;
    color: #666666;
    font-weight: 400;
    white-space: nowrap;
  }

  td {
    color: #333;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }
}

.header-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;

  .header-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
}

.expand-icon {
  display: inline-block;
  width: 1rem;
  text-align: center;
  margin-right: 0.25rem;
}
.analysis-table-body{
  // max-height: 25rem;
  // overflow-y: auto;
}

.table-wrapper {
  // max-height: 25rem;
  // overflow-y: auto;
  // border: 0.125rem solid rgba(0,106,255,0.2);

  &::-webkit-scrollbar {
    width: 0.375rem;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 0.185rem;
  }
}

thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #E1EFFD;

  th {
    position: sticky;
    top: 0;
    background: #E1EFFD;
  }
}

.history-dialog {
  //background: rgba(0, 0, 0, 0.9);
  .el-dialog {
    max-width: 1400px;
    margin: 0 auto;

    .el-dialog__body {
      padding: 20px;
    }
  }

  .el-table {
    width: 100%;

    .el-table__header-wrapper {
      th {
        background: #E1EFFD !important;
      }
    }

    .el-table__fixed {
      height: 100% !important;

      .el-table__fixed-header-wrapper {
        th {
          background: #E1EFFD !important;
        }
      }
    }
  }

  // Override element-ui dialog close button
  .el-dialog__headerbtn {
    display: none;
  }

  // Custom close button
  .dialog-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    border-radius: 50%;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06);
    }

    i {
      font-size: 20px;
      color: #909399;
    }
  }
}
