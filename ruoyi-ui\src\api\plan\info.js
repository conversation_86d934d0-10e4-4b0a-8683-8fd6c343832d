import request from '@/utils/request'

// 查询计划列表
export function listInfo (query) {
  return request({
    url: '/plan/info/list',
    method: 'get',
    params: query
  })
}

// 查询计划调整列表
export function adjustmentListInfo (query) {
  return request({
    url: '/plan/info/adjustmentList',
    method: 'get',
    params: query
  })
}

// 查询计划详细
export function getInfo (id) {
  return request({
    url: '/plan/info/' + id,
    method: 'get'
  })
}

// 查询调整计划详细
export function getAdjustInfo (id) {
  return request({
    url: '/plan/info/adjust/' + id,
    method: 'get'
  })
}

// 新增计划
export function addInfo (data) {
  return request({
    url: '/plan/info',
    method: 'post',
    data: data
  })
}

// 修改计划
export function updateInfo (data) {
  return request({
    url: '/plan/info',
    method: 'put',
    data: data
  })
}

// 删除计划
export function delInfo (id) {
  return request({
    url: '/plan/info/' + id,
    method: 'delete'
  })
}
// 查询计划历史版本列表
export function listhistory (query) {
  return request({
    url: '/plan/info/history',
    method: 'get',
    params: query
  })
}

// 查询计划状态
export function getPlanStatus (id) {
  return request({
    url: '/plan/info/getPlanStatus/' + id,
    method: 'get'
  })
}
// 获取审批人
export function getApproveUser (id) {
  return request({
    url: '/plan/info/getApproveUser/' + id,
    method: 'get'
  })
}

// 获取审批流
export function getApproveFlow (id) {
  return request({
    url: '/plan/info/approvalForm/' + id,
    method: 'get'
  })
}
