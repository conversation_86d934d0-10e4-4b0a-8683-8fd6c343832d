<script>
export default {
  name: 'import',
  dicts: ["quarter", "assess_stage", "deliver_modality"],
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {

    }
  },
  methods: {
    getDataList() {
      return this.dataList;
    },
    handleDelete(row, index) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.dataList.splice(index, 1);
        this.$message({
          type: 'success',
          message: '删除成功',
        });
      });
    },
  }
}
</script>

<template>
  <div>
    <slot :dataList="dataList" :handleDelete="handleDelete">
      <!-- 默认表格，如果没有提供自定义内容，则显示此表格 -->
      <el-table :data="dataList">
        <el-table-column label="序号" align="center" prop="id" width="50">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工程项目" align="center" prop="gcProjectName" />
        <el-table-column label="年份" align="center" prop="year" />
        <el-table-column label="季度" align="center" prop="quarter">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.quarter" :value="scope.row.quarter"/>
          </template>
        </el-table-column>
        <el-table-column label="评估阶段" align="center" prop="assessStage">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.assess_stage" :value="scope.row.assessStage"/>
          </template>
        </el-table-column>
        <el-table-column label="总分" align="center" prop="totalScore" />
        <el-table-column label="实测实量分数" align="center" prop="actualMeasureScore" />
        <el-table-column label="质量风险分数" align="center" prop="qualityRiskScore" />
        <el-table-column label="安全文明分数" align="center" prop="secureCultureScore" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row, scope.$index)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </slot>
  </div>
</template>

<style scoped lang="scss">

</style>
